package com.swcares.psi.aoMergeOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.dto.AoMergeApplyPageDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCounterUpgrade;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeUpgradeStatusUpDateVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并订单柜台升舱产品详情订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeCounterUpgradeMapper extends BaseMapper<AoMergeCounterUpgrade> {
    List<AoMergeCounterUpgradeAccountsReportVo> getAccountsList(@Param("dto") AoMergeCounterUpgradeAccountsReportDto dto);
    PsiPage<AoMergeCounterUpgradeAccountsReportVo> getAccountsListPage(PsiPage<AoMergeCounterUpgradeAccountsReportVo> page, @Param("dto") AoMergeCounterUpgradeAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(@Param("dto") AoMergeCounterUpgradeAccountsReportDto dto);
    PsiPage<AoMergeUpgradeStatusUpDateVo> applyPage( PsiPage<AoMergeUpgradeStatusUpDateVo> pag,@Param("dto") AoMergeApplyPageDto dto);
}
