<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.common.mapper.AoTempCashReturnMapper">
    <delete id="deleteByEntity" parameterType="com.swcares.psi.ao.common.entity.AoTempCashReturnEntity">
        delete from ao_temp_cash_return
        <where>
            <if test="entity.id != null and entity.id != ''">
                ID=#{entity.id}
            </if>

            <if test="entity.callbackPayNo != null and entity.callbackPayNo != ''">
                AND CALLBACK_PAY_NO=#{entity.callbackPayNo}
            </if>

            <if test="entity.orderNo != null and entity.orderNo != ''">
                AND ORDER_NO=#{entity.orderNo}
            </if>

            <if test="entity.cashReceiveUserId != null and entity.cashReceiveUserId != ''">
                AND CASH_RECEIVE_USER_ID=#{entity.cashReceiveUserId}
            </if>

        </where>
    </delete>
    <delete id="deleteByOrderNo" parameterType="String">
        delete from ao_temp_cash_return
        <where>
            ORDER_NO in
            <foreach collection="param" item="ele" open="(" close=")" separator=",">
                #{ele}
            </foreach>
            and CALLBACK_PAY_NO=#{callbackPayNo}
        </where>
    </delete>

    <select id="getCashReceiveTransfer" resultType="com.swcares.psi.ao.common.entity.AoTempCashReturnEntity">
        select CALLBACK_PAY_NO,
               QRCODE_PATH,
               CHARGE_TYPE,
               CASH_RECEIVE_USER_ID,
               CREATE_TIME
        from ao_temp_cash_return
        where TURN_STATUS='0' and ORDER_TYPE in ('1','2','3','4','5','0')
        group by   CALLBACK_PAY_NO,
                   QRCODE_PATH,
                   CHARGE_TYPE,
                   CASH_RECEIVE_USER_ID,
                   CREATE_TIME
    </select>



</mapper>