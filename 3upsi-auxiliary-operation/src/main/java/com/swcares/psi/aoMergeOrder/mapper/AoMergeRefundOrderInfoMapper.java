package com.swcares.psi.aoMergeOrder.mapper;

import com.swcares.psi.aoMergeOrder.dto.AoMergeH5RefundOrderListDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeRefundOrderInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.vo.AoMergeH5RefundOrderListVo;
import com.swcares.psi.aoMergeOrder.vo.RefundOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.RefundSendTaskVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付退款订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeRefundOrderInfoMapper extends BaseMapper<AoMergeRefundOrderInfo> {

    List<RefundOrderTaskVo> getRefundOrderAll();
    List<RefundSendTaskVo> getRefundSendOrder();
    PsiPage<AoMergeH5RefundOrderListVo> h5RefundOrderList( PsiPage<AoMergeH5RefundOrderListVo> page,@Param("dto") AoMergeH5RefundOrderListDto dto,@Param("userId") String userId,@Param("userNo") String userNo);
}
