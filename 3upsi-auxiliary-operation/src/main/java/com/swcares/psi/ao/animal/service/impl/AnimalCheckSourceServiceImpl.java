package com.swcares.psi.ao.animal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.animal.dto.ReserveDto;
import com.swcares.psi.ao.animal.entity.AoAnimalCheckSource;
import com.swcares.psi.ao.animal.mapper.AoAnimalCheckSourceMapper;
import com.swcares.psi.ao.animal.service.AnimalCheckSourceService;
import com.swcares.psi.base.util.Asserts;
import com.swcares.psi.combine.constant.MessageCode;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName：AnimalCheckSourceServiceImpl
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/1/10 15:09
 * @version： v1.0
 */
@Service
public class AnimalCheckSourceServiceImpl  extends ServiceImpl<AoAnimalCheckSourceMapper, AoAnimalCheckSource>  implements AnimalCheckSourceService {


//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAnimalCheckSource(ReserveDto reserve) {
        Asserts.isNotEmpty(reserve.getTicketNum(), MessageCode.PARAM_IS_NULL.getCode());
        AoAnimalCheckSource checkSource = new AoAnimalCheckSource();
        checkSource.setReserveId(reserve.getId());
        checkSource.setCreateTime(new Date());
        checkSource.setSourceJson(JSON.toJSONString(reserve));
        this.save(checkSource);
    }
}
