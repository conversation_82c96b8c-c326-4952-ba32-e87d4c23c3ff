package com.swcares.psi.aoMergeOrder.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashReturnPayOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergePayOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeRefundOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeReportEventLog;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSubOrderJoinCashReturnPay;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.payHander.AliMergePayClientConfig;
import com.swcares.psi.aoMergeOrder.service.AoMergeCashReturnPayOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.AoMergeReportEventLogService;
import com.swcares.psi.aoMergeOrder.service.AoMergeSubOrderJoinCashReturnPayService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergePayOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeRefundOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.base.data.api.common.SmsBusinessTypeEnum;
import com.swcares.psi.base.data.api.common.SmsSendType;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.api.entity.SmsSendRecord;
import com.swcares.psi.base.data.api.entity.SmsTemplate;
import com.swcares.psi.base.data.api.vo.PaxInfoParseVo;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.FltPassengerRealInfoService;
import com.swcares.psi.base.data.service.SmsService;
import com.swcares.psi.base.data.service.SmsTemplateService;
import com.swcares.psi.base.util.DateUtils;
import com.swcares.psi.collect.qrCode.alipay.AliNativeProcess;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.retrievephone.PaxerPhoneUtil;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.worldpay.WorldpayUtil;
import com.swcares.psi.common.worldpay.entity.WpRecord;
import com.swcares.psi.common.worldpay.mapper.WpRecordMapper;
import com.swcares.psi.stimulate.utils.StimulateOrderUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class AoMegerOrderNotifyService {

    @Value("${electronic_voucher.param.key}")
    private String key;

    @Value("${electronic_voucher.sms.merge_path}")
    private String path;

    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;
    @Autowired
    IAoMergePayOrderInfoService aoMergePayOrderInfoService;

    @Autowired
    AoMegerOrderNotifyLockService aoMegerOrderNotifyLockService;

    @Autowired
    AliNativeProcess aliNativeProcess;
    @Resource(name = "mergeOrderAliPayConfig")
    AliMergePayClientConfig alipayClientConfig;

    @Autowired
    FltPassengerRealInfoService passengerRealInfoService;
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;

    @Autowired
    private PaxerPhoneUtil paxerPhoneUtil;

    @Autowired
    private SmsTemplateService smsTemplateService;

    @Autowired
    private SmsService smsService;

    @Autowired
    AoMergeCashReturnPayOrderInfoService aoMergeCashReturnPayOrderInfoService;

    @Autowired
    AoMergeSubOrderJoinCashReturnPayService aoMergeSubOrderJoinCashReturnPayService;
    @Autowired
    private IAoMergeRefundOrderInfoService aoMergeRefundOrderInfoService;
    @Autowired
    private AoCommonService aoCommonService;
    @Autowired
    private AoMergeReportEventLogService aoMergeReportEventLogService;

    @Autowired
    private WorldpayUtil worldpayUtil;
    @Autowired
    private WpRecordMapper wpRecordMapper;

    @Autowired
    private StimulateOrderUtils stimulateOrderUtils;
    /**
     * 微信支付结果回调处理
     * @param map
     */
    public void wxPayNotify(Map<String, String> map ,AoMergeOrderInfo mergeOrderInfo){
        if (map == null) {
            return;
        }
        String out_trade_no = map.get("out_trade_no");
        String return_code = map.get("return_code");
        String time_end = map.get("time_end");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        log.info("微信扫码支付回调订单触发,订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        //支付成功
        if ("SUCCESS".equals(return_code)) {
            if (mergeOrderInfo == null) {
                log.info("微信支付回调,订单不存在,订单号{}", out_trade_no);
                return;
            }else if(!AoMergeCons.MERGE_ORDER_STATUS_UNPAY.equals(mergeOrderInfo.getMergeOrderStatus())){
                log.info("微信支付回调订单触发,订单{}状态发生改变 ", out_trade_no);
                //todo-hc 通知
                return;
            }
            paySuccess(mergeOrderInfo,  DateUtils.parseStringToLocalDateTime(time_end,DateUtils.YYYY_MM_DD_HH_MM_SS), LocalDateTime.now(), "微信支付回调");

        } else {
            //todo-hc  其他情况待处理
        }
    }


    /**
     * 微信缴纳支付结果回调处理
     * @param map
     */
    public void wxpayTurnNotifyUrl(Map<String, String> map ){
        if (map == null) {
            return;
        }
        String out_trade_no = map.get("out_trade_no");
        String return_code = map.get("return_code");
        String time_end = map.get("time_end");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        log.info("微信缴纳支付回调订单触发,订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        //支付成功
        if ("SUCCESS".equals(return_code)) {
            LocalDateTime turnTime = DateUtils.parseStringToLocalDateTime(time_end, DateUtils.YYYY_MM_DD_HH_MM_SS);
            AoMergeCashReturnPayOrderInfo one = aoMergeCashReturnPayOrderInfoService.lambdaQuery()
                    .eq(AoMergeCashReturnPayOrderInfo::getPayOrderNo, out_trade_no).one();
            one.setPayStatus(AoMergeCashReturnPayOrderInfo.PAY_ORDER_PAY_STATUS_SUCCESS);
            one.setPayTime(turnTime);
            aoMergeCashReturnPayOrderInfoService.saveOrUpdate(one);
            List<AoMergeSubOrderJoinCashReturnPay> list = aoMergeSubOrderJoinCashReturnPayService.lambdaQuery()
                    .eq(AoMergeSubOrderJoinCashReturnPay::getCashReturnPayId, one.getId())
                    .list();
            List<String> collect = list.stream().map(AoMergeSubOrderJoinCashReturnPay::getMergeOrderId).distinct().collect(Collectors.toList());

            aoMergeOrderInfoService.lambdaUpdate()
                    .in(AoMergeOrderInfo::getId,collect)
                    .set(AoMergeOrderInfo::getTurnStatus,AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                    .set(AoMergeOrderInfo::getTurnTime,turnTime)
                    .set(AoMergeOrderInfo::getTurnNumber,out_trade_no)
                    .update();

        } else {
            //todo-hc  其他情况待处理
        }
    }



    /**
     * 支付宝扫码支付后异步通知
     */
    @Transactional(rollbackFor = Exception.class)
    public void alipayNotifyUrl(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = convertRequestParamsToMap(request);
        try {

            boolean signVerified = AlipaySignature.rsaCheckV1(map, alipayClientConfig.getPublicKey(),
                    alipayClientConfig.getCharset(), alipayClientConfig.getSigType());
            if (!signVerified) {
                log.error("支付宝回调签名认证失败,paramsJson:{}", map);
                return;
            }
        } catch (AlipayApiException e) {
            log.error("支付宝回调签名认证异常,paramsJson:{},errorMsg:{}", map, e.getMessage(), e);
            return;
        }

        String total_amount = parameterMap.get("total_amount")[0];
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        String trade_status = parameterMap.get("trade_status")[0];
        String gmt_payment = parameterMap.get("gmt_payment")[0];
        String trade_no = parameterMap.get("trade_no")[0];
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo, out_trade_no)
                .one();
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getId, payOrderInfo.getId())
                .one();
        if (mergeOrderInfo == null) {
            log.info("支付宝扫码支付回调订单触发,订单{}不存在 ", out_trade_no);
            return;
        }else if(!AoMergeCons.MERGE_ORDER_STATUS_UNPAY.equals(mergeOrderInfo.getMergeOrderStatus())){
            log.info("支付宝支付回调订单触发,订单{}状态发生改变 ", out_trade_no);
            //todo-hc 通知
            return;
        }
        //支付成功
        if ("TRADE_SUCCESS".equals(trade_status)) {
            paySuccess(mergeOrderInfo, DateUtils.parseStringToLocalDateTime(gmt_payment,DateUtils.YYYY_MM_DD_HH_MM_SS), LocalDateTime.now(), "支付宝支付回调");

        } else {
            log.info("支付宝扫码支付回调订单触发,订单{}没有支付成功->>{}", trade_status, JSON.toJSONString(parameterMap));
        }

    }

    /**
     * 支付宝缴纳支付结果回调处理
     * @param request
     */
    public void alipay_turn_notify_url(HttpServletRequest request ){
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = convertRequestParamsToMap(request);
        try {

            boolean signVerified = AlipaySignature.rsaCheckV1(map, alipayClientConfig.getPublicKey(),
                    alipayClientConfig.getCharset(), alipayClientConfig.getSigType());
            if (!signVerified) {
                log.error("支付宝回调签名认证失败,paramsJson:{}", map);
                return;
            }
        } catch (AlipayApiException e) {
            log.error("支付宝回调签名认证异常,paramsJson:{},errorMsg:{}", map, e.getMessage(), e);
            return;
        }
        String total_amount = parameterMap.get("total_amount")[0];
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        String trade_status = parameterMap.get("trade_status")[0];
        String gmt_payment = parameterMap.get("gmt_payment")[0];
        String trade_no = parameterMap.get("trade_no")[0];
        //支付成功
        if ("SUCCESS".equals(trade_status)) {
            LocalDateTime turnTime = DateUtils.parseStringToLocalDateTime(gmt_payment, DateUtils.YYYY_MM_DD_HH_MM_SS);
            AoMergeCashReturnPayOrderInfo one = aoMergeCashReturnPayOrderInfoService.lambdaQuery()
                    .eq(AoMergeCashReturnPayOrderInfo::getPayOrderNo, out_trade_no).one();
            one.setPayStatus(AoMergeCashReturnPayOrderInfo.PAY_ORDER_PAY_STATUS_SUCCESS);
            one.setPayTime(turnTime);
            aoMergeCashReturnPayOrderInfoService.saveOrUpdate(one);
            List<AoMergeSubOrderJoinCashReturnPay> list = aoMergeSubOrderJoinCashReturnPayService.lambdaQuery()
                    .eq(AoMergeSubOrderJoinCashReturnPay::getCashReturnPayId, one.getId())
                    .list();
            List<String> collect = list.stream().map(AoMergeSubOrderJoinCashReturnPay::getMergeOrderId).distinct().collect(Collectors.toList());

            aoMergeOrderInfoService.lambdaUpdate()
                    .in(AoMergeOrderInfo::getId,collect)
                    .set(AoMergeOrderInfo::getTurnStatus,AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                    .set(AoMergeOrderInfo::getTurnTime,turnTime)
                    .set(AoMergeOrderInfo::getTurnNumber,out_trade_no)
                    .update();

        } else {
            //todo-hc  其他情况待处理
        }
    }


    private static Map<String, String> convertRequestParamsToMap(HttpServletRequest request) {
        Map<String, String> retMap = new HashMap<String, String>();

        Set<Map.Entry<String, String[]>> entrySet = request.getParameterMap().entrySet();

        for (Map.Entry<String, String[]> entry : entrySet) {
            String name = entry.getKey();
            String[] values = entry.getValue();
            int valLen = values.length;

            if (valLen == 1) {
                retMap.put(name, values[0]);
            } else if (valLen > 1) {
                StringBuilder sb = new StringBuilder();
                for (String val : values) {
                    sb.append(",").append(val);
                }
                retMap.put(name, sb.toString().substring(1));
            } else {
                retMap.put(name, "");
            }
        }

        return retMap;
    }


    @Transactional(rollbackFor = Exception.class)
    public void yeePayNotify(JSONObject jsonObject,AoMergeOrderInfo mergeOrderInfo) {
        LocalDateTime now = LocalDateTime.now();
        String customerRequestNo = jsonObject.get("customerRequestNo").toString();
        String retCode = jsonObject.get("retCode").toString();
        String accountAmount = jsonObject.get("accountAmount").toString();
        String bankOrderNo = jsonObject.get("bankOrderNo").toString();
        log.info("易宝支付回调订单触发,订单号-{} ,订单金额-{} ,支付结果-{},银行订单号-{} ", customerRequestNo, accountAmount, "0000".equals(retCode), bankOrderNo);

        if (mergeOrderInfo == null || (!mergeOrderInfo.equals(AoMergeCons.MERGE_ORDER_STATUS_UNPAY))){
            log.info("易宝支付回调订单触发,订单{}不存在 ", customerRequestNo);
            return;
        }

        if ("0000".equals(retCode)) {
            paySuccess(mergeOrderInfo, now, now, "易宝支付回调");
        } else {
            //todo-hc 支付失败
            log.info("易宝支付失败回调触发:{} ", jsonObject.toString());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void worldpayNotify(String orderNo) {
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getOrderNo, orderNo)
                .one();
        if (mergeOrderInfo == null) {
            log.info("worldPay支付回调订单触发,订单{}不存在 ", orderNo);
            return;
        }else if(!(AoMergeCons.MERGE_ORDER_STATUS_UNPAY.equals(mergeOrderInfo.getMergeOrderStatus())||AoMergeCons.MERGE_ORDER_STATUS_SUCCESS_ING.equals(mergeOrderInfo.getMergeOrderStatus()))){
            log.info("worldPay支付回调订单触发,订单{}状态发生改变 ", orderNo);
            //todo-hc 通知
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        paySuccess(mergeOrderInfo, now, now, "worldPay支付回调");

    }

    @Transactional(rollbackFor = Exception.class)
    public void worldpayRefundNotifyUrl(String payOrderNo,String subOrderNo) {
        AoMergeSuborderInfo one = aoMergeSuborderInfoService.lambdaQuery().eq(AoMergeSuborderInfo::getSuborderOrderNo, subOrderNo).one();
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo, payOrderNo).one();
        aoMergeRefundOrderInfoService.lambdaUpdate()
                .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, one.getAoMergeOrderInfoId())
                .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, one.getId())
                .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)
                .set(AoMergeRefundOrderInfo::getRefundEndTime,LocalDateTime.now())
                .update();

        aoMergeSuborderInfoService.lambdaUpdate()
                .eq(AoMergeSuborderInfo::getId,one.getId())
                .set(AoMergeSuborderInfo::getSuborderOrderStatus,AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus,AoMergeCons.SUBORDER_PAY_STATUS_REFUND)
                .update();

        AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
        aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
        aoMergeReportEventLog.setAoMergeSuborderInfoId(one.getId());
        aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
        aoMergeReportEventLog.setEventExplain("退款完成");
        aoMergeReportEventLogService.save(aoMergeReportEventLog);

        aoCommonService.refundStatusUpdate(one.getAoMergeOrderInfoId());
    }


    @Transactional(rollbackFor = Exception.class)
    public void worldpayCapturedNotifyUrl(String mergeOrderNo) {
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getOrderNo, mergeOrderNo)
                .one();
        if (mergeOrderInfo == null) {
            log.info("captured回调订单不存在{}", mergeOrderNo);
        } else  {

            LambdaQueryWrapper<WpRecord> capture = Wrappers.<WpRecord>lambdaQuery().eq(WpRecord::getOrderNo, mergeOrderInfo.getOrderNo())
                    .eq(WpRecord::getOptionType, "CAPTURE");
            Integer integer = wpRecordMapper.selectCount(capture);
            if (integer < 1) {

                List<AoMergeRefundOrderInfo> list = aoMergeRefundOrderInfoService.lambdaQuery()
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                        .list();
                if (list.size() > 0) {
                    AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.getById(mergeOrderInfo.getOrderPayId());
                    for (AoMergeRefundOrderInfo ele : list) {
                        AoMergeSuborderInfo suborderInfo = aoMergeSuborderInfoService.getById(ele.getAoMergeSuborderInfoId());
                        ele.setRefundSendTime(LocalDateTime.now());
                        ele.setRefundRequestSend(AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES);
                        worldpayUtil.orderRefund(payOrderInfo.getPayOrderNo(), payOrderInfo.getCurrencySpecies(), suborderInfo.getPracticalOrderPrice().toString(), suborderInfo.getSuborderOrderNo());
                        aoMergeRefundOrderInfoService.saveOrUpdate(ele);
                    }
                }

                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                        .eq(AoMergeSuborderInfo::getPermitRefund, AoMergeCons.SUBORDER_PERMIT_REFUND_NO)
                        .set(AoMergeSuborderInfo::getPermitRefund, AoMergeCons.SUBORDER_PERMIT_REFUND_YES)
                        .update();
            }
        }
    }





    public void paySuccess(AoMergeOrderInfo mergeOrderInfo, LocalDateTime now, LocalDateTime now2, String recordFrom) {
        mergeOrderInfo.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_SUCCESS);
        aoMergeOrderInfoService.saveOrUpdate(mergeOrderInfo);
        List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                .eq(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_ORDER_STATUS_UNPAY)
                .eq(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_PAY_STATUS_UNPAY)
                .list();
        AoMergePayOrderInfo one = aoMergePayOrderInfoService.getById(mergeOrderInfo.getOrderPayId());
        one.setPayTime(now);
        one.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_SUCCESS);
        aoMergePayOrderInfoService.saveOrUpdate(one);
        for (AoMergeSuborderInfo ele : list) {
            if (AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY.equals(one.getPayType())) {
                aoMergeSuborderInfoService.lambdaUpdate()
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS)
                        .eq(AoMergeSuborderInfo::getId, ele.getId())
                        .update();
            } else {
                aoMergeSuborderInfoService.lambdaUpdate()
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS)
                        .set(AoMergeSuborderInfo::getPermitRefund, AoMergeCons.SUBORDER_PERMIT_REFUND_YES)
                        .eq(AoMergeSuborderInfo::getId, ele.getId())
                        .update();
            }

            AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
            aoMergeReportEventLog.setCreateTime(now2);
            aoMergeReportEventLog.setAoMergeSuborderInfoId(ele.getId());
            aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS);
            aoMergeReportEventLog.setEventExplain("支付完成");
            aoMergeReportEventLogService.save(aoMergeReportEventLog);


            //给国内逾重旅客发送短信
            if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
                smsOverWeighLectronicVoucher(ele.getPayPaxId(), ele.getSuborderOrderNo());
            }
            //处理支付成功之后的升舱功能处理
            if (ele.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_UPGRADE)) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        aoMegerOrderNotifyLockService.upgradeThreadFunction(ele.getId());
                    }
                }).start();
            }
        }
    }


    /**
     * 给国内逾重旅客发送短信
     *
     * @param paxId     旅客id
     * @param orderNo   子订单订单号
     */
    public void smsOverWeighLectronicVoucher(String paxId, String orderNo) {


        PaxInfoParseVo passengerById = passengerRealInfoService.getPassengerById(paxId);

        List<FltFlightRealInfo> list = fltFlightRealInfoService.lambdaQuery()
                .eq(FltFlightRealInfo::getFlightDate, com.swcares.psi.common.utils.DateUtils.parseStrToDate(passengerById.getFlightDate(), com.swcares.psi.common.utils.DateUtils.YYYY_MM_DD))
                .eq(FltFlightRealInfo::getFlightNumber, passengerById.getFlightNum())
                .list();
        if(list.size()<1 ||  !"D".equals(list.get(0).getFlightType())) {
            return;
        }

        log.info("开始发送国内逾重电子凭证短信通知,旅客-{},paxid-{}", passengerById.getPsgName(), paxId);
        String paxPhoneNum =null;
        Map<String, String> paxersPhones = paxerPhoneUtil.getPaxersPhones(Collections.singletonList(paxId));
        if(paxersPhones.get(paxId)==null){
            log.info("国内逾重电子凭证短信电话号码获取失败,旅客-{},paxid-{}",passengerById.getPsgName(), paxId);
            return;
        }else{
            paxPhoneNum=paxersPhones.get(paxId);
        }
        SmsTemplate smsTemplate;
        try {
            smsTemplate = smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.AO_OVER_WEIGHT_ELECTRONIC_VOUCHER.getCode());
        } catch (BusinessException bue) {
            log.info("国内逾重电子凭证短信模板不存在,旅客-{},paxid-{}", passengerById.getPsgName(), paxId);
            return;
        }
        String templateContent = smsTemplate.getTemplateContent();
        String orderNoParam = AesEncryptUtil.aesEncrypt(key, orderNo);
        //nacos配置path必须以参数`param=`结尾
        String content = templateContent + path + orderNoParam;
        try {
            if (StringUtils.isNotEmpty(paxPhoneNum)) {
                log.info("旅客-{},paxid-{} 获取联系方式成功！{},发送国内逾重电子凭证短信通知", passengerById.getPsgName(), paxId, paxPhoneNum);

                SmsSendRecord smsSendRecord = smsService.send(smsTemplate, content, paxPhoneNum.split(","), false, SmsSendType.MANUAL, sendRecord -> {
                    sendRecord.setReciverName(passengerById.getPsgName());
                    sendRecord.setPnrRef(passengerById.getPnrRef());
                    sendRecord.setFlightNo(passengerById.getFlightNum());
                    sendRecord.setOrg(passengerById.getOrig());
                    sendRecord.setDst(passengerById.getDest());
                    sendRecord.setTktNo(passengerById.getEtNum());
                    DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime flightDate = LocalDateTime.parse(passengerById.getFlightDate() + " 00:00:00", dtf2);
                    sendRecord.setFlightDate(flightDate);
                    sendRecord.setSendUser(AuthenticationUtil.getUserNo());
                    sendRecord.setSegment(passengerById.getOrig() + "-" + passengerById.getDest());
                });
                if (smsSendRecord == null || !"0".equals(smsSendRecord.getSendState())) {
                    log.info("国内逾重电子凭证短信发送失败,旅客 {} 联系方式{}", passengerById.getPsgName(), paxPhoneNum);
                }
            } else {
                log.info("旅客-{},paxid-{} 获取联系方式失败！,不执行国内逾重电子凭证短信发送通知", passengerById.getPsgName(), paxId);
            }
        } catch (Exception e) {
            log.error("国内逾重电子凭证短信发送异常：{}", e);
        }
    }

    public String worldpayResultAffirm(String orderNo) {
        AoMergeOrderInfo one = aoMergeOrderInfoService.lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, orderNo)
                .one();
        if(one==null){
            log.info("worldPay前端支付确认订单不存在{}",orderNo);
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }
        Integer count = aoMergePayOrderInfoService.lambdaQuery()
                .eq(AoMergePayOrderInfo::getId, one.getOrderPayId())
                .eq(AoMergePayOrderInfo::getPayType, AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY)
                .count();
        if(count<1){
            log.info("worldPay前端支付确认订单不是worldPay支付{}",orderNo);
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }else  if(AoMergeCons.MERGE_ORDER_STATUS_UNPAY.equals(one.getMergeOrderStatus())){
            one.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_SUCCESS_ING);
            aoMergeOrderInfoService.saveOrUpdate(one);
            return AoMergeCons.MERGE_ORDER_STATUS_SUCCESS_ING;

        }
        return one.getMergeOrderStatus();
    }
}
