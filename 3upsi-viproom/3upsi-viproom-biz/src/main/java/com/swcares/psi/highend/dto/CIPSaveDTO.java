package com.swcares.psi.highend.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.psi.common.utils.encryption.Encryption;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：$.$
 * Description：(CIP新增和修改的DTO)
 * Copyright © 2022$ xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/3/4 14:28
 * @version v1.0
 */
@Data
public class CIPSaveDTO {
    @ExcelIgnore
    @ApiModelProperty(value = "主键")
    private String id;
    @ExcelProperty(value = "旅客姓名")
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;
    @ExcelProperty(value = "所属单位")
    @ApiModelProperty(value = "所属单位")
    private String compName;
    @ExcelProperty(value = "旅客性别")
    @ApiModelProperty(value = "旅客性别，M-男，F-女")
    private String sex;
    @Encryption
    @ExcelProperty(value = "单位联系电话")
    @ApiModelProperty(value = "单位联系电话")
    private String compTel;
    @ExcelProperty(value = "证件类型")
    @ApiModelProperty(value = "证件类型")
    private String idType;
    @ExcelProperty(value = "职位")
    @ApiModelProperty(value = "职位")
    private String jobPosition;
    @Encryption
    @ExcelProperty(value = "证件号码")
    @ApiModelProperty(value = "证件号码")
    private String idNo;
    @ExcelProperty(value = "称谓")
    @ApiModelProperty(value = "称谓")
    private String paxTitle;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty(value = "出生日期")
    @ApiModelProperty(value = "出生日期")
    private Date birthDt;
    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

}
