package com.swcares.psi.ao.register.controller;

import com.swcares.psi.ao.register.constant.CrmConstant;
import com.swcares.psi.ao.register.constant.enums.VipRegisterReturnstartEnum;
import com.swcares.psi.ao.register.dto.VerificationReturnDto;
import com.swcares.psi.ao.register.dto.VipJoinVerificationDto;
import com.swcares.psi.ao.register.handle.crm.SMSHandle;
import com.swcares.psi.ao.register.service.RegisterService;
import com.swcares.psi.ao.register.vo.*;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.core.util.WebUtils;
import com.swcares.psi.common.redis.RedisService;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

/**
 * ClassName：com.swcares.psi.ao.register.controller.RegisterController <br>;
 * Description：会员入会 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/1/12 16:16 <br>;
 * @version v1.0 <br>;
 */
@Slf4j
@Api(tags = "会员注册")
@RestController
@RequestMapping("/api/ao/VipRegister")
public class RegisterController {

    @Autowired
    private RegisterService registerService;

    @Autowired
    private SMSHandle smsHandle;

    @Autowired
    private RedisService redisService;


//====================================邀请入会==========================================================================================
    @ApiOperation(value = "邀请入会>会员验证(支持批量)")
    @PostMapping("joinVipVerification")
    public RenderResult joinVipVerification(@RequestBody List<VipJoinVerificationDto> dtoList, HttpServletRequest req) throws IOException{
        List<VipJoinVerificationDto> result = new ArrayList<>();
        //1，会员验证 >1旅服本地验证
        List<VipJoinVerificationDto> lvFuResult = registerService.joinVipVerification(dtoList);

        if (CollectionUtils.isNotEmpty(lvFuResult)) {
            //1，会员验证 >crm验证
            String ip = req.getRemoteAddr();
            for(VipJoinVerificationDto cDto :lvFuResult){
                VerificationReturnDto verificationReturnDto = registerService.vipVerification(cDto.getCertificateID(),ip);
                String statusCode = verificationReturnDto.getStatusCode();
                log.info("邀请入会>crm会员验证,证件号:{}返回信息:{}",cDto.getCertificateID(),verificationReturnDto.getResultMsg().getMessage());
                if (!("2".equals(statusCode) || "3".equals(statusCode))){
                    result.add(cDto);
                }

            }
        }
        //返回不是会员的旅客
        return RenderResult.success(result);
    }

    @ApiOperation(value = "邀请入会>现场办理会员注册(支持批量)提交")
    @PostMapping("joinVipSceneRegister")
    public RenderResult<Object> joinVipSceneRegister(@RequestBody List<SMSRegisterDto> dtoList, HttpServletRequest req) throws Exception {
        List<String> successList = new ArrayList<>();
        List<SMSRegisterBatchFailVo> failVoList = new ArrayList<>();
        Map<String,Object> resultMap = new HashMap<>();


        RenderResult<Object> renderResult = null;
        for(SMSRegisterDto dto:dtoList){
            try{
                renderResult = registerService.joinVipSceneRegister(dto);
            }catch (Exception e){
                log.error("邀请入会>现场办理会员注册,证件号:{}，注册异常:",dto.getCertificateID(),e);
            }
            if(renderResult == null || (renderResult != null && !MessageCode.SUCCESS.getCode().equals(renderResult.getCode()))){
                SMSRegisterBatchFailVo vo = new SMSRegisterBatchFailVo();
                vo.setSmsRegisterDto(dto);
                vo.setReason(renderResult != null? renderResult.getMsg():"系统异常");
                failVoList.add(vo);
            }else if(renderResult != null && MessageCode.SUCCESS.getCode().equals(renderResult.getCode())){
                successList.add(dto.getPassengerName());
            }
        }
        resultMap.put("successData",successList);
        resultMap.put("failData",failVoList);
        return RenderResult.success(resultMap);
    }

    @PostMapping("joinSendSMSRegister")
    @ApiOperation(value = "邀请入会>发送邀请入会短信(支持批量)")
    @ApiImplicitParam(dataTypeClass = SMSRegisterDto.class)
    public RenderResult<Object> joinSendSMSRegister(@Validated @RequestBody List<SMSRegisterDto> dtoList) throws Exception {
        Map<String,Object> resultMap = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<SMSRegisterBatchFailVo> failVoList = new ArrayList<>();
        for(SMSRegisterDto dto:dtoList){
            VipRegisterReturnstartEnum returnstartEnum = VipRegisterReturnstartEnum.VIP_SMS_SEND_FAIL ;
            try {

                 returnstartEnum = registerService.sendSMSRegister(dto);
            }catch (Exception e){
                log.error("邀请入会>发送邀请入会短信,证件号:{}，注册异常:",dto.getCertificateID(),e);
            }
            if(VipRegisterReturnstartEnum.VIP_SMS_SEND_OK.getCode().equals(returnstartEnum.getCode())){
                successList.add(dto.getPassengerName());
            }else {
                SMSRegisterBatchFailVo vo = new SMSRegisterBatchFailVo();
                vo.setSmsRegisterDto(dto);
                vo.setReason(returnstartEnum.getMessage());
                failVoList.add(vo);
            }
        }
        resultMap.put("successData",successList);
        resultMap.put("failData",failVoList);
        return RenderResult.success(resultMap);
    }

    @ApiOperation(value = "现场入会-获取短信验证码")
    @PostMapping("joinVipSendVerificationCode")
    public RenderResult joinVipSendVerificationCode(@RequestBody  VipJoinVerificationDto dto){

        if (dto != null){
            boolean value = registerService.sendVerificationCodeByIdNo(dto.getCertificateID(),dto.getPhoneNumber());
            if (value){
                return RenderResult.success(VipRegisterReturnstartEnum.VIP_SEND_SMS_VERIFICATION_SUCCESS.getMessage());
            }else {
                return RenderResult.fail(VipRegisterReturnstartEnum.VIP_SEND_SMS_VERIFICATION_FAIL.getMessage());
            }
        }
        return RenderResult.fail(VipRegisterReturnstartEnum.VIP_PHONENUMBER_NULL.getMessage());
    }

//==============================================================================================================================

    /**
     * Title：vipVerification <br>;
     * Description：会员验证<br>;
     * @param: <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult <br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 11:24 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "会员验证")
    @PostMapping("vipVerification")
    public RenderResult vipVerification(@RequestBody Map<String, String> requestBody, HttpServletRequest req) throws IOException{

        String ip = req.getRemoteAddr();
        if (requestBody != null){
            String certificateID = requestBody.get("certificateID");
            VerificationReturnDto verificationReturnDto = registerService.vipVerification(certificateID,ip);
            String statusCode = verificationReturnDto.getStatusCode();
            log.info("会员验证返回信息:{}",verificationReturnDto.getResultMsg().getMessage());
            if ("1".equals(statusCode)){
                return RenderResult.success(VipRegisterReturnstartEnum.VIP_NOT_REGISTER.getMessage());
            }
            if ("2".equals(statusCode) || "3".equals(statusCode)){
                return RenderResult.build(VipRegisterReturnstartEnum.VIP_REGISTERED.getCode(), VipRegisterReturnstartEnum.VIP_REGISTERED.getMessage(), VipRegisterReturnstartEnum.VIP_REGISTERED.getMessage());
            }else {
                return RenderResult.build(VipRegisterReturnstartEnum.VIP_VERIFICATION_FAIL.getCode(), VipRegisterReturnstartEnum.VIP_VERIFICATION_FAIL.getMessage(), VipRegisterReturnstartEnum.VIP_VERIFICATION_FAIL.getMessage());
            }
        }
        return RenderResult.build(VipRegisterReturnstartEnum.VIP_CERTIFICATE_NULL.getCode(), VipRegisterReturnstartEnum.VIP_CERTIFICATE_NULL.getMessage(), VipRegisterReturnstartEnum.VIP_CERTIFICATE_NULL.getMessage());
    }


    /**
     * 02--客服人员点击（发送邀请短信）按钮
     * @return
     */
    @PostMapping("sendSMSRegister")
    @ApiOperation(value = "发送邀请入会短信")
    @ApiImplicitParam(dataTypeClass = SMSRegisterDto.class)
    public RenderResult<Object> sendSMSRegister(@Validated @RequestBody SMSRegisterDto smsRegisterDto) throws Exception {
        VipRegisterReturnstartEnum returnstartEnum = registerService.sendSMSRegister(smsRegisterDto);
        return RenderResult.build(returnstartEnum.getCode(), returnstartEnum.getMessage(), returnstartEnum.getMessage());
    }




    /**
     * Title：sendVerificationCode <br>;
     * Description：发送短信验证码<br>;
     * @param: <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult <br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 11:24 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "获取短信验证码")
    @PostMapping("sendVerificationCode")
    public RenderResult sendVerificationCode(@RequestBody Map<String, String> phoneNumberMap){

        if (phoneNumberMap != null){
            String phoneNumber = phoneNumberMap.get("phoneNumber");
            boolean value = registerService.sendVerificationCode(phoneNumber);
            if (value){
                return RenderResult.success(VipRegisterReturnstartEnum.VIP_SEND_SMS_VERIFICATION_SUCCESS.getMessage());
            }else {
                return RenderResult.fail(VipRegisterReturnstartEnum.VIP_SEND_SMS_VERIFICATION_FAIL.getMessage());
            }
        }
        return RenderResult.fail(VipRegisterReturnstartEnum.VIP_PHONENUMBER_NULL.getMessage());
    }

    /**
     * Title：vipSMSRegister <br>;
     * Description：现场入会，校验验证码<br>;
     * @param: <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult <br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 11:24 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "现场办理会员注册")
    @PostMapping("vipSceneRegister")
    public RenderResult<Object> vipSceneRegister(@RequestBody SMSRegisterDto smsRegisterDto, HttpServletRequest req) throws Exception {

        String phoneNumber = smsRegisterDto.getPhoneNumber();
        String registerCode = (String) redisService.get(CrmConstant.VIP_REGISTER_REDIS_KEY + phoneNumber);

        if (registerCode != null){
            if (smsRegisterDto.getCode() != null){
                if (registerCode.equals(smsRegisterDto.getCode())){
                    //删除缓存信息
                    redisService.deleteKey(CrmConstant.VIP_REGISTER_REDIS_KEY + smsRegisterDto.getPhoneNumber());
                    //生成vip注册信息
                    String ipFrom = WebUtils.getRemoteIp();
                    SMSRegisterVo smsRegisterVo = registerService.vipRegister(smsRegisterDto, ipFrom);
                    if (smsRegisterVo.isOk()) {
                        return RenderResult.success(smsRegisterVo.getCardVo());
                    }else {
                        return RenderResult.build(smsRegisterVo.getVipRegisterReturnstartEnum().getCode(), smsRegisterVo.getVipRegisterReturnstartEnum().getMessage(), smsRegisterVo.getVipRegisterReturnstartEnum().getMessage());
                    }
                }else {
                return RenderResult.build(VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_FAIL.getCode(), VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_FAIL.getMessage(), VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_FAIL.getMessage());
                }
            }
            return RenderResult.build(VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_NULL.getCode(), VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_NULL.getMessage(), VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_NULL.getMessage());
        }
        return  RenderResult.build(VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_INVALID.getCode(), VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_INVALID.getMessage(), VipRegisterReturnstartEnum.VIP_SMS_VERIFICATION_INVALID.getMessage());
    }

    /**
     * Title：vipSMSRegister <br>;
     * Description： 根据短信链接完成入会,此接口对公网访问，要求安全级别中等<br>;
     * @param: id （链接携带的id）<br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult <br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 11:24 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "短信办理会员注册")
    @PostMapping("/public/vipSMSRegister")
    public RenderResult<Object> vipSMSRegister(@RequestBody VIPSMSRegisterDto vipsmsRegisterVO, HttpServletRequest request) throws Exception {

        //判断是否被禁
        if (StringUtils.isNotEmpty((String) redisService.get(CrmConstant.VIP_REGISTER_BLACKLIST_IP_PREFIX + request.getRemoteAddr()))) {
            return RenderResult.build(VipRegisterReturnstartEnum.VIP_ACCOUNT_CLOSURE.getCode(), VipRegisterReturnstartEnum.VIP_ACCOUNT_CLOSURE.getMessage(), null);
        }

        //验证签名
        if (!smsHandle.checkSignatureVerification(vipsmsRegisterVO)) {
            redisService.set(CrmConstant.VIP_REGISTER_BLACKLIST_IP_PREFIX + request.getRemoteAddr(), new Date().toString(), 60);
            return RenderResult.build(VipRegisterReturnstartEnum.VIP_SIGNATURE_ERROR.getCode(), VipRegisterReturnstartEnum.VIP_SIGNATURE_ERROR.getMessage(), null);
        }
        String ipFrom = WebUtils.getRemoteIp();
        vipsmsRegisterVO.setClientIp(ipFrom);

        SMSRegisterVo smsRegisterVo = registerService.vipSMSRegister(vipsmsRegisterVO);
        if (smsRegisterVo.isOk()) {
            return RenderResult.success(smsRegisterVo.getCardVo());
        }else {
            return RenderResult.build(smsRegisterVo.getVipRegisterReturnstartEnum().getCode(), smsRegisterVo.getVipRegisterReturnstartEnum().getMessage(), smsRegisterVo.getVipRegisterReturnstartEnum().getMessage());
        }
    }

    @ApiOperation(value = "获取短信办理会员信息")
    @GetMapping("/public/getVipSMSRegisterInfo/{id}/{timeStamp}/{sign}")
    public RenderResult<Object> geVipSMSRegisterInfo(@PathVariable("id") String id, @PathVariable("timeStamp") String timeStamp, @PathVariable("sign") String sign, HttpServletRequest request) throws Exception {
        final String ipFrom = WebUtils.getRemoteIp();

        //判断是否被禁
        if (StringUtils.isNotEmpty((String) redisService.get(CrmConstant.VIP_REGISTER_BLACKLIST_IP_PREFIX + ipFrom))) {
            return RenderResult.build(VipRegisterReturnstartEnum.VIP_ACCOUNT_CLOSURE.getCode(), VipRegisterReturnstartEnum.VIP_ACCOUNT_CLOSURE.getMessage(), null);
        }

        VIPSMSRegisterDto vipsmsRegisterVO = new VIPSMSRegisterDto();
        vipsmsRegisterVO.setId(id);
        vipsmsRegisterVO.setTimeStamp(timeStamp);
        vipsmsRegisterVO.setSign(sign);

        //验证签名
        if (!smsHandle.checkSignatureVerification(vipsmsRegisterVO)) {
            redisService.set(CrmConstant.VIP_REGISTER_BLACKLIST_IP_PREFIX + ipFrom, new Date().toString(), 60);
            return RenderResult.build(VipRegisterReturnstartEnum.VIP_SIGNATURE_ERROR.getCode(), VipRegisterReturnstartEnum.VIP_SIGNATURE_ERROR.getMessage(), null);
        }

        SMSRegisterVo smsRegisterVo = registerService.getVipSMSRegisterInfo(vipsmsRegisterVO);
        if (smsRegisterVo.isOk()) {
            return RenderResult.success(smsRegisterVo.getSmsRegisterDto());
        }else {
            return RenderResult.build(smsRegisterVo.getVipRegisterReturnstartEnum().getCode(), smsRegisterVo.getVipRegisterReturnstartEnum().getMessage(), smsRegisterVo.getVipRegisterReturnstartEnum().getMessage());
        }
    }

    /**
     * Title：getVipReward <br>;
     * Description：获取奖励配置<br>;
     * @param: <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult <br>;
     * <AUTHOR> <br>;
     * date 2022/2/8 10:55 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "获取邀请入会活动、里程配置")
    @GetMapping("getVipReward")
    public RenderResult<Object> getVipReward(){
        try {
            VipRewardVo vipReward = registerService.getVipReward();
            return RenderResult.success(vipReward);
        }catch (Exception e){
            log.error("获取奖励配置出错", e.getMessage());
        }
        return RenderResult.build(VipRegisterReturnstartEnum.VIP_GET_REWARD_FAIL.getCode(), VipRegisterReturnstartEnum.VIP_GET_REWARD_FAIL.getMessage(), VipRegisterReturnstartEnum.VIP_GET_REWARD_FAIL.getMessage());
    }

    /**
     * Title：getVipReward <br>;
     * Description：修改奖励配置<br>;
     * @param: <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult <br>;
     * <AUTHOR> <br>;
     * date 2022/2/8 16:06 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "修改奖励配置")
    @PostMapping("changeVipReward")
    public RenderResult<Object> changeVipReward(@RequestBody VipRewardVo vipRewardVo){
        try {
            VipRewardVo rewardVo = registerService.changeVipReward(vipRewardVo);
            return RenderResult.success(rewardVo);
        } catch (Exception e) {
            log.error("修改配置奖励错误:", e.getMessage() ,e);
        }
        log.info("修改奖励配置失败:{}", vipRewardVo);
        return RenderResult.build(VipRegisterReturnstartEnum.VIP_CHANGE_REWARD_FAIL.getCode(), VipRegisterReturnstartEnum.VIP_CHANGE_REWARD_FAIL.getMessage(), VipRegisterReturnstartEnum.VIP_CHANGE_REWARD_FAIL.getMessage());
    }
}
