package com.swcares.psi.base.base;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：JPA基础数据层 <br>
 * Package：com.swcares.psi.common.base <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月12日 19:16 <br>
 * @version v1.0 <br>
 */
// 加入注解其他继承此Dao层的JpaRepository不需要使用@Repository注解
// JPA会自动扫描所有继承此注解的类，动态加载到spring容器中
@NoRepositoryBean
public interface BaseJpaDao<T, ID extends Serializable> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {

    /**
     * Title：save() <br>
     * Description：保存或更新方法 <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:52 <br>
     * @param entity S
     * @return S
     */
    <S extends T> S save(S entity);

    /**
     * Title：saveAndFlush（）<br>
     * Description：手动地控制将实体类中的数据传送到数据库  <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:54 <br>
     * @param entity S
     * @return S
     */
    <S extends T> S saveAndFlush(S entity);

    /**
     * Title：saveAll() <br>
     * Description：保存或更新所有实体 <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:55 <br>
     * @param  entities Iterable<S>
     * @return S
     */
    <S extends T> List<S> saveAll(Iterable<S> entities);

    /**
     * Title：flush（） <br>
     * Description：刷新数据 <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:56 <br>
     */
    void flush();

    /**
     * Title：deleteById() <br>
     * Description：通过ID删除 <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:57 <br>
     * @param id ID
     */
    void deleteById(ID id);

    /**
     * Title：delete() <br>
     * Description：删除整个实体,建议使用deleteById，此方法的实质就是通过ID删除对象 <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:57 <br>
     * @param entity T
     */
    void delete(T entity);

    /**
     * Title：deleteAll() <br>
     * Description：批量删除 <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:57 <br>
     * @param entities Iterable<? extends T>
     */
    void deleteAll(Iterable<? extends T> entities);

    /**
     * Title：deleteInBatch() <br>
     * Description：批量删除，效率高于deleteAll <br>
     * author：于琦海 <br>
     * date：2020/3/11 9:57 <br>
     * @param entities Iterable<? extends T>
     */
    void deleteInBatch(Iterable<T> entities);

    /**
     * Title：deleteAll() <br>
     * Description：清表，慎用 <br>
     * author：于琦海 <br>
     * date：2020/3/11 10:01 <br>
     */
    void deleteAll();

    /**
     * Title：findTById() <br>
     * Description：通过ID查询实体 <br>
     * author：于琦海 <br>
     * date：2020/3/11 10:02 <br>
     * @param id ID
     * @return T
     */
    T findTById(ID id);

    /**
     * Title：count() <br>
     * Description：获取总条数 <br>
     * author：于琦海 <br>
     * date：2020/3/11 10:03 <br>
     * @return long
     */
    long count();

    /**
     * Title：existsById() <br>
     * Description： <br>
     * author：于琦海 <br>
     * date：2020/3/11 10:03 <br>
     * @param id ID
     * @return boolean
     */
    boolean existsById(ID id);

    /**
     * Title：findAll(); <br>
     * Description：查询所有 <br>
     * author：于琦海 <br>
     * date：2020/3/24 14:34 <br>
     * @return List<T>
     */
    List<T> findAll();

    /**
     * Title：findAll() <br>
     * Description：查询所有并排序 <br>
     * author：于琦海 <br>
     * date：2020/3/24 14:34 <br>
     * @param sort Sort
     * @return List<T>
     */
    List<T> findAll(Sort sort);
}
