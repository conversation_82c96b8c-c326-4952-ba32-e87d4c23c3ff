-- 创建保护前的航班，日期为明天，状态为取消状态
INSERT INTO `flt_flight_real_info` (
    `ID`,	`FOC_ID`,	`FLIGHT_NUMBER`,	`FLIGHT_DATE`,	`LOCAL_DATE`,
    `ORG`,	`DST`,	`STD`, `STA`,
    `<PERSON><PERSON><PERSON><PERSON>DE`,	`FLIGHT_STATE`,	`DELAY_TYPE`,	`FLIGHT_TYPE`,
    `SALE_SEAT`,	`FLIGHT_GROUP`,	`CABIN_CREW`,	`SEAT_LAYOUT`,	`INNER_REASON`,
    `EXTER_REASON`,		`FLIGHT_MODEL`,	`HAS_OXYGEN`,	`ALTERNATE_AIRPORT`,	`ALTERNATE_AIRPORT2`,
    `PLAN_TEMP_DETAIL`,	`HTD`,	`HTA`,	`DELETED`,
    `FLT_NO_SFX`,	`HISTORY_DATA_ID`,	`EVENT_TOUCH2_BOOL`,	`CHANGED_BOOL`,	`TIMEZONE_OFFSET`,
    `BAY`,	`BAY_2`,	`OUT`
)
VALUES
    (
        '1',		'103128701',		'3U1000',		DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY),		NULL,
        'SZX',		'CKG',		CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 07:00:00'),  CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y-%m-%d'),' 09:00:00'),
        '300A',		'C',		'T',		'D',
        189,		'103128758',		'1151,1843,2439,S2018625,S2005441,S2017143,12462,12478,S2022533,S2025082',		'C12Y177',		NULL,
        '中南区域天气流控限制',	'A321',		'N',		'MIG',		'CTU',
        '3000',		'2020-09-23 16:45:00',		'2020-09-23 18:50:00',	0,
        NULL,		'2010231939300798',		'',		1,		0,
        '209',		NULL,		'2020-09-23 17:14:00'
    );

INSERT INTO `flt_flight_real_info` (
    `ID`,	`FOC_ID`,	`FLIGHT_NUMBER`,	`FLIGHT_DATE`,	`LOCAL_DATE`,
    `ORG`,	`DST`,	`STD`, `STA`,
    `PLANECODE`,	`FLIGHT_STATE`,	`DELAY_TYPE`,	`FLIGHT_TYPE`,
    `SALE_SEAT`,	`FLIGHT_GROUP`,	`CABIN_CREW`,	`SEAT_LAYOUT`,	`INNER_REASON`,
    `EXTER_REASON`,		`FLIGHT_MODEL`,	`HAS_OXYGEN`,	`ALTERNATE_AIRPORT`,	`ALTERNATE_AIRPORT2`,
    `PLAN_TEMP_DETAIL`,	`HTD`,	`HTA`,	`DELETED`,
    `FLT_NO_SFX`,	`HISTORY_DATA_ID`,	`EVENT_TOUCH2_BOOL`,	`CHANGED_BOOL`,	`TIMEZONE_OFFSET`,
    `BAY`,	`BAY_2`,	`OUT`
)
VALUES
    (
        '2',		'103128702',		'3U1000',		DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY),		NULL,
        'CKG',		'LXA',		CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 10:30:00'),  CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 13:00:00'),
        '300A',		'C',		'T',		'D',
        189,		'103128758',		'1151,1843,2439,S2018625,S2005441,S2017143,12462,12478,S2022533,S2025082',		'C12Y177',		NULL,
        '中南区域天气流控限制',	'A321',		'N',		'MIG',		'CTU',
        '3000',		'2020-09-23 16:45:00',		'2020-09-23 18:50:00',	0,
        NULL,		'2010231939300798',		'',		1,		0,
        '209',		NULL,		'2020-09-23 17:14:00'
    );

-- 创建保护前航班的调整记录
INSERT INTO `foc50_t2001`
(
    `FLIGHT_ID`, `FLIGHT_DATE`, `FLIGHT_TYPE`, `FLIGHT_NO`, `AC_TYPE`,
    `AC_REG`, `DEPARTURE_AIRPORT`, `ARRIVAL_AIRPORT`, `HTD`, `STD`,
    `ETD`, `ATD`, `HTA`, `STA`, `ETA`,
    `ATA`, `ADJUST_TYPE`, `FLG_DELAY`, `FLG_VR`, `FLG_PATCH`,
    `FLG_CS`, `BASE`, `CARRIER`, `FILIALE`, `SERIAL`,
    `AC_LINK_LINE`, `CREW_LINK_LINE`, `DEPA_DIV_AIRPORT`, `FPL_DIV_AIRPORT1`, `FPL_DIV_AIRPORT2`,
    `ID`, `GROUP_ID`, `LOAD_TIME`, `DEALED_BOOL`, `DELAY_DATE`,
    `CANCEL_DATE`, `CHANGE_AC_DATE`, `VR_DATE`, `BAY`, `BAY_2`,
    `OUT`, `BAY_OUTGOING`, `BAY_INCOMING`, `ON_BOARD_TIME`, `ESTIMATE_SK_TIME`,
    `OPEN_DOOR_TIME`, `CLOSE_DOOR_TIME`, `SERIES_TIME`, `SERIES_TIME_ARR`, `DELAY_CODE_TRUE`)
VALUES
    (
        '103128701', DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'), 'N', '3U6754', 'A321',
        NULL, 'ZGBH', 'ZUTF', CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 07:00:00'), CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 07:00:00'),
        NULL, NULL, CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 09:00:00'), CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 09:00:00'), NULL,
        NULL, '9', NULL, NULL, NULL,
        'C', 'ZUUU', '3U', '1000', '410',
        '410', '103623364', NULL, NULL, NULL,
        '2202232108220002', '2202232108220003', '2022-02-23 21:08:22', 1, NULL,
        CURRENT_DATE, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL);

-- 创建保护后的航班，日期为后天
INSERT INTO `flt_flight_real_info` (
    `ID`,	`FOC_ID`,	`FLIGHT_NUMBER`,	`FLIGHT_DATE`,	`LOCAL_DATE`,
    `ORG`,	`DST`,	`STD`, `STA`,
    `PLANECODE`,	`FLIGHT_STATE`,	`DELAY_TYPE`,	`FLIGHT_TYPE`,
    `SALE_SEAT`,	`FLIGHT_GROUP`,	`CABIN_CREW`,	`SEAT_LAYOUT`,	`INNER_REASON`,
    `EXTER_REASON`,		`FLIGHT_MODEL`,	`HAS_OXYGEN`,	`ALTERNATE_AIRPORT`,	`ALTERNATE_AIRPORT2`,
    `PLAN_TEMP_DETAIL`,	`HTD`,	`HTA`,	`DELETED`,
    `FLT_NO_SFX`,	`HISTORY_DATA_ID`,	`EVENT_TOUCH2_BOOL`,	`CHANGED_BOOL`,	`TIMEZONE_OFFSET`,
    `BAY`,	`BAY_2`,	`OUT`
)
VALUES
    (
        '3',		'103129701',		'3U1002',		DATE_ADD(CURRENT_DATE, INTERVAL 2 DAY),		NULL,
        'SZX',		'CKG',		CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 2 DAY), '%Y-%m-%d'),' 08:00:00'),  CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 2 DAY), '%Y-%m-%d'),' 10:00:00'),
        '300A',		'C',		'T',		'D',
        189,		'103128758',		'1151,1843,2439,S2018625,S2005441,S2017143,12462,12478,S2022533,S2025082',		'C12Y177',		NULL,
        '中南区域天气流控限制',	'A321',		'N',		'MIG',		'CTU',
        '3000',		'2020-09-23 16:45:00',		'2020-09-23 18:50:00',	0,
        NULL,		'2010231939300798',		'',		1,		0,
        '209',		NULL,		'2020-09-23 17:14:00'
    );



-- 创建保护前的旅客信息，注意protect_type
INSERT INTO `flt_passenger_real_info`
(
    `ID`, `PINFO_ID`, `FLIGHT_ID`, `FLIGHT_NUMBER`, `FLIGHT_DATE`,
    `SALE_FLIGHT`, `ORG`, `DST`, `DEPARTURE_TIME`, `ARRIVAL_TIME`,
    `MAIN_CABIN`, `SUB_CABIN`, `CABIN_CHANGE_TYPE`, `SEG_SERIAL_NUMBER`, `PASSENGER_NAME`,
    `PASSENGER_NAME_EN`, `GENDER`, `BIRTH_DATE`, `ID_TYPE`, `ID_NUMBER`,
    `ID_NUMBER_LFB`, `NATIONALITY`, `VIP_TYPE`, `PASSENGER_TYPE`, `GROUP_TAG`,
    `GROUP_NAME`, `GROUP_NUMBER`, `FQTV_NUMBER`, `LOYAL_LEVEL`, `MAJOR_ACCOUNT_NUMBER`,
    `PNR_REF`, `CRS_PNR`, `PNR_CREATE_DATE`, `PASSENGER_NUMBER`, `ALONG_PERSON`,
    `FLIGHT_SEGMENT`, `PRIOR_TICKET`, `NEXT_TICKET`, `INFT_TICKET_NUMBER`, `INFANT_INFO`,
    `BOOKING_OFFICE`, `BOOKING_IATA_CODE`, `TICKET_NUMBER`, `TICKET_PRICE`, `TICKET_ISSUE_DATE`,
    `TICKET_SOURCE`, `TICKET_TYPE`, `TOUR_CODE`, `ISSUE_CODE`, `ISSUE_OFFICE_TYPE`,
    `ISSUE_CITY`, `BOOKING_STATUS`, `FARE_BASIS_CODE`, `INVOLUNTARY_INDICATOR`, `BAGGAGE_ALLOWANCE`,
    `COUPON_STATUS`, `PNR_ACTION_CODE`, `ISSUE_OFFICE`, `ISSUE_IATA_CODE`, `CHECK_STATUS`,
    `CHECK_DATE`, `COUPON_NUMBER`, `SEAT_NUMBER`, `IS_CARRY`, `GATE`,
    `BOARDING_NUMBER`, `BOARDING_STATUS`, `CHECK_IN_TYPE`, `CHECK_IN_AGENT`, `CHECK_IN_CITY`,
    `IS_CANCEL`, `SHOW_TYPE`, `RESPONSIBILITY_OFFICE`, `IS_GOVERNMENT_PROCT`, `LF_NUMBER`,
    `PROTECTION_TYPE`, `PROTECTION_ID`, `CARRY_CABIN`, `SHORT_REMARK2`, `SHORT_REMARK3`,
    `CHECK_IN_PID`, `ET_STATUS`, `LAND_SOUNDS_REMARK`, `REMARKS`, `LONG_REMARK2`,
    `STANDBY_NUMBER`, `STANDBY_ACCEPTED`, `STANDBY_REASON_CODE`, `ORIGINAL_TICKET_NUMBER`, `ORIGINAL_IATA_CODE`,
    `ORIGINAL_ISSUE_PLACE`, `ORIGINAL_ISSUE_DATE`, `DATA_COMING_TIME`, `LAST_PSG_ID`, `LAST_CKI_ID`
)
VALUES
    (
        '231220122639051', '0', '103128701', '3U1000', DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),
        '3U1000', 'SZX', 'CKG', CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 07:00:00'), CONCAT(DATE_FORMAT(CURRENT_DATE, '%Y-%m-%d'),' 09:00:00'),
        'Y', 'B', NULL, NULL, NULL,
        'XIAO/YANRONG', 'F', '1959-08-31 00:00:00', 'PSPT', 'vfGhWTtEoUNB/LFsqXWv7a7QgywWzyIEIUIFXhlPyr8=',
        '4710', 'CN', NULL, 'ADT', NULL,
        NULL, NULL, NULL, NULL, NULL,
        'MHN95B', 'HP0C7Z', '2023-05-23 04:24:00', 1, NULL,
        'YVR-TFU', NULL, NULL, NULL, NULL,
        'CAN276', NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, 'HK', NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        'N', NULL, NULL, NULL, NULL,
        '2', '231220122639052', NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, '2023-05-23 13:08:54', '646c4a4ae4b02334419c6f00', NULL);

INSERT INTO `flt_passenger_contacts`(`ID`, `PASSR_ID`, `PHONE_NUMBER`, `PHONE_TYPE`, `CONTACT`)
VALUES ('231220122639051', '231220122639051', 'CY/TqTPROT4xmvjxm1DNTQ==', 'CTCT', 'CY/TqTPROT4xmvjxm1DNTQ==');



-- 创建保护后的旅客信息，注意protect_type
INSERT INTO `flt_passenger_real_info`
(
    `ID`, `PINFO_ID`, `FLIGHT_ID`, `FLIGHT_NUMBER`, `FLIGHT_DATE`,
    `SALE_FLIGHT`, `ORG`, `DST`, `DEPARTURE_TIME`, `ARRIVAL_TIME`,
    `MAIN_CABIN`, `SUB_CABIN`, `CABIN_CHANGE_TYPE`, `SEG_SERIAL_NUMBER`, `PASSENGER_NAME`,
    `PASSENGER_NAME_EN`, `GENDER`, `BIRTH_DATE`, `ID_TYPE`, `ID_NUMBER`,
    `ID_NUMBER_LFB`, `NATIONALITY`, `VIP_TYPE`, `PASSENGER_TYPE`, `GROUP_TAG`,
    `GROUP_NAME`, `GROUP_NUMBER`, `FQTV_NUMBER`, `LOYAL_LEVEL`, `MAJOR_ACCOUNT_NUMBER`,
    `PNR_REF`, `CRS_PNR`, `PNR_CREATE_DATE`, `PASSENGER_NUMBER`, `ALONG_PERSON`,
    `FLIGHT_SEGMENT`, `PRIOR_TICKET`, `NEXT_TICKET`, `INFT_TICKET_NUMBER`, `INFANT_INFO`,
    `BOOKING_OFFICE`, `BOOKING_IATA_CODE`, `TICKET_NUMBER`, `TICKET_PRICE`, `TICKET_ISSUE_DATE`,
    `TICKET_SOURCE`, `TICKET_TYPE`, `TOUR_CODE`, `ISSUE_CODE`, `ISSUE_OFFICE_TYPE`,
    `ISSUE_CITY`, `BOOKING_STATUS`, `FARE_BASIS_CODE`, `INVOLUNTARY_INDICATOR`, `BAGGAGE_ALLOWANCE`,
    `COUPON_STATUS`, `PNR_ACTION_CODE`, `ISSUE_OFFICE`, `ISSUE_IATA_CODE`, `CHECK_STATUS`,
    `CHECK_DATE`, `COUPON_NUMBER`, `SEAT_NUMBER`, `IS_CARRY`, `GATE`,
    `BOARDING_NUMBER`, `BOARDING_STATUS`, `CHECK_IN_TYPE`, `CHECK_IN_AGENT`, `CHECK_IN_CITY`,
    `IS_CANCEL`, `SHOW_TYPE`, `RESPONSIBILITY_OFFICE`, `IS_GOVERNMENT_PROCT`, `LF_NUMBER`,
    `PROTECTION_TYPE`, `PROTECTION_ID`, `CARRY_CABIN`, `SHORT_REMARK2`, `SHORT_REMARK3`,
    `CHECK_IN_PID`, `ET_STATUS`, `LAND_SOUNDS_REMARK`, `REMARKS`, `LONG_REMARK2`,
    `STANDBY_NUMBER`, `STANDBY_ACCEPTED`, `STANDBY_REASON_CODE`, `ORIGINAL_TICKET_NUMBER`, `ORIGINAL_IATA_CODE`,
    `ORIGINAL_ISSUE_PLACE`, `ORIGINAL_ISSUE_DATE`, `DATA_COMING_TIME`, `LAST_PSG_ID`, `LAST_CKI_ID`
)
VALUES
    (
        '231220122639052', '0', '103129701', '3U1002', DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 2 DAY), '%Y-%m-%d'),
        '3U1002', 'SZX', 'CKG', CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 2 DAY), '%Y-%m-%d'),' 08:00:00'), CONCAT(DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY), '%Y-%m-%d'),' 10:00:00'),
        'Y', 'B', NULL, NULL, NULL,
        'XIAO/YANRONG', 'F', '1959-08-31 00:00:00', 'PSPT', 'vfGhWTtEoUNB/LFsqXWv7a7QgywWzyIEIUIFXhlPyr8=',
        '4710', 'CN', NULL, 'ADT', NULL,
        NULL, NULL, NULL, NULL, NULL,
        'MHN95B', 'HP0C7Z', '2023-05-23 04:24:00', 1, NULL,
        'YVR-TFU', NULL, NULL, NULL, NULL,
        'CAN276', NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, 'HK', NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        'N', NULL, NULL, NULL, NULL,
        '1', '231220122639051', NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, '2023-05-23 13:08:54', '646c4a4ae4b02334419c6f00', NULL);

INSERT INTO `flt_passenger_contacts`(`ID`, `PASSR_ID`, `PHONE_NUMBER`, `PHONE_TYPE`, `CONTACT`)
VALUES ('231220122639052', '231220122639052', 'CY/TqTPROT4xmvjxm1DNTQ==', 'CTCT', 'CY/TqTPROT4xmvjxm1DNTQ==');





