package com.swcares.psi.ao.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.swcares.psi.ao.utils.AmountConvers;
import com.swcares.psi.common.utils.ExcelBaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AoViproomItemSellReportVO implements ExcelBaseVo {
    @ExcelProperty("序号")
    @ApiModelProperty(value = "序号")
    private Integer seqId;
    @ExcelProperty("订单号")
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ExcelProperty("银行订单号")
    @ApiModelProperty(value = "银行订单号")
    private String bankOrderNo;
    @ExcelProperty("销售日期")
    @ApiModelProperty(value = "销售日期")
    private String orderDate;
    @ExcelProperty("关联客票号")
    @ApiModelProperty(value = "关联客票号")
    private String tktNo;
    @ExcelProperty("航班日期")
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ExcelProperty("航班号")
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ExcelProperty("航段")
    @ApiModelProperty(value = "航段")
    private String flightRoute;
    @ExcelProperty("人数")
    @ApiModelProperty(value = "订单人数")
    private String customerNo;
    @ExcelIgnore
    @AmountConvers
    private String price;
    @ExcelProperty("付费进厅金额")
    @ApiModelProperty(value = "订单价格导出值")
    private String priceExcel;

    @ExcelIgnore
    @ApiModelProperty(value = "币种(人民币....)")
    private String currencySpecies;

    @ExcelProperty(value = "收款币种")
    private String currencySpeciesName;

    @ExcelProperty("支付方式")
    @ApiModelProperty(value = "支付方式(0微信、1易宝、2支付宝、3现金，4pos机)")
    private String chargeType;
    @ExcelProperty("支付状态")
    @ApiModelProperty(value = "支付状态(1支付完成，2支付失败，3退款成功，4退款失败)")
    private String applyStatus;

    @ExcelProperty("订单状态")
    @ApiModelProperty(value = "订单状态(0未支付，1支付失败，2支付取消，3支付完成，4退款中，5退款失败，6退款成功,7取消)")
    private String orderStatus;

    @ExcelProperty("支付时间")
    @ApiModelProperty(value = "支付操作时间")
    private String operationDate;

    @ExcelProperty(value = "乘机人")
    @ApiModelProperty(value = "购买产品旅客对应客票号乘机人")
    private String paxName;

    @ExcelProperty("旅客类别")
    @ApiModelProperty(value = "旅客类别")
    private String paxTypeDesc;

    @ExcelProperty("常客卡号")
    @ApiModelProperty(value = "常客卡号")
    private String ffpNumber;

    @ExcelProperty("国际/国内")
    @ApiModelProperty(value = "航段类型(国内/国际/地区)")
    private String segmentType;
    @ExcelProperty("现金缴纳单号")
    @ApiModelProperty(value = "现金缴纳单号")
    private String cashNo;
    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注")
    private String remark;
    @ExcelProperty("销售渠道")
    @ApiModelProperty(value = "销售渠道")
    private String sellChannel;
    @ExcelIgnore
    @ApiModelProperty(value = "缴纳状态(0成功，1失败)")
    private String payStatus;
    @ExcelProperty("操作人号")
    @ApiModelProperty(value = "操作人号")
    private String userNo;
    @ExcelProperty("操作人")
    @ApiModelProperty(value = "操作人")
    private String userName;



    /**
     * 部门
     */

    @ExcelIgnore
    @ApiModelProperty(value = "部门")
    private String department;

    /**
     * 部门ID
     */
    @ExcelIgnore
    @ApiModelProperty(value = "部门Id")
    private String departmentId;

    @ExcelProperty(value = "部门")
    @ApiModelProperty(value = "完整部门名称")
    private String completeDepartmentInfo;
    @ExcelProperty("缴纳流水号")
    @ApiModelProperty(value = "缴纳流水号")
    private String payNumber;
    @ExcelProperty(value = "行政区域")
    @ApiModelProperty(value = "航站所属分公司")
    private String areaCompany;

    @Override
    public void valueTypeConvers() {
        /*if (StringUtils.isNotEmpty(price)) {
            this.priceExcel = new BigDecimal(price);
        }*/
        //将字段改为String类型是因为导出时需要进行脱敏
        this.priceExcel = price;
    }
}
