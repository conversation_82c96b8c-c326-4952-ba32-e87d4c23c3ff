package com.swcares.psi.aoMergeOrder.service.impl;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.register.constant.CrmConstant;
import com.swcares.psi.ao.register.constant.enums.VipRegisterReturnstartEnum;
import com.swcares.psi.ao.register.vo.SMSRegisterDto;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSelfInvoiceCacheDto;
import com.swcares.psi.aoMergeOrder.dto.InvoiceInfoMergeSaveDto;
import com.swcares.psi.aoMergeOrder.dto.MergeInvoiceInfoDto;
import com.swcares.psi.aoMergeOrder.entity.*;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeInvoiceInfoMapper;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceDetailVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceInfoVo;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PaxPayLoginInfo;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.core.util.WebUtils;
import com.swcares.psi.common.invoice.v2.InvoiceParam;
import com.swcares.psi.common.invoice.v2.InvoiceUtilV2;
import com.swcares.psi.common.invoice.v2.constant.InvoiceOrderType;
import com.swcares.psi.common.invoice.v2.constant.InvoiceStatus;
import com.swcares.psi.common.invoice.v2.entity.YyInvoice;
import com.swcares.psi.common.invoice.v2.mapper.YyInvoiceMapper;
import com.swcares.psi.common.redis.RedisService;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.security.util.PsiUserUtils;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>
 * 合并支付开发票记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Service
public class AoMergeInvoiceInfoServiceImpl extends ServiceImpl<AoMergeInvoiceInfoMapper, AoMergeInvoiceInfo> implements IAoMergeInvoiceInfoService {
    @Autowired
    private InvoiceUtilV2 invoiceUtilV2;
    @Autowired
    private AoMergeSubOrderJoinInvoiceInfoService aoMergeSubOrderJoinInvoiceInfoService;
    @Autowired
    private IAoMergeSuborderInfoService aoMergeSuborderInfoService;
    @Autowired
    private IAoMergeOrderInfoService aoMergeOrderInfoService;

    @Autowired
    private IAoMergePayOrderInfoService payOrderInfoService;

    @Autowired
    YyInvoiceMapper yyInvoiceMapper;
    @Autowired
    private PsiUserUtils psiUserUtils;

    @Autowired
    RedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInvoice(List<InvoiceInfoMergeSaveDto> dtos) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userName = authentication.getRealName();
        if (StringUtils.isEmpty(userName) || userNo.startsWith("paxuser_")) {
            PaxPayLoginInfo paxPayLoginInfo = psiUserUtils.getPaxPayLoginInfo();
            if (paxPayLoginInfo == null) {
                log.info("合并订单开票登录信息异常{}-{}", userNo, userName);
            } else {
                userName = paxPayLoginInfo.getName();
            }
        }
        for (InvoiceInfoMergeSaveDto dto : dtos) {
            log.info("发票开具上传参数:{}", JSON.toJSONString(dto));
            List<String> subMergeOrderId = dto.getSubMergeOrderId();
            Integer count = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery().in(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, subMergeOrderId).count();
            if(count>0){
                throw new BusinessException(MessageCode.ORDER_INVOICE_YES.getCode());
            }
            AoMergeInvoiceInfo aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
            BeanUtils.copyProperties(dto, aoMergeInvoiceInfo);
            aoMergeInvoiceInfo.setProductType("1");
            aoMergeInvoiceInfo.setUserNo(userNo);
            aoMergeInvoiceInfo.setUserName(userName);
            aoMergeInvoiceInfo.setInvoiceStatus(InvoiceStatus.AWAIT_INVOICE);
            List<AoMergeSuborderInfo> suborderList = aoMergeSuborderInfoService.lambdaQuery()
                    .isNull(AoMergeSuborderInfo::getInvoiceJoinId)
                    .in(AoMergeSuborderInfo::getId, subMergeOrderId)
                    .list();
            for (AoMergeSuborderInfo suborderInfo : suborderList) {
                if (!AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS.equals(suborderInfo.getSuborderOrderStatus())) {
                    throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_INVOICE.getCode());
                }
            }
            InvoiceParam.Builder builder = InvoiceParam.builder()
                    .customerAddress(dto.getPurchaserAddress())
                    .customerBank(dto.getPurchaserBank())
                    .customerBankAccount(dto.getPurchaserBankNo())
                    .customerName(dto.getInvoiceTitle())
                    .customerType(dto.getTitleType())
                    .orderType(InvoiceOrderType.MERGE_ORDER);
            if (StringUtils.isNotEmpty(dto.getTaxNo())) {
                builder.customerTaxNo(dto.getTaxNo().toUpperCase());
            }
            if (StringUtils.isNotEmpty(dto.getEmail())) {
                builder.customerMail(Arrays.asList(dto.getEmail().split(",")));
            }
            if (StringUtils.isNotEmpty(dto.getPurchaserPhone())) {
                builder.customerPhone(Arrays.asList(dto.getPurchaserPhone().split(",")));
            }
            if(StringUtils.isNotEmpty(dto.getPhone())){
                builder.invoicePhone(Arrays.asList(dto.getPhone().split(",")));
            }
            List<AoMergeSubOrderJoinInvoiceInfo> joinInvoiceInfoList = new ArrayList<>();
            for (AoMergeSuborderInfo ele : suborderList) {
                InvoiceOrderType invoiceOrderType;
                switch (ele.getOrderType()) {
                    case AoOrderConstant.ORDER_TYPE_CODE_MANY_SEATS:
                        invoiceOrderType = InvoiceOrderType.MANY_SEAT;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_SCENE_SEATS:
                        invoiceOrderType = InvoiceOrderType.SCENE_SEAT;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG:
                        invoiceOrderType = InvoiceOrderType.OVERWEIGHT;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_VIP:
                        invoiceOrderType = InvoiceOrderType.VIP_ROOM;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_UPGRADE:
                        invoiceOrderType = InvoiceOrderType.COUNTER_UPGRADE;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_ON_UPGRADE:
                        invoiceOrderType = InvoiceOrderType.UPGRADE_ONBOARD;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT + AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_UPGRADE:
                        invoiceOrderType = InvoiceOrderType.PAY_ONLINE_UPGRADE;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT + AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_OVERWEIGHT_PKG:
                        invoiceOrderType = InvoiceOrderType.PAY_ONLINE_OVERWEIGHT;
                        break;
                    default:
                        invoiceOrderType = InvoiceOrderType.PAY_ONLINE_REVALIDATION;
                }
                if (new BigDecimal("0").compareTo(ele.getPracticalOrderPrice()) == -1) {
                    builder.addItem(invoiceOrderType, ele.getFlightType(), ele.getPracticalOrderPrice().toString(), false);
                }
                AoMergeSubOrderJoinInvoiceInfo joinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                joinInvoiceInfo.setMergeOrderId(ele.getAoMergeOrderInfoId());
                joinInvoiceInfo.setSuborderOrderId(ele.getId());
                joinInvoiceInfoList.add(joinInvoiceInfo);
            }
            builder.tktNo(suborderList.get(0).getTktNo())
                    .flightSegmentType(suborderList.get(0).getFlightType())
                    .org(suborderList.get(0).getOrg());

            if (!invoiceUtilV2.validateInvoiceNo(builder)) {
                throw new BusinessException(MessageCode.INVOICE_NO_REPEAT.getCode());
            }

            String invoiceNo = invoiceUtilV2.genInvoiceNo(builder);
            String seqNo = invoiceUtilV2.build(builder);

            aoMergeInvoiceInfo.setInvoiceNo(invoiceNo);
            aoMergeInvoiceInfo.setSeqNo(seqNo);
            for (AoMergeSubOrderJoinInvoiceInfo ele : joinInvoiceInfoList) {
                ele.setSeqNo(seqNo);
            }
            aoMergeSubOrderJoinInvoiceInfoService.saveBatch(joinInvoiceInfoList);
            join:
            for (AoMergeSubOrderJoinInvoiceInfo ele : joinInvoiceInfoList) {
                for (AoMergeSuborderInfo sub : suborderList) {
                    if (ele.getSuborderOrderId().equals(sub.getId())) {
                        sub.setInvoiceJoinId(ele.getId());
                        continue join;
                    }
                }
            }
            aoMergeSuborderInfoService.saveOrUpdateBatch(suborderList);
            aoMergeInvoiceInfo.setPhone(AesEncryptUtil.encrypt(dto.getPhone()));
            aoMergeInvoiceInfo.setPurchaserPhone(AesEncryptUtil.encrypt(dto.getPurchaserPhone()));
            aoMergeInvoiceInfo.setCreateTime(LocalDateTime.now());
            this.save(aoMergeInvoiceInfo);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInvoiceByC(List<InvoiceInfoMergeSaveDto> dtos) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userName = authentication.getRealName();
        if (StringUtils.isEmpty(userName) || userNo.startsWith("paxuser_")) {
            PaxPayLoginInfo paxPayLoginInfo = psiUserUtils.getPaxPayLoginInfo();
            if (paxPayLoginInfo == null) {
                log.info("合并订单开票登录信息异常{}-{}", userNo, userName);
            } else {
                userName = paxPayLoginInfo.getName();
            }
        }
        for (InvoiceInfoMergeSaveDto dto : dtos) {
            log.info("发票开具上传参数:{}", JSON.toJSONString(dto));
            List<String> subMergeOrderId = dto.getSubMergeOrderId();
            Integer count = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery().in(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, subMergeOrderId).count();
            if(count>0){
                for (String subId : dto.getSubMergeOrderId()) {
                    Integer subCount = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery().eq(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, subId).count();
                    if (subCount > 0) {
                        AoMergeSuborderInfo byId = aoMergeSuborderInfoService.getById(subId);
                        throw new BusinessException(MessageCode.ORDER_INVOICE_YES_BY_C.getCode(), new String[]{byId.getPaxName()});
                    }
                }
            }
            AoMergeInvoiceInfo aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
            BeanUtils.copyProperties(dto, aoMergeInvoiceInfo);
            aoMergeInvoiceInfo.setProductType("1");
            aoMergeInvoiceInfo.setUserNo(userNo);
            aoMergeInvoiceInfo.setUserName(userName);
            aoMergeInvoiceInfo.setInvoiceStatus(InvoiceStatus.AWAIT_INVOICE);
            List<AoMergeSuborderInfo> suborderList = aoMergeSuborderInfoService.lambdaQuery()
                    .isNull(AoMergeSuborderInfo::getInvoiceJoinId)
                    .in(AoMergeSuborderInfo::getId, subMergeOrderId)
                    .list();
            for (AoMergeSuborderInfo suborderInfo : suborderList) {
                if (!AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS.equals(suborderInfo.getSuborderOrderStatus())) {
                    throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_INVOICE.getCode());
                }
            }
            InvoiceParam.Builder builder = InvoiceParam.builder()
                    .customerAddress(dto.getPurchaserAddress())
                    .customerBank(dto.getPurchaserBank())
                    .customerBankAccount(dto.getPurchaserBankNo())
                    .customerName(dto.getInvoiceTitle())
                    .customerType(dto.getTitleType())
                    .orderType(InvoiceOrderType.MERGE_ORDER);
            if (StringUtils.isNotEmpty(dto.getTaxNo())) {
                builder.customerTaxNo(dto.getTaxNo().toUpperCase());
            }
            if (StringUtils.isNotEmpty(dto.getEmail())) {
                builder.customerMail(Arrays.asList(dto.getEmail().split(",")));
            }
            if (StringUtils.isNotEmpty(dto.getPurchaserPhone())) {
                builder.customerPhone(Arrays.asList(dto.getPurchaserPhone().split(",")));
            }
            if(StringUtils.isNotEmpty(dto.getPhone())){
                builder.invoicePhone(Arrays.asList(dto.getPhone().split(",")));
            }

            List<AoMergeSubOrderJoinInvoiceInfo> joinInvoiceInfoList = new ArrayList<>();
            for (AoMergeSuborderInfo ele : suborderList) {
                InvoiceOrderType invoiceOrderType;
                switch (ele.getOrderType()) {
                    case AoOrderConstant.ORDER_TYPE_CODE_MANY_SEATS:
                        invoiceOrderType = InvoiceOrderType.MANY_SEAT;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_SCENE_SEATS:
                        invoiceOrderType = InvoiceOrderType.SCENE_SEAT;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG:
                        invoiceOrderType = InvoiceOrderType.OVERWEIGHT;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_VIP:
                        invoiceOrderType = InvoiceOrderType.VIP_ROOM;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_UPGRADE:
                        invoiceOrderType = InvoiceOrderType.COUNTER_UPGRADE;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_ON_UPGRADE:
                        invoiceOrderType = InvoiceOrderType.UPGRADE_ONBOARD;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT + AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_UPGRADE:
                        invoiceOrderType = InvoiceOrderType.PAY_ONLINE_UPGRADE;
                        break;
                    case AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT + AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_OVERWEIGHT_PKG:
                        invoiceOrderType = InvoiceOrderType.PAY_ONLINE_OVERWEIGHT;
                        break;
                    default:
                        invoiceOrderType = InvoiceOrderType.PAY_ONLINE_REVALIDATION;
                }
                if (new BigDecimal("0").compareTo(ele.getPracticalOrderPrice()) == -1) {
                    builder.addItem(invoiceOrderType, ele.getFlightType(), ele.getPracticalOrderPrice().toString(), false);
                }
                AoMergeSubOrderJoinInvoiceInfo joinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                joinInvoiceInfo.setMergeOrderId(ele.getAoMergeOrderInfoId());
                joinInvoiceInfo.setSuborderOrderId(ele.getId());
                joinInvoiceInfoList.add(joinInvoiceInfo);
            }
            builder.tktNo(suborderList.get(0).getTktNo())
                    .flightSegmentType(suborderList.get(0).getFlightType())
                    .org(suborderList.get(0).getOrg());

            if (!invoiceUtilV2.validateInvoiceNo(builder)) {
                throw new BusinessException(MessageCode.INVOICE_NO_REPEAT.getCode());
            }

            String invoiceNo = invoiceUtilV2.genInvoiceNo(builder);
            String seqNo = invoiceUtilV2.build(builder);

            aoMergeInvoiceInfo.setInvoiceNo(invoiceNo);
            aoMergeInvoiceInfo.setSeqNo(seqNo);
            for (AoMergeSubOrderJoinInvoiceInfo ele : joinInvoiceInfoList) {
                ele.setSeqNo(seqNo);
            }
            aoMergeSubOrderJoinInvoiceInfoService.saveBatch(joinInvoiceInfoList);
            join:
            for (AoMergeSubOrderJoinInvoiceInfo ele : joinInvoiceInfoList) {
                for (AoMergeSuborderInfo sub : suborderList) {
                    if (ele.getSuborderOrderId().equals(sub.getId())) {
                        sub.setInvoiceJoinId(ele.getId());
                        continue join;
                    }
                }
            }
            aoMergeSuborderInfoService.saveOrUpdateBatch(suborderList);
            aoMergeInvoiceInfo.setPhone(AesEncryptUtil.encrypt(dto.getPhone()));
            aoMergeInvoiceInfo.setPurchaserPhone(AesEncryptUtil.encrypt(dto.getPurchaserPhone()));
            aoMergeInvoiceInfo.setCreateTime(LocalDateTime.now());
            this.save(aoMergeInvoiceInfo);
        }
    }

    public void updateInvoiceStatus(AoMergeInvoiceInfo ele){
        YyInvoice yyInvoice = yyInvoiceMapper.selectOne(Wrappers.<YyInvoice>lambdaQuery()
                .eq(YyInvoice::getSeqNo, ele.getSeqNo())
        );
        if(yyInvoice ==null) return;
        if (yyInvoice.getInvoiceStatus().equals(InvoiceStatus.INVOICE_SUCCESS)) {
            ele.setInvoiceStatus(yyInvoice.getInvoiceStatus());
            ele.setInvoiceTime(yyInvoice.getLastQueryResultTime());
            this.saveOrUpdate(ele);
        }else if (yyInvoice.getInvoiceStatus().equals(InvoiceStatus.INVOICE_FAILED) || yyInvoice.getInvoiceStatus().equals(InvoiceStatus.INVOICE_CREATE_FAILED) ) {
            ele.setInvoiceStatus(InvoiceStatus.INVOICE_FAILED);
            ele.setCauseFailure(yyInvoice.getInvoiceFailedMsg());
            this.saveOrUpdate(ele);
        }
    }

    @Override
    public PsiPage<AoMergeInvoiceInfoVo> getInvoiceInfoPage(MergeInvoiceInfoDto dto) {
        PsiPage<AoMergeInvoiceInfoVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.getInvoiceInfoPage(page,dto);
    }

    @Override
    public List<AoMergeInvoiceInfoVo> getInvoiceInfoList(MergeInvoiceInfoDto dto) {
        return baseMapper.getInvoiceInfoList(dto);
    }

    @Override
    public AoMergeInvoiceDetailVo invoiceDetail(String mergeInvoiceId) {
        AoMergeInvoiceDetailVo aoMergeInvoiceDetailVo = baseMapper.invoiceDetail(mergeInvoiceId);
        for (AoMergeInvoiceDetailVo.PaxInfo ele : aoMergeInvoiceDetailVo.getPaxInfoList()){
            ele.setIdNo(AesEncryptUtil.decryption(ele.getIdNo()));
        }
        return aoMergeInvoiceDetailVo;
    }


    @Override
    public String getInvoiceSign(String mergeOrderNo) {
        String authorization = WebUtils.getRequest().getHeader("Authorization");
        if(StringUtils.isEmpty(authorization)){
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        AoMergeOrderInfo one = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getOrderNo, mergeOrderNo).one();

        if(ObjectUtils.isEmpty(one)){
            log.error("自助开票订单不存在{}",mergeOrderNo);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }

        AoMergePayOrderInfo payOrderInfo = payOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getId, one.getOrderPayId()).one();
        if (!"CNY".equals(payOrderInfo.getCurrencySpecies())) {
            log.error("自助开票订单支付币种不是人民币{}-{}",mergeOrderNo,payOrderInfo.getCurrencySpecies());
            return null;
        }


        String pass = mergeOrderNo + UUID.randomUUID()  + new Date();
        String id = SecureUtil.md5(pass);
        log.info("自助开票{}，生成的id-》{}",mergeOrderNo, id);
        AoMergeSelfInvoiceCacheDto aoMergeSelfInvoiceCacheDto = new AoMergeSelfInvoiceCacheDto();
        aoMergeSelfInvoiceCacheDto.setMergeOrderNo(mergeOrderNo);
        aoMergeSelfInvoiceCacheDto.setToken(authorization);
        //获取数据放入缓存，生成密钥
        if (!redisService.set(id, aoMergeSelfInvoiceCacheDto, 60*60)) {
            log.error("自助开票{}，放入缓存时出错",mergeOrderNo);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        return id;
    }

    @Override
    public AoMergeSelfInvoiceCacheDto getInvoiceParam(String signId) {
        AoMergeSelfInvoiceCacheDto selfInvoiceCacheDto = (AoMergeSelfInvoiceCacheDto) redisService.get(signId);
//        redisService.deleteKey(signId);
        return selfInvoiceCacheDto;
    }
}
