<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.flightUpgrades.mapper.GradeOrderMapper">
    <sql id="sql">
        SELECT
        PASSENGER_NAME,FLIGHT_NO,FLIGHT_DATE,DEPT_CODE,DEST_CODE,SEAT_NO_UPGRADE,SEAT_NO,MAIN_CABIN,IFNULL(CARRY_CABIN,SUB_CABIN) as SUB_CABIN,
        (case  when LAST_PASSENGER=0 then'否'
        when LAST_PASSENGER=1 then '是'
        end) LAST_PASSENGER
        ,TKT_NO,PRICE,GET_DATE_TIME,CREATE_TIME,REMARK
        FROM UP_GRADE_ORDER UGO  WHERE
        <if test="flightNo != null and flightNo != '' ">
            FLIGHT_NO=#{flightNo} AND
        </if>

        <if test="flightDateStar != null and flightDateStar != '' ">
            FLIGHT_DATE &gt;= #{flightDateStar} AND
        </if>
        <if test=" flightDateEnd != null and flightDateEnd != '' ">
            FLIGHT_DATE &lt;= #{flightDateEnd} AND
        </if>
        <if test="org != null and org != '' ">
            DEPT_CODE=#{org} AND
        </if>
        <if test="dst != null and dst != '' ">
            DEST_CODE=#{dst} AND
        </if>
        <if test="name != null and name != '' ">
            PASSENGER_NAME like concat('%',#{name},'%') AND
        </if>
        DEL_FLAG='1'
    </sql>

    <select id="getGradeOrderByParams" parameterType="string" resultType="com.swcares.psi.ao.flightUpgrades.vo.QueryResultVO">
        <include refid="sql"/>
    </select>

    <update id="updateGradeOrderByParams" >
        update UP_GRADE_ORDER UGO set UGO.CONFIRM=#{g.confirm},UGO.UPDATE_TIME=now()

        <if test="g.remark != null and g.remark != '' ">
            ,UGO.REMARK=#{g.remark}
        </if>
        <if test="g.finalSeatNo != null and g.finalSeatNo != '' ">
            , UGO.FINAL_SEAT_NO=#{g.finalSeatNo}
        </if>
        <if test="updateId != null and updateId != '' ">
            , UGO.UPDATE_ID=#{updateId}
        </if>
        <if test="g.source != null and g.source != '' ">
            , UGO.SOURCE=#{g.source}
        </if>
        where
        UGO.ID=#{g.goId}
    </update>

    <update id="updateGradeOrderByParam"    >
   update UP_GRADE_ORDER UGO set UGO.REPLY_CONTENT=#{g.replyContent},UGO.UPDATE_TIME_GO=now(),
   UGO.IS_SUCCESS=#{g.isSuccess}
   where UGO.ID=#{g.id}
</update>


    <select id="queryFltPassengerReal" resultType="com.swcares.psi.base.data.api.entity.FltPassengerRealInfo">
        select * from flt_passenger_real_info where FLIGHT_NUMBER=#{flight} and FLIGHT_DATE=STR_TO_DATE(#{date},'%Y-%m-%d %H:%i:%s')
        and TICKET_NUMBER=#{ticket} and  (IS_CANCEL ='N'  OR CHECK_STATUS='AC')

    </select>

    <select id="paramsDownInfo" parameterType="string" resultType="com.swcares.psi.ao.flightUpgrades.vo.QueryResultVO">
        <include refid="sql"/>
    </select>
    <select id="pidGetGradeOrderInfo" parameterType="com.swcares.psi.ao.flightUpgrades.entity.GradeOrder" resultType="com.swcares.psi.ao.flightUpgrades.vo.QueryResultVO">
        SELECT UGO.* FROM UP_GRADE_ORDER UGO  WHERE
        <if test="gradeOrder.flightNo != null and gradeOrder.flightNo != '' ">
            UGO.FLIGHT_NO=#{gradeOrder.flightNo} AND
        </if>

        <if test="gradeOrder.flightDate != null and gradeOrder.flightDate != '' ">
            UGO.FLIGHT_DATE = #{gradeOrder.flightDate} AND
        </if>

        <if test="gradeOrder.deptCode != null and gradeOrder.deptCode != '' ">
            UGO.DEPT_CODE=#{gradeOrder.deptCode} AND
        </if>
        UGO.DEL_FLAG='1'
    </select>

    <select id="pidQueryInfo" parameterType="com.swcares.psi.ao.flightUpgrades.dto.PidQueryUpgradesDTO" resultType="com.swcares.psi.ao.flightUpgrades.vo.PidQueryVO">
        select * from UP_GRADE_ORDER where FLIGHT_NO=#{dto.flightNo} AND FLIGHT_DATE = #{dto.flightDate} AND DEPT_CODE=#{dto.orgCityAirp} AND DEST_CODE=#{dto.dstCityAirp}
    </select>
</mapper>

