package com.swcares.psi.modules.wifi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.base.data.api.common.SmsBusinessTypeEnum;
import com.swcares.psi.base.data.api.dto.FlightInfoManagementDto;
import com.swcares.psi.base.data.api.dto.WifiListUpdateNoticeDto;
import com.swcares.psi.base.data.api.dto.WifiListUpdateNoticeSaveDto;
import com.swcares.psi.base.data.api.entity.SmsTemplate;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.vo.FlightInfoManagementVo;
import com.swcares.psi.base.data.api.vo.WifiListNoticePaxInfoVo;
import com.swcares.psi.base.data.api.vo.WifiListUpdateNoticeUserVo;
import com.swcares.psi.base.data.mapper.FlightInfoManagementMapper;
import com.swcares.psi.base.data.service.SmsTemplateService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.rce.RceSendMessageFailedException;
import com.swcares.psi.common.utils.rce.RceUtil;
import com.swcares.psi.message.api.enums.MessageTypeEnum;
import com.swcares.psi.message.api.form.MessageSendForm;
import com.swcares.psi.message.service.MessageService;
import com.swcares.psi.message.util.MessageBuilder;
import com.swcares.psi.modules.wifi.entity.WifiListUpdateNoticeUserEntity;
import com.swcares.psi.modules.wifi.entity.WifiPaxWhiteListInfo;
import com.swcares.psi.modules.wifi.mapper.WifiListUpdateNoticeUserMapper;
import com.swcares.psi.modules.wifi.service.WifiListUpdateNoticeUserService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WifiListUpdateNoticeUserServiceImpl extends ServiceImpl<WifiListUpdateNoticeUserMapper, WifiListUpdateNoticeUserEntity> implements WifiListUpdateNoticeUserService {

    @Autowired
    SmsTemplateService smsTemplateService;
    @Autowired
    private RceUtil rceUtil;
    @Autowired
    SysAirportInfoService sysAirportInfoService;
    @Autowired
    private MessageService messageService;

    @Autowired
    private FlightInfoManagementMapper flightInfoManagementMapper;

    @Override
    public PsiPage<WifiListUpdateNoticeUserVo> getPage(WifiListUpdateNoticeDto dto) {
        PsiPage<WifiListUpdateNoticeUserVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.getPage(page, dto);
    }

    @Override
    public void saveNoticeList(WifiListUpdateNoticeSaveDto dto) {
        PsiUser psiUser = (PsiUser) AuthenticationUtil.getAuthentication();
        log.info("{}-{}修改wifi通知名单>>{}", psiUser.getRealName(), psiUser.getUsername(), JSON.toJSONString(dto));
        LocalDateTime now = LocalDateTime.now();
        if (/*StringUtils.isEmpty(dto.getPhone()) || */StringUtils.isEmpty(dto.getTuno()) || StringUtils.isEmpty(dto.getUserName())) {
            throw new BusinessException(MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"员工姓名,工号"});
        }

        WifiListUpdateNoticeUserEntity one = this.lambdaQuery()
                .eq(WifiListUpdateNoticeUserEntity::getId, dto.getId())
                .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiListUpdateNoticeUserEntity.STATUS_USE)
                .one();
        if (one != null) {
            if (!one.getTuno().equals(dto.getTuno())) {
                WifiListUpdateNoticeUserEntity tow = this.lambdaQuery()
                        .eq(WifiListUpdateNoticeUserEntity::getTuno, dto.getTuno())
                        .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiListUpdateNoticeUserEntity.STATUS_USE)
                        .one();
                if (tow != null) {
                    throw new BusinessException(MessageCode.DATA_EXIST.getCode());
                }
            }
            BeanUtils.copyProperties(dto, one);
            one.setUpdateTime(now);
            one.setUpdateUser(psiUser.getRealName() + "-" + psiUser.getUsername());
        } else {
            WifiListUpdateNoticeUserEntity tow = this.lambdaQuery()
                    .eq(WifiListUpdateNoticeUserEntity::getTuno, dto.getTuno())
                    .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiListUpdateNoticeUserEntity.STATUS_USE)
                    .one();
            if (tow != null) {
                throw new BusinessException(MessageCode.DATA_EXIST.getCode());
            }
            one = new WifiListUpdateNoticeUserEntity();
            BeanUtils.copyProperties(dto, one);
            one.setCreateTime(now);
            one.setCreateUser(psiUser.getRealName() + "-" + psiUser.getUsername());
        }
        one.setStatus(WifiListUpdateNoticeUserEntity.STATUS_USE);
        this.saveOrUpdate(one);
    }

    @Override
    public void delete(List<String> list) {
        PsiUser psiUser = (PsiUser) AuthenticationUtil.getAuthentication();
        log.info("{}-{}删除wifi通知名单>>{}", psiUser.getRealName(), psiUser.getUsername(), JSON.toJSONString(list));
        if (list.size() < 1) {
            return;
        }
        this.lambdaUpdate().in(WifiListUpdateNoticeUserEntity::getId, list)
                .set(WifiListUpdateNoticeUserEntity::getStatus, WifiListUpdateNoticeUserEntity.STATUS_DELETE)
                .set(WifiListUpdateNoticeUserEntity::getUpdateTime, LocalDateTime.now())
                .set(WifiListUpdateNoticeUserEntity::getUpdateUser, psiUser.getRealName() + "-" + psiUser.getUsername())
                .update();
    }


    @Override
    public void sendIssueNotice(String paxId) {
        try {
            log.info("wifi白名单旅客出票-paxId>>{}", paxId);
            WifiListNoticePaxInfoVo noticePax = baseMapper.getNoticePax(paxId);
            if (noticePax == null) {
                log.info("wifi白名单旅客出票通知短信发送,旅客不存在-paxId>>{}", paxId);
                return;
            }
            if (!isFlightsWifiEnable(paxId)) {
                log.info("wifi白名单旅客乘坐航班无wifi或wifi不可用，不进行短信通知业务人员。paxId:{}", paxId);
                return;
            }

            log.info("wifi白名单旅客出票通知短信开始发送>>{}-{}", noticePax.getPaxName(), paxId);
            SmsTemplate oneSmsTemplateByCode;
            try {
                oneSmsTemplateByCode = smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_ISSUE.getCode());
            } catch (BusinessException bue) {
                log.info("wifi白名单旅客出票通知短信发送,短信模板不存在-paxId>>{}", paxId);
                return;
            }
            String templateContent = oneSmsTemplateByCode.getTemplateContent();
            templateContent = templateContent.replaceAll("\\[year\\]", noticePax.getYear());
            templateContent = templateContent.replaceAll("\\[month\\]", noticePax.getMonth());
            templateContent = templateContent.replaceAll("\\[day\\]", noticePax.getDay());
            templateContent = templateContent.replaceAll("\\[flightNo\\]", noticePax.getFlightNo());
            templateContent = templateContent.replaceAll("\\[orgCd\\]", noticePax.getOrgCd());
            templateContent = templateContent.replaceAll("\\[dstCd\\]", noticePax.getDstCd());
            templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
            List<WifiListUpdateNoticeUserEntity> list = this.lambdaQuery()
                    .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiPaxWhiteListInfo.STATUS_USE)
                    .list();

            for (WifiListUpdateNoticeUserEntity ele : list) {
                sendRCEMessage(paxId, "出票", templateContent, ele.getTuno());
            }
        } catch (Exception e) {
            log.error("wifi白名单旅客出票通知办公助手消息发送异常:{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendTrfdNotice(String paxId) {
        try {
            log.info("wifi白名单旅客退票paxId>>{}", paxId);
            WifiListNoticePaxInfoVo noticePax = baseMapper.getNoticePax(paxId);
            if (noticePax == null) {
                log.info("wifi白名单旅客退票通知短信发送,旅客不存在-paxId>>{}", paxId);
                return;
            }
            if (!isFlightsWifiEnable(paxId)) {
                log.info("wifi白名单旅客乘坐航班无wifi或wifi不可用，不进行短信通知业务人员。paxId:{}", paxId);
                return;
            }

            log.info("wifi白名单旅客退票通知短信开始发送>>{}-{}", noticePax.getPaxName(), paxId);
            SmsTemplate oneSmsTemplateByCode;
            try {
                oneSmsTemplateByCode = smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_CHANGE.getCode());
            } catch (BusinessException bue) {
                log.info("wifi白名单旅客退票通知短信发送,短信模板不存在-paxId>>{}", paxId);
                return;
            }

            String templateContent = oneSmsTemplateByCode.getTemplateContent();
            templateContent = templateContent.replaceAll("\\[year\\]", noticePax.getYear());
            templateContent = templateContent.replaceAll("\\[month\\]", noticePax.getMonth());
            templateContent = templateContent.replaceAll("\\[day\\]", noticePax.getDay());
            templateContent = templateContent.replaceAll("\\[flightNo\\]", noticePax.getFlightNo());
            templateContent = templateContent.replaceAll("\\[orgCd\\]", noticePax.getOrgCd());
            templateContent = templateContent.replaceAll("\\[dstCd\\]", noticePax.getDstCd());
            templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
            List<WifiListUpdateNoticeUserEntity> list = this.lambdaQuery()
                    .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiPaxWhiteListInfo.STATUS_USE)
                    .list();

            for (WifiListUpdateNoticeUserEntity ele : list) {
                sendRCEMessage(paxId, "退票", templateContent, ele.getTuno());
            }
        } catch (Exception e) {
            log.info("wifi白名单旅客退票办公助手消息发送:{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendCancelNotice(String jsonStr) {
        try {

            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONObject changeContent = jsonObject.getJSONObject("changeContent");
            if (!"CancelPnr".equals(changeContent.get("subEvent").toString())) {
                log.info("wifi白名单旅客取消PNR paxId>>{}---subEvent不匹配-{}", jsonObject.get("id").toString(), changeContent.get("subEvent").toString());
                return;
            }

            final String paxId = jsonObject.get("id").toString();
            log.info("wifi白名单旅客取消PNR paxId>>{}", paxId);
            WifiListNoticePaxInfoVo noticePax = baseMapper.getNoticePax(paxId);
            if (noticePax == null) {
                log.info("wifi白名单旅客改期通知短信发送,旅客不存在-paxId>>{}", paxId);
                return;
            }
            if (!isFlightsWifiEnable(paxId)) {
                log.info("wifi白名单旅客乘坐航班无wifi或wifi不可用，不进行短信通知业务人员。paxId:{}", paxId);
                return;
            }

            log.info("wifi白名单旅客取消PNR通知短信开始发送>>{}-{}", noticePax.getPaxName(), paxId);
            SmsTemplate oneSmsTemplateByCode;
            try {
                oneSmsTemplateByCode =  smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_CANCEL.getCode());
            } catch (BusinessException bue) {
                log.info("wifi白名单旅客取消PNR通知短信发送,短信模板不存在-paxId>>{}", paxId);
                return;
            }

            String templateContent = oneSmsTemplateByCode.getTemplateContent();
            templateContent = templateContent.replaceAll("\\[year\\]", noticePax.getYear());
            templateContent = templateContent.replaceAll("\\[month\\]", noticePax.getMonth());
            templateContent = templateContent.replaceAll("\\[day\\]", noticePax.getDay());
            templateContent = templateContent.replaceAll("\\[flightNo\\]", noticePax.getFlightNo());
            templateContent = templateContent.replaceAll("\\[orgCd\\]", noticePax.getOrgCd());
            templateContent = templateContent.replaceAll("\\[dstCd\\]", noticePax.getDstCd());
            templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
            List<WifiListUpdateNoticeUserEntity> list = this.lambdaQuery()
                    .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiPaxWhiteListInfo.STATUS_USE)
                    .list();

            for (WifiListUpdateNoticeUserEntity ele : list) {
                sendRCEMessage(paxId, "取消", templateContent, ele.getTuno());
            }
        } catch (Exception e) {
            log.info("wifi白名单旅客取消PNR办公助手消息发送:{}", e.getMessage(), e);
        }

    }

    @Override
    public void sendProtectioNotice(String jsonStr) {
        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONObject changeContent = jsonObject.getJSONObject("changeContent");
            if (!"UNTK".equals(changeContent.get("subEvent").toString())) {
                return;
            }
            String[] oldVals = changeContent.get("oldVal").toString().split("/");
            String[] newVals = changeContent.get("newVal").toString().split("/");
            if (oldVals[1].equals(newVals[1]) && newVals[3].equals(oldVals[3])) {
                log.info("wifi白名单旅客保护通知短信发送,保护航班相同-paxId>>{}", jsonObject.get("id").toString());
                return;
            }

            final String paxId = jsonObject.get("id").toString();
            log.info("wifi白名单旅客保护通知paxId>>{}", paxId);
            WifiListNoticePaxInfoVo noticePax = baseMapper.getNoticePax(paxId);
            if (noticePax == null) {
                log.info("wifi白名单旅客保护通知短信发送,旅客不存在-paxId>>{}", paxId);
                return;
            }
            if (!isFlightsWifiEnable(paxId)) {
                log.info("wifi白名单旅客乘坐航班无wifi或wifi不可用，不对业务人员推送短信。paxId:{}", paxId);
                return;
            }

            log.info("wifi白名单旅客保护通知短信开始发送>{}-{}", noticePax.getPaxName(), jsonObject.get("id").toString());
            SmsTemplate oneSmsTemplateByCode = null;
            try {
                oneSmsTemplateByCode = smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_PASSENGERPROTECTION.getCode());
            } catch (Exception e) {
                log.error("wifi白名单旅客保护通知短信发送,未获取到短信模板", e);
            }
            if (oneSmsTemplateByCode == null) {
                log.info("wifi白名单旅客保护通知短信发送,短信模板不存在");
                return;
            }
            String templateContent = oneSmsTemplateByCode.getTemplateContent();
            templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
            templateContent = templateContent.replaceAll("\\[year\\]", oldVals[3].split("-")[0]);
            templateContent = templateContent.replaceAll("\\[month\\]", oldVals[3].split("-")[1]);
            templateContent = templateContent.replaceAll("\\[day\\]", oldVals[3].split("-")[2]);
            templateContent = templateContent.replaceAll("\\[flightNo\\]", oldVals[1]);
            SysAirportInfoEntity orgInfo = sysAirportInfoService.lambdaQuery()
                    .eq(SysAirportInfoEntity::getCode, oldVals[0].substring(0, 3))
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();

            templateContent = templateContent.replaceAll("\\[orgCd\\]", orgInfo == null ? oldVals[0].substring(0, 3) : orgInfo.getAirportName());
            SysAirportInfoEntity dstInfo = sysAirportInfoService.lambdaQuery()
                    .eq(SysAirportInfoEntity::getCode, oldVals[0].substring(3))
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            templateContent = templateContent.replaceAll("\\[dstCd\\]", dstInfo == null ? oldVals[0].substring(3) : dstInfo.getAirportName());

            templateContent = templateContent.replaceAll("\\[newYear\\]", newVals[3].split("-")[0]);
            templateContent = templateContent.replaceAll("\\[newMonth\\]", newVals[3].split("-")[1]);
            templateContent = templateContent.replaceAll("\\[newDay\\]", newVals[3].split("-")[2]);
            templateContent = templateContent.replaceAll("\\[newFlightNo\\]", newVals[1]);
            SysAirportInfoEntity orgNewInfo = sysAirportInfoService.lambdaQuery()
                    .eq(SysAirportInfoEntity::getCode, newVals[0].substring(0, 3))
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();

            templateContent = templateContent.replaceAll("\\[newOrgCd\\]", orgNewInfo == null ? newVals[0].substring(0, 3) : orgNewInfo.getAirportName());
            SysAirportInfoEntity dstNewInfo = sysAirportInfoService.lambdaQuery()
                    .eq(SysAirportInfoEntity::getCode, newVals[0].substring(3))
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            templateContent = templateContent.replaceAll("\\[newDstCd\\]", dstNewInfo == null ? newVals[0].substring(3) : dstNewInfo.getAirportName());
            List<WifiListUpdateNoticeUserEntity> list = this.lambdaQuery()
                    .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiPaxWhiteListInfo.STATUS_USE)
                    .list();

            for (WifiListUpdateNoticeUserEntity ele : list) {
                sendRCEMessage(paxId, "保护", templateContent, ele.getTuno());
            }
        } catch (Exception e) {
            log.info("wifi白名单旅客保护办公助手消息发送:{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendRevalidationNotice(String jsonStr) {
        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONObject changeContent = jsonObject.getJSONObject("changeContent");
            if (!"FlightChange".equals(changeContent.get("subEvent").toString())) {
                return;
            }
            SmsTemplate oneSmsTemplateByCode = null;
            String templateContent = null;
            String[] oldVals = changeContent.get("oldVal").toString().split("/");
            String[] newVals = changeContent.get("newVal").toString().split("/");
            final String paxId = jsonObject.get("id").toString();
            log.info("wifi白名单旅客改期通知 paxId>>{}", paxId);
            WifiListNoticePaxInfoVo noticePax = baseMapper.getNoticePax(paxId);
            if (noticePax == null) {
                log.info("wifi白名单旅客改期通知短信发送,旅客不存在-paxId>>{}", paxId);
                return;
            }
            if (!isFlightsWifiEnable(paxId)) {
                log.info("wifi白名单旅客乘坐航班无wifi或wifi不可用，不进行短信通知业务人员。paxId:{}", paxId);
                return;
            }

            log.info("wifi白名单旅客改期通知短信开始发送>>{}-{}", noticePax.getPaxName(), jsonObject.get("id").toString());
            //由具体航班变更为OPEN航班
            if (!changeContent.get("oldVal").toString().contains("3U OPEN") &&
                    changeContent.get("newVal").toString().contains("3U OPEN")) {
                try {
                    oneSmsTemplateByCode =  smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_REVALIDATION_OPEN.getCode());
                } catch (BusinessException bue) {
                    log.info("wifi白名单旅客改期通知短信发送,短信模板不存在");
                    return;
                }


                templateContent = oneSmsTemplateByCode.getTemplateContent();
                templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
                templateContent = templateContent.replaceAll("\\[year\\]", oldVals[3].split("-")[0]);
                templateContent = templateContent.replaceAll("\\[month\\]", oldVals[3].split("-")[1]);
                templateContent = templateContent.replaceAll("\\[day\\]", oldVals[3].split("-")[2]);
                templateContent = templateContent.replaceAll("\\[flightNo\\]", oldVals[2]);
                SysAirportInfoEntity orgInfo = sysAirportInfoService.lambdaQuery()
                        .eq(SysAirportInfoEntity::getCode, oldVals[1].substring(0, 3))
                        .eq(SysAirportInfoEntity::getIsUse, "0")
                        .one();

                templateContent = templateContent.replaceAll("\\[orgCd\\]", orgInfo == null ? oldVals[1].substring(0, 3) : orgInfo.getAirportName());
                SysAirportInfoEntity dstInfo = sysAirportInfoService.lambdaQuery()
                        .eq(SysAirportInfoEntity::getCode, oldVals[1].substring(3))
                        .eq(SysAirportInfoEntity::getIsUse, "0")
                        .one();
                templateContent = templateContent.replaceAll("\\[dstCd\\]", dstInfo == null ? oldVals[1].substring(3) : dstInfo.getAirportName());


            } else

                //OPEN航班变更为具体航班
                if (changeContent.get("oldVal").toString().contains("3U OPEN") &&
                        !changeContent.get("newVal").toString().contains("3U OPEN")) {
                    try {
                        oneSmsTemplateByCode =  smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_REVALIDATION_FLIGHT.getCode());
                    } catch (BusinessException bue) {
                        log.info("wifi白名单旅客改期通知短信发送,短信模板不存在");
                        return;
                    }

                    templateContent = oneSmsTemplateByCode.getTemplateContent();
                    templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
                    templateContent = templateContent.replaceAll("\\[year\\]", newVals[3].split("-")[0]);
                    templateContent = templateContent.replaceAll("\\[month\\]", newVals[3].split("-")[1]);
                    templateContent = templateContent.replaceAll("\\[day\\]", newVals[3].split("-")[2]);
                    templateContent = templateContent.replaceAll("\\[flightNo\\]", newVals[2]);
                    SysAirportInfoEntity orgInfo = sysAirportInfoService.lambdaQuery()
                            .eq(SysAirportInfoEntity::getCode, newVals[1].substring(0, 3))
                            .eq(SysAirportInfoEntity::getIsUse, "0")
                            .one();

                    templateContent = templateContent.replaceAll("\\[orgCd\\]", orgInfo == null ? newVals[1].substring(0, 3) : orgInfo.getAirportName());
                    SysAirportInfoEntity dstInfo = sysAirportInfoService.lambdaQuery()
                            .eq(SysAirportInfoEntity::getCode, newVals[1].substring(3))
                            .eq(SysAirportInfoEntity::getIsUse, "0")
                            .one();

                    templateContent = templateContent.replaceAll("\\[dstCd\\]", dstInfo == null ? newVals[1].substring(3) : dstInfo.getAirportName());

                } else
                    //旧航班（非OPEN航班）变为新航班（非OPEN航班）且新旧航班的航班日期或航班号不一样
                    if (!newVals[3].equals(oldVals[3]) ||
                            !oldVals[2].equals(newVals[2])) {
                        try {
                            oneSmsTemplateByCode =  smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.WIFI_REVALIDATION.getCode());
                        } catch (BusinessException bue) {
                            log.info("wifi白名单旅客改期通知短信发送,短信模板不存在");
                            return;
                        }
                        templateContent = oneSmsTemplateByCode.getTemplateContent();
                        templateContent = templateContent.replaceAll("paxName", noticePax.getPaxName());
                        templateContent = templateContent.replaceAll("\\[year\\]", oldVals[3].split("-")[0]);
                        templateContent = templateContent.replaceAll("\\[month\\]", oldVals[3].split("-")[1]);
                        templateContent = templateContent.replaceAll("\\[day\\]", oldVals[3].split("-")[2]);
                        templateContent = templateContent.replaceAll("\\[flightNo\\]", oldVals[2]);
                        SysAirportInfoEntity orgInfo = sysAirportInfoService.lambdaQuery()
                                .eq(SysAirportInfoEntity::getCode, oldVals[1].substring(0, 3))
                                .eq(SysAirportInfoEntity::getIsUse, "0")
                                .one();

                        templateContent = templateContent.replaceAll("\\[orgCd\\]", orgInfo == null ? oldVals[1].substring(0, 3) : orgInfo.getAirportName());
                        SysAirportInfoEntity dstInfo = sysAirportInfoService.lambdaQuery()
                                .eq(SysAirportInfoEntity::getCode, oldVals[1].substring(3))
                                .eq(SysAirportInfoEntity::getIsUse, "0")
                                .one();
                        templateContent = templateContent.replaceAll("\\[dstCd\\]", dstInfo == null ? oldVals[1].substring(3) : dstInfo.getAirportName());

                        templateContent = templateContent.replaceAll("\\[newYear\\]", newVals[3].split("-")[0]);
                        templateContent = templateContent.replaceAll("\\[newMonth\\]", newVals[3].split("-")[1]);
                        templateContent = templateContent.replaceAll("\\[newDay\\]", newVals[3].split("-")[2]);
                        templateContent = templateContent.replaceAll("\\[newFlightNo\\]", newVals[2]);
                        SysAirportInfoEntity orgNewInfo = sysAirportInfoService.lambdaQuery()
                                .eq(SysAirportInfoEntity::getCode, newVals[1].substring(0, 3))
                                .eq(SysAirportInfoEntity::getIsUse, "0")
                                .one();
                        templateContent = templateContent.replaceAll("\\[newOrgCd\\]", orgNewInfo == null ? newVals[1].substring(0, 3) : orgNewInfo.getAirportName());
                        SysAirportInfoEntity dstNewInfo = sysAirportInfoService.lambdaQuery()
                                .eq(SysAirportInfoEntity::getCode, newVals[1].substring(3))
                                .eq(SysAirportInfoEntity::getIsUse, "0")
                                .one();
                        templateContent = templateContent.replaceAll("\\[newDstCd\\]", dstNewInfo == null ? newVals[1].substring(3) : dstNewInfo.getAirportName());
                    }


            List<WifiListUpdateNoticeUserEntity> list = this.lambdaQuery()
                    .eq(WifiListUpdateNoticeUserEntity::getStatus, WifiPaxWhiteListInfo.STATUS_USE)
                    .list();

            for (WifiListUpdateNoticeUserEntity ele : list) {
                sendRCEMessage(paxId, "改签", templateContent, ele.getTuno());
            }
        } catch (Exception e) {
            log.info("wifi白名单旅客改签办公助手消息发送:{}", e.getMessage(), e);
        }
    }

    /**
     * 关联查询旅客乘坐航班是否有可用WIFI
     *
     * @param paxId
     * @return
     */
    public boolean isFlightsWifiEnable(final String paxId) {
        String planeCode = this.getBaseMapper().getPaxFlightsPlaneCode(paxId);
        if (StringUtils.isNotBlank(planeCode)) {
            FlightInfoManagementDto dto = new FlightInfoManagementDto();
            dto.setAcReg(planeCode);
            dto.setHaveWifi("1");
            dto.setWifiStatus("0");
            List<FlightInfoManagementVo> list = flightInfoManagementMapper.getList(dto);
            if (!list.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 办公助手消息通知
     *
     * @param paxId
     * @param event
     * @param templateContent
     * @param userNo
     */
    private void sendRCEMessage(String paxId, String event, String templateContent, String userNo) {
        boolean flag = false;
        try {
            rceUtil.sendMessage(userNo, templateContent);
            flag = true;
            log.info("WIFI航班旅客行程变更办公助手通知成功！ID:{}，发生:{} 事件，通知工作人员:{}", paxId, event, userNo);
        } catch (RceSendMessageFailedException e) {
            log.error("WIFI航班旅客行程变更办公助手通知失败！ID:{}，发生:{} 事件，通知工作人员:{}", paxId, event, userNo, e);
        } catch (Exception e) {
            log.error("WIFI航班旅客行程变更办公助手通知出错！ID:{}，发生:{} 事件，通知工作人员:{}", paxId, event, userNo, e);
        }

        if (flag) {
            MessageSendForm messageSendForm = MessageBuilder.getMessageSendForm(MessageTypeEnum.WIFI_FLIGHT);
            //获取发送内容
            messageSendForm.setMsgContent(templateContent);
            messageSendForm.setMsgReplyUser(new String[]{userNo});
            try {
                messageService.sendMsgToUser(messageSendForm);
            } catch (Exception e) {
                log.error("WIFI航班旅客行程变更办公助手通知后站内信通知记录出错！ID:{}，发生:{} 事件，通知工作人员:{}", paxId, event, userNo, e);
            }
        }
    }

}

