

package com.swcares.psi.combine.user.impl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.swcares.psi.combine.constant.CommonConstants;
import com.swcares.psi.combine.user.PsiUserField;
import java.util.Collection;
import java.util.Collections;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * <AUTHOR>
 * @date 2020-05-27
 * 扩展用户信息
 */
public class PsiUser implements PsiUserField, UserDetails, Authentication {

    /**
     * 用户ID
     */
    private String id;

    /**
     * 用户名 工号
     */
    private String username;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 用户所属部门ID
     */
    private String deptId;

    /**
     * 用户所属部门
     */
    private String deptName;

    /**
     * 登录方式
     */
    private Integer loginFrom;

    /**
     * 是否已经进行认证
     */
    private boolean authenticated;

    /**
     * 失效时间
     */
    private Integer expire;

    /**
     * OAuth2 Access Token
     */
    private String accessToken;

    /**
     * 账号类型：11 员工 12 供应商
     */
    private String userType;

    /**
     * 供应商ID
     */
    private String supplierId;

    public PsiUser() {
    }

    public PsiUser(String username, String userId, String password, String deptId, Integer loginFrom) {
        this.username = username;
        this.id = userId;
        this.password = password;
        this.deptId = deptId;
        this.loginFrom = loginFrom;
        this.authenticated = false;
    }

    /**
     * 缓存半个小时
     *
     * @return
     */
    @Override
    public Integer ttl() {
        if (expire != null) {
            return expire - (int) (System.currentTimeMillis() / 1000);
        }
        return CommonConstants.TOKEN_DURATION;
    }

    @JsonIgnore
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.emptyList();
    }

    @JsonIgnore
    @Override
    public Object getCredentials() {
        return this.id;
    }

    @JsonIgnore
    @Override
    public Object getDetails() {
        return null;
    }

    @JsonIgnore
    @Override
    public Object getPrincipal() {
        return this.username;
    }

    @Override
    public boolean isAuthenticated() {
        return this.authenticated;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        this.authenticated = isAuthenticated;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.username;
    }

    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isEnabled() {
        return true;
    }

    @JsonIgnore
    @Override
    public String getName() {
        return this.username;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public Integer getLoginFrom() {
        return loginFrom;
    }

    public void setLoginFrom(Integer loginFrom) {
        this.loginFrom = loginFrom;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        if (expire <= 0) {
            throw new IllegalArgumentException("设置过期时间必须大于当前时间！");
        }
        this.expire = expire;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }
}
