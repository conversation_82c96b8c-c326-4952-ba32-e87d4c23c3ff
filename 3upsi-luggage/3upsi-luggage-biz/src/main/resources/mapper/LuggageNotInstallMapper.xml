<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.luggageNotInstall.mapper.LuggageNotInstallMapper">

    <sql id="luggageNotInstallList">
        SELECT
        PKG.ID AS id,
        PKG.PKG_NO AS pkgNo,
        PKG.FLIGHT_NO AS flightNo,
        date_format(PKG.FLIGHT_DATE,'%Y-%m-%d') AS flightDate,
        PKG.CREATE_TERMINAL AS createTerminal,
        CONCAT(get_city_name(PKG.CREATE_TERMINAL),PKG.CREATE_TERMINAL) AS createTerminalName,
        PKG.DST AS dst,
        CONCAT(get_city_name(PKG.DST),PKG.DST) AS dstName,
        PKG.PAX_NAME AS paxName,
        date_format(PKG.CREATE_TIME,'%Y-%m-%d %H:%i:%s') AS createTime,
        get_user_name(PKG.CREATE_USER) AS createUser,
        date_format(PKG.OUT_TIME,'%Y-%m-%d %H:%i:%s') AS outTime,
        PKG.OUT_USER AS outUser,
        PKG.PKG_HANDLE AS pkgHandle,
        PKG.PKG_IMG AS pkgImg,
        GROUP_CONCAT(EMP.TU_CNAME) AS employeeName,
        PKG.REMARK AS remark,
        CLA.CLAIM_STATUS AS isClaim,
        CASE CLA.CLAIM_STATUS WHEN '0' THEN '已认领' WHEN '1' THEN '已联系旅客待认领' WHEN '2' THEN '已快递给旅客' ELSE '未认领' END AS
        isClaimName,
        PKG.REASON AS reason,
        PKG.PKG_TYPE AS pkgType,
        date_format(PKG.EXPRESS_DATE,'%Y-%m-%d') AS expressDate,
        PKG.EXPRESS_FLIGHT AS expressFlight
        FROM
        DP_LUGGAGE_NOT_INSTALL PKG
        LEFT JOIN DP_LUGGAGE_NOT_INSTALL_EMPLOYEE LNIE ON PKG.ID = LNIE.LUGGAGE_NOT_INSTALL_ID
        LEFT JOIN EMPLOYEE EMP ON LNIE.EMPLOYEE_ID = EMP.ID
        LEFT JOIN DP_LUGGAGE_NOT_INSTALL_CLAIM AS CLA ON CLA.LUGGAGE_NOT_INSTALL_ID = PKG.ID
        LEFT JOIN EMPLOYEE EMP2 ON PKG.CREATE_USER = EMP2.TUNO
        WHERE
        1 = 1
        AND
        PKG.IS_DEL = '0'
        <if test="param.flightStartDate != null and param.flightStartDate != ''">
            AND PKG.FLIGHT_DATE <![CDATA[ >= ]]> CONCAT(#{param.flightStartDate},' 00:00:00' )
        </if>
        <if test="param.flightEndDate != null and param.flightEndDate != ''">
            AND PKG.FLIGHT_DATE <![CDATA[ <= ]]> CONCAT(#{param.flightEndDate},' 23:59:59' )
        </if>
        <if test="param.createTime != null and param.createTime != ''">
            AND DATE_FORMAT(PKG.CREATE_TIME,'%Y-%m-%d') = DATE_FORMAT(#{param.createTime},'%Y-%m-%d')
        </if>
        <if test="param.outTime != null and param.outTime != ''">
            AND DATE_FORMAT(PKG.OUT_TIME,'%Y-%m-%d') = DATE_FORMAT(#{param.outTime},'%Y-%m-%d')
        </if>
        <if test="param.pkgHandle != null and param.pkgHandle != ''">
            AND PKG.PKG_HANDLE LIKE CONCAT('%',#{param.pkgHandle},'%')
        </if>
        <if test="param.createTerminal != null and param.createTerminal != ''">
            AND PKG.CREATE_TERMINAL = #{param.createTerminal}
        </if>
        <if test="param.pkgStatus != null and param.pkgStatus != ''">
            AND PKG.PKG_STATUS IN
            <foreach collection="param.pkgStatusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="param.dst != null and param.dst != ''">
            AND PKG.DST = #{param.dst}
        </if>
        <if test="param.paxName != null and param.paxName != ''">
            AND PKG.PAX_NAME LIKE CONCAT('%',#{param.paxName},'%')
        </if>
        <if test="param.flightNo != null and param.flightNo != ''">
            AND PKG.FLIGHT_NO = #{param.flightNo}
        </if>
        <if test="param.pkgNo != null and param.pkgNo != ''">
            AND PKG.PKG_NO LIKE CONCAT('%',#{param.pkgNo},'%')
        </if>
        <if test="param.createUser != null and param.createUser != ''">
            AND EMP2.TU_CNAME LIKE CONCAT('%',#{param.createUser},'%')
        </if>
        <if test="param.outUser != null and param.outUser != ''">
            AND PKG.OUT_USER LIKE CONCAT('%',#{param.outUser},'%')
        </if>
        GROUP BY PKG.ID
        ORDER BY PKG.CREATE_TIME DESC,PKG.DST
    </sql>


    <select id="getLuggageNotInstallPage"
            resultType="com.swcares.psi.luggageNotInstall.vo.LuggageNotInstallPageVo">
        <include refid="luggageNotInstallList"/>
    </select>
    <select id="getLuggageNotInstallList"
            resultType="com.swcares.psi.luggageNotInstall.vo.LuggageNotInstallPageVo">
        <include refid="luggageNotInstallList"/>
    </select>
    <select id="getLuggageNotInstallInfo"
            resultType="com.swcares.psi.luggageNotInstall.vo.LuggageNotInstallDetailInfoVo">
          SELECT
            PKG.ID AS id,
            PKG.PKG_NO AS pkgNo,
            PKG.FLIGHT_NO AS flightNo,
            date_format( PKG.FLIGHT_DATE, '%Y-%m-%d' ) AS flightDate,
            PKG.CREATE_TERMINAL AS createTerminal,
            CONCAT( get_city_name ( PKG.CREATE_TERMINAL ), PKG.CREATE_TERMINAL ) AS createTerminalName,
            PKG.DST AS dst,
            CONCAT( get_city_name ( PKG.DST ), PKG.DST ) AS dstName,
            PKG.PAX_NAME AS paxName,
            date_format( PKG.CREATE_TIME, '%Y-%m-%d %H:%i:%s' ) AS createTime,
            get_user_name ( PKG.CREATE_USER ) AS createUser,
            date_format( PKG.OUT_TIME, '%Y-%m-%d %H:%i:%s' ) AS outTime,
            PKG.OUT_USER AS outUser,
            PKG.PKG_HANDLE AS pkgHandle,
            PKG.PKG_IMG AS pkgImg,
            GROUP_CONCAT( EMP.TU_CNAME ) AS employeeName,
            PKG.REMARK AS remark,
            get_user_name(PKG.UPDATE_USER) AS updateUser,
            DATE_FORMAT( PKG.UPDATE_DATE , '%Y-%m-%d %H:%i:%s' ) AS updateDate,
            CLA.CLAIM_STATUS AS claimStatus,
            CASE
                CLA.CLAIM_STATUS
                WHEN '0' THEN
                '已认领'
                WHEN '1' THEN
                '已联系旅客待认领'
                WHEN '2' THEN
                '已快递给旅客' ELSE '未认领'
            END AS claimStatusName,
            CLA.CLAIM_PAX AS claimPax,
            CLA.CLAIM_PHONE AS claimPhone,
            CLA.ID_TYPE AS idType,
            CLA.ID_NO AS idNo,
            DATE_FORMAT( CLA.WAITING_TIME, '%Y-%m-%d' ) AS waitingTime,
            CLA.CLAIM_PRICE AS claimPrice,
            CLA.EXPRESS_COMPANY AS expressCompany,
            DATE_FORMAT( CLA.EXPRESS_TIME, '%Y-%m-%d' ) AS expressTime,
            CLA.EXPRESS_NO AS expressNo,
            CLA.EXPRESS_ADDRESS AS expressAddress,
            get_user_name ( CLA.CLAIM_USER ) AS claimUser,
            DATE_FORMAT( CLA.CLAIM_TIME, '%Y-%m-%d %H:%i:%s' ) AS claimTime,
            PKG.REASON AS reason,
            PKG.PKG_TYPE AS pkgType,
            date_format(PKG.EXPRESS_DATE,'%Y-%m-%d') AS expressDate,
            PKG.EXPRESS_FLIGHT AS expressFlight
        FROM
            DP_LUGGAGE_NOT_INSTALL PKG
            LEFT JOIN DP_LUGGAGE_NOT_INSTALL_EMPLOYEE LNIE ON PKG.ID = LNIE.LUGGAGE_NOT_INSTALL_ID
            LEFT JOIN EMPLOYEE EMP ON LNIE.EMPLOYEE_ID = EMP.ID
            LEFT JOIN DP_LUGGAGE_NOT_INSTALL_CLAIM AS CLA ON CLA.LUGGAGE_NOT_INSTALL_ID = PKG.ID
        WHERE
            PKG.ID = #{id}
    </select>
</mapper>
