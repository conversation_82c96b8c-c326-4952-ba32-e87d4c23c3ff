package com.swcares.psi.stimulate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 辅营产品订单线下导入
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("AO_ORDER_IMPORT")
@ApiModel(value="AoOrderImport对象", description="辅营产品订单线下导入")
public class AoOrderImport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "1一人多座,2现场座位销售(选座),3逾重行李,4贵宾室,5柜台升舱")
    @TableField("ORDER_TYPE")
    private String orderType;

    @ApiModelProperty(value = "航班日期")
    @TableField("FLIGHT_DATE")
    private LocalDate flightDate;

    @ApiModelProperty(value = "订单创建时间")
    @TableField("ORDER_TIME")
    private LocalDateTime orderTime;

    @ApiModelProperty(value = "航班号")
    @TableField("FLIGHT_NO")
    private String flightNo;

    @ApiModelProperty(value = "订单日期")
    @TableField("ORDER_DATE")
    private LocalDateTime orderDate;

    @ApiModelProperty(value = "航线性质 D 国内  I 国际 R地区")
    @TableField("FLIGHT_TYPE")
    private String flightType;

    @ApiModelProperty(value = "旅客姓名")
    @TableField("PAX_NAME")
    private String paxName;

    @ApiModelProperty(value = "客票号")
    @TableField("TKT_NO")
    private String tktNo;

    @ApiModelProperty(value = "币种名称")
    @TableField("CURRENCY_SPECIES")
    private String currencySpecies;

    @ApiModelProperty(value = "币种编码")
    @TableField("CURRENCY_SPECIES_CODE")
    private String currencySpeciesCode;

    @ApiModelProperty(value = "订单总金额(元)")
    @TableField("ORDER_PRICE")
    private BigDecimal orderPrice;

    @ApiModelProperty(value = "订单状态 1 支付成功  2退款成功")
    @TableField("ORDER_STATUS")
    private String orderStatus;

    @ApiModelProperty(value = "订单创建人名称")
    @TableField("ORDER_CREATE_USER")
    private String orderCreateUser;

    @ApiModelProperty(value = "订单创建人工号")
    @TableField("ORDER_CREATE_NO")
    private String orderCreateNo;

    @ApiModelProperty(value = "订单创建人的部门")
    @TableField("ORDER_CREATE_USER_DEPARTMENT")
    private String orderCreateUserDepartment;

    @ApiModelProperty(value = "订单创建人的部门名称")
    @TableField("ORDER_CREATE_USER_DEPARTMENT_NAME")
    private String orderCreateUserDepartmentName;

    @ApiModelProperty(value = "订单创建人的科室")
    @TableField("ORDER_CREATE_ADMINISTRATIVE")
    private String orderCreateAdministrative;

    @ApiModelProperty(value = "订单创建人的科室名称")
    @TableField("ORDER_CREATE_ADMINISTRATIVE_NAME")
    private String orderCreateAdministrativeName;

    @ApiModelProperty(value = "订单号")
    @TableField("ORDER_NO")
    private String orderNo;

    @ApiModelProperty(value = "出发航站三字码")
    @TableField("ORG_CODE")
    private String orgCode;

    @ApiModelProperty(value = "出发航站名称")
    @TableField("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "到达航站三字码")
    @TableField("DST_CODE")
    private String dstCode;

    @ApiModelProperty(value = "到达航站名称")
    @TableField("DST_NAME")
    private String dstName;

    @ApiModelProperty(value = "服务航站")
    @TableField("SERVICE_PORT")
    private String servicePort;


}
