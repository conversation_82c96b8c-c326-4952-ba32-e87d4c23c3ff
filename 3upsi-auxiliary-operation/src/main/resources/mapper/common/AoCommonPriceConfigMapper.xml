<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.common.mapper.AoCommonPriceConfigMapper">

    <update id="removeOverWeightConfig">
        update ao_common_price_config set STATUS='0'
        where STATUS='1' and BUSINESS_TABLE_ID in (
        select item.id from  ao_overweight_item_config item where item.OVERWEIGHT_AIRLINE_ID =#{airLineId} and item.STATUS='1'

        )

    </update>

</mapper>
