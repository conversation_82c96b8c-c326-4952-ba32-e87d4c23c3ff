<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.invoice.mapper.AoInvoiceInfoMapper">
    <sql id="selectSql">
        select aii.ID,
               aii.ORDER_NO,
               aii.INVOICE_NO,
               CASE
                   WHEN aii.TITLE_TYPE = '0' THEN
                       '企业'
                   ELSE
                       '个人'
                   END                                            AS TITLE_TYPE,
               aii.INVOICE_TITLE,
               aii.TAX_NO,
               aii.INVOICE_PRICE,
               aii.PRODUCT_NAME,
               aii.PRODUCT_PRICE,
               aii.PRODUCT_NUMBER,
               aii.EMAIL,
               aii.PHONE,
               aii.PURCHASER_PHONE,
               aii.PURCHASER_ADDRESS,
               aii.PURCHASER_BANK,
               aii.PURCHASER_BANK_NO,
               CASE
                   WHEN aii.INVOICE_STATUS = '0' THEN
                       '未开票'
                   WHEN aii.INVOICE_STATUS = '1' THEN
                       '开票成功'
                   ELSE
                       '开票失败'
                   END                                            AS INVOICE_STATUS,
               aii.CAUSE_FAILURE,
               CASE
                   WHEN aii.SOURCE_INFORMATION = '1' THEN
                       '辅营B端'
                   WHEN aii.SOURCE_INFORMATION = '2' THEN
                       '辅营C端'
                   WHEN aii.SOURCE_INFORMATION = '3' THEN
                       'ipad'
                   ELSE
                       aii.SOURCE_INFORMATION
                   END                                            AS sourceInformation,
               aii.USER_NAME,
               CASE
                   WHEN aii.PRODUCT_TYPE = '1' THEN
                       '辅营产品'
                    WHEN aii.PRODUCT_TYPE = '2' THEN
                    '机上销售 '
                   ELSE
                       aii.PRODUCT_TYPE
                   END                                            AS productType,
               date_format(aii.INVOICE_TIME, '%Y-%m-%d %H:%i:%S') as INVOICE_TIME,
               aii.USER_NO,
               date_format(aii.CREATE_TIME, '%Y-%m-%d %H:%i:%S')  as CREATE_TIME,
               aoi.ORDER_TYPE_CODE                                as orderType,
               if(
                (SELECT DATA_CODE FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0' AND (DATA_CODE = aoi.org OR
                DATA_CODE = aoi.dst) limit 1 ) is null,ffri.FLIGHT_TYPE,'GAT'
                ) AS flightType
        from ao_invoice_info aii
                 inner join ao_order_info aoi on aii.ORDER_NO = aoi.ORDER_NO
                 left join flt_flight_real_info ffri on ffri.id = aoi.FLIGHT_ID
    </sql>
    <sql id="whereSql">
        <where>
            aoi.ORDER_TYPE_CODE is not  null
            <if test="dto.invoiceId != null and dto.invoiceId != '' ">
                and aii.ID=#{dto.invoiceId}
            </if>
            <if test="dto.orderNo != null and dto.orderNo != '' ">
                and aii.ORDER_NO=#{dto.orderNo}
            </if>
            <if test="dto.invoiceNo != null and dto.invoiceNo != '' ">
                and aii.INVOICE_NO=#{dto.invoiceNo}
            </if>

            <if test="dto.beginDate != null and dto.beginDate != '' ">
                and aii.CREATE_TIME &gt;= date_format(#{dto.beginDate},'%Y-%m-%d 00:00:00')
            </if>

            <if test="dto.endDate != null and dto.endDate != '' ">
                and aii.CREATE_TIME &lt;= date_format(#{dto.endDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="dto.titleType != null and dto.titleType != '' ">
                and aii.TITLE_TYPE =#{dto.titleType}
            </if>

            <if test="dto.invoiceStatus != null and dto.invoiceStatus != '' ">
                and aii.INVOICE_STATUS =#{dto.invoiceStatus}
            </if>

            <if test="dto.orderType != null and dto.orderType != '' ">
                and aoi.ORDER_TYPE_CODE =#{dto.orderType}
            </if>
            <if test="dto.productType != null and dto.productType != '' ">
                and aii.PRODUCT_TYPE =#{dto.productType}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != '' ">
                and aoi.FLIGHT_NO =#{dto.flightNo}
            </if>
            <if test="dto.paxName != null and dto.paxName != '' ">
                and aoi.PAX_NAME =#{dto.paxName}
            </if>
        </where>
        order by aii.CREATE_TIME  desc
    </sql>

    <sql id="padSql">
        select aii.ID,
        aii.ORDER_NO,
        aii.INVOICE_NO,
        CASE
        WHEN aii.TITLE_TYPE = '0' THEN
        '企业'
        ELSE
        '个人'
        END                                            AS TITLE_TYPE,
        aii.INVOICE_TITLE,
        aii.TAX_NO,
        aii.INVOICE_PRICE,
        aii.PRODUCT_NAME,
        aii.PRODUCT_PRICE,
        aii.PRODUCT_NUMBER,
        aii.EMAIL,
        aii.PHONE,
        aii.PURCHASER_PHONE,
        aii.PURCHASER_ADDRESS,
        aii.PURCHASER_BANK,
        aii.PURCHASER_BANK_NO,
        CASE
        WHEN aii.INVOICE_STATUS = '0' THEN
        '未开票'
        WHEN aii.INVOICE_STATUS = '1' THEN
        '开票成功'
        ELSE
        '开票失败'
        END                                            AS INVOICE_STATUS,
        aii.CAUSE_FAILURE,
        CASE
        WHEN aii.SOURCE_INFORMATION = '1' THEN
        '辅营B端'
        WHEN aii.SOURCE_INFORMATION = '2' THEN
        '辅营C端'
        WHEN aii.SOURCE_INFORMATION = '3' THEN
        'ipad'
        ELSE
        aii.SOURCE_INFORMATION
        END                                            AS sourceInformation,
        aii.USER_NAME,
        CASE
        WHEN aii.PRODUCT_TYPE = '1' THEN
        '辅营产品'
        WHEN aii.PRODUCT_TYPE = '2' THEN
        '机上销售 '
        ELSE
        aii.PRODUCT_TYPE
        END                                            AS productType,
        date_format(aii.INVOICE_TIME, '%Y-%m-%d %H:%i:%S') as INVOICE_TIME,
        aii.USER_NO,
        date_format(aii.CREATE_TIME, '%Y-%m-%d %H:%i:%S')  as CREATE_TIME,
        aoi.ORDER_TYPE_CODE                                as orderType,
        ffri.FLIGHT_TYPE
        from ao_invoice_info aii
        inner join AO_ORDER_INFO_NEW aoi on aii.ORDER_NO = aoi.ORDER_NO
        left join flt_flight_real_info ffri on ffri.FLIGHT_NUMBER = aoi.FLIGHT_NO
        and ffri.FLIGHT_DATE = aoi.FLIGHT_DATE
        and ffri.ORG=aoi.ORG
        and ffri.DST=aoi.DST

    </sql>
    <sql id="padWhereSql">
        <where>
            aoi.ORDER_TYPE_CODE is not  null
            <if test="dto.invoiceId != null and dto.invoiceId != '' ">
               and  aii.ID=#{dto.invoiceId}
            </if>
            <if test="dto.orderNo != null and dto.orderNo != '' ">
                and aii.ORDER_NO=#{dto.orderNo}
            </if>
            <if test="dto.invoiceNo != null and dto.invoiceNo != '' ">
                and aii.INVOICE_NO=#{dto.invoiceNo}
            </if>

            <if test="dto.beginDate != null and dto.beginDate != '' ">
                and aii.CREATE_TIME &gt;= date_format(#{dto.beginDate},'%Y-%m-%d 00:00:00')
            </if>

            <if test="dto.endDate != null and dto.endDate != '' ">
                and aii.CREATE_TIME &lt;= date_format(#{dto.endDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="dto.titleType != null and dto.titleType != '' ">
                and aii.TITLE_TYPE =#{dto.titleType}
            </if>

            <if test="dto.invoiceStatus != null and dto.invoiceStatus != '' ">
                and aii.INVOICE_STATUS =#{dto.invoiceStatus}
            </if>

            <if test="dto.orderType != null and dto.orderType != '' ">
                and aoi.ORDER_TYPE_CODE =#{dto.orderType}
            </if>
            <if test="dto.productType != null and dto.productType != '' ">
                and aii.PRODUCT_TYPE =#{dto.productType}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != '' ">
                and aoi.FLIGHT_NO =#{dto.flightNo}
            </if>
            <if test="dto.paxName != null and dto.paxName != '' ">
                and aoi.PAX_NAME  like concat('%',#{dto.paxName},'%')
            </if>
        </where>
        order by aii.CREATE_TIME  desc
    </sql>

    <sql id="invoicePageSql">
        (
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
        )union
        (
        <include refid="padSql"></include>
        <include refid="padWhereSql"></include>
        )
    </sql>

    <select id="findInvoiceInfo" parameterType="com.swcares.psi.ao.invoice.dto.InvoiceInfoDto"
    resultType="com.swcares.psi.ao.invoice.vo.AoInvoiceInfoVo">
        <include refid="invoicePageSql"></include>
    </select>

    <select id="findInvoiceInfoPage" parameterType="com.swcares.psi.ao.invoice.dto.InvoiceInfoDto"
            resultType="com.swcares.psi.ao.invoice.vo.AoInvoiceInfoVo">
        <include refid="invoicePageSql"></include>
    </select>


    <sql id="findSql">
           select
            odr.ORDER_NO as orderNo,
            invoice.TITLE_TYPE as titleType,
            invoice.INVOICE_NO as invoiceNo,
            invoice.INVOICE_TITLE as invoiceTitle,
            invoice.TAX_NO as taxNo,
            invoice.INVOICE_PRICE as invoicePrice,
            invoice.PRODUCT_NAME as productName,
            invoice.PRODUCT_PRICE as productPrice,
            invoice.PRODUCT_NUMBER  as productNumber,
            invoice.EMAIL  as email,
            invoice.USER_NAME  as paxName,
            invoice.PHONE  as phone,
            invoice.PURCHASER_PHONE  as purchaserPhone,
            invoice.PURCHASER_ADDRESS  as purchaserAddress,
            invoice.PURCHASER_BANK  as purchaserBank,
            invoice.PURCHASER_BANK_NO  as purchaserBankNo,
            invoice.SOURCE_INFORMATION  as sourceInformation
        from   ao_invoice_info invoice
        left join AO_ORDER_INFO_NEW odr  on odr.ORDER_NO=invoice.ORDER_NO
    </sql>


    <select id="findPadInvoiceInfo" resultType="com.swcares.psi.ao.pad.dto.PadInvoiceInfoSaveDto">
       <include refid="findSql"></include>
        where
        odr.FLIGHT_DATE=DATE_FORMAT(#{flightDate},'%Y-%m-%d')
        and odr.FLIGHT_NO=#{flightNo}
        and odr.DST=#{dst}
        and odr.ORG=#{org}

    </select>
    <select id="findPadInvoiceInfoByOrderNo" resultType="com.swcares.psi.ao.pad.dto.PadInvoiceInfoSaveDto">
        <include refid="findSql"></include>
        where
        invoice.ORDER_NO in
        <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

</mapper>
