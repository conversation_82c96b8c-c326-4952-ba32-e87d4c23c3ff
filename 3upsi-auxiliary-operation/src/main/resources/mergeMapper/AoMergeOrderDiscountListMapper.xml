<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeOrderDiscountListMapper">

    <sql id="selectSql">
        select
        id,
        ITEMS_CODE,
        ITEMS_NAME,
        CONCAT (cast((DISCOUNT * 10) as decimal(10,1)),'折') as DISCOUNT,
        REMARK,
        case STATUS
        when '0' then '停用'
        when '1' then '启用'
        end as status,
        ifnull(date_format(UPDATE_DATE,'%Y-%m-%d %H:%i:%s'),date_format(CREATE_TIME,'%Y-%m-%d %H:%i:%s')) as updateDate,
        ifnull(UPDATE_USER,CREATE_USER) as updateUser
        from ao_merge_order_discount_list
        <where>
            IS_DELETE='0'
            <if test="dto.status != null and dto.status != ''">
                and STATUS=#{dto.status}
            </if>
            <if test="dto.itemsCode != null and dto.itemsCode != ''">
              and ITEMS_CODE like concat('%',#{dto.itemsCode},'%')
            </if>
        </where>
        order by UPDATE_DATE desc
    </sql>


    <select id="getPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo">
        <include refid="selectSql"></include>
    </select>
    <select id="getList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo">
        <include refid="selectSql"></include>
    </select>
</mapper>
