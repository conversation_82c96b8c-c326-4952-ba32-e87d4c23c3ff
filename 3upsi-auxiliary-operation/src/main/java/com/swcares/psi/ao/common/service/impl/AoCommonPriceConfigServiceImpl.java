package com.swcares.psi.ao.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.common.entity.AoCommonPriceConfig;
import com.swcares.psi.ao.common.mapper.AoCommonPriceConfigMapper;
import com.swcares.psi.ao.common.service.AoCommonPriceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AoCommonPriceConfigServiceImpl extends ServiceImpl<AoCommonPriceConfigMapper, AoCommonPriceConfig> implements AoCommonPriceConfigService {
    @Override
    public void removeOverWeightConfig(String airLineId) {
        baseMapper.removeOverWeightConfig(airLineId);
    }
}
