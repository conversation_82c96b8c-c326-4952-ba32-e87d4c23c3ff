

package com.swcares.psi.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.admin.api.entity.SysMenu;
import com.swcares.psi.admin.api.vo.MenuVO;
import com.swcares.psi.admin.api.vo.ResourceTreeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 菜单权限表 mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2020-05-27
 */
@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {

	/**
	 * 通过角色编号查询菜单
	 *
	 * @param roleId 角色ID
	 * @return
	 */
	List<MenuVO> listMenusByRoleId(String roleId);

	/**
	 * 通过角色ID查询权限
	 *
	 * @param roleIds Ids
	 * @return
	 */
	List<String> listPermissionsByRoleIds(@Param("roleIds") String roleIds);

	/**
	 * 根据用户id跟资源端口查询资源（树）列表
	 *
	 * @param userId
	 * @param resourceProt
	 * @return
	 */
	List<ResourceTreeVo> listResourceTreeByUserIdAndProt(@Param("userId") String userId, @Param("resourceProt") String resourceProt);

	/**
	 * 根据角色列表跟资源端口查询资源列表
	 *
	 * @param roleIds
	 * @param resourceProt
	 * @return
	 */
	List<ResourceTreeVo> listResourceTreeByRoleIdAndProt(@Param("roleIds") List<String> roleIds, @Param("resourceProt") String resourceProt);

	/**
	 *
	 * 根据用户id跟资源端口查询资源列表
	 * @param userId
	 * @param resourceProt
	 * @return
	 */
	List<String> listModuleResourcesByUserIdAndProt(@Param("userId") String userId, @Param("resourceProt") String resourceProt);

	/**
	 * 根据角色列表跟资源端口加载资源
	 *
	 * @param roleIds
	 * @param resourceProt
	 * @return
	 */
	List<String> listModuleResourcesByRoleIdAndProt(@Param("roleIds") List<String> roleIds, @Param("resourceProt") String resourceProt);

}
