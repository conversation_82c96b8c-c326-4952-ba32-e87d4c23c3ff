#!/bin/bash

#开始修改配置
echo "开始修改配置"
cd / && jar xf 3upsi-upms-biz.jar
#/BOOT-INF/classes/bootstrap.yml	

echo "修改register地址为-${nacoshost}"
####################################	
echo "修改register端口为-${nacosport}"
####################################	
sed -i "s?nacoshost?${nacoshost}?g" /BOOT-INF/classes/bootstrap.yml	
####################################	
sed -i "s?nacosport?${nacosport}?g" /BOOT-INF/classes/bootstrap.yml	
echo "结束修改配置"
mv /3upsi-upms-biz.jar /tmp/
jar -cvfM0 3upsi-upms-biz.jar BOOT-INF/ META-INF/ org/
rm -rf ./BOOT-INF/ ./META-INF/ ./org/
java -jar /3upsi-upms-biz.jar
