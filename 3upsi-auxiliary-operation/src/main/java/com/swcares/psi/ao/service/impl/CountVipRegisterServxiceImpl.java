package com.swcares.psi.ao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.register.entity.VipRegisterEntity;
import com.swcares.psi.ao.register.mapper.CountVipRegisterMapper;
import com.swcares.psi.ao.register.mapper.RegisterMapper;
import com.swcares.psi.ao.register.vo.CountVIPNumberVo;
import com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto;
import com.swcares.psi.ao.register.vo.CountVipRegisterInfoVO;
import com.swcares.psi.ao.register.vo.VIPRegisterInfoVo;
import com.swcares.psi.ao.service.CountVipRegisterService;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.encryption.DecryptMethod;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * ClassName：com.swcares.psi.ao.service.impl.CountVipRegisterServxiceImpl <br>;
 * Description：统计会员注册信息服务实现 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/1/24 14:50 <br>;
 * @version v1.0 <br>;
 */
@Slf4j
@AllArgsConstructor
@Service("countVipRegisterServxiceImpl")
public class CountVipRegisterServxiceImpl extends ServiceImpl< RegisterMapper, VipRegisterEntity> implements CountVipRegisterService {

    public CountVipRegisterMapper countVipRegisterMapper;

    @Override
    public PsiPage<CountVipRegisterInfoVO> getVipRegisterInfosByUser(CountVipRegisterInfoParemsDto paremsVo) {
        PsiPage<CountVipRegisterInfoVO> page = new PsiPage<>(paremsVo);
        return countVipRegisterMapper.selectVipRegisterInfosByParems(page, paremsVo);
    }

    @Override
    public PsiPage<CountVIPNumberVo> countVipRegisterInfo(CountVipRegisterInfoParemsDto paremsVo) {
        PsiPage<CountVIPNumberVo> page = countVipRegisterMapper.countVipRegisterInfo(new PsiPage<>(paremsVo), paremsVo);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(elem -> {
                if (elem.getUserDopt() != null) {
                    // 分页只展示最后一级部门信息
                    elem.setUserDopt(elem.getUserDopt().substring(elem.getUserDopt().lastIndexOf("-")+1));
                }
            });
        }
        return page;
    }

    @EncryptMethod
    @DecryptMethod
    @Override
    public PsiPage<VIPRegisterInfoVo> getVipRegisterInfos(CountVipRegisterInfoParemsDto paremsVo) {
        PsiPage<VIPRegisterInfoVo> page = countVipRegisterMapper.selectVipRegisterInfos(new PsiPage<>(paremsVo), paremsVo);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(elem -> {
                if (elem.getUserDopt() != null) {
                    // 分页只展示最后一级部门信息
                    elem.setUserDopt(elem.getUserDopt().substring(elem.getUserDopt().lastIndexOf("-")+1));
                }
            });
        }
        return page;
    }

    @EncryptMethod
    @DecryptMethod
    @Override
    public List<VIPRegisterInfoVo> getVipRegisterInfosList(final CountVipRegisterInfoParemsDto paremsVo) {
        return countVipRegisterMapper.selectVipRegisterInfosList(paremsVo);
    }

    @Override
    public PsiPage<CountVIPNumberVo> countMemVipRegisterInfo(CountVipRegisterInfoParemsDto paremsVo) {
        PsiPage<CountVIPNumberVo> page = new PsiPage<>(paremsVo);
        return countVipRegisterMapper.countMemVipRegisterInfo(page, paremsVo);
    }

    @Override
    public CountVIPNumberVo getVipRegisterTotal(CountVipRegisterInfoParemsDto paremsVo) {
        return countVipRegisterMapper.countVipRegisterTotal(paremsVo);
    }
}
