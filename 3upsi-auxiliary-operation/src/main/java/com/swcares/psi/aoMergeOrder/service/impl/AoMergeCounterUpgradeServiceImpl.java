package com.swcares.psi.aoMergeOrder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.psi.ao.cons.AoCons;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeApplyPageDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCounterUpgrade;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeCounterUpgradeMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCounterUpgradeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.enums.SysConfigEnum;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeUpgradeStatusUpDateVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.base.util.Asserts;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.encryption.DecryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.travelsky.hub.model.peentity.CompletePayOutPutBean;
import com.travelsky.hub.model.peentity.UpgOrderDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 合并订单柜台升舱产品详情订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Service
public class AoMergeCounterUpgradeServiceImpl extends ServiceImpl<AoMergeCounterUpgradeMapper, AoMergeCounterUpgrade> implements IAoMergeCounterUpgradeService {

    @Autowired
    private IAoMergeSuborderInfoService aoMergeSubOrderSaveService;
    @Autowired
    private SysAirportInfoService sysAirportInfoService;

    //订单状态-创建订单成功
    private static final String ORDER_STATUS_CREATE_SUCCESS = "0";
    //订单状态-业务初始化成功未支付
    private static final String ORDER_STATUS_NOT_PAY = "100";
    //订单状态-升舱成功
    private static final String ORDER_STATUS_SUCCESS = "500";
    //订单状态-订单延迟支付自动取消
    private static final String ORDER_STATUS_FAIL = "201";

    //订单状态-订单支付成功
    private static final String ORDER_PAY_SUCCESS = "300";

    //订单状态-订单出票成功
    private static final String ORDER_DRAWER_SUCCESS = "400";

    //订单状态-订单状态初始化失败
    private static final String ORDER_STATUS_INIT_FAIL = "101";
    //支付类型 M/P/0 --> PE支付类型修改：CC信用卡/在线支付；FP里程
    //private static final String PAY_TYPE_M = "M";
    private static final String PAY_TYPE_M = "CC";
    //支付银行代号或者英文简称 --> PE更新：银行卡类型：MASTER、JCB、UNION、VISA、AE
    //private static final String BANK_TYPE_ALIPAY = "ALIPAY";
    private static final String BANK_TYPE_ALIPAY = "MASTER";
    //支付方式子类(可为空) ：JDGH
    private static final String PAY_CHANNEL_SUBCLASS = "";



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upgradeUser(AoMergeCounterUpgrade byId) {
        String id = byId.getId();
        try {
            if (StringUtils.isBlank(byId.getId())) {
                this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate().set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_FAIL)
                        .eq(AoMergeCounterUpgrade::getId, byId.getId()));
                return;
            }
            UpgOrderDetail orderTetail = PeUpgradeUtils.getOrderTetail(byId.getUpgradeOrder());
            // 订单支付信息更新并升舱 completePay-->
            log.info("=====正在升舱信息：{}", JSON.toJSONString(orderTetail));
            log.info("=====正在升舱，PE订单号为：{},数据主键:{}", byId.getUpgradeOrder(), id);
            // 订单状态为100的时候可以进行升舱操作
            if (null != orderTetail && orderTetail.getOrderStatus().equals(ORDER_STATUS_SUCCESS)) {
                // 订单状态为500代表升舱成功修改状态
                this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate().set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_SUCCESS)
                        .set(AoMergeCounterUpgrade::getEmdNo, orderTetail.getEmdNo())
                        .eq(AoMergeCounterUpgrade::getId, id));
                log.info("数据主键{};{},升舱成功！", id, byId.getUpgradeOrder());
            } else if (null != orderTetail && orderTetail.getOrderStatus().equals(ORDER_STATUS_INIT_FAIL)) {
                //2022-04-28 目前的处理信息看，有订单初始化失败的情况，修改为升舱失败
                this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate().set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_FAIL)
                        .set(AoMergeCounterUpgrade::getErrorInfo, StringUtils.isNotBlank(orderTetail.getErrorMsg()) ? orderTetail.getErrorMsg() : "PE订单初始化失败")
                        .eq(AoMergeCounterUpgrade::getId, id));
            } else if (null != orderTetail && (orderTetail.getOrderStatus().equals(ORDER_PAY_SUCCESS) || orderTetail.getOrderStatus().equals(ORDER_DRAWER_SUCCESS))) {
                //PE订单处理过渡状态,该状态不做处理直接放行
            } else if (null != orderTetail && orderTetail.getOrderStatus().equals(ORDER_STATUS_FAIL)) {
                //2022-04-28 目前的处理信息看，有订单延迟支付自动取消的情况，修改为升舱失败
                this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate().set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_FAIL)
                        .set(AoMergeCounterUpgrade::getErrorInfo, StringUtils.isNotBlank(orderTetail.getErrorMsg()) ? orderTetail.getErrorMsg() : "PE订单延迟支付自动取消")
                        .eq(AoMergeCounterUpgrade::getId, id));
            } else if (null != orderTetail && orderTetail.getOrderStatus().equals(ORDER_STATUS_NOT_PAY)) {
                CompletePayOutPutBean completePayOutPutBean = PeUpgradeUtils.completePay(byId.getUpgradeOrder(), PAY_TYPE_M, byId.getUpgradeOrder(), BANK_TYPE_ALIPAY, PAY_CHANNEL_SUBCLASS);
                log.info("=========升舱结果===============");
                log.info("orderStatus:" + completePayOutPutBean.getOrderStatus() + ";"
                        + "isSuccess:" + completePayOutPutBean.getIsSuccess() + ";"
                        + "orderNum:" + completePayOutPutBean.getOrderNum() + ";"
                        + "errorCode:" + completePayOutPutBean.getErrorCode() + ";"
                        + "errorMsg:" + completePayOutPutBean.getErrorMsg() + ";");
            } else if (null != orderTetail && StringUtils.isNotBlank(orderTetail.getErrorMsg())) {
                log.info("当前订单:'{}'无法升舱,当前订单状态值:'{}',无法升舱原因：'{}'", byId.getUpgradeOrder(), orderTetail.getOrderStatus(), orderTetail.getErrorMsg());
                //如果不是初始化状态之外的状态，视为订单无法升舱，直接修改状态
                if (!StringUtils.equals(orderTetail.getOrderStatus(), ORDER_STATUS_CREATE_SUCCESS)) {
                    this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate().set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_FAIL)
                            .set(AoMergeCounterUpgrade::getErrorInfo, orderTetail.getErrorMsg())
                            .eq(AoMergeCounterUpgrade::getId, id));
                }
            }
        } catch (Exception e) {
            log.info("升舱失败！{}", e.getMessage(), e);
            this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate().set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_FAIL)
                    .set(AoMergeCounterUpgrade::getErrorInfo, e.getMessage())
                    .eq(AoMergeCounterUpgrade::getId, id));
        }
    }


    @Override
    public List<AoMergeCounterUpgradeAccountsReportVo> getAccountsList(AoMergeCounterUpgradeAccountsReportDto dto) {
        return baseMapper.getAccountsList(dto);
    }

    @Override
    public PsiPage<AoMergeCounterUpgradeAccountsReportVo> getAccountsListPage(AoMergeCounterUpgradeAccountsReportDto dto) {
        PsiPage<AoMergeCounterUpgradeAccountsReportVo> page = new PsiPage<>(dto);
        return baseMapper.getAccountsListPage(page,dto);
    }

    @Override
    public List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeCounterUpgradeAccountsReportDto dto) {
        return baseMapper.payTypesSummaryMoney(dto);
    }


    @Override
    public void upgradeStatusUpDateApply(String subOrderId, String applyUpDateStatusReason) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        log.info("{}-{}-{}申请柜台升舱状态修改" , userNo,realName,subOrderId);
        LocalDateTime now = LocalDateTime.now();
        String[] split = subOrderId.split(",");
        List<AoMergeCounterUpgrade> list = this.lambdaQuery()
                .in(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, split)
                .list();

        if(list.size()<1){
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        List<AoMergeCounterUpgrade> collect = list.stream().filter(e -> StringUtils.isNotEmpty(e.getApplyUser())).collect(Collectors.toList());
        for (AoMergeCounterUpgrade e : collect) {
            List<SysAirportInfoEntity> list1 = sysAirportInfoService.lambdaQuery().in(SysAirportInfoEntity::getCode, new String[]{e.getOrg(), e.getDst()}).list();
            Optional<SysAirportInfoEntity> orgPort = list1.stream().filter(ee -> ee.getCode().equals(e.getOrg())).findFirst();
            Optional<SysAirportInfoEntity> dstPort = list1.stream().filter(ee -> ee.getCode().equals(e.getDst())).findFirst();
            SysAirportInfoEntity orgEntity = orgPort.isPresent() ?  orgPort.get():null ;
            SysAirportInfoEntity dstEntity = dstPort.isPresent() ?  dstPort.get():null ;
            StringBuilder stringBuilder = new StringBuilder(e.getPaxName() + " ");
            stringBuilder.append(e.getFlightDate().toString() + " ");
            stringBuilder.append(e.getFlightNo() + " ");
            stringBuilder.append((orgEntity == null ? "" : (orgEntity.getCode() + "(" + orgEntity.getAirportName() + ")")) + " ");
            stringBuilder.append("-");
            stringBuilder.append((dstEntity == null ? "" : (dstEntity.getCode() + "(" + dstEntity.getAirportName() + ")")) + " ");
            throw new BusinessException(MessageCode.UPGRADE_APPLY_ALREADY_REQUESTED.getCode(), new String[]{stringBuilder.toString()});
        }
        this.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate()
                .set(AoMergeCounterUpgrade::getApplyUpdateStatusReason, applyUpDateStatusReason)
                .set(AoMergeCounterUpgrade::getApplyUser, realName)
                .set(AoMergeCounterUpgrade::getApplyUserNo, userNo)
                .set(AoMergeCounterUpgrade::getApplyDate, now)
                .set(AoMergeCounterUpgrade::getUpgradeUpdateStatus,"1")
                .in(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, split));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upgradeStatusUpDateRevocation(List<String> subOrderIdList) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        log.info("{}-{}-{}撤销柜台升舱状态修改申请" , userNo,realName, subOrderIdList.stream().collect(Collectors.joining(",")));
        List<AoMergeCounterUpgrade> list = this.lambdaQuery()
                .in(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, subOrderIdList)
                .list();

        if(list.size()<1){
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        for (AoMergeCounterUpgrade one : list){
            if ("2".equals(one.getUpgradeUpdateStatus())) {
                List<SysAirportInfoEntity> list1 = sysAirportInfoService.lambdaQuery().in(SysAirportInfoEntity::getCode, new String[]{one.getOrg(), one.getDst()}).list();
                Optional<SysAirportInfoEntity> orgPort = list1.stream().filter(e -> e.getCode().equals(one.getOrg())).findFirst();
                Optional<SysAirportInfoEntity> dstPort = list1.stream().filter(e -> e.getCode().equals(one.getDst())).findFirst();
                SysAirportInfoEntity orgEntity = orgPort.isPresent() ?  orgPort.get():null ;
                SysAirportInfoEntity dstEntity = dstPort.isPresent() ?  dstPort.get():null ;
                StringBuilder stringBuilder = new StringBuilder(one.getPaxName()+" ");
                stringBuilder.append(one.getFlightDate().toString()+" ");
                stringBuilder.append(one.getFlightNo()+" ");
                stringBuilder.append((orgEntity==null?"":(orgEntity.getCode()+"("+orgEntity.getAirportName()+")"))+" ");
                stringBuilder.append("-");
                stringBuilder.append((dstEntity==null?"":(dstEntity.getCode()+"("+dstEntity.getAirportName()+")"))+" ");
                throw new BusinessException(MessageCode.UPGRADE_APPLY_ALREADY_PASSED.getCode(),new String[]{stringBuilder.toString()});
            } else if (StringUtils.isEmpty(one.getUpgradeUpdateStatus())) {
                List<SysAirportInfoEntity> list1 = sysAirportInfoService.lambdaQuery().in(SysAirportInfoEntity::getCode, new String[]{one.getOrg(), one.getDst()}).list();
                Optional<SysAirportInfoEntity> orgPort = list1.stream().filter(e -> e.getCode().equals(one.getOrg())).findFirst();
                Optional<SysAirportInfoEntity> dstPort = list1.stream().filter(e -> e.getCode().equals(one.getDst())).findFirst();
                SysAirportInfoEntity orgEntity = orgPort.isPresent() ?  orgPort.get():null ;
                SysAirportInfoEntity dstEntity = dstPort.isPresent() ?  dstPort.get():null ;
                StringBuilder stringBuilder = new StringBuilder(one.getPaxName()+" ");
                stringBuilder.append(one.getFlightDate().toString()+" ");
                stringBuilder.append(one.getFlightNo()+" ");
                stringBuilder.append((orgEntity==null?"":(orgEntity.getCode()+"("+orgEntity.getAirportName()+")"))+" ");
                stringBuilder.append("-");
                stringBuilder.append((dstEntity==null?"":(dstEntity.getCode()+"("+dstEntity.getAirportName()+")"))+" ");
                throw new BusinessException(MessageCode.UPGRADE_APPLY_CANCE_ORDER_NOT_FIND.getCode(),new String[]{stringBuilder.toString()});
            }
            this.lambdaUpdate().set(AoMergeCounterUpgrade::getApproveUserNo, null)
                    .set(AoMergeCounterUpgrade::getApplyUserNo, null)
                    .set(AoMergeCounterUpgrade::getApplyUser, null)
                    .set(AoMergeCounterUpgrade::getUpgradeUpdateStatus, null)
                    .set(AoMergeCounterUpgrade::getApproveDate, null)
                    .set(AoMergeCounterUpgrade::getApproveUser, null)
                    .set(AoMergeCounterUpgrade::getApplyDate, null)
                    .eq(AoMergeCounterUpgrade::getId, one.getId())
                    .update();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void apply(List<String> idList) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        log.info("{}-{}-{}审核柜台升舱状态修改申请" , userNo,realName, idList.stream().collect(Collectors.joining(",")));
        for (String id : idList) {
            AoMergeCounterUpgrade one = this.lambdaQuery()
                    .eq(AoMergeCounterUpgrade::getId, id)
                    .eq(AoMergeCounterUpgrade::getUpgradeUpdateStatus, "1")
                    .one();
            Asserts.isNotNull(one, MessageCode.DATA_NOT_EXIST.getCode());
            String financeOrderNo = AoUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_COUNTER_UPGRADE_SEQUENCE, AoCons.COUNTER_UPGRADE_ACCOUNT_ENTRY_NO_KEY);
            this.lambdaUpdate()
                    .set(AoMergeCounterUpgrade::getApproveUserNo, userNo)
                    .set(AoMergeCounterUpgrade::getApproveUser, realName)
                    .set(AoMergeCounterUpgrade::getUpgradeUpdateStatus, "2")
                    .set(AoMergeCounterUpgrade::getUpgradeStatus, "2")
                    .set(AoMergeCounterUpgrade::getApproveDate, LocalDateTime.now())
                    .eq(AoMergeCounterUpgrade::getId, id)
                    .update();
            aoMergeSubOrderSaveService.lambdaUpdate()
                    .eq(AoMergeSuborderInfo::getId,one.getAoMergeSuborderInfoId())
                    .set(AoMergeSuborderInfo::getFinanceOrderNo,financeOrderNo)
                    .update();
        }
    }

    @Override
    @DecryptMethod
    public PsiPage<AoMergeUpgradeStatusUpDateVo> applyPage(AoMergeApplyPageDto dto) {
        PsiPage<AoMergeUpgradeStatusUpDateVo> page=new PsiPage<AoMergeUpgradeStatusUpDateVo>(dto);
        return baseMapper.applyPage(page,dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void automaticApprove() {
        List<AoMergeCounterUpgrade> list = this.lambdaQuery()
                .eq(AoMergeCounterUpgrade::getUpgradeUpdateStatus, "1")
                .list();
        LocalDateTime now = LocalDateTime.now();
        list.forEach(ele -> {
            String financeOrderNo = AoUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_COUNTER_UPGRADE_SEQUENCE, AoCons.COUNTER_UPGRADE_ACCOUNT_ENTRY_NO_KEY);
            ele.setApproveUserNo("系统");
            ele.setApproveUser("系统");
            ele.setApproveDate(now);
            ele.setUpgradeStatus(AoMergeCons.UPGRADE_STATUS_SUCCESS);
            ele.setUpgradeUpdateStatus("2");
            aoMergeSubOrderSaveService.lambdaUpdate()
                    .eq(AoMergeSuborderInfo::getId,ele.getAoMergeSuborderInfoId())
                    .set(AoMergeSuborderInfo::getFinanceOrderNo,financeOrderNo)
                    .update();
        });
        if (!list.isEmpty()) {
            this.updateBatchById(list);
        }
        log.info("系统执行修改柜台升舱系统升舱状态任务执行，完成修改订单列表:{}", list.stream().map(AoMergeCounterUpgrade::getUpgradeOrder).collect(Collectors.joining(",")));
    }
}
