package com.swcares.psi.label.vo;

import com.swcares.psi.common.utils.query.PsiPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：. <br>
 * Description：(用一句话描述这个类或者接口表示什么)<br>
 * Copyright ©  xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022/2/7 11:29<br>
 * @version v1.0 <br>
 */
@Data
public class LabelPassengerVo implements Serializable {

    @ApiModelProperty(value = "旅客数据集合", required = true)
    private PsiPage<PassengerVo> paxPage;

}
