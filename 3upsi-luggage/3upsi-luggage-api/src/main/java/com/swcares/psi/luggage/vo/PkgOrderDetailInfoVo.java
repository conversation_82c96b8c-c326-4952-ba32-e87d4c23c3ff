package com.swcares.psi.luggage.vo;

import com.swcares.psi.common.compensate.entity.OrderInfo;
import com.swcares.psi.common.utils.encryption.Encryption;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on  2021/3/23
 * Title:       [赔偿单详情]
 * Description: [赔偿单详情]
 *
 * @author: like
 * @version: 1.0
 */
@Data
public class PkgOrderDetailInfoVo extends OrderInfo {

    @ApiModelProperty("旅客姓名")
    private String paxName;

    @ApiModelProperty("身份类型")
    private String idType;

    @Encryption
    @ApiModelProperty("身份证号")
    private String idNo;

    @ApiModelProperty("航程")
    private String segment;

    @ApiModelProperty("航站")
    private String serviceCity;

    @ApiModelProperty("票号")
    private String tktNo;

    @ApiModelProperty("行李号")
    private String pkgNo;

    @ApiModelProperty("飞机号")
    private String planeCode;

    @ApiModelProperty("飞机型号")
    private String acType;

    @ApiModelProperty("备注")
    private String orderRemark;

    @ApiModelProperty("领取状态")
    private String receiveStatus;

    @ApiModelProperty("批准人")
    private String auditor;

    @ApiModelProperty(value = "创建人")
    private String createUser;
}
