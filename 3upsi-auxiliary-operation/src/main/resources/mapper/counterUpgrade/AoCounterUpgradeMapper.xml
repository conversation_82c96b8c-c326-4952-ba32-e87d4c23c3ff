<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.counterUpgrade.mapper.AoCounterUpgradeConfigMapper">

    <sql id="dataTables">
        ao_counter_upgrade_config
    </sql>

    <select id="getPage" resultType="com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgradeConfig">
        SELECT
            ID,
            ORG_PLACE,
            DST_PLACE,
            ifnull(PRICE,"-") as price,
            ifnull(FOREIGN_PRICE,"-") as foreignPrice,
            ifnull(FOREIGN_PRICE_UNIT,"-") as foreignPriceUnit,
            EFFECT_PRICE,
            EFFECT_FOREIGN_PRICE,
            EFFECT_FOREIGN_PRICE_UNIT,
            date_format(EFFECT_TIME,'%Y-%m-%d %T') AS EFFECT_TIME,
            `STATUS`,
            CREATE_USER,
            CREATE_TIME,
            UPDATE_USER,
            UPDATE_TIME
        FROM
            <include refid="dataTables"/>
        <where>
            IS_DELETE = 0
            <if test="aoCounterUpgradeConfigDto.orgPlace != null and aoCounterUpgradeConfigDto.orgPlace != ''">
                AND ORG_PLACE = #{aoCounterUpgradeConfigDto.orgPlace}
            </if>
            <if test="aoCounterUpgradeConfigDto.dstPlace != null and aoCounterUpgradeConfigDto.dstPlace != ''">
                AND DST_PLACE = #{aoCounterUpgradeConfigDto.dstPlace}
            </if>
            <if test="startDate != null ">
                AND EFFECT_TIME <![CDATA[ > ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND EFFECT_TIME <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
    </select>

</mapper>