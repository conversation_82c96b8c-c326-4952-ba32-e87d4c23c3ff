package com.swcares.psi.aoMergeOrder.payHander;

import com.swcares.psi.config.YeeBaseConfig;
import com.swcares.psi.pay.bean.wxpay.WxpayBaseConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.pad.payHander.PadConfigInitHander <br>
 * Description：合并订单支付宝支付配置<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022/3/7 14:13<br>
 * @version v1.0 <br>
 */
@Configuration
@Slf4j
@Data
public class MergeOrderPayConfigInitHander {

    @Value("${aopayconfig.aliqc.mergeOrder.notifyUrl}")
     String aliNotifyUrl; //支付回调地址

    /**
     * 支付宝支付
     *
     * @return
     */
    @Bean(name = "mergeOrderAliPayConfig")
    public AliMergePayClientConfig initAliPayService() {
        AliMergePayClientConfig config = new AliMergePayClientConfig();
        try {
            PropertiesConfiguration propertiesConfiguration = new PropertiesConfiguration("payConfig/zfbinfo.yml");
            config.setAppId(propertiesConfiguration.getString("appid"));
            config.setSigType(propertiesConfiguration.getString("sign_type"));
            config.setCharset(propertiesConfiguration.getString("charset"));
            config.setFormat(propertiesConfiguration.getString("format"));
            config.setPayUrl(propertiesConfiguration.getString("open_api_domain"));
            config.setPrivateKey(propertiesConfiguration.getString("private_key"));
            config.setPublicKey(propertiesConfiguration.getString("alipay_public_key"));
            config.setAuthCallbackUrl(aliNotifyUrl);
        } catch (ConfigurationException e) {
            e.printStackTrace();
        }
        return config;
    }

    @Value("${aopayconfig.aliqc.mergeOrder.trunNotifyUrl}")
     String aliTrunnotifyUrl; //缴纳支付回调地址
    /**
     * 支付宝缴纳支付
     *
     * @return
     */
    @Bean(name = "mergeOrderTrunAliPayConfig")
    public AliMergePayClientConfig initTrunAliPayService() {
        AliMergePayClientConfig config = new AliMergePayClientConfig();
        try {
            PropertiesConfiguration propertiesConfiguration = new PropertiesConfiguration("payConfig/zfbinfo.yml");
            config.setAppId(propertiesConfiguration.getString("appid"));
            config.setSigType(propertiesConfiguration.getString("sign_type"));
            config.setCharset(propertiesConfiguration.getString("charset"));
            config.setFormat(propertiesConfiguration.getString("format"));
            config.setPayUrl(propertiesConfiguration.getString("open_api_domain"));
            config.setPrivateKey(propertiesConfiguration.getString("private_key"));
            config.setPublicKey(propertiesConfiguration.getString("alipay_public_key"));
            config.setAuthCallbackUrl(aliTrunnotifyUrl);
        } catch (ConfigurationException e) {
            e.printStackTrace();
        }
        return config;
    }




    @Autowired
    WxpayBaseConfig wxpayBaseConfig;
    @Value("${aopayconfig.wxqc.mergeOrder.notifyUrl}")
    String notifyUrl; //支付回调地址
    @Value("${aopayconfig.wxqc.mergeOrder.refundNotifyUrl}")
    String refundNotifyUrl;//退款回调地址
    @Bean(name = "mergeOrderWxPayConfig")
    public WxMergePayClientConfig initWxPayService() {
        WxMergePayClientConfig config = new WxMergePayClientConfig();
        BeanUtils.copyProperties(wxpayBaseConfig,config);
        config.setNotifyUrl(notifyUrl);
        config.setRefundNotifyUrl(refundNotifyUrl);
        return config;
    }

    //微信缴纳支付配置
    @Value("${aopayconfig.wxqc.mergeOrder.trunNotifyUrl}")
    String trunNotifyUrl; //缴纳支付回调地址
    @Bean(name = "mergeOrderWxTrunPayConfig")
    public WxMergePayClientConfig trunWxPayService() {
        WxMergePayClientConfig config = new WxMergePayClientConfig();
        BeanUtils.copyProperties(wxpayBaseConfig,config);
        config.setNotifyUrl(trunNotifyUrl);
        config.setRefundNotifyUrl(null);
        return config;
    }


    @Value("${aopayconfig.yee.mergeOrder.yee_customerNo}")
    String yeeCustomerNo; //收款商编
    @Value("${aopayconfig.yee.mergeOrder.yee_receiverCallbackUrl}")
    String yeeNotifyUrl; //支付回调地址
    @Value("${aopayconfig.yee.mergeOrder.yee_timeoutExpress}")
    String yeeTimeoutExpress; //支付回调地址
    @Value("${aopayconfig.yee.mergeOrder.yee_frontUrl}")
    String yeeFrontUrl; //支付前端回调地址


    @Bean(name = "mergeOrderYeePayConfig")
    public YeeMergeOrderPayConfiguration initYeePayService() {
        YeeMergeOrderPayConfiguration config = new YeeMergeOrderPayConfiguration();
        config.setYee_receiverCallbackUrl(yeeNotifyUrl);
        config.setYee_frontUrl(yeeFrontUrl);
        config.setYee_customerNo(yeeCustomerNo);
        config.setYee_timeoutExpress(yeeTimeoutExpress);
//        config.setReceiverCallbackUrl(yeeRefundNotifyUrl);
//        config.setPayerCallBackUrl(yeePayerCallBackUrl);
        return config;
    }




}