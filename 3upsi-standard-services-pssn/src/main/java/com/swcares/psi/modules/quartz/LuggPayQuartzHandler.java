package com.swcares.psi.modules.quartz;

import com.swcares.psi.base.data.service.SmsService;
import com.swcares.psi.base.data.service.SmsTemplateService;
import com.swcares.psi.base.util.DateUtils;
import com.swcares.psi.combine.config.PSISystemConfiguration;
import com.swcares.psi.combine.constant.CacheConstants;
import com.swcares.psi.common.compensate.dao.OrdersInfoDao;
import com.swcares.psi.common.compensate.dao.PassengerInfoDao;
import com.swcares.psi.common.compensate.entity.OrderInfo;
import com.swcares.psi.common.compensate.entity.PassengerInfo;
import com.swcares.psi.common.security.util.PsiUserUtils;
import com.swcares.psi.config.CompensatePayConfig;
import com.swcares.psi.irregular.flight.dao.AoTransRecordDao;
import com.swcares.psi.irregular.flight.dao.ApplyOrderDao;
import com.swcares.psi.irregular.flight.dao.ApplyPaxDao;
import com.swcares.psi.irregular.flight.dao.impl.PaxReceiveDaoImpl;
import com.swcares.psi.irregular.flight.entity.AoTransRecord;
import com.swcares.psi.irregular.flight.entity.ApplyOrderInfo;
import com.swcares.psi.irregular.flight.entity.ApplyPaxInfo;
import com.swcares.psi.irregular.flight.service.ApplyOrderQuartzService;
import com.swcares.psi.irregular.flight.service.FlightCompensateService;
import com.swcares.psi.irregular.flight.service.PaxInfoService;
import com.swcares.psi.irregular.flight.service.impl.FlightCompensateServiceImpl;
import com.swcares.psi.irregular.flight.vo.ApplyOrderInfoVo;
import com.swcares.psi.message.api.dto.SysMessageSaveDto;
import com.swcares.psi.message.api.enums.MessageTypeEnum;
import com.swcares.psi.message.api.enums.MsgStateEnum;
import com.swcares.psi.message.service.SysMessageSendService;
import com.swcares.psi.pay.bean.PayConstant;
import com.swcares.psi.pay.bean.alipay.AliPayInquireTrans;
import com.swcares.psi.pay.bean.alipay.AliPayResult;
import com.swcares.psi.pay.bean.alipay.AlipayPartioner;
import com.swcares.psi.pay.bean.chinapay.ChinaPayConstant;
import com.swcares.psi.pay.bean.chinapay.ChinaPayInqueryResult;
import com.swcares.psi.pay.bean.chinapay.ChinaPayTransOrder;
import com.swcares.psi.pay.bean.chinapay.ChinaPayTransResult;
import com.swcares.psi.pay.bean.wxpay.WxQueryResult;
import com.swcares.psi.pay.bean.wxpay.WxTransferOrder;
import com.swcares.psi.pay.bean.wxpay.WxTransferResult;
import com.swcares.psi.pay.service.alipay.AliPayTrans;
import com.swcares.psi.pay.service.chinapay.ChinaPayTransactionType;
import com.swcares.psi.pay.service.wx.WxTransactionType;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ParseException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import static com.swcares.psi.combine.constant.CacheConstants.LUGG_QUARTZ_LOOK;
import static com.swcares.psi.pay.bean.PayConstant.PAY_ORDER_TITLE;

/**
 * ClassName：com.swcares.psi.modules.quartz <br>
 * Description：行李支付定时任务处理 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月07日 16:51 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class LuggPayQuartzHandler extends BasePayQuartzHandler{
    @Autowired
    private SmsTemplateService smsTemplateService;
    @Autowired
    private SmsService smsService;
    @Resource
    private ApplyOrderQuartzService applyOrderQuartzService;
    @Resource
    private AoTransRecordDao aoTransRecordDao;
    //可支付订单状态
    private static final String APPLY_TYPE_LG= "1";
    //可支付订单状态
    private static final String PAY_REMARK="川航补偿金";
    //补偿单关闭
    private static final String APPLY_ORDER_CLOSE = "您申领的补偿中，存在补偿单已关闭，请重新申请";
    @Autowired
    private PsiUserUtils psiUserUtils;
    @Autowired
    private LuggPayQuartzHandler payQuartzHandler;

    @Autowired
    private PSISystemConfiguration systemConfiguration;

    @Autowired
    private PaxReceiveDaoImpl paxReceiveDao;

    @Autowired
    private PaxInfoService paxInfoService;

    @Autowired
    private ApplyPaxDao applyPaxDao;

    @Autowired
    private OrdersInfoDao ordersInfoDao;

    @Autowired
    private PassengerInfoDao passengerInfoDao;

    @Autowired
    private ApplyOrderDao applyOrderDao;

    @Autowired
    private FlightCompensateService flightCompensateService;

    @Autowired
    private SysMessageSendService sysMessageSendService;

    @Autowired
    private Redisson redisson;

    public void payHandler() {
        RLock lock = redisson.getLock(LUGG_QUARTZ_LOOK + "1");
        if (lock == null || !lock.tryLock()) {
            log.info("------->>>【服务补偿-行李补偿】【异常行李】支付定时任务已有其他服务器执行！！！本次执行结束");
            return;
        }
        try {
            payQuartzHandler.doPayHandler();
        }catch (Exception e){
            log.error("【【服务补偿-行李补偿】异常行李支付定时任务报错】",e);
        }finally {
            log.info("------->>>【服务补偿-行李补偿】【异常行李】支付定时任务执行结束，并发锁释放");
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doPayHandler(){
        //查询待支付订单 异常行李模块
        List<ApplyOrderInfoVo> dataList = applyOrderQuartzService.findUnpaidOrderInfo(APPLY_TYPE_LG);
        log.info("------->>>【服务补偿-行李补偿】【异常行李】补偿支付定时任务开始，查询待支付订单共计{}条", dataList.size());
        if (dataList.size() < 1) {
            log.info("------->>>【服务补偿-行李补偿】【异常行李】补偿支付定时任务结束，查询无待支付订单！！！");
            return;
        }

        // 非生产环境模拟支付流程
        final boolean mockTransfer;
        final String mockTransferKey = "LUG";
        final PSISystemConfiguration.DpTransferMock dpTransferMock;

        if (systemConfiguration.getDpTransferMocks() != null && systemConfiguration.getDpTransferMocks().containsKey(mockTransferKey))  {
            mockTransfer = true;
            dpTransferMock = systemConfiguration.getDpTransferMocks().get(mockTransferKey);
        } else {
            mockTransfer = false;
            dpTransferMock = null;
        }

        Set<String> applyCodes = new HashSet<>();
        for(ApplyOrderInfoVo applyOrderInfoVo : dataList){
            applyCodes.add(applyOrderInfoVo.getApplyCode());
        }
        // 查询存在关闭补偿单的申领单集合
        List<ApplyOrderInfoVo> applyCodeListWithCloseOrder = applyOrderQuartzService.findApplyCodeListWithCloseOrder(applyCodes.toArray(new String[applyCodes.size()]));
        applyCodes.clear();
        // 把存在关闭补偿单的申领单号存进map中
        for(ApplyOrderInfoVo applyOrderInfoVo : applyCodeListWithCloseOrder){
            applyCodes.add(applyOrderInfoVo.getApplyCode());
        }
        for (ApplyOrderInfoVo orderInfo : dataList) {
            String status;
            //领取状态
            String receive_status = PayConstant.RECEIVE_STATUS_UNPAID;
            //支付时间
            String receiveTime = null;
            log.info("------->>>【服务补偿-行李补偿】【异常行李】补偿支付定时任务，开始处理订单-申领单号：{},支付状态(0未支付,1已支付,2支付失败，3支付中)：{}", orderInfo.getApplyCode(), orderInfo.getPayStatus());
            AoTransRecord aoTransRecord = new AoTransRecord();
            aoTransRecord.setApplyCode(orderInfo.getApplyCode());
            aoTransRecord.setApplyAccount(orderInfo.getGetMoneyAccount());
            aoTransRecord.setPayType(orderInfo.getGetMoneyWay());
            aoTransRecord.setPayStartTime(new Date());
            aoTransRecord.setCreateTime(aoTransRecord.getPayStartTime());
            Date payDate = ObjectUtils.isNotEmpty(orderInfo.getPayDate()) ? orderInfo.getPayDate() : new Date();
            orderInfo.setPayDate(payDate);

            if (mockTransfer && dpTransferMock != null) {
                // 模拟支付流程
                aoTransRecord.setPayStatus(String.valueOf(dpTransferMock.getPayStatus()));
                aoTransRecord.setTransCode(dpTransferMock.getPayReturnMsg());
                aoTransRecord.setTransMsg(dpTransferMock.getPayReturnMsg());
                aoTransRecord.setTransAmount(orderInfo.getTransAmount());
                log.info("异常行李支付模拟返回结果：{}, applyCode:{}, payType:{}", dpTransferMock.getPayStatus(), orderInfo.getApplyCode(), orderInfo.getGetMoneyWay());
            } else {
                // 真实支付流程
                final String merDate = DateUtils.parseDateToStr(payDate, DateUtils.YYYYMMDD);

                if(applyCodes.contains(orderInfo.getApplyCode())){
                    log.info("【服务补偿-行李补偿】【异常行李】申领单apply_code :" + orderInfo.getApplyCode() + "关联的补偿单已关闭！");
                    aoTransRecord.setErrCodeDes(APPLY_ORDER_CLOSE);
                    aoTransRecord.setErrCode("ORDER_CLOSED");
                    //支付状态
                    status = PayConstant.PAY_STATUS_FAIL;
                    aoTransRecord.setPayStatus(status);
                }else{
                    status = PayConstant.PAY_STATUS_UNPAID;
                    //验证订单是否已支付成功 && 是否为未支付订单
                    //状态为未支付执行支付操作
                    if (!confirmHasPaid(aoTransRecord, orderInfo)
                            && PayConstant.PAY_STATUS_UNPAID.equals(orderInfo.getPayStatus())) {
                        //支付
                        try {
                            conductPayment(aoTransRecord, orderInfo, merDate);
                        } catch (Exception e) {
                            log.error("【服务补偿-行李补偿】【异常行李】：转账异常》》》旅客："+orderInfo.getPaxName()+"的申领单--单号：" + orderInfo.getApplyCode() , e);
                            continue;
                        }
                    }
                }
                log.info("【服务补偿-行李补偿】【异常行李】申领单号【{}】 调用三方支付接口后返回支付状态(0未支付,1已支付,2支付失败,3支付中)【{}】", orderInfo.getApplyCode(), aoTransRecord.getPayStatus());
            }

            //支付状态为成功
            if (PayConstant.PAY_STATUS_PAID.equals(aoTransRecord.getPayStatus())) {
                receiveTime = DateUtils.parseDateToStr(aoTransRecord.getPayReturnTime() == null ? new Date() : aoTransRecord.getPayReturnTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            }

            if(PayConstant.PAY_STATUS_PAID.equals(aoTransRecord.getPayStatus())){
                //1.成功的处理逻辑（1.支付记录表  2.申领单表的申领状态 - 已领取  3.赔偿单的旅客表状态（考虑卡卷） - 已领取）
                handlerSuccess(orderInfo, aoTransRecord, payDate, receiveTime, applyOrderQuartzService, aoTransRecordDao);
            }else if(PayConstant.PAY_STATUS_FAIL.equals(aoTransRecord.getPayStatus())){
                //2.失败的逻辑处理（1.支付记录表(不扫描失败数据，所以不用管是第几次失败)  2.申领单表的申领状态 - 失败  3.赔偿单的旅客表状态（考虑卡卷） - 待领取）
                handlerFailed(orderInfo, aoTransRecord, payDate, receiveTime, applyOrderQuartzService, aoTransRecordDao);
            }else if(PayConstant.PAY_STATUS_INPROCESS.equals(aoTransRecord.getPayStatus())){
                // 3.处理中的逻辑（1.支付记录表（要看是第一次支付中还是后续支付中，第一次才插入） 2.申领单表里面的支付状态改为领取中）
                handlerProcessing(orderInfo, aoTransRecord, payDate, receiveTime, applyOrderQuartzService, aoTransRecordDao);
            }

            sendSmsToPassenger(orderInfo, aoTransRecord, smsService, smsTemplateService);
            log.info("------->>>【服务补偿-行李补偿】【异常行李】补偿支付定时任务，处理订单结束-订单号：{}", orderInfo.getApplyCode());
        }
    }

    @Override
    public void handlerSuccess(ApplyOrderInfoVo orderInfo, AoTransRecord aoTransRecord, Date payDate, String receiveTime, ApplyOrderQuartzService applyOrderQuartzService, AoTransRecordDao aoTransRecordDao) {
        super.handlerSuccess(orderInfo, aoTransRecord, payDate, receiveTime, applyOrderQuartzService, aoTransRecordDao);

        final ApplyOrderInfo applyOrderInfo = applyOrderDao.findByApplyCode(orderInfo.getApplyCode());
        if (applyOrderInfo == null && applyOrderInfo.getOrderId() == null) {
            return;
        }

        final String orderId = applyOrderInfo.getOrderId();
        OrderInfo order = ordersInfoDao.getByOrderId(orderId);
        if (order == null || order.getPkgRemittanceObjectType() == null)  {
            return;
        }

        // 站内信通知
        sendMessageToRemittanceOrderCreator(order, true);
    }

    /**
     * 转账失败流程处理（针对系统自动汇款转账订单，需将补偿单进行注销处理，后续业务任务重新发起补偿单）
     *
     * @param orderInfo
     * @param aoTransRecord
     * @param payDate
     * @param receiveTime
     * @param applyOrderQuartzService
     * @param aoTransRecordDao
     */
    @Override
    public void handlerFailed(ApplyOrderInfoVo orderInfo, AoTransRecord aoTransRecord, Date payDate, String receiveTime, ApplyOrderQuartzService applyOrderQuartzService, AoTransRecordDao aoTransRecordDao){
        // 执行现失败处理流程
        super.handlerFailed(orderInfo, aoTransRecord, payDate, receiveTime, applyOrderQuartzService, aoTransRecordDao);

        final ApplyOrderInfo applyOrderInfo = applyOrderDao.findByApplyCode(orderInfo.getApplyCode());
        if (applyOrderInfo == null && applyOrderInfo.getOrderId() == null) {
            return;
        }

        final String orderId = applyOrderInfo.getOrderId();
        OrderInfo order = ordersInfoDao.getByOrderId(orderId);
        if (order == null || order.getPkgRemittanceObjectType() == null)  {
            return;
        }
        List<PassengerInfo> passengerInfos = passengerInfoDao.findByOrderId(orderId);
        final PassengerInfo passengerInfo = passengerInfos.get(0);
        // 转账失败旅客领取状态应被重置为未领取
        if (passengerInfo.getReceiveStatus() != 0) {
            return;
        }

        // 站内信通知
        sendMessageToRemittanceOrderCreator(order, false);

        log.info("------->>>【服务补偿-行李补偿】行李补偿支付定时任务，【自动汇款转账失败】-订单号：{}", orderId);
        // 汇款转账失败后补偿单做注销处理（防止系统转账任务循环执行自动汇款流程，后续需业务重新发起补偿）
        flightCompensateService.updateOrderStatus(orderId, FlightCompensateServiceImpl.ORDER_STATUS_CANCEL);

        order = ordersInfoDao.getByOrderId(orderId);
        if (order != null && FlightCompensateServiceImpl.ORDER_STATUS_CANCEL.equals(order.getStatus())) {
            log.info("------->>>【服务补偿-行李补偿】行李补偿支付定时任务，【自动汇款转账失败-更新补偿单为已注销成功】-订单号：{}", orderId);
        } else {
            log.info("------->>>【服务补偿-行李补偿】行李补偿支付定时任务，【自动汇款转账失败-更新补偿单为已注销失败】-订单号：{}", orderId);
        }
    }

    /**
     * 系统自动汇款转账结果（成功或失败）发送站内信给补偿单创建人员
     *
     * @param orderInfo
     * @param isSuccess
     */
    private void sendMessageToRemittanceOrderCreator(final OrderInfo orderInfo, final boolean isSuccess)  {
        String userNo = orderInfo.getCreateId();

        StringBuilder content = new StringBuilder();
        content.append("<p>补偿类型：").append(isSuccess? "异常行李汇款结果：成功" : "异常行李汇款结果：失败").append("</p>");
        content.append("<p>补偿航班：").append(orderInfo.getFlightNo()+" "+orderInfo.getFlightDate()).append("</p>");
        content.append("<p>补偿总金额：").append(Double.parseDouble(orderInfo.getPayeeMoney())+"元").append("</p>");

        SysMessageSaveDto sysMessageSaveDto = new SysMessageSaveDto();
        sysMessageSaveDto.setMsgTitle(isSuccess? "补偿单汇款成功" : "补偿单汇款失败");
        sysMessageSaveDto.setMsgContent(content.toString());
        sysMessageSaveDto.setMsgType(MessageTypeEnum.LUGGAGE_REMITTANCE.getType());
        sysMessageSaveDto.setPcUrl(MessageFormat.format(MessageTypeEnum.LUGGAGE_REMITTANCE.getPcUrl(), orderInfo.getOrderId()));
        sysMessageSaveDto.setMobileUrl(MessageFormat.format(MessageTypeEnum.LUGGAGE_REMITTANCE.getMobileUrl(), orderInfo.getOrderId()));

        sysMessageSaveDto.setMsgImportance(1);
        sysMessageSaveDto.setMsgReplyUser(new String[]{userNo});
        sysMessageSaveDto.setMsgSendType(MsgStateEnum.MSGSENDTYPE_SYSTEM.getCode());
        sysMessageSaveDto.setBusinessMsgSendUser("-1");
        sysMessageSendService.sysMessageSave(sysMessageSaveDto);
    }

    /**
     * Title： confirmHasPaid<br>
     * Description： 查询订单支付记录 <br>
     * author：傅欣荣 <br>
     * date：2020/4/7 20:18 <br>
     *
     * @param
     * @return
     */
    private boolean confirmHasPaid(AoTransRecord aoTransRecord, ApplyOrderInfoVo applyOrderInfo) {
        log.info("------->>>【服务补偿-行李补偿】行李补偿支付定时任务，正在执行【查询】-订单号：{}，领取方式(0微信，1银联,2支付宝,3优惠券,4现金,5 易宝)：{}",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay());
        boolean flag = false;
        //微信支付
        if (PayConstant.PAY_CLASS_WECHAT.equals(applyOrderInfo.getGetMoneyWay())) {
            WxQueryResult wxQueryResult = CompensatePayConfig.getWxPayService(CompensatePayConfig.PayType.LUG_WXPAY).query(null, applyOrderInfo.getApplyCode(),
                    WxTransactionType.TRANSFERS_QUERY, WxQueryResult.class);
            aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_INPROCESS);

            log.info("------->>>【服务补偿-行李补偿】行李补偿支付定时任务，正在执行【查询】-订单号：{}，领取方式(0微信，1银联,2支付宝,3优惠券,4现金,5 易宝)：{}，查询反馈结果{}",
                    applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), wxQueryResult.toString());

            //支付成功的判断逻辑： status == SUCCESS && null == error_code
            if (PayConstant.PAY_SUCCESS.equalsIgnoreCase(wxQueryResult.getStatus()) && null == wxQueryResult.getErr_code()) {
                aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_PAID);
                flag = true;

                //支付中的判断逻辑： status == PAY_PROCESSING
            }else if(PayConstant.PAY_PROCESSING.equalsIgnoreCase(wxQueryResult.getStatus())){
                aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_INPROCESS);

                //支付失败的判断逻辑： status == FAILED
            }else if(PayConstant.PAY_FAILED.equalsIgnoreCase(wxQueryResult.getStatus())){
                aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_FAIL);
            }

            encapsulatedByWeiXin(aoTransRecord, wxQueryResult);

        } else if (PayConstant.PAY_CLASS_ALIPAY.equals(applyOrderInfo.getGetMoneyWay())) {
                //支付宝支付
                AliPayInquireTrans aliPayTrans = new AliPayInquireTrans();
                AliPayResult transfer = new AliPayResult();
                try {
                    aliPayTrans.setOut_biz_no(applyOrderInfo.getApplyCode());

                    transfer = CompensatePayConfig.getAliPayService(CompensatePayConfig.PayType.LUG_ALIPAY).aliPayQuire(aliPayTrans);
                } catch (Exception e) {
                    log.error("【服务补偿-行李补偿】【支付宝】订单查询获取失败》》》》", e);
                }
                //文档地址： https://opendocs.alipay.com/open/58a29899_alipay.fund.trans.common.query?pathHash=aad07c6d&ref=api&scene=f9fece54d41f49cbbd00dc73655a01a4
                // 查询支付成功：code = 10000 && status = SUCCESS
                if(PayConstant.ALIPAY_GATEWAY_SUCCESS.equals(transfer.getCode()) && PayConstant.PAY_SUCCESS.equalsIgnoreCase(transfer.getStatus())) {
                    aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_PAID);
                    flag = true;
                }

                // 查询支付失败：code = 10000 && status = FAIL
                if(PayConstant.ALIPAY_GATEWAY_SUCCESS.equals(transfer.getCode()) && (PayConstant.PAY_FAIL.equalsIgnoreCase(transfer.getStatus()))){
                    aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_FAIL);
                }

                // 查询支付中：code = 10000 && ( status = DEALING || status = REFUND))
                if(PayConstant.ALIPAY_GATEWAY_SUCCESS.equals(transfer.getCode()) &&
                        (PayConstant.ALIPAY_DEALING.equalsIgnoreCase(transfer.getStatus()) || PayConstant.ALIPAY_REFUND.equalsIgnoreCase(transfer.getStatus()))){
                    aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_INPROCESS);
                }
                aoTransRecord.setTransAmount(String.valueOf(applyOrderInfo.getTransAmount()));
                encapsulatedByAliPay(aoTransRecord, transfer);
            } else if (PayConstant.PAY_CLASS_CHINAPAY.equals(applyOrderInfo.getGetMoneyWay())) {
                //银联支付
                //默认支付中
                aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_INPROCESS);
                //查询需要支付的银联订单状态
                String orderNo = applyOrderInfo.getApplyCode().length() > 16 ? applyOrderInfo.getApplyCode().substring(0, 16) : applyOrderInfo.getApplyCode();
                ChinaPayInqueryResult cpir = CompensatePayConfig.getChinaPayService(CompensatePayConfig.PayType.LUG_CHINAPAY).query(orderNo, orderNo,
                        ChinaPayTransactionType.INQUIRE_URL, ChinaPayInqueryResult.class);

                //判断是否支付成功
                if (PayConstant.CHINAPAY_SUCCESS.equalsIgnoreCase(cpir.getStat())) {
                    aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_PAID);
                    flag = true;
                }

                // 支付失败： stat = 6(银行已退单) || stat = 9(重汇已退单)
                if (PayConstant.CHINAPAY_DETIL_6.equalsIgnoreCase(cpir.getStat()) || PayConstant.CHINAPAY_DETIL_9.equalsIgnoreCase(cpir.getStat())) {
                    aoTransRecord.setPayStatus(PayConstant.PAY_STATUS_FAIL);
                }
                cpir.setUsrName(applyOrderInfo.getPaxName());
                cpir.setBankName(applyOrderInfo.getOpenBankName());
                cpir.setCardNo(applyOrderInfo.getIdNo());
                aoTransRecord.setTransAmount(String.valueOf(applyOrderInfo.getTransAmount()));
                encapsulatedByChinaPay(aoTransRecord, cpir);
            }
        return flag;
    }
    /**
     * Title：conductPayment <br>
     * Description：进行支付处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 15:20 <br>
     *
     * @param aoTransRecord
     * @param applyOrderInfoVo
     * @return
     */
    private void conductPayment(AoTransRecord aoTransRecord, ApplyOrderInfoVo applyOrderInfoVo, String merDate) {
        log.info("------->>>【服务补偿-行李补偿】行李补偿支付定时任务，正在执行【支付】-订单号：{}，领取方式(0微信，1银联,2支付宝,3优惠券,4现金,5 易宝)：{}",
                applyOrderInfoVo.getApplyCode(), applyOrderInfoVo.getGetMoneyWay());
        ApplyOrderInfo applyOrderInfo = applyOrderInfoVo.getApplyOrderInfo(applyOrderInfoVo);        //微信
        aoTransRecord.setTransAmount(applyOrderInfoVo.getTransAmount());
        if (PayConstant.PAY_CLASS_WECHAT.equals(applyOrderInfo.getGetMoneyWay())) {
            payWeiXinHandler(aoTransRecord, applyOrderInfo);
        }
        //支付宝
        if (PayConstant.PAY_CLASS_ALIPAY.equals(applyOrderInfo.getGetMoneyWay())) {
            payAlipayHandler(aoTransRecord, applyOrderInfo);
        }
        //银联
        if (PayConstant.PAY_CLASS_CHINAPAY.equals(applyOrderInfo.getGetMoneyWay())) {
            applyOrderInfo.setTransAmount( String.valueOf(Integer.parseInt(applyOrderInfo.getTransAmount()) * 100) );
            payChinaPayHandler(aoTransRecord, applyOrderInfo,merDate );
        }

    }


    /**
     * Title：payWeiXinHandler <br>
     * Description： 微信支付逻辑处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 18:57 <br>
     *
     * @param
     * @return
     */
    private void payWeiXinHandler(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo) {
        WxTransferOrder tranOder = new WxTransferOrder();
        tranOder.setOutNo(applyOrderInfo.getApplyCode());
        tranOder.setAmount(new BigDecimal(applyOrderInfo.getTransAmount()));
        tranOder.setPayeeAccount(applyOrderInfo.getGetMoneyAccount());
        tranOder.setPayeeName(applyOrderInfo.getApplyInputName() != null ? applyOrderInfo.getApplyInputName() : applyOrderInfo.getApplyName() != null ? applyOrderInfo.getApplyName() : applyOrderInfo.getApplyUser());
        tranOder.setRemark(PAY_REMARK);
        tranOder.setTransactionType(WxTransactionType.TRANSFERS);
        WxTransferResult transferResult = (WxTransferResult) CompensatePayConfig.getWxPayService(CompensatePayConfig.PayType.LUG_WXPAY).transfer(tranOder, WxTransferResult.class);
        log.info("------->>>【服务补偿-行李补偿】【行李】微信补偿支付定时任务，正在执行【支付】-订单号：{}，领取方式(0微信，1银联,2支付宝,3优惠券,4现金,5 易宝)：{}，支付反馈结果{}",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), transferResult.toString());

        //支付结果默认是处理中
        String payStatus = PayConstant.PAY_STATUS_INPROCESS;

        //参考支付文档：https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=14_2
        //支付【成功】的判断逻辑： return_code == SUCCESS && result_code == SUCCESS
        if (PayConstant.PAY_SUCCESS.equalsIgnoreCase(transferResult.getResult_code())
                && PayConstant.PAY_SUCCESS.equalsIgnoreCase(transferResult.getReturn_code())) {
            aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                    transferResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            aoTransRecord.setReturnOrderNo(transferResult.getPayment_no());
            //支付状态设置为已支付
            payStatus = PayConstant.PAY_STATUS_PAID;
        }

        //失败 return_code == SUCCESS && result_code == FAIL && errCode包含以上
        if (PayConstant.PAY_FAIL.equalsIgnoreCase(transferResult.getResult_code())
                && PayConstant.PAY_SUCCESS.equalsIgnoreCase(transferResult.getReturn_code())
                && PayConstant.ERRORCODE.indexOf(transferResult.getErr_code().toUpperCase()) > -1) {
            aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                    transferResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            //支付状态设置为已支付
            payStatus = PayConstant.PAY_STATUS_FAIL;

        }
        // 对支付中和部分支付失败（不知道是真失败，还是假失败）的逻辑不做处理，由定时任务去查询最新支付状态时做处理
        // @See 当前类#confirmHasPaid

        aoTransRecord.setTransCode(transferResult.getReturn_code());
        aoTransRecord.setTransMsg(transferResult.getReturn_msg());
        aoTransRecord.setErrCode(transferResult.getErr_code());
        aoTransRecord.setErrCodeDes(transferResult.getErr_code_des());
        aoTransRecord.setPayStatus(payStatus);
    }


    /**
     * Title：payAlipayHandler  <br>
     * Description：  支付宝转账逻辑处理     <br>
     * <AUTHOR>   <br>
     * @date 2020/9/22 13:26  <br>
     * @param  aoTransRecord, applyOrderInfo]
     * @return void
     */
    private void payAlipayHandler(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo) {
        log.info("【服务补偿-行李补偿】【行李】 支付宝支付-开始》》》");
        AliPayTrans alpt = new AliPayTrans();
        alpt.setOut_biz_no(applyOrderInfo.getApplyCode());
        //收款方信息
        AlipayPartioner appart = new AlipayPartioner();
        appart.setIdentity(applyOrderInfo.getGetMoneyAccount());
        appart.setName(applyOrderInfo.getApplyInputName() != null ? applyOrderInfo.getApplyInputName() : applyOrderInfo.getApplyName() != null ? applyOrderInfo.getApplyName() : applyOrderInfo.getApplyUser());
        alpt.setOrder_title(PAY_ORDER_TITLE);
        alpt.setRemark(PAY_REMARK);
        alpt.setTrans_amount(applyOrderInfo.getTransAmount());
        alpt.setAppart(appart);
        AliPayResult aprt = (AliPayResult) CompensatePayConfig.getAliPayService(CompensatePayConfig.PayType.LUG_ALIPAY).transfer(alpt, AliPayResult.class);
        //文档地址：https://opendocs.alipay.com/open/02byuo?pathHash=********&scene=ca56bca529e64125a2786703c6192d41&ref=api
        String payStatus = PayConstant.PAY_STATUS_INPROCESS;
        if (PayConstant.ALIPAY_GATEWAY_SUCCESS.equalsIgnoreCase(aprt.getCode()) && PayConstant.PAY_SUCCESS.equalsIgnoreCase(aprt.getStatus())) {
            if (StringUtils.isNotEmpty(aprt.getTrans_date())) {
                aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(aprt.getTrans_date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
            aoTransRecord.setReturnOrderNo(aprt.getPay_fund_order_id());
            payStatus = PayConstant.PAY_STATUS_PAID;
        }else if(StringUtils.isNotEmpty(aprt.getSub_code()) &&  PayConstant.ALIPAY_ERROR_CODE.indexOf(aprt.getSub_code()) > -1){
            //@TODO 这个字段是标记失败的，正式用支付宝的时候需要重新测试，并在几十个错误码里面找出失败的
            // sub_code ！= null  && sub_code在errorCode中
            payStatus = PayConstant.PAY_STATUS_FAIL;
        }else{
            payStatus = PayConstant.PAY_STATUS_INPROCESS;
        }

        aoTransRecord.setTransCode(aprt.getCode() +":"+aprt.getStatus());
        aoTransRecord.setTransMsg(aprt.getMsg());
        aoTransRecord.setAppId(aprt.getAppId());
        aoTransRecord.setTransSubCode(aprt.getSub_code());
        aoTransRecord.setErrCode(aprt.getSub_code());
        aoTransRecord.setErrCodeDes(aprt.getSub_msg());
        aoTransRecord.setApplyCode(aprt.getOut_biz_no());
        aoTransRecord.setPayStatus(payStatus);
    }

    /**
     * Title：payAlipayHandler  <br>
     * Description：  银联支付处理逻辑  <br>
     *
     * @param aoTransRecord, applyOrderInfo
     * @return void
     * <AUTHOR>   <br>
     * @date 2020/9/21 15:39  <br>
     */
    private void payChinaPayHandler(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo, String merDate) {

        ChinaPayTransOrder cppt = new ChinaPayTransOrder();
        cppt.setDpChinaPayMock(getDpChinaPayMock());

        String payStatus = PayConstant.PAY_STATUS_INPROCESS;
        if(null == applyOrderInfo.getPayDate()){
            log.info("【服务补偿-行李补偿】【行李】 银联支付-订单支付时间为null】");
            return ;
        }
        cppt.setMerDate(merDate);
        cppt.setApplyOrderNo(applyOrderInfo.getApplyCode().length() > 16 ? applyOrderInfo.getApplyCode().substring(0, 16) : applyOrderInfo.getApplyCode());
        cppt.setCardNo(applyOrderInfo.getGetMoneyAccount());
        cppt.setUsrName(applyOrderInfo.getApplyInputName() != null ? applyOrderInfo.getApplyInputName() : applyOrderInfo.getApplyName() != null ? applyOrderInfo.getApplyName() : applyOrderInfo.getApplyUser());
        cppt.setOpenBank(applyOrderInfo.getOpenBankName());
        cppt.setPurpose(PAY_REMARK);
        cppt.setMerSeqId(cppt.getApplyOrderNo());
        cppt.setTransAmt(applyOrderInfo.getTransAmount());
        ChinaPayTransResult cppr = new ChinaPayTransResult();
        try {
            cppr = CompensatePayConfig.getChinaPayService(CompensatePayConfig.PayType.LUG_CHINAPAY).transfer(cppt,ChinaPayTransResult.class);
        } catch (Exception e) {
            log.error("【服务补偿-行李补偿】【行李】支付——进行银联支付出错！",e);
            return ;
        }

        // responseCode = 0000代表接收成功
        if (PayConstant.CHINAPAY_SUCCESS_CODE.equalsIgnoreCase(cppr.getResponseCode())) {
            // stat = s 交易成功
            if (PayConstant.CHINAPAY_SUCCESS.equalsIgnoreCase(cppr.getStat())) {
                aoTransRecord.setReturnOrderNo(cppr.getCpSeqId());
                payStatus = PayConstant.PAY_STATUS_PAID;
            } else if (PayConstant.CHINAPAY_DETIL_6.equalsIgnoreCase(cppr.getStat()) || PayConstant.CHINAPAY_DETIL_9.equalsIgnoreCase(cppr.getStat())) {
                // stat = 6(银行已退单) || stat = 9(重汇已退单)
                payStatus = PayConstant.PAY_STATUS_FAIL;
                aoTransRecord.setErrCodeDes("状态码：" + cppr.getStat() + "，支付失败");
                aoTransRecord.setErrCode(cppr.getStat());
            } else {
                payStatus = PayConstant.PAY_STATUS_INPROCESS;
                aoTransRecord.setErrCodeDes("状态码：" + cppr.getStat() + "，支付处理中");
                aoTransRecord.setErrCode(cppr.getStat());
            }
        }else if(ChinaPayConstant.SAME_PAY.equalsIgnoreCase(cppr.getResponseCode())){
            payStatus = PayConstant.PAY_STATUS_INPROCESS;
            aoTransRecord.setErrCodeDes("状态码：" + cppr.getStat() + "，支付处理中");
            aoTransRecord.setErrCode(cppr.getStat());
        }else if (ChinaPayConstant.BANK_REFUSED.equalsIgnoreCase(cppr.getResponseCode()) || ChinaPayConstant.NO_MONEY.equalsIgnoreCase(cppr.getResponseCode())){
            payStatus = PayConstant.PAY_STATUS_FAIL;
            aoTransRecord.setErrCodeDes("状态码：" + cppr.getStat() + "，支付失败");
            aoTransRecord.setErrCode(cppr.getStat());
        } else {
            payStatus = PayConstant.PAY_STATUS_INPROCESS;
            aoTransRecord.setErrCodeDes("状态码：" + cppr.getStat() + "，支付处理中");
            aoTransRecord.setErrCode(cppr.getStat());
        }

        log.info("【服务补偿-行李补偿】行李支付,申领单号：【{}】, 反馈状态code为：【{}】,stat=【{}】",  applyOrderInfo.getApplyCode(), cppr.getResponseCode(), cppr.getStat());

        aoTransRecord.setTransCode(cppr.getResponseCode());
        aoTransRecord.setAppId(cppr.getMerId());
        aoTransRecord.setApplyCode(cppr.getMerSeqId());
        aoTransRecord.setTransSubCode(cppr.getStat());
        aoTransRecord.setPayStatus(payStatus);
        try {

            if(StringUtils.isNotEmpty(cppr.getMerDate())){
                aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(cppr.getMerDate(),DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
            if(StringUtils.isNotEmpty(cppr.getCpDate())){
                aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(cppr.getCpDate(),DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
        } catch (ParseException e1) {
            log.error("【服务补偿-行李补偿】【行李 定时任务订单执行出现出错(银联支付-支付订单)】------->>"
                    + "【服务补偿-行李补偿】【时间日期转换异常】出现异常的订单："+applyOrderInfo.getApplyCode(),e1);
            if(Integer.parseInt(cppr.getResponseCode()) == ChinaPayConstant.CHINAPAY_EXE_SUCCESS
                    && PayConstant.CHINAPAY_SUCCESS.equals(cppr.getStat())){
                return ;
            }
            return ;
        } catch (Exception e1) {
            log.error("【服务补偿-行李补偿】【行李定时任务订单执行出现出错(银联支付-支付订单)】------->>"
                    + "出现异常的订单："+applyOrderInfo.getApplyCode(),e1);
            if(Integer.parseInt(cppr.getResponseCode()) == ChinaPayConstant.CHINAPAY_EXE_SUCCESS
                    && PayConstant.CHINAPAY_SUCCESS.equals(cppr.getStat())){
                log.error("【服务补偿-行李补偿】【行李定时任务订单执行出现出错(银联支付-支付订单)】------->>支付反馈成功！");
                return ;
            }
            return ;
        }
      }





    /**
     * Title： encapsulatedByChinaPay <br>
     * Description： 封装转账记录表对象-银联反馈<br>
     * author：郑雯雯 <br>
     * date：2020-09-30 09：16<br>
     *
     * @param
     * @return
     */
    private void encapsulatedByChinaPay(AoTransRecord aoTransRecord, ChinaPayInqueryResult cpr) {
            aoTransRecord.setTransCode(cpr.getCode());
            aoTransRecord.setAppId(cpr.getMerId());
            aoTransRecord.setApplyCode(cpr.getMerSeqId());
            aoTransRecord.setReturnOrderNo(cpr.getCpSeqId());
            aoTransRecord.setTransSubCode(cpr.getStat());
            if (StringUtils.isNotEmpty(cpr.getMerDate())) {
                aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(cpr.getMerDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
            if (StringUtils.isNotEmpty(cpr.getCpDate())) {
                aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(cpr.getCpDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
    }





    /**
     * Title： encapsulatedByAlibaba <br>
     * Description：  封装转账记录表对象-支付宝反馈  <br>
     * <AUTHOR>   <br>
     * @date 2020/9/21 18:25  <br>
     * @param
     * @return
     */
    private void encapsulatedByAliPay(AoTransRecord aoTransRecord, AliPayResult aliPayResult) {
        if (StringUtils.isNotEmpty(aliPayResult.getTrans_date())) {
            aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(aliPayResult.getTrans_date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        aoTransRecord.setTransCode(aliPayResult.getCode());
        aoTransRecord.setTransMsg(aliPayResult.getMsg());
        aoTransRecord.setAppId(aliPayResult.getAppId());
        aoTransRecord.setTransSubCode(aliPayResult.getSub_code());
        aoTransRecord.setErrCode(aliPayResult.getSub_code());
        aoTransRecord.setErrCodeDes(aliPayResult.getSub_msg());
        aoTransRecord.setApplyCode(aliPayResult.getOut_biz_no());
    }


    /**
     * Title： encapsulatedByWeiXin <br>
     * Description：封装转账记录表对象-微信反馈<br>
     * author：傅欣荣 <br>
     * date：2020/4/7 18:37 <br>
     *
     * @param
     * @return
     */
    private void encapsulatedByWeiXin(AoTransRecord aoTransRecord, WxQueryResult wxQueryResult) {
        if(StringUtils.isNotEmpty(wxQueryResult.getPayment_amount())){
            aoTransRecord.setTransAmount( String.valueOf(Integer.parseInt(wxQueryResult.getPayment_amount()) * 100) );
        }
        aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                wxQueryResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        aoTransRecord.setReturnOrderNo(wxQueryResult.getDetail_id());
        aoTransRecord.setTransCode(wxQueryResult.getReturn_code());
        aoTransRecord.setTransMsg(wxQueryResult.getReturn_msg());
        aoTransRecord.setErrCode(wxQueryResult.getErr_code());
        aoTransRecord.setErrCodeDes(wxQueryResult.getErr_code_des());

    }

    /**
     * 行李自动汇款定时任务入口
     */
    public void pkgRemittanceJob() {
        RLock lock = null;
        try {
            lock = redisson.getLock(CacheConstants.PKG_REMITTANCE_LOCK);
            if (lock != null && lock.tryLock()) {
                log.info("行李自动汇款任务获取到分布式锁后开始执行！");
                payQuartzHandler.pkgRemittanceOrders();
            } else {
                log.info("行李自动汇款任务未获取到分布式锁！");
            }
        } catch (Exception e) {
            log.error("行李自动汇款任务执行出错！", e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 汇款转账自动生成申领单相关信息
     */
    @Transactional
    public void pkgRemittanceOrders() {
        List<OrderInfo> orders = ordersInfoDao.getPkgRemittanceOrders(new Date(System.currentTimeMillis()-systemConfiguration.getDpPkgRemittanceOrdersEffectDuration()*1000));
        orders.forEach(this::pkgRemittanceOrder);
    }

    private void pkgRemittanceOrder(final OrderInfo order)  {
        final String orderId = order.getOrderId();
        try {
            List<PassengerInfo> passengerInfos = passengerInfoDao.findByOrderId(orderId);
            final PassengerInfo passengerInfo = passengerInfos.get(0);
            // 限制赔偿旅客领取状态为未领取
            if (passengerInfo.getReceiveStatus() != 0) {
                return;
            }
            final Date now = new Date();

            final String paxId = passengerInfo.getPaxId();
            final String paxName = passengerInfo.getPaxName();
            final String idNo = passengerInfo.getIdNo();
            final String mobile = passengerInfo.getTelephone();
            final String applyCode = paxInfoService.getApplyCode();

            // 申领单生成
            ApplyOrderInfo applyOrderInfo = new ApplyOrderInfo();
            applyOrderInfo.setOrderId(orderId);
            applyOrderInfo.setPaxId(paxId);
            // 申领人：旅客
            applyOrderInfo.setApplyUser(paxName);
            // 支付方式：银联
            applyOrderInfo.setGetMoneyWay("1");
            applyOrderInfo.setGetMoneyAccount(order.getPayeeAccount());
            // 申领方式：现场
            applyOrderInfo.setIsScene("1");
//            applyOrderInfo.setBankCode("");
            // 领取人：汇款对象
            applyOrderInfo.setApplyName(order.getPayee());
            applyOrderInfo.setApplyInputName(order.getPayee());
//            applyOrderInfo.setApplyIdNumber("");
            applyOrderInfo.setOpenBankName(order.getPayeeBank());
            applyOrderInfo.setTransAmount(order.getPayeeMoney());
            applyOrderInfo.setTelephone(mobile);

            applyOrderInfo.setApplyCode(applyCode);
            // 领取方式：本人
            applyOrderInfo.setApplyWay("0");
            // 领取状态：领取中
            applyOrderInfo.setStatus("0");
            // 审核状态：通过
            applyOrderInfo.setApplyStatus("1");
            applyOrderInfo.setAuditTime(now);
            applyOrderInfo.setCreateTime(now);
            // 支付状态：未支付
            applyOrderInfo.setPayStatus("0");
            applyOrderInfo.setQuickPay("0");
            applyOrderInfo.setApplyType(APPLY_TYPE_LG);
            applyOrderDao.save(applyOrderInfo);

            // 申领旅客生成
            ApplyPaxInfo applyPaxInfo = new ApplyPaxInfo();
            applyPaxInfo.setApplyCode(applyCode);
            applyPaxInfo.setPaxId(paxId);
            applyPaxInfo.setStatus("1");
            applyPaxInfo.setActingRole("0");
            applyPaxInfo.setOrderId(orderId);
            applyPaxInfo.setIdNo(idNo);
            applyPaxDao.save(applyPaxInfo);

            // 赔偿旅客领取状态更新为：领取中
            paxReceiveDao.updatePaxReceiveStatus("1", "0", "2", paxId, orderId, applyCode);

        } catch (Exception e) {
            log.error("服务补偿-异常行李汇款自动生成申领信息出错！orderId:{}", orderId, e);
        }
    }

    /**
     * 模拟银联支付转账响应结果
     *
     * @return
     */
    private PSISystemConfiguration.DpChinaPayMock getDpChinaPayMock() {
        final String mockTransferKey = "LUG";

        if (systemConfiguration.getDpChinaPayMocks() != null && systemConfiguration.getDpChinaPayMocks().containsKey(mockTransferKey))  {
            return systemConfiguration.getDpChinaPayMocks().get(mockTransferKey);
        }
        return null;
    }

}
