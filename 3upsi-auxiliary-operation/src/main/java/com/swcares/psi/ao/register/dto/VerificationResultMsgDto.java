package com.swcares.psi.ao.register.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.psi.ao.register.dto.VerificationResultMsgDto <br>;
 * Description：crm系统验证会员返回信息dto <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/1/20 13:20 <br>;
 * @version v1.0 <br>;
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VerificationResultMsgDto {

    /**
     * 结果状态码
     */
    @JsonProperty("StatusCode")
    private String StatusCode;

    /**
     * 结果描述
     */
    @JsonProperty("Message")
    private String Message;

    /**
     * 手机号
     */
    @JsonProperty("Mobile")
    private String Mobile;

    /**
     * 客户id
     */
    @JsonProperty("CustomerID")
    private String CustomerID;

}
