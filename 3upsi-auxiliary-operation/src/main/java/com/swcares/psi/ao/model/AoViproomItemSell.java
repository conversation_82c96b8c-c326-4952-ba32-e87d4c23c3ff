package com.swcares.psi.ao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 辅营贵宾厅套餐销售信息表 <br/>
 *
 * <AUTHOR> <br/>
 * @date    2020-12-13 12:12:38 <br/>
 * @since   1.0 <br/>
 */
@Data
@Builder
@TableName("ao_viproom_item_sell")
public class AoViproomItemSell implements Serializable {
    private static final long serialVersionUID=1L;
	/** 主键ID */
	@TableId(type = IdType.ASSIGN_ID)
	private String id;
	/** 套餐ID */
	private String itemId;
	/** 订单号 */
	private String orderId;
	/** 旅客ID */
	private String paxId;
	/** 旅客姓名 */
	private String paxName;
	/** 旅客类型 */
	private String paxType;
	/** 旅客卡类型 */
	private String cardType;
	/** 证件号 */
	private String idNo;
	/** 航班号 */
	private String flightNo;
	/** 航班日期 */
	private LocalDateTime flightDate;
	/** 出发地（三字码） */
	private String org;
	/** 到达地（三字码） */
	private String dst;
	/** 出发地 */
	private String orgName;
	/** 到达地 */
	private String dstName;
	/** 计划起飞时间 */
	private LocalDateTime std;
	/** 客票号 */
	private String tktNo;
	/** 航段类型(国内/国际/地区) */
	private String segmentType;
	/** 舱等(经济舱/公务舱) */
	private String subCabin;
	/** 登机口 */
	private String gate;
	/** 创建人 */
	private String createUser;
	/** 创建时间 */
	private LocalDateTime createTime;
	/** 更新人 */
	private String updateUser;
	/** 更新时间 */
	private LocalDateTime updateTime;

}