package com.swcares.psi.ao.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 辅营支付订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AoPayOrderInfo对象", description="辅营支付订单表")
@TableName("AO_PAY_ORDER_INFO")
public class AoPayOrderInfo implements Serializable {

    @ApiModelProperty(value = "主键")
    @TableId(value="ID",type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "支付单号(各支付平台的交易单号)")
    @TableField("PAY_ORDER_NO")
    private String payOrderNo;

    @ApiModelProperty(value = "支付类型(0现金,1微信,2支付宝,3易宝)")
    @TableField("PAY_TYPE")
    private String payType;

    @ApiModelProperty(value = "支付订单创建日期")
    @TableField("PAY_ORDER_DATE")
    private LocalDateTime payOrderDate;

    @ApiModelProperty(value = "支付金额,单位分")
    @TableField("PRICE")
    private Integer price;

    @ApiModelProperty(value = "支付状态(0未支付,1已支付,2已部分退款,3全部退款)")
    @TableField("PAY_STATUS")
    private String payStatus;

    @ApiModelProperty(value = "微信,支付宝支付的二维码地址")
    @TableField("QRCODE_PATH")
    private String qrcodePath;

    @ApiModelProperty(value = "二维码生成时间")
    @TableField("QRCODE_DATE")
    private LocalDateTime qrcodeDate;

    @ApiModelProperty(value = "币种")
    @TableField("CURRENCY_SPECIES")
    private String currencySpecies;

    @ApiModelProperty(value = "币种编码")
    @TableField("CURRENCY_SPECIES_CODE")
    private String currencySpeciesCode;

    @ApiModelProperty(value = "支付完成时间")
    @TableField("PAY_TIME")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "全部退款完成时间")
    @TableField("REFUND_END_TIME")
    private LocalDateTime refundEndTime;

    @ApiModelProperty(value = "ao_order_info_new表主键")
    @TableField("AO_ORDER_ID")
    private String aoOrderId;


}
