package com.swcares.psi.flightdelay.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.flightdelay.entity.FlightDelayInfoEntity;
import com.swcares.psi.flightdelay.enums.FlightStatusEnum;
import com.swcares.psi.flightdelay.mapper.FlightDelayInfoMapper;
import com.swcares.psi.flightdelay.service.DelayFlightService;
import com.swcares.psi.flightdelay.service.FlightDelayCommonService;
import com.swcares.psi.flightdelay.utils.FlightDelayCommonUtil;
import com.swcares.psi.flightdelay.vo.DelayFlightInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * ClassName：com.swcares.psi.flightdelay.service <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 10月27日 14:49 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class DelayFlightServiceImpl implements DelayFlightService {
    //国内航班
    private static final String DOMESTIC_FLIGHT = "D";

    //国际航班
    private static final String INTERNATIONAL_FLIGHT = "I";

    @Resource
    private FlightDelayInfoMapper flightDelayInfoMapper;

    @Resource
    private FlightDelayCommonService flightDelayCommonService;

    @Override
    public void sendDelayMessageToPax(){
        // 任务开始时间
        List<DelayFlightInfoVo> candidateDelayFlightInfoVo = flightDelayInfoMapper.getDelayFlightInfo();
        if (candidateDelayFlightInfoVo.size() <= 0) {
            log.info("没有需要处理的延误航班。");
            return;
        }
        log.info("开始发送延误航班通知短信");
        candidateDelayFlightInfoVo=flightDelayCommonService.removeFlightInBlackList(candidateDelayFlightInfoVo);

        if (candidateDelayFlightInfoVo.size() <= 0){
            log.info("移除黑名单中的航班后，没有需要处理的延误航班");
        }

        List<DelayFlightInfoVo> delayFlightsWhoseDelayTimeMoveBackward = new ArrayList<>(); //延误提前的航班列表
        List<DelayFlightInfoVo> delayFlightsShouldSendNotification = new ArrayList<>(); //需要发送延误短信的航班列表

        for (DelayFlightInfoVo delayFlightInfoVo : candidateDelayFlightInfoVo) {
            if (isDepartTimeOfFlightMoveBackward(delayFlightInfoVo)){
                delayFlightInfoVo.setFlightState(FlightStatusEnum.DELAY_ADVANCE.getKey());
                delayFlightsWhoseDelayTimeMoveBackward.add(delayFlightInfoVo);
                continue;
            }

            if (shouldSendDelayNotification(delayFlightInfoVo)){
                delayFlightsShouldSendNotification.add(delayFlightInfoVo);
            }
        }

        sendDelayTimeMoveBackwardNotification(delayFlightsWhoseDelayTimeMoveBackward);

        sendDelayNotification(delayFlightsShouldSendNotification);

        log.info("延误航班通知短信发送完成");
    }

    /* 
     * 发送普通的延误通知短信
     *      
     * @param delayFlightInfoVos  
     * @return {@link void }
     * <AUTHOR>
     * @Date 2023-06-30
     * @Since 
     */ 
    private void sendDelayNotification(List<DelayFlightInfoVo> delayFlightInfoVos){
        if (CollectionUtil.isEmpty(delayFlightInfoVos)){
            return;
        }


        List<DelayFlightInfoVo> flightsShouldSendDelayNotification = new ArrayList<>();
        flightsShouldSendDelayNotification.addAll(delayFlightInfoVos);

        List<DelayFlightInfoVo> sendACList = flightDelayCommonService
                .getConnectFlight(delayFlightInfoVos, FlightStatusEnum.DELAY.getKey());
        if (CollectionUtil.isNotEmpty(sendACList)){
            sendACList.stream().distinct().forEach(delayFlightInfoVo -> flightsShouldSendDelayNotification.add(delayFlightInfoVo));
        }

        flightsShouldSendDelayNotification.stream().forEach(ele->{
            ele.setFlightState(FlightStatusEnum.DELAY.getKey());
        });

        log.info("延误航班发送:" + JSON.toJSONString(flightsShouldSendDelayNotification) + "共:" + flightsShouldSendDelayNotification.size() + "条");
        if (flightsShouldSendDelayNotification.size() > 0) {
            flightDelayCommonService.sendFlightDelayMessage(flightsShouldSendDelayNotification, FlightStatusEnum.DELAY.getKey());
        }
    }

    /* 
     * 发送延误提前的短信
     *      
     * @param delayFlightsWhoseDelayTimeMoveBackward  
     * @return {@link void }
     * <AUTHOR>
     * @Date 2023-06-30
     * @Since 
     **/ 
    private void sendDelayTimeMoveBackwardNotification(List<DelayFlightInfoVo> delayFlightsWhoseDelayTimeMoveBackward){
        if (CollectionUtil.isEmpty(delayFlightsWhoseDelayTimeMoveBackward)){
            return;
        }

        List<DelayFlightInfoVo> flightsShouldSendDANotification = new ArrayList<>();
        flightsShouldSendDANotification.addAll(delayFlightsWhoseDelayTimeMoveBackward);
        List<DelayFlightInfoVo> sendACList = flightDelayCommonService
                .getConnectFlight(delayFlightsWhoseDelayTimeMoveBackward, FlightStatusEnum.DELAY.getKey());
        if (CollectionUtil.isNotEmpty(sendACList)){
            sendACList.stream().distinct().forEach(delayFlightInfoVo -> flightsShouldSendDANotification.add(delayFlightInfoVo));
        }

        flightsShouldSendDANotification.stream().forEach(ele->{
            ele.setFlightState(FlightStatusEnum.DELAY_ADVANCE.getKey());
        });
        log.info("延误提前，发送通知短信的航班{}", JSON.toJSONString(flightsShouldSendDANotification));
        flightDelayCommonService.sendFlightDelayMessage(flightsShouldSendDANotification, FlightStatusEnum.DELAY_ADVANCE.getKey());

    }

    /* 
     * 判断普通延误是否应该发送延误通知短信
     *      
     * @param delayFlightInfoVo  
     * @return {@link boolean }
     * <AUTHOR>
     * @Date 2023-06-30
     * @Since 
     **/ 
    private boolean shouldSendDelayNotification(DelayFlightInfoVo delayFlightInfoVo){
        if (!isFlightDelayAdjustInTime(delayFlightInfoVo)){
            return false;
        }

        List<FlightDelayInfoEntity> notificationHistory = getFlightDelayNotificationRecordHistory(delayFlightInfoVo);

        if (CollectionUtil.isEmpty(notificationHistory)){
            Date etd = DateUtils.parseStrToDate(delayFlightInfoVo.getEtd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            Date std = DateUtils.parseStrToDate(delayFlightInfoVo.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            Map<String, String> timesDifferenceMap = FlightDelayCommonUtil.getTimesDifference(etd, std);
            int delayIntervalInHours = Integer.valueOf(timesDifferenceMap.get("hours").toString());
            if (isFirstDelayIntervalLongEnough(delayIntervalInHours, delayFlightInfoVo.getFlightType())){
                return true;
            } else {
                log.info( "航班{} {} {}-{}是首次延误，航班类型是{}, 原本的起飞时间是{}，调整后的起飞时间是{}，时间间隔不够长。" ,
                        delayFlightInfoVo.getFlightDate(), delayFlightInfoVo.getFlightNo(),
                        delayFlightInfoVo.getORG(), delayFlightInfoVo.getDST(), delayFlightInfoVo.getFlightType(),
                        DateUtils.formatDate(std, DateUtils.YYYY_MM_DD_HH_MM_SS), DateUtils.formatDate(etd, DateUtils.YYYY_MM_DD_HH_MM_SS));
                return false;
            }
        } else {
            FlightDelayInfoEntity previousNotification = notificationHistory.get(0);
            if (isDelayIntervalLongEnough(delayFlightInfoVo, previousNotification)){
                return true;
            } else {
                return false;
            }
        }


    }

    private List<FlightDelayInfoEntity> getFlightDelayNotificationRecordHistory(DelayFlightInfoVo delayFlightInfoVo){
        Map<String, Object> searchCriteria = FlightDelayCommonUtil.setQueryParam(delayFlightInfoVo);
        searchCriteria.remove("etd");
        List<String> allDelayStatus = new ArrayList<>();
        allDelayStatus.add(FlightStatusEnum.DELAY.getKey());
        allDelayStatus.add(FlightStatusEnum.DELAY_ADVANCE.getKey());
        allDelayStatus.add(FlightStatusEnum.DELAY_RECOVER.getKey());
        allDelayStatus.add(FlightStatusEnum.TIMING.getKey());
        searchCriteria.put("flightState",allDelayStatus);
        List<FlightDelayInfoEntity> notificationHistory = flightDelayInfoMapper.getFlightDelaySendRecordOfMultiState(searchCriteria);
        return notificationHistory;
    }




    /*
     * 调整前的ETD（没有ETD取STD）前半个小时之外做的调整，则返回true；否则返回false。
     */
    private boolean isFlightDelayAdjustInTime(DelayFlightInfoVo delayFlightInfoVo){
        Date loadTime = DateUtils.parseStrToDate(delayFlightInfoVo.getLoadTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        //获取上次调整时间
        Date previousDepartTime = getPreviousDepartTime(delayFlightInfoVo.getFlightId());
        Map<String, String> timesDifferenceMap = FlightDelayCommonUtil.getTimesDifference(previousDepartTime, loadTime);
        int j = Integer.valueOf(timesDifferenceMap.get("minutes").toString());
        if (j >= 30) {
            return true;
        } else {
            log.info("航班{} {} {}-{}调整的时间{}距离原本的航班起飞时间{}不足30分钟，不发送延误短信。", delayFlightInfoVo.getFlightDate(),
                    delayFlightInfoVo.getFlightNo(), delayFlightInfoVo.getORG(), delayFlightInfoVo.getDST(),
                    DateUtils.formatDate(loadTime, DateUtils.YYYY_MM_DD_HH_MM_SS),
                    DateUtils.formatDate(previousDepartTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
            return false;
        }
    }

    private Date getPreviousDepartTime(String flightId) {
        Date previousDepartTime;
        List<DelayFlightInfoVo> flightDepartTimeHistories = flightDelayInfoMapper.getFocFlightEtdHistory(flightId);
        DelayFlightInfoVo historyFlight = flightDepartTimeHistories.get(1);  //航班如果发生了延误，至少应该有两条记录
        if (StringUtils.isNotBlank(historyFlight.getEtd())) {
            previousDepartTime = DateUtils.parseStrToDate(historyFlight.getEtd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        } else {
            previousDepartTime = DateUtils.parseStrToDate(historyFlight.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        }
        return previousDepartTime;
    }



    //判断指定的航班是发是延误提前的情况
    private boolean isDepartTimeOfFlightMoveBackward(DelayFlightInfoVo delayFlightInfoVo){
        //获取该航班上以前发送的延误类通知的发送记录
        Map<String, Object> searchCriteria = FlightDelayCommonUtil.setQueryParam(delayFlightInfoVo);
        searchCriteria.remove("etd");
        List<String> allDelayStatus = new ArrayList<>();
        allDelayStatus.add(FlightStatusEnum.DELAY.getKey());
        allDelayStatus.add(FlightStatusEnum.DELAY_ADVANCE.getKey());
        allDelayStatus.add(FlightStatusEnum.DELAY_RECOVER.getKey());
        searchCriteria.put("flightState",allDelayStatus);
        List<FlightDelayInfoEntity> delayNoficationHistory = flightDelayInfoMapper.getFlightDelaySendRecordOfMultiState(searchCriteria);

        if (CollectionUtil.isEmpty(delayNoficationHistory)){
            return false; //以前没有发送过延误短信，因此不是延误提前
        }

        //取出航班四要素最新延误信息的计划起飞时间与当前延误航班信息比对是否延误提前
        FlightDelayInfoEntity flightDelayInfoEntity = delayNoficationHistory.get(0);
        String originalDepTime = flightDelayInfoEntity.getEtd() == null ? flightDelayInfoEntity.getStd() : flightDelayInfoEntity.getEtd();
        Integer j = originalDepTime.compareTo(delayFlightInfoVo.getEtd());

        if (j > 0) {
            return true;  //原本的起飞时间大于新调整的ETD，是延误提前
        } else {
            return false;
        }

    }


    private boolean isDelayIntervalLongEnough(DelayFlightInfoVo delayFlightInfoVo, FlightDelayInfoEntity preAdjustFlightInfo) {
        String preAdjustFlightState = preAdjustFlightInfo.getFlightState();
        Date previousDepartTime = DateUtils.parseStrToDate(preAdjustFlightInfo.getEtd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (previousDepartTime == null){
            previousDepartTime = DateUtils.parseStrToDate(preAdjustFlightInfo.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        }

        Date currentETD = DateUtils.parseStrToDate(delayFlightInfoVo.getEtd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> timesDifferenceMap = FlightDelayCommonUtil.getTimesDifference(currentETD, previousDepartTime);

        //如果上一次发送是延误类的短信，用本次调整的预计起飞时间减去上次发短信时的预计起 飞时间，若时间差大于1.5小时，再发送航延短信；
        if ( FlightStatusEnum.DELAY.getKey().equals(preAdjustFlightState)
            || FlightStatusEnum.DELAY_RECOVER.getKey().equals(preAdjustFlightState)
                || FlightStatusEnum.DELAY_ADVANCE.getKey().equals(preAdjustFlightState)) {
            int i = Integer.valueOf(timesDifferenceMap.get("minutes").toString());
            if (i >= 90) {
                return true;
            } else {
                log.info( "航班{} {} {}-{}上一次通知的类型是{}，通知的的起飞时间是{}，调整后的起飞时间是{}，时间间隔不够长。" ,
                        delayFlightInfoVo.getFlightDate(), delayFlightInfoVo.getFlightNo(), delayFlightInfoVo.getORG(), delayFlightInfoVo.getDST(),
                        preAdjustFlightState, DateUtils.formatDate(previousDepartTime, DateUtils.YYYY_MM_DD_HH_MM_SS),
                        DateUtils.formatDate(currentETD, DateUtils.YYYY_MM_DD_HH_MM_SS));
                return false;
            }
        }


        if (FlightStatusEnum.TIMING.getKey().equals(preAdjustFlightState)
            || FlightStatusEnum.CANCEL_RECOVER.getKey().equals(preAdjustFlightState)) {
            //如果上次的短信是调时短信，那么按照第一次延误处理
            int i = Integer.valueOf(timesDifferenceMap.get("hours").toString());

            if (isFirstDelayIntervalLongEnough(i, delayFlightInfoVo.getFlightType())){
                return true;
            } else {
                log.info( "航班{} {} {}-{}航班类型是{}, 上一次通知的类型是{}，通知的的起飞时间是{}，调整后的起飞时间是{}，时间间隔不够长。" ,
                        delayFlightInfoVo.getFlightDate(), delayFlightInfoVo.getFlightNo(), delayFlightInfoVo.getORG(), delayFlightInfoVo.getDST(),
                        delayFlightInfoVo.getFlightState(), preAdjustFlightState,DateUtils.formatDate(previousDepartTime, DateUtils.YYYY_MM_DD_HH_MM_SS),
                        DateUtils.formatDate(currentETD, DateUtils.YYYY_MM_DD_HH_MM_SS));
                return false;
            }
        }

        return false;
    }

    private boolean isFirstDelayIntervalLongEnough(int intervalInHours, String flightType) {


        if (INTERNATIONAL_FLIGHT.equals(flightType)){
            //2.国际航班大于等于3小时
            if (intervalInHours >= 3) {
                return true;
            } else {
                return false;
            }
        }
        else {
            //1.判断航班类型国内大于等于2小时发送短信
            if (intervalInHours >= 2) {
                return true;
            } else {
                return false;
            }
        }
    }

}