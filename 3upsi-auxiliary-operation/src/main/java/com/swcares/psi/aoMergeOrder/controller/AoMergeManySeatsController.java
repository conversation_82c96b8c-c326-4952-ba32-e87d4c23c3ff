package com.swcares.psi.aoMergeOrder.controller;


import com.component.core.exception.ColumnAuthException;
import com.component.core.processor.FieldPermissionsProcessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.swcares.psi.ao.manySeats.dto.ManySeatsInputReportDto;
import com.swcares.psi.ao.manySeats.vo.ManySeatsInputReportVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsInputReportDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeManySeatsService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsInputReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsPageVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合并支付一人多座产品详情订单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@Api(tags = "合并支付一人多座")
@RequestMapping("/api/ao/mergeOrder/manySeats")
public class AoMergeManySeatsController {

    @Autowired
    private IAoMergeManySeatsService seatsService;

    @Autowired
    private FieldPermissionsProcessor fieldPermissionsProcessor;

    @GetMapping("/accounts/page")
    @ApiOperation(value = "一人多座结算报表分页查询")
    public RenderResult<AoMergeManySeatsPageVo> getSceneSeatsAccountsReportPage(AoMergeManySeatsAccountsReportDto dto){
        PsiPage<AoMergeManySeatsAccountsReportVo> page = seatsService.getAccountsListPage(dto);
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVo = seatsService.payTypesSummaryMoney(dto);
        AoMergeManySeatsPageVo pageVo = new AoMergeManySeatsPageVo();
        pageVo.setPage(page);
        pageVo.setPayTypesSummaryMoneyVo(payTypesSummaryMoneyVo);
        return RenderResult.success(pageVo);
    }

    @GetMapping("/accounts/report")
    @ApiOperation(value = "一人多座结算报表导出")
    public void aoSceneSeatsAccountExport(AoMergeManySeatsAccountsReportDto dto, HttpServletResponse response, HttpServletRequest request) throws Exception {
        response.setContentType("application/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("一人多座结算报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeManySeatsAccountsReportVo> list = seatsService.getAccountsList(dto);

        AoMergeManySeatsPageVo pageVo = new AoMergeManySeatsPageVo();
        PsiPage<AoMergeManySeatsAccountsReportVo> page = new PsiPage<>();
        page.setRecords(list);
        pageVo.setPage(page);
        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            pageVo = (AoMergeManySeatsPageVo)fieldPermissionsProcessor.process(serverHttpRequest, pageVo);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            throw new ColumnAuthException(e);
        }
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVos = seatsService.payTypesSummaryMoney(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), Arrays.asList("一人多座结算", "合计"), Arrays.asList(pageVo.getPage().getRecords(),payTypesSummaryMoneyVos), Arrays.asList(AoMergeManySeatsAccountsReportVo.class, PayTypesSummaryMoneyVo.class), null);

    }

    @ApiOperation(value = "一人多座录入")
    @GetMapping("manySeatsInputPage")
    public RenderResult<PsiPage<AoMergeManySeatsInputReportVo>> manySeatsInputPage(AoMergeManySeatsInputReportDto dto) {
        return RenderResult.success(seatsService.getAoMergeManySeatsInputReportListPage(dto));
    }

    @ApiOperation(value = "一人多座录入导出")
    @GetMapping("/report/inputExport")
    public void inputExport(AoMergeManySeatsInputReportDto dto, HttpServletResponse response, HttpServletRequest request) throws Exception{
        response.setContentType("application/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("一人多座国内录入报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeManySeatsInputReportVo> list = seatsService.getAoMergeManySeatsInputReportList(dto);

        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            list = (List<AoMergeManySeatsInputReportVo>)fieldPermissionsProcessor.process(serverHttpRequest, list);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            throw new ColumnAuthException(e);
        }

        ExcelUtils.writeExcel(response.getOutputStream(), list ,AoMergeManySeatsInputReportVo.class , "一人多座国内录入",
                null);
    }

}

