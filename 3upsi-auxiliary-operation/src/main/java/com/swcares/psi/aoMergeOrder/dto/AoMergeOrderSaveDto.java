package com.swcares.psi.aoMergeOrder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AoMergeOrderSaveDto {

    @ApiModelProperty("支付方式 0微信、1易宝、2支付宝、3现金，4pos机，5worldpay")
    private String payType;

    @ApiModelProperty(value = "金额")
    private String orderPrice;

    @ApiModelProperty(value = "币种(CNY....)")
    private String currencySpecies;


    @ApiModelProperty(value = "子订单集合")
    List<AoMergeSuborderInfoDto> subOrderList;

}
