package com.swcares.psi.aoMergeOrder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 销售记录同比增长情况Vo
 * <AUTHOR>
 * @date 2023/12/6 19:43
 */
@Data
public class AoMergeComparisonGrowthVo {

    @ApiModelProperty(value = "今天的销售额")
    private List<AoMergeComparisonGrowthInteriorVo> today;
    @ApiModelProperty(value = "今天销售订单数")
    private Integer todayOrderNum;

    @ApiModelProperty(value = "最近七天销售额")
    private List<AoMergeComparisonGrowthInteriorVo> sevenDay;

    @ApiModelProperty(value = "最近七天销售订单数")
    private Integer sevenDayOrderNum;

    @ApiModelProperty(value = "最近七天销售额增长比")
    private String sevenDayOrderGrowth;

    @ApiModelProperty(value = "最近三十天销售额")
    private List<AoMergeComparisonGrowthInteriorVo> thirtyDay;

    @ApiModelProperty(value = "最近三十天销售订单数")
    private Integer thirtyDayOrderNum;

    @ApiModelProperty(value = "最近三十天销售额增长比")
    private String thirtyDayOrderGrowth;

    @ApiModelProperty(value = "最近六个月销售额")
    private List<AoMergeComparisonGrowthInteriorVo> sixMonth;

    @ApiModelProperty(value = "最近六个月销售订单数")
    private Integer sixMonthOrderNum;

    @ApiModelProperty(value = "最近六个月销售额增长比")
    private String sixMonthOrderGrowth;


}
