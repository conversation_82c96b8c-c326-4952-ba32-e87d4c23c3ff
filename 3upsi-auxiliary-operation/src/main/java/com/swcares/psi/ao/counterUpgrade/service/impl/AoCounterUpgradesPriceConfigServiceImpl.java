package com.swcares.psi.ao.counterUpgrade.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.common.entity.AoCommonPriceConfig;
import com.swcares.psi.ao.common.service.AoCommonPriceConfigService;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradeConfigPriceDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradesPriceConfigDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradesPriceConfigSaveDto;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgradesPriceConfig;
import com.swcares.psi.ao.counterUpgrade.mapper.AoCounterUpgradesPriceConfigMapper;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradesPriceConfigService;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeConfigPriceH5Vo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradesPriceConfigPageVo;
import com.swcares.psi.ao.pad.improHander.ImporEntity;
import com.swcares.psi.ao.pad.improHander.OlpCostUpgradeImporListener;
import com.swcares.psi.ao.pad.vo.OlpCostUpgradesPriceConfigPageVo;
import com.swcares.psi.base.data.api.dto.SysDictDto;
import com.swcares.psi.base.data.api.entity.SysAirLineInfoEntity;
import com.swcares.psi.base.data.api.entity.SysDict;
import com.swcares.psi.base.data.service.ISysDictService;
import com.swcares.psi.base.data.service.SysAirLineInfoService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 柜台升舱价格配置操作service (外币版本)
 */
@Service
@Slf4j
public class AoCounterUpgradesPriceConfigServiceImpl extends ServiceImpl<AoCounterUpgradesPriceConfigMapper, AoCounterUpgradesPriceConfig> implements AoCounterUpgradesPriceConfigService {

    @Autowired
    private AoCommonPriceConfigService aoCommonPriceConfigService;


    @Autowired
    private SysAirLineInfoService sysAirLineInfoService;
    @Autowired
    ISysDictService dictService;



    private void currencyTypeNameSet(AoCommonPriceConfig aoCommonPriceConfig,List<SysDict> sysDictList){
        sysDictList.stream().forEach(e->{
            if(e.getDataCode().equals(aoCommonPriceConfig.getCurrencyType())){
                aoCommonPriceConfig.setCurrencyTypeName(e.getDataValue());
            }
        });
    }
    private void currencyTypeSet(AoCommonPriceConfig aoCommonPriceConfig,List<SysDict> sysDictList){
        sysDictList.stream().forEach(e->{
            if(e.getDataValue().equals(aoCommonPriceConfig.getCurrencyTypeName())){
                aoCommonPriceConfig.setCurrencyType(e.getDataCode());
            }
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateEntity(AoCounterUpgradesPriceConfigSaveDto dto) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        log.info("柜台升舱操作参数{}-{}-{}", dto.getId(),authentication.getRealName(),JSON.toJSONString(dto));
        LocalDateTime now = LocalDateTime.now();
        if(StringUtils.isEmpty(dto.getAriLineId())){
            throw new BusinessException(MessageCode.AO_ARILINE_PARAM_EXCEPTION.getCode());
        }
        if (ObjectUtils.isEmpty(dto.getPriceList())) {
            throw new BusinessException(MessageCode.AO_PRICE_PARAM_EXCEPTION.getCode());
        }
        if(StringUtils.isEmpty(dto.getId())){
            Integer count = this.lambdaQuery().eq(AoCounterUpgradesPriceConfig::getAriLineId, dto.getAriLineId())
                    .count();
            if(count>0){
                throw new BusinessException(MessageCode.DATA_EXIST.getCode());
            }
            AoCounterUpgradesPriceConfig build = AoCounterUpgradesPriceConfig.builder()
                    .ariLineId(dto.getAriLineId())
                    .createTime(now)
                    .updateDate(now)
                    .updateNo(userNo)
                    .createUser(userNo)
                    .status(dto.getStatus())
                    .build();
            this.save(build);
            dto.getPriceList().stream().forEach(x->{
                AoCommonPriceConfig aoCommonPriceConfig = AoCommonPriceConfig.builder()
                        .businessTableId(build.getId())
                        .createTime(now)
                        .createUser(userNo)
                        .currencyPrice(x.getCurrencyPrice())
                        .currencyType(x.getCurrencyType())
                        .currencyTypeName(x.getCurrencyTypeName())
                        .effectDateTime(x.getEffect()?x.getEffectDateTime():now)
                        .status(AoCommonPriceConfig.STATUS_ENABLE)
                        .build();
                aoCommonPriceConfigService.save(aoCommonPriceConfig);
            });

        }else{
            AoCounterUpgradesPriceConfig byId = this.getById(dto.getId());
            if(byId==null){
                throw new BusinessException(MessageCode.DATA_EXIST.getCode());
            }
            aoCommonPriceConfigService.lambdaUpdate()
                    .eq(AoCommonPriceConfig::getBusinessTableId,dto.getId())
                    .eq(AoCommonPriceConfig::getStatus,AoCommonPriceConfig.STATUS_ENABLE)
                    .set(AoCommonPriceConfig::getStatus,AoCommonPriceConfig.STATUS_DELETE)
                    .update();
            this.lambdaUpdate()
                    .eq(AoCounterUpgradesPriceConfig::getId,byId.getId())
                    .set(AoCounterUpgradesPriceConfig::getUpdateDate,now)
                    .set(AoCounterUpgradesPriceConfig::getUpdateNo,userNo)
                    .set(AoCounterUpgradesPriceConfig::getStatus,dto.getStatus())
                    .update();
            dto.getPriceList().stream().forEach(x->{
                AoCommonPriceConfig aoCommonPriceConfig = AoCommonPriceConfig.builder()
                        .businessTableId(dto.getId())
                        .createTime(now)
                        .createUser(userNo)
                        .currencyPrice(x.getCurrencyPrice())
                        .currencyType(x.getCurrencyType())
                        .effectDateTime(x.getEffect()?x.getEffectDateTime():now)
                        .currencyTypeName(x.getCurrencyTypeName())
                        .status(AoCommonPriceConfig.STATUS_ENABLE)
                        .build();
                aoCommonPriceConfigService.save(aoCommonPriceConfig);
            });
        }
    }

    @Override
    public PsiPage<AoCounterUpgradesPriceConfigPageVo> getPage(AoCounterUpgradesPriceConfigDto dto) {
        PsiPage<OlpCostUpgradesPriceConfigPageVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.getPage(page,dto);
    }

    @Override
    public List<AoCounterUpgradesPriceConfigPageVo> getList(AoCounterUpgradesPriceConfigDto dto) {
        return baseMapper.getList(dto);
    }





    @Override
    public List<ImporEntity> importExcel(MultipartFile file) {
        List<ImporEntity> dataList = new ArrayList<>();
        List<ImporEntity> errorDataList = new ArrayList<>();
        ImporEntity currentElement =null;
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        LocalDateTime now = LocalDateTime.now();
        if(ObjectUtils.isEmpty(file)){
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        if (!file.getOriginalFilename().endsWith(".xls") && !file.getOriginalFilename().endsWith(".xlsx")) {
            throw new BusinessException(MessageCode.FILE_TYPE_ERROR.getCode());
        }
        List<SysDict> currency_type = dictService.lambdaQuery().eq(SysDict::getDataTypeCode, "currency_type")
                .eq(SysDict::getDataStatus, "0")
                .list();
        List<String> collect = currency_type.stream().map(SysDict::getDataValue).collect(Collectors.toList());
        HashSet<String> set = new HashSet<>(collect);
        SysDictDto sysDictDto = new SysDictDto();
        sysDictDto.setDataTypeCode("currency_type");
        sysDictDto.setStatus(SysDict.DATA_STATUS_AVAILABLE);
        List<SysDict> sysDictList = dictService.getSysDictList(sysDictDto);

        try (InputStream inputStream = file.getInputStream()){
            EasyExcel.read(inputStream, ImporEntity.class, new OlpCostUpgradeImporListener(dataList)).headRowNumber(2).sheet().doRead();
        } catch (IOException e) {
            log.error("柜台升舱价格导入文教解析异常:{}",e.getMessage(),e);
            throw new BusinessException(MessageCode.EXCEL_ANALYZE_FAIL.getCode());
        }
        for (ImporEntity ele : dataList) {
            try {
                HashSet<String> foreignCurrencyType = new HashSet<>();
                currentElement = ele;
                //验证
                if (ele.getDst().equals(ele.getOrg())) {
                    ele.setRemark("航段-起始站与航段目的站重复：不可输入重复的航段-起始站与航段-目的站");
                    errorDataList.add(ele);
                    continue;
                }
                SysAirLineInfoEntity line = sysAirLineInfoService.lambdaQuery()
                        .eq(SysAirLineInfoEntity::getOrg, ele.getOrg())
                        .eq(SysAirLineInfoEntity::getDst, ele.getDst())
                        .eq(SysAirLineInfoEntity::getIsUse, "0")
                        .one();
                if (line == null) {
                    ele.setRemark("航线不存在");
                    errorDataList.add(ele);
                    continue;
                }
                if (ObjectUtils.isEmpty(ele.getEffectCost())) {
                    ele.setRemark("待生效价格不能为空");
                    errorDataList.add(ele);
                    continue;
                }
                if (ele.getEffectCost() < 0) {
                    ele.setRemark("待生效价格不能小于0");
                    errorDataList.add(ele);
                    continue;
                }
                LocalDateTime localDate = null;
                if (ObjectUtils.isNotEmpty(ele.getEffectCost())) {
                    if (ObjectUtils.isEmpty(ele.getEffectDateStr())) {
                        ele.setRemark("待生效价格时间不能为空");
                        errorDataList.add(ele);
                        continue;
                    }
                    try {
                        localDate = DateUtils.parseStringToLocalDateTime(ele.getEffectDateStr(), DateUtils.YYYY_MM_DD_HH_MM_SS);
                        if (LocalDateTime.now().isAfter(localDate)) {
                            ele.setRemark("待生效价格存在时,待生效日期不能为空且不能选择到今日之前的日期");
                            errorDataList.add(ele);
                            continue;
                        }
                    } catch (Exception ss) {
                        ele.setRemark("待生效时间格式不对,正确格式:" + DateUtils.YYYY_MM_DD_HH_MM_SS);
                        errorDataList.add(ele);
                        continue;
                    }

                }

                if (ObjectUtils.isNotEmpty(ele.getForeignCurrencyPriceEffect1())) {
                    if (ele.getForeignCurrencyPriceEffect1() < 0) {
                        ele.setRemark("待生效价格-外币不能小于0");
                        errorDataList.add(ele);
                        continue;
                    }
                    if (set.size() == 0 || !set.contains(ele.getForeignCurrencyTypeEffect1())) {
                        ele.setRemark("待生效外币币种不合法");
                        errorDataList.add(ele);
                        continue;
                    }
                    foreignCurrencyType.add(ele.getForeignCurrencyTypeEffect1());

                }
                if (ObjectUtils.isNotEmpty(ele.getForeignCurrencyPriceEffect2())) {
                    if (ele.getForeignCurrencyPriceEffect2() < 0) {
                        ele.setRemark("待生效价格-外币不能小于0");
                        errorDataList.add(ele);
                        continue;
                    }
                    if (set.size() == 0 || !set.contains(ele.getForeignCurrencyTypeEffect2())) {
                        ele.setRemark("待生效外币币种不合法");
                        errorDataList.add(ele);
                        continue;
                    }
                    if (foreignCurrencyType.contains(ele.getForeignCurrencyTypeEffect2())) {
                        ele.setRemark("待生效外币币种重复-" + ele.getForeignCurrencyTypeEffect2());
                        errorDataList.add(ele);
                        continue;
                    } else {
                        foreignCurrencyType.add(ele.getForeignCurrencyTypeEffect2());
                    }

                }
                if (ObjectUtils.isNotEmpty(ele.getForeignCurrencyPriceEffect3())) {
                    if (ele.getForeignCurrencyPriceEffect3() < 0) {
                        ele.setRemark("待生效价格-外币不能小于0");
                        errorDataList.add(ele);
                        continue;
                    }
                    if (set.size() == 0 || !set.contains(ele.getForeignCurrencyTypeEffect3())) {
                        ele.setRemark("待生效外币币种不合法");
                        errorDataList.add(ele);
                        continue;
                    }
                    if (foreignCurrencyType.contains(ele.getForeignCurrencyTypeEffect3())) {
                        ele.setRemark("待生效外币币种重复-" + ele.getForeignCurrencyTypeEffect3());
                        errorDataList.add(ele);
                        continue;
                    }

                }
                AoCounterUpgradesPriceConfig costUpgradesCash = this.lambdaQuery().eq(AoCounterUpgradesPriceConfig::getAriLineId, line.getId()).one();
                if (costUpgradesCash == null) {
                    costUpgradesCash = AoCounterUpgradesPriceConfig.builder().build();
                    costUpgradesCash.setAriLineId(line.getId());
                    costUpgradesCash.setCreateTime(now);
                    costUpgradesCash.setCreateUser(userNo);
                }
                    costUpgradesCash.setUpdateDate(now);
                    costUpgradesCash.setUpdateNo(userNo);
                    costUpgradesCash.setStatus(AoCounterUpgradesPriceConfig.STATUS_ENABLE);
                    costUpgradesCash.setRemarks(realName + "-" + userNo + "-模板导入");
                    this.saveOrUpdate(costUpgradesCash);
                    aoCommonPriceConfigService.lambdaUpdate()
                            .gt(AoCommonPriceConfig::getEffectDateTime,now)
                            .eq(AoCommonPriceConfig::getBusinessTableId,costUpgradesCash.getId())
                            .set(AoCommonPriceConfig::getStatus,AoCommonPriceConfig.STATUS_DELETE)
                            .update();
                    AoCommonPriceConfig aoCommonPriceConfig = AoCommonPriceConfig.builder()
                            .businessTableId(costUpgradesCash.getId())
                            .createTime(now)
                            .createUser(userNo)
                            .currencyPrice(ele.getEffectCost())
                            .currencyType("CNY")
                            .effectDateTime(localDate)
                            .status(AoCommonPriceConfig.STATUS_ENABLE)
                            .build();
                    currencyTypeNameSet(aoCommonPriceConfig, sysDictList);
                    aoCommonPriceConfigService.save(aoCommonPriceConfig);
                    if (ObjectUtils.isNotEmpty(ele.getForeignCurrencyPriceEffect1())) {
                        AoCommonPriceConfig foreign1 = AoCommonPriceConfig.builder()
                                .businessTableId(costUpgradesCash.getId())
                                .createTime(now)
                                .createUser(userNo)
                                .currencyPrice(ele.getForeignCurrencyPriceEffect1())
                                .currencyTypeName(ele.getForeignCurrencyTypeEffect1())
                                .effectDateTime(localDate)
                                .status(AoCommonPriceConfig.STATUS_ENABLE)
                                .build();
                        currencyTypeSet(foreign1, sysDictList);
                        aoCommonPriceConfigService.save(foreign1);
                    }
                    if (ObjectUtils.isNotEmpty(ele.getForeignCurrencyPriceEffect2())) {
                        AoCommonPriceConfig foreign2 = AoCommonPriceConfig.builder()
                                .businessTableId(costUpgradesCash.getId())
                                .createTime(now)
                                .createUser(userNo)
                                .currencyPrice(ele.getForeignCurrencyPriceEffect2())
                                .currencyTypeName(ele.getForeignCurrencyTypeEffect2())
                                .effectDateTime(localDate)
                                .status(AoCommonPriceConfig.STATUS_ENABLE)
                                .build();
                        currencyTypeSet(foreign2, sysDictList);
                        aoCommonPriceConfigService.save(foreign2);
                    }
                    if (ObjectUtils.isNotEmpty(ele.getForeignCurrencyPriceEffect3())) {
                        AoCommonPriceConfig foreign3 = AoCommonPriceConfig.builder()
                                .businessTableId(costUpgradesCash.getId())
                                .createTime(now)
                                .createUser(userNo)
                                .currencyPrice(ele.getForeignCurrencyPriceEffect3())
                                .currencyTypeName(ele.getForeignCurrencyTypeEffect3())
                                .effectDateTime(localDate)
                                .status(AoCommonPriceConfig.STATUS_ENABLE)
                                .build();
                        currencyTypeSet(foreign3, sysDictList);
                        aoCommonPriceConfigService.save(foreign3);
                    }
            } catch (Exception e) {
                currentElement.setRemark("导入价格异常");
                errorDataList.add(currentElement);
                log.error("导入价格异常>>{}", currentElement != null ? JSON.toJSONString(currentElement) : "excl读取报错", e.getMessage(), e);
            }
        }
        return errorDataList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCounterUpgrade(List<String> ids) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userName = authentication.getRealName();
        List<AoCounterUpgradesPriceConfig> list = new ArrayList<>();
        for (String id : ids) {
            AoCounterUpgradesPriceConfig aoCounterUpgradesPriceConfig = AoCounterUpgradesPriceConfig.builder()
                    .id(id)
                    .status(AoCounterUpgradesPriceConfig.STATUS_DISABLE)
                    .remarks(userName+"在"+ DateUtils.parseLocalDateTimeToString(LocalDateTime.now(),DateUtils.YYYY_MM_DD_HH_MM_SS)+"删除数据"+id)
                    .build();
            list.add(aoCounterUpgradesPriceConfig);
        }
        return this.updateBatchById(list);
    }

    @Override
    public AoCounterUpgradeConfigPriceH5Vo getPrice(AoCounterUpgradeConfigPriceDto dto) {
        SysAirLineInfoEntity one = sysAirLineInfoService.lambdaQuery()
                .eq(SysAirLineInfoEntity::getOrg, dto.getOrgPlace())
                .eq(SysAirLineInfoEntity::getDst, dto.getDstPlace())
                .eq(SysAirLineInfoEntity::getIsUse, "0")
                .one();
        if(one==null){
            return null;
        }
        AoCounterUpgradesPriceConfig one1 = this.lambdaQuery()
                .eq(AoCounterUpgradesPriceConfig::getAriLineId, one.getId())
                .eq(AoCounterUpgradesPriceConfig::getStatus,StringUtils.isEmpty(dto.getStatus())? AoCounterUpgradesPriceConfig.STATUS_ENABLE:dto.getStatus())
                .one();
        if(one1==null){
            return null;
        }
        LocalDateTime maxLocalDate=null;
        LocalDateTime now = LocalDateTime.now();
        List<AoCommonPriceConfig> list = aoCommonPriceConfigService.lambdaQuery()
                .eq(AoCommonPriceConfig::getStatus, AoCommonPriceConfig.STATUS_ENABLE)
                .le(AoCommonPriceConfig::getEffectDateTime,now )
                .eq(AoCommonPriceConfig::getBusinessTableId, one1.getId())
                .list();
        Map<String,AoCommonPriceConfig> sxData=new HashMap();
        for (AoCommonPriceConfig ele : list){
            if(maxLocalDate==null  || ele.getEffectDateTime().compareTo(maxLocalDate)>0){
                maxLocalDate=ele.getEffectDateTime();
            }
        }
        for(AoCommonPriceConfig ele:list){
            if (maxLocalDate.compareTo(ele.getEffectDateTime()) == 0) {
                if (sxData.get(ele.getCurrencyType()) == null) {
                    sxData.put(ele.getCurrencyType(), ele);
                } else {
                    AoCommonPriceConfig oele = sxData.get(ele.getCurrencyType());
                    if (oele.getEffectDateTime().isBefore(ele.getEffectDateTime())) {
                        sxData.put(ele.getCurrencyType(), ele);
                    }
                }
            }
        }
        List<AoCommonPriceConfig> dxslist = aoCommonPriceConfigService.lambdaQuery()
                .eq(AoCommonPriceConfig::getStatus, AoCommonPriceConfig.STATUS_ENABLE)
                .gt(AoCommonPriceConfig::getEffectDateTime,now )
                .eq(AoCommonPriceConfig::getBusinessTableId, one1.getId())
                .list();
        Map<String,AoCommonPriceConfig> dsxData=new HashMap();
        for(AoCommonPriceConfig ele:dxslist){
            if(sxData.get(ele.getCurrencyType())==null){
                dsxData.put(ele.getCurrencyType(),ele);
            }else{
                AoCommonPriceConfig oele = sxData.get(ele.getCurrencyType());
                if(oele.getEffectDateTime().isBefore(ele.getEffectDateTime())){
                    dsxData.put(ele.getCurrencyType(),ele);
                }
            }
        }
        list=new ArrayList<>();
        for (Map.Entry<String, AoCommonPriceConfig> entry : sxData.entrySet()) {
            list.add(entry.getValue());
        }
        for (Map.Entry<String, AoCommonPriceConfig> entry : dsxData.entrySet()) {
            list.add(entry.getValue());
        }

        if(ObjectUtils.isEmpty(list)){
            return null;
        }
        AoCounterUpgradeConfigPriceH5Vo aoCounterUpgradeConfigPriceH5Vo = new AoCounterUpgradeConfigPriceH5Vo();
        list.stream().forEach(e->{
            aoCounterUpgradeConfigPriceH5Vo.setDstPlace(one.getDst());
            aoCounterUpgradeConfigPriceH5Vo.setOrgPlace(one.getOrg());
            if("CNY".equals(e.getCurrencyType())){
                if(now.isAfter(e.getEffectDateTime())){
                    aoCounterUpgradeConfigPriceH5Vo.setCurrentPrice(e.getCurrencyPrice());
                }else{

                    aoCounterUpgradeConfigPriceH5Vo.setForeignPrice(e.getCurrencyPrice());
                }
            }else{
                if (now.isAfter(e.getEffectDateTime())) {
                    aoCounterUpgradeConfigPriceH5Vo.addForeignCurrencyPrice(e.getCurrencyType(), e.getCurrencyPrice(), e.getCurrencyTypeName(),e.getEffectDateTime());
                } else {
                    aoCounterUpgradeConfigPriceH5Vo.addForeignPrice(e.getCurrencyType(), e.getCurrencyPrice(), e.getCurrencyTypeName(),e.getEffectDateTime());
                }
            }

        });
        return aoCounterUpgradeConfigPriceH5Vo;
    }
}
