package com.swcares.psi.ao.common.service;

import com.alibaba.fastjson.JSONObject;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.entity.AoTempCashReturnEntity;
import com.swcares.psi.ao.common.vo.PaxInfoVo;
import com.swcares.psi.base.data.api.vo.AoDataPermissionsVo;
import com.swcares.psi.common.worldpay.vo.WorldpayAuthorisedVo;
import com.swcares.psi.common.worldpay.vo.WorldpayCancelledVo;
import com.swcares.psi.common.worldpay.vo.WorldpayRefundedVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/18 9:24
 */
public interface CommonService {
    /**
     * 通过客票信息（前端扫码扫出来的信息）查询旅客详细信息
     * @param tktNo
     * @return
     */
    List<PaxInfoVo> getPaxInfoByScan(String tktNo, String flitghtDate, String flitghtNo, String boardingNumber, String org);

    /**
     * 修改订单收款方式
     * @param orderNo
     * @param payType
     */
    void updateOrderPayType( String orderNo, String payType);

    /**
     * 取消订单
     * @param orderNo
     */
    void cancelOrder( String orderNo);

    /**
     * 发起退款
     * @param orderNo
     */
    void startRefund( String orderNo);

    /**
     * 退款
     * @param orderNo
     */
    String refund( String orderNo,String refundSause,String refundExplain);

    /**
     * 取消退款
     * @param orderNo
     */
    void  cancelRefund( String orderNo);

    /**
     * 现金收款
     * @param orderNo
     * @param posOrderNo
     */
    String cash( String orderNo,String posOrderNo);

    /**
     * 获取订单支付二维码
     * @param orderNo
     * @return  返回二维码存放的ftp服务器地址
     */
    String getShowQrCode( String orderNo);

    /**
     * 保存值班主任发起的缴纳转账订单
     *
     * @return  返回二维码存放地址
     */
    Map<String,String> getReturnQrCode(String orderType, String chargeType, String orderNo);


    /**
     * worldPay支付回调
     * @param request
     * @return  null值表示异常  true 表示可能是合并订单的支付  false表示正常处理
     */
    Boolean worldpayNotifyUrl(WorldpayAuthorisedVo request);
    /**
     * worldPay取消回调
     * @param request
     * @return  null值表示异常  true 表示可能是合并订单的支付  false表示正常处理
     */
    Boolean worldpayCancelledNotify(WorldpayCancelledVo request);

    /**
     * worldPay退款回调
     * @param vo
     * @return  null值表示异常  true 表示可能是合并订单的支付  false表示正常处理
     */
    Boolean worldpayRefundNotifyUrl(WorldpayRefundedVo vo);
    /**
     * 支付宝扫码支付回调
     * @param request
     */
    String alipayNotifyUrl(HttpServletRequest request);
    /**
     * 值班主任支付宝扫码缴纳汇款回调
     * @param request
     */
    void alipayTurnNotifyUrl(HttpServletRequest request);

    /**
     * 微信扫码支付回调
     * @param streamToMap
     */
    String wxpayNotifyUrl(Map<String, String> streamToMap);
    /**
     * 微信扫码支付回调
     * @param map
     */
    void wxpayH5NotifyUrl( Map<String, String> map);
    /**
     * 微信支付退款通知回调
     * @param map
     */
    String wxpayRefundNotifyUrl(Map<String, String> map);

    /**
     * 值班主任微信扫码缴纳汇款回调
     * @param map
     */
    void wxpayTurnNotifyUrl( Map<String, String> map);

    /**
     * 值班主任缴纳支付结果查询
     * @param orderNo  订单号
     * @param chargeType 支付类型
     * @return
     */
    Boolean getReturnStatus(String orderNo,String chargeType);


    /**
     * C端支付
     * @param orderNo
     * @return
     */
    Map<String, String>  payByC(String orderNo );
    /**
     * 值机主任待处理现金订单数量
     * @param orderStatus 未处理订单状态
     * @return
     */
    Integer unDisposeOrder(String orderStatus,String chargeType);

    /**
     * 易宝支付回调
     * @param
     * @return
     */
    void yeePayNotify(JSONObject jsonObject);


    /**
     * 订单状态查询定时任务 逻辑实现
     * @param list
     */
    void vaidatePayResults(List<AoOrderInfoEntity> list);
    /**
     * 缴纳状态查询定时任务 逻辑实现
     * @param list
     */
    void getCashReceiveTransfer(List<AoTempCashReturnEntity> list);
    /**
     * 退款状态查询定时任务 逻辑实现
     * @param list
     */
    void vaidateRefundResults(List<AoOrderInfoEntity> list);


    /**
     * 设置数据权限
     * @param roleId 授权的角色ID
     * @param dataRoleId  数据角色ID
     * @param permissionType  业务类型
     */
    void saveDataPermissions(String roleId,String dataRoleId,String permissionType);

    /**
     * 查询角色数据权限
     * @param roleId 角色ID
     */
    List<AoDataPermissionsVo> getPermission(String roleId);


}
