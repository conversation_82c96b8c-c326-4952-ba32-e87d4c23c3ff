<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeRefundOrderInfoMapper">

    <select id="getRefundOrderAll" resultType="com.swcares.psi.aoMergeOrder.vo.RefundOrderTaskVo">
        select
            foi.id as refundOrderId,
            soi.id as subOrderId,
            moi.id as mergeOrderId,
            foi.REFUND_NO,
            moi.ORDER_NO as mergeOrderNo,
            mpi.BANK_ORDER_NO as bankOrderNo,
            soi.SUBORDER_ORDER_NO,
            mpi.PAY_TYPE,
            mpi.PAY_ORDER_NO
        from  ao_merge_refund_order_info foi
        left join  ao_merge_suborder_info  soi on foi.AO_MERGE_SUBORDER_INFO_ID=soi.id
        left join  ao_merge_order_info moi on soi.AO_MERGE_ORDER_INFO_ID=moi.id
        left join ao_merge_pay_order_info mpi on mpi.id =moi.ORDER_PAY_ID
        where foi.REFUND_STATUS in ('1','2') and foi.REFUND_REQUEST_SEND='1' and foi.status='1' and mpi.PAY_TYPE in ('0','1','2')
    </select>


    <select id="getRefundSendOrder" resultType="com.swcares.psi.aoMergeOrder.vo.RefundSendTaskVo">
        select
        foi.id as refundOrderId,
        soi.id as subOrderId,
        moi.id as mergeOrderId,
        foi.REFUND_NO,
        moi.ORDER_NO as mergeOrderNo,
        soi.SUBORDER_ORDER_NO,
        mpi.PAY_TYPE,
        mpi.BANK_ORDER_NO as bankOrderNo,
        foi.REFUND_PRICE,
        foi.REFUND_SEND_TIME as sendTime,
        mpi.PRICE,
        mpi.CURRENCY_SPECIES
        from  ao_merge_refund_order_info foi
        inner join  ao_merge_suborder_info  soi on foi.AO_MERGE_SUBORDER_INFO_ID=soi.id
        left join  ao_merge_order_info moi on soi.AO_MERGE_ORDER_INFO_ID=moi.id
        left join ao_merge_pay_order_info mpi on mpi.id =moi.ORDER_PAY_ID
        where foi.REFUND_STATUS ='1'  and foi.REFUND_REQUEST_SEND='0' and foi.status='1' and mpi.PAY_TYPE in ('0','1','2','5')
    </select>


    <select id="h5RefundOrderList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeH5RefundOrderListVo">
        select
        amfoi.id as  refundOrderId,
        amoi.ORDER_NO as  orderNo,
        amfoi.REFUND_NO as refundOrderNo,
        amfoi.REFUND_STATUS as refundStatus,
        DATE_FORMAT( amfoi. CREATE_DATE, '%Y-%m-%d %H:%i:%s' ) as createDate,
        DATE_FORMAT( amsi. STD, '%Y-%m-%d %H:%i:%s' ) as std,
        amsi.FLIGHT_NO as flightNo,
        amsi.PAX_NAME as paxName,
        ampoi.CURRENCY_SPECIES as currencySpecies,
        ampoi.CURRENCY_SPECIES_NAME as currencySpeciesName,
        amsi.ORDER_TYPE as orderType,
        amsi.SEGMENT as segment,
        amsi.PRACTICAL_ORDER_PRICE as refundPrice,
        if(amsi.ORDER_TYPE='5',
        (select UPGRADE_STATUS from ao_merge_counter_upgrade cu where cu.AO_MERGE_SUBORDER_INFO_ID=amfoi.AO_MERGE_SUBORDER_INFO_ID),
        null
        ) as upgradeStatus


        from ao_merge_refund_order_info amfoi
        inner join ao_merge_suborder_info amsi on amsi.id=amfoi.AO_MERGE_SUBORDER_INFO_ID
        inner join ao_merge_order_info amoi on amoi.id=amsi.AO_MERGE_ORDER_INFO_ID
        inner join ao_merge_pay_order_info ampoi on ampoi.id=amfoi.AO_MERGE_ORDER_PAY_ID
        <where>
            amfoi.status='1'
            <if test="dto.refundStatus != null and dto.refundStatus != ''">
                and find_in_set(amfoi.REFUND_STATUS, #{dto.refundStatus})
            </if>



            <if test="dto.param != null and dto.param != ''">
                and  ( amsi.ORDER_TYPE_NAME like concat('%',#{dto.param},'%')
                or amsi.SUBORDER_ORDER_NO like concat('%',#{dto.param},'%')
                or amsi.PAX_NAME like concat('%',#{dto.param},'%')
                )
            </if>

            <if test=" userNo != null and userNo != '' ">
                AND ( amfoi.CREATE_NO=#{userNo}
                <!-- # 指定用户只能查看的数据-->
                <if test=" userId != null and userId != ''">
                    OR amfoi.CREATE_NO in (
                    select DISTINCT(emy.TUNO) from data_permissions dp
                    LEFT JOIN user_role ur on dp.ROLE_ID=ur.ROLE_ID
                    LEFT JOIN user_role ur2 on ur2.ROLE_ID=dp.PERMISSIONS_ROLE_ID
                    LEFT JOIN employee emy on ur2.EMPLOYEE_ID=emy.id
                    where ur.EMPLOYEE_ID=#{userId}
                    )
                </if>
                )
            </if>


        </where>
        order by amfoi.CREATE_DATE desc,amfoi.APPROVE_TIME desc
    </select>
</mapper>
