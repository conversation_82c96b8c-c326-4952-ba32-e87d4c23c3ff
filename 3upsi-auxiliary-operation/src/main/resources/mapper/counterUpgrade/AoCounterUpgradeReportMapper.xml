<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.counterUpgrade.mapper.AoCounterUpgradeReportMapper">

    <sql id="reportSelect">
        SELECT (<include refid="com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper.getPaxHedCategories"></include>) as paxTypeDesc,
        pax.FQTV_NUMBER as ffpNumber,
        aoi.PAX_NAME,
        aoi.COMPLETE_DEPARTMENT_INFO,
        aoi.ORDER_NO,
        aoi.BANK_ORDER_NO,
        aoi.FLIGHT_NO,
        date_format(aoi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        aoi.ORG,
        aoi.DST,
        aoi.TKT_NO,
        acu.ORIGINAL_CABIN,
        acu.NEW_CABIN,
        acu.FLIGHT_TYPE,
        CASE
        WHEN aoi.CHARGE_TYPE = '0' THEN '微信'
        WHEN aoi.CHARGE_TYPE = '1' THEN '银行卡'
        WHEN aoi.CHARGE_TYPE = '2' THEN '支付宝'
        WHEN aoi.CHARGE_TYPE = '3' THEN '现金'
        WHEN aoi.CHARGE_TYPE = '4' THEN 'pos机'
        when aoi.CHARGE_TYPE = '5' then 'worldPay'
        ELSE aoi.CHARGE_TYPE
        END AS chargeType,
        aoi.CURRENCY_SPECIES,
        case aoi.CURRENCY_SPECIES
        when 'CNY' THEN CONCAT(aoi.CURRENCY_SPECIES,'(人民币)')
        ELSE
        (SELECT CONCAT(aoi.CURRENCY_SPECIES,'(', DATA_VALUE,')') FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=aoi.CURRENCY_SPECIES limit 1)
        end AS currencySpeciesName,

        CASE apr.PAY_TYPE
            when '3' then
                concat('-',aoi.ORDER_PRICE)
            else aoi.ORDER_PRICE
        end as ORDER_PRICE,
        aoi.SEGMENT_TYPE,
        CASE apr.PAY_TYPE
            WHEN  '0' THEN
                '未支付'
            WHEN  '1' THEN
                '支付完成'
            WHEN  '2' THEN
                '支付失败'
            WHEN  '3' THEN
                '退款成功'
            WHEN  '4' THEN
                '退款失败'
        END  as payStatus,
        CASE apr.PAY_TYPE
            WHEN  '0' THEN
                '未支付'
            WHEN  '1' THEN
                '支付完成'
            WHEN  '2' THEN
                '支付失败'
            WHEN  '3' THEN
                '退款成功'
            WHEN  '4' THEN
                '退款失败'
        END  as orderStatus,
        aoi.CELL_CHANNEL,
        aoi.USER_NO,
        aoi.USER_NAME,
        aoi.DEPARTMENT,
        aoi.DEPARTMENT_ID,
        aoi.TURN_NUMBER,
        CASE
        WHEN aoi.TURN_STATUS = '0' THEN '成功'
        WHEN aoi.TURN_STATUS = '1' THEN '失败'
        ELSE aoi.TURN_STATUS
        END AS turnStatus,
        aoi.AREA_COMPANY,
        date_format(apr.OPERATION_DATE,'%Y-%m-%d %H:%i:%s')as OPERATION_DATE,
        acu.EMD_NO,
        CASE acu.UPGRADE_STATUS
        WHEN '0' THEN '初始化'
        WHEN '1' THEN '升舱中'
        WHEN '2' THEN '升舱成功'
        WHEN '3' THEN '升舱失败'
        END as upgradeStatus,
        acu.ACCOUNT_ENTRY_NO,
        acu.ERROR_INFO AS errorInfo
        FROM
        ao_counter_upgrade acu
        LEFT JOIN
        ao_order_info aoi
        ON
        acu.ORDER_NO = aoi.ORDER_NO
        LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
        left join flt_passenger_real_info pax on pax.id=aoi.pax_id
    </sql>
    <sql id="reportWhere">
        <where>
            apr.PAY_TYPE  in ('1','3','4')

            <if test="aoCounterUpgradeAccountsReportDto.departmentName != null and aoCounterUpgradeAccountsReportDto.departmentName !='' ">
                AND aoi.DEPARTMENT like concat('%',#{aoCounterUpgradeAccountsReportDto.departmentName},'%')
            </if>

            <if test="aoCounterUpgradeAccountsReportDto.areaCompany != null and aoCounterUpgradeAccountsReportDto.areaCompany !='' ">
                AND aoi.AREA_COMPANY like concat('%',#{aoCounterUpgradeAccountsReportDto.areaCompany},'%')
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.flightStartDate != null and aoCounterUpgradeAccountsReportDto.flightStartDate !='' ">
                AND aoi.FLIGHT_DATE &gt;= date_format(#{aoCounterUpgradeAccountsReportDto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.flightEndDate != null and aoCounterUpgradeAccountsReportDto.flightEndDate !='' ">
                AND aoi.FLIGHT_DATE &lt;= date_format(#{aoCounterUpgradeAccountsReportDto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.orderStartDate != null and aoCounterUpgradeAccountsReportDto.orderStartDate !='' ">
                AND  aoi.ORDER_DATE &gt;= date_format(#{aoCounterUpgradeAccountsReportDto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.orderEndDate != null and aoCounterUpgradeAccountsReportDto.orderEndDate !='' ">
                AND  aoi.ORDER_DATE &lt;= date_format(#{aoCounterUpgradeAccountsReportDto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="aoCounterUpgradeAccountsReportDto.payBeginDate != null and aoCounterUpgradeAccountsReportDto.payBeginDate !=''">
                and apr.OPERATION_DATE &gt;= date_format(#{aoCounterUpgradeAccountsReportDto.payBeginDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.payEndDate != null and aoCounterUpgradeAccountsReportDto.payEndDate !='' ">
                and apr.OPERATION_DATE &lt;= date_format(#{aoCounterUpgradeAccountsReportDto.payEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="aoCounterUpgradeAccountsReportDto.payType != null and aoCounterUpgradeAccountsReportDto.payType !='' ">
                AND FIND_IN_SET(apr.PAY_TYPE,#{aoCounterUpgradeAccountsReportDto.payType}) >0
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.flightNo != null and aoCounterUpgradeAccountsReportDto.flightNo !='' ">
                AND aoi.FLIGHT_NO = #{aoCounterUpgradeAccountsReportDto.flightNo}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.orderNo != null and aoCounterUpgradeAccountsReportDto.orderNo !='' ">
                AND aoi.ORDER_NO = #{aoCounterUpgradeAccountsReportDto.orderNo}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.bankOrderNo != null and aoCounterUpgradeAccountsReportDto.bankOrderNo !='' ">
                AND aoi.BANK_ORDER_NO = #{aoCounterUpgradeAccountsReportDto.bankOrderNo}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.org != null and aoCounterUpgradeAccountsReportDto.org !='' ">
                AND aoi.ORG = #{aoCounterUpgradeAccountsReportDto.org}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.dst != null and aoCounterUpgradeAccountsReportDto.dst !='' ">
                AND aoi.DST = #{aoCounterUpgradeAccountsReportDto.dst}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.tktNo != null and aoCounterUpgradeAccountsReportDto.tktNo !='' ">
                AND aoi.TKT_NO = #{aoCounterUpgradeAccountsReportDto.tktNo}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.segmentType != null and aoCounterUpgradeAccountsReportDto.segmentType !='' ">
                AND aoi.SEGMENT_TYPE = #{aoCounterUpgradeAccountsReportDto.segmentType}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.chargeType != null and aoCounterUpgradeAccountsReportDto.chargeType !='' ">
                AND FIND_IN_SET(aoi.CHARGE_TYPE,#{aoCounterUpgradeAccountsReportDto.chargeType}) >0
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.payStatus != null and aoCounterUpgradeAccountsReportDto.payStatus !='' ">
                AND FIND_IN_SET(aoi.PAY_STATUS,#{aoCounterUpgradeAccountsReportDto.payStatus}) >0
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.cellChannel != null and aoCounterUpgradeAccountsReportDto.cellChannel !='' ">
                AND aoi.CELL_CHANNEL = #{aoCounterUpgradeAccountsReportDto.cellChannel}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.turnNumber != null and aoCounterUpgradeAccountsReportDto.turnNumber !='' ">
                AND aoi.TURN_NUMBER = #{aoCounterUpgradeAccountsReportDto.turnNumber}
            </if>
            <if test="aoCounterUpgradeAccountsReportDto.financeOrderNo != null and aoCounterUpgradeAccountsReportDto.financeOrderNo !='' ">
                and aoi.FINANCE_ORDER_NO  like concat('%',#{aoCounterUpgradeAccountsReportDto.financeOrderNo},'%')
            </if>
        </where>
    </sql>

    <select id="getCounterUpgradeAccountsReportPage" resultType="com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeAccountsReportVo">
        <include refid="reportSelect"></include>
        <include refid="reportWhere"></include>
        ORDER BY apr.OPERATION_DATE DESC
    </select>

    <select id="getCounterUpgradeAccountsReportList" resultType="com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeAccountsReportVo">
        <include refid="reportSelect"></include>
        <include refid="reportWhere"></include>
        ORDER BY apr.OPERATION_DATE DESC
    </select>

    <sql id="tempReportSql">
        SELECT
        acu.ORDER_NO,
        LEFT(aoi.FINANCE_ORDER_NO,3) AS paper,
        RIGHT(aoi.FINANCE_ORDER_NO,7) AS ticketType,
        date_format(aoi.FLIGHT_DATE,'%Y%m%d') AS tktIssueDate,
        LEFT(aoi.TKT_NO,3) AS connectedCompany,
        SUBSTRING(aoi.TKT_NO,4,3) AS connectedType,
        RIGHT(aoi.TKT_NO,7) AS  connectedTktNo,
        aoi.ORG,
        aoi.DST,
        RIGHT(aoi.FLIGHT_NO,4) AS flightNo,
        date_format(aoi.FLIGHT_DATE,'%Y%m%d') AS flightDate,
        aoi.ORDER_PRICE AS denomination
        FROM
        ao_counter_upgrade acu
        LEFT JOIN
        ao_order_info aoi
        ON
        acu.ORDER_NO = aoi.ORDER_NO
        <where>
            aoi.ORDER_STATUS ='2'
            AND aoi.SEGMENT_TYPE = '国内'
            <if test="aoCounterUpgradeInputTemplateReportDto.flightStartDate != null and aoCounterUpgradeInputTemplateReportDto.flightStartDate !='' ">
                AND aoi.FLIGHT_DATE &gt;= date_format(#{aoCounterUpgradeInputTemplateReportDto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="aoCounterUpgradeInputTemplateReportDto.flightEndDate != null and aoCounterUpgradeInputTemplateReportDto.flightEndDate !='' ">
                AND aoi.FLIGHT_DATE &lt;= date_format(#{aoCounterUpgradeInputTemplateReportDto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="aoCounterUpgradeInputTemplateReportDto.flightNo != null and aoCounterUpgradeInputTemplateReportDto.flightNo !='' ">
                AND aoi.FLIGHT_NO = #{aoCounterUpgradeInputTemplateReportDto.flightNo}
            </if>
            <if test="aoCounterUpgradeInputTemplateReportDto.org != null and aoCounterUpgradeInputTemplateReportDto.org !='' ">
                AND aoi.ORG = #{aoCounterUpgradeInputTemplateReportDto.org}
            </if>
            <if test="aoCounterUpgradeInputTemplateReportDto.dst != null and aoCounterUpgradeInputTemplateReportDto.dst !='' ">
                AND aoi.DST = #{aoCounterUpgradeInputTemplateReportDto.dst}
            </if>
            <if test="aoCounterUpgradeInputTemplateReportDto.connectedCompany != null and aoCounterUpgradeInputTemplateReportDto.connectedCompany != ''">
                AND LEFT(aoi.TKT_NO,3) =  #{aoCounterUpgradeInputTemplateReportDto.connectedCompany}
            </if>
            <if test="aoCounterUpgradeInputTemplateReportDto.ticketType != null and aoCounterUpgradeInputTemplateReportDto.ticketType !='' ">
                AND RIGHT(aoi.TKT_NO,7) = #{aoCounterUpgradeInputTemplateReportDto.ticketType}
            </if>

        </where>
    </sql>

    <sql id="payTypesSummaryMoneySql">
        select t.* from (
            SELECT
            sum(CASE WHEN rs.chargeType = '0' THEN rs.ORDER_PRICE ELSE 0 end ) AS wxMoney,
            sum(CASE WHEN rs.chargeType = '1' THEN rs.ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
            sum(CASE WHEN rs.chargeType = '2'  THEN rs.ORDER_PRICE ELSE 0 end ) AS alipayMoney,
            sum(CASE WHEN (rs.chargeType = '3' or rs.chargeType = '4') THEN rs.ORDER_PRICE ELSE 0 end) AS cashMoney,
            sum(CASE WHEN rs.chargeType = '5' THEN rs.ORDER_PRICE ELSE 0 end) AS worldPay,
            sum(rs.ORDER_PRICE  ) AS allMoney,
            case  rs.CURRENCY_SPECIES
            when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
            else   concat(rs.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
            end as currencySpecies
            from(
            SELECT
            aoi.CURRENCY_SPECIES,
            aoi.CHARGE_TYPE AS chargeType,
            CASE apr.PAY_TYPE
            when '3' then
            concat('-',aoi.ORDER_PRICE)
            else aoi.ORDER_PRICE
            end as ORDER_PRICE,
            aoi.ORDER_PRICE AS totalPrice
            FROM
            ao_counter_upgrade acu
            LEFT JOIN
            ao_order_info aoi
            ON
            acu.ORDER_NO = aoi.ORDER_NO
            LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
            <include refid="reportWhere"></include>
            ) rs
            group by rs.CURRENCY_SPECIES
        ) t where t.allMoney > 0
    </sql>

    <select id="getCounterUpgradeInputTemplatePage" resultType="com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeInputTemplateReportVo">
      <include refid="tempReportSql"></include>
    </select>

    <select id="getCounterUpgradeInputTemplateList" resultType="com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeInputTemplateReportVo">
        <include refid="tempReportSql"></include>
    </select>
    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo">
        <include refid="payTypesSummaryMoneySql"></include>
    </select>
</mapper>