package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.dto.AoMergeApplyPageDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCounterUpgrade;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeUpgradeStatusUpDateVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 合并订单柜台升舱产品详情订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeCounterUpgradeService extends IService<AoMergeCounterUpgrade> {
    /**
     * 升舱
     *
     * @param upgradeOrder
     * @return
     */
    void upgradeUser(AoMergeCounterUpgrade upgradeOrder);


    List<AoMergeCounterUpgradeAccountsReportVo> getAccountsList(AoMergeCounterUpgradeAccountsReportDto dto);

    PsiPage<AoMergeCounterUpgradeAccountsReportVo> getAccountsListPage(AoMergeCounterUpgradeAccountsReportDto dto);

    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeCounterUpgradeAccountsReportDto dto);

    void upgradeStatusUpDateApply(String subOrderId,String applyUpDateStatusReason);
    void upgradeStatusUpDateRevocation(List<String> subOrderIdList);
    void apply(List<String> idList);
    PsiPage<AoMergeUpgradeStatusUpDateVo> applyPage(AoMergeApplyPageDto dto);
    void automaticApprove();
}
