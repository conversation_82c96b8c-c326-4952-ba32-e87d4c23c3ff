package com.swcares.psi.admin.mapper;

import com.swcares.psi.admin.api.vo.DropdownBoxDepartmentView;
import com.swcares.psi.admin.api.vo.DepartMentVO;
import com.swcares.psi.admin.api.vo.DepartmentTreeVO;
import com.swcares.psi.admin.api.entity.Role;
import com.swcares.psi.base.base.BaseDAO;
import com.swcares.psi.base.util.DateUtils;
import com.swcares.psi.common.utils.query.QueryResults;
import com.swcares.psi.admin.api.entity.Department;
import com.swcares.psi.admin.api.vo.UsersInfoView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.eupsi.department.dao.DpartmentBaseDao <br>
 * Description：部门dao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年4月13日 下午8:05:12 <br>
 * @version v1.0 <br>
 */
@Repository
public class DepartmentBaseDao {
    @Resource
    private BaseDAO baseDao;

    /**
     * Title：getDpartmentTreeByToId <br>
     * Description：通过部门id获取上下级的所有部门<br>
     * author：王磊 <br>
     * date：2020年4月13日 下午8:14:38 <br>
     * @param deptId
     * @return <br>
     */
    @SuppressWarnings("unchecked")
    public List<DepartmentTreeVO> getDpartmentTreeByToId(String toId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("select t.id,t.toid AS 'value',t.tofname AS title,t.toid AS 'key',t.topid AS parentKey from ");
        sql.append("department t,(select getChildDepts(:toId"+") cds) x ");
        sql.append("where FIND_IN_SET(TOID,cds)>0 AND t.tobgnf = 0 ");
        paramsMap.put("toId", toId);
        return (List<DepartmentTreeVO>) baseDao.findBySQL_comm(sql.toString(), paramsMap,
                DepartmentTreeVO.class);
    }

    public List<DepartmentTreeVO> getDpartmentTreeByToIdWithDeleted(String toId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("select t.id,t.toid AS 'value',t.tofname AS title,t.toid AS 'key',t.topid AS parentKey,t.dep_locate AS locate from ");
        sql.append("department t ");
        sql.append("where FIND_IN_SET(TOID,getChildDepts(:toId))");
        paramsMap.put("toId", toId);
        return (List<DepartmentTreeVO>) baseDao.findBySQL_comm(sql.toString(), paramsMap,
                DepartmentTreeVO.class);
    }

    /**
     *
     * Title：getDpartmentChildById <br>
     * Description：通过ID获取子部门 <br>
     * author：王磊 <br>
     * date：2020年4月14日 下午3:50:36 <br>
     * @param deptId
     * @return departAndUserVO<br>
     */
    @SuppressWarnings("unchecked")
    public List<Department> getDpartmentChildById(String deptId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("select * from department t where t.topid =:deptId ");
        paramsMap.put("deptId", deptId);
        return (List<Department>) baseDao.findBySQL_comm(sql.toString(), paramsMap,
                Department.class);
    }
    /**
     * Title：getDepartmentChildInfo <br>
     * Description： 根据部门id获取下面子部门<br>
     * author：王建文 <br>
     * date：2020-4-15 10:44 <br>
     * @param
     * @return
     */
    public QueryResults getDepartmentChildInfo(String deptId, int current, int pageSize, String toId, String toFname){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT t.id as id, ");
        sql.append(" t.TOID as toId,t.TOFNAME AS toFname, ");
        sql.append(" T.TOSNAME AS toSname,T.TOORDER AS toOrder, ");
        sql.append(" T.TOPID AS toPid,T.TOTYPE AS toType, ");
        sql.append(" T.UPDATE_TIME AS updateTime,T.TOBGNF AS toBgnF, ");
        sql.append(" (select count(DISTINCT d.id) from department d where FIND_IN_SET(d.TOPID,t.TOID)>0)AS childDeptCount,");
        sql.append(" (select count(DISTINCT e.id) from employee e where FIND_IN_SET(t.TOID,e.TOID)>0 ) AS personCount ");
        sql.append(" from department t where t.topid  ");
        sql.append(" IN (SELECT TOID from DEPARTMENT where id=:deptId ) ");
        if (StringUtils.isNotBlank(toId)) {
            paramsMap.put("toId", "%" + toId + "%");
            sql.append(" AND t.TOID LIKE:toId  ");
        }
        if (StringUtils.isNotBlank(toFname)) {
            paramsMap.put("toFname", "%" + toFname + "%");
            sql.append(" AND t.TOFNAME LIKE:toFname ");
        }
        sql.append(" ORDER BY toId+0");
        paramsMap.put("deptId", deptId);
        return baseDao.findBySQLPage_comm(
                sql.toString(),
                current,
                pageSize,
                paramsMap,
                DepartMentVO.class);
    }
    /**
     * Title：updateDepartStatus <br>
     * Description： 部门激活禁用<br>
     * author：王建文 <br>
     * date：2020-4-15 11:02 <br>
     * @param
     * @return
     */
    public void updateDepartStatus(String id,int status){
        StringBuffer sql = new StringBuffer();
        String updateTime = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS);
        sql.append(" UPDATE DEPARTMENT SET ");
        sql.append(" TOBGNF=?, ");
        sql.append(" UPDATE_TIME=?");
        sql.append(" where ID=? ");
        baseDao.batchUpdate(sql.toString(), status, updateTime, id);
    }

    /**
     * Title：findDepartmentName <br>
     * Description：查询部门的 <br>
     * author：于琦海 <br>
     * date：2020/4/20 10:31 <br>
     *
     * @return List<DepartmentView>
     * @param: DepartmentView
     */
    public List<DropdownBoxDepartmentView> findDepartmentName(List<String> departmentById) {
        List<Object> params = new LinkedList<>();
        StringBuilder sql = new StringBuilder("SELECT DISTINCT D.TOID AS ID ,D.TOFNAME AS NAME FROM DEPARTMENT D ");
        sql.append(" WHERE D.TOPID IN ( ");
        for (String id : departmentById) {
            if (id != null) {
                sql.append(" ").append("?,");
                params.add(id);
            }
        }
        sql.delete(sql.length() - 1, sql.length());
        sql.append(" ").append(")").append(" AND D.TOBGNF = 0");
        return (List<DropdownBoxDepartmentView>) baseDao.findBySQL_comm(sql.toString(),
                DropdownBoxDepartmentView.class, params
        );
    }

    /**
     * Title：findUserByDepartmentId() <br>
     * Description：通过toId查询用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/20 15:56 <br>
     *
     * @param sql        String
     * @param parameters Map<String, Object>
     * @return List<UserInfoView>
     */
    public List<UsersInfoView> findUserByDepartmentId(String sql, Map<String, Object> parameters) {
        return (List<UsersInfoView>) baseDao.findBySQL_comm(sql, parameters, UsersInfoView.class);
    }

    /**
     * Title：findRoleByToId <br>
     * Description：根据部门ID查询role信息，且role必须是启用的 <br>
     * author：于琦海 <br>
     * date：2020/5/6 13:23 <br>
     * @param toId String
     * @return Integer
     */
    public Integer findRoleByToId(String toId) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ROLE WHERE DEPARTMENT_ID =:toId AND STATUS = 1 ");
        Map<String, Object> parameters = new HashMap<>(1);
        parameters.put("toId", toId);
        List<Role> roles = (List<Role>) baseDao.findBySQL_comm(sql.toString(), parameters, Role.class);
        Integer result = 0;
        if (Objects.nonNull(roles) && roles.size() > 0) {
            result = roles.size();
        }
        return result;
    }

    /**
     * Title：findDepartmentInfo <br>
     * Description：通过roleID查询出部门信息 <br>
     * author：于琦海 <br>
     * date：2020/5/6 15:50 <br>
     * @param roleId String
     * @return List<DropdownBoxDepartmentView>
     */
    public List<DropdownBoxDepartmentView> findDepartmentInfo(String roleId) {
        StringBuilder sql = new StringBuilder("SELECT D.TOID AS ID ,D.TOFNAME AS NAME FROM DEPARTMENT D LEFT JOIN ROLE R " +
                "ON R.DEPARTMENT_ID = D.TOID where R.ID=:roleId");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("roleId", roleId);
        return (List<DropdownBoxDepartmentView>) baseDao.findBySQL_comm(sql.toString(), parameters, DropdownBoxDepartmentView.class);
    }

}
