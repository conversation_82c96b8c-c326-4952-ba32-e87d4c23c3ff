package com.swcares.psi.aoMergeOrder.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 逾重结算报表筛选条件Dto
 */
@Data
public class AoMergeOverweightBkgAccountsReportDto extends BaseDto {

    @ApiModelProperty(value = "订单日期-开始")
    private String orderDateStart;

    @ApiModelProperty(value = "订单日期-结束")
    private String orderDateEnd;

    @ApiModelProperty(value = "航班日期-开始")
    private String flightDateStart;

    @ApiModelProperty(value = "航班日期-结束")
    private String flightDateEnd;

    @ApiModelProperty(value = "支付日期-开始")
    private String payTimeStart;

    @ApiModelProperty(value = "支付日期-结束")
    private String payTimeEnd;

    @ApiModelProperty(value = "子订单号")
    private String subOrderNo;

    @ApiModelProperty(value = "银行订单号")
    private String bankOrderNo;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "出发地")
    private String org;

    @ApiModelProperty(value = "目的地")
    private String dst;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "航班性质")
    private String flightType;

    @ApiModelProperty(value = "支付方式")
    private String payType;

    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "缴纳单号")
    private String turnNumber;

    @ApiModelProperty(value = "行政区域")
    private String areaCompany;

    @ApiModelProperty(value = "财务订单号")
    private String financeOrderNo;
}
