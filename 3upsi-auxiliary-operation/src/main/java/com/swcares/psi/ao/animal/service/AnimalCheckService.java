package com.swcares.psi.ao.animal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.ao.animal.dto.AnimalCheckH5Dto;
import com.swcares.psi.ao.animal.dto.AnimalCheckPageDto;
import com.swcares.psi.ao.animal.dto.ReserveDto;
import com.swcares.psi.ao.animal.entity.AoAnimalCheck;
import com.swcares.psi.ao.animal.vo.AnimalCheckExportVo;
import com.swcares.psi.ao.animal.vo.AnimalCheckH5PaxListVo;
import com.swcares.psi.ao.animal.vo.AnimalCheckH5Vo;
import com.swcares.psi.ao.animal.vo.AnimalCheckPageVo;

import java.util.List;

/**
 * @ClassName：AnimalCheckService
 * @Description：小动物托运实现
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/1/3 10:43
 * @version： v1.0
 */
public interface AnimalCheckService  extends IService<AoAnimalCheck> {

    IPage<AnimalCheckPageVo> getAnimalCheckPage(AnimalCheckPageDto dto);


    List<AnimalCheckExportVo> getAnimalCheckList(AnimalCheckPageDto dto);

    IPage<AnimalCheckH5Vo> getAnimalCheckH5FltPage(AnimalCheckH5Dto dto);

    IPage<AnimalCheckH5PaxListVo> getAnimalCheckH5PaxList(AnimalCheckH5Dto dto);

    void saveAnimalData(ReserveDto dto) throws Exception;
}
