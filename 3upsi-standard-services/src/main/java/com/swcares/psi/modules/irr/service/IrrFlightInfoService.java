package com.swcares.psi.modules.irr.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.base.data.api.dto.IrrFlightInfoPageDto;
import com.swcares.psi.base.data.api.entity.IrrFlightInfo;
import com.swcares.psi.base.data.api.vo.IrrFlightInfoVo;

import java.util.List;

/**
 * ClassName：com.swcares.psi.modules.irr.service.impl.IrrFlightInfoService <br>;
 * Description：irr打标航班信息服务 <br>;
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2023/1/12 13:48 <br>;
 * @version v1.0 <br>;
 */
public interface IrrFlightInfoService extends IService<IrrFlightInfo> {
    IPage<IrrFlightInfoVo> getIrrFlightInfoPage(IrrFlightInfoPageDto irrFlightInfoPageDto);

    /**
     * Title：getIrrFlightTaskInfos <br>;
     * Description：获取打标定时任务所需数据 <br>;
     * @param:  <br>;
     * @return: List<IrrFlightInfo> <br>;
     * <AUTHOR> <br>;
     * date 2023/1/13 10:47 <br>;
     * @throws  <br>;
     */
    List<IrrFlightInfo> getIrrFlightTaskInfos();

    /**
     * Title：getIrrFlightTaskInfos <br>;
     * Description：获取打标定时任务所需数据 <br>;
     * @param: 需要更新的对象 <br>;
     * @return: List<IrrFlightInfo>-失败的数据集合 <br>;
     * <AUTHOR> <br>;
     * date 2023/1/13 10:47 <br>;
     * @throws  <br>;
     */
    List<IrrFlightInfo> updateIrrFlightInfos(List<IrrFlightInfo> irrFlightInfos);

    List<IrrFlightInfoVo> irrFlightInfoDown(IrrFlightInfoPageDto irrFlightInfoPageDto);
}
