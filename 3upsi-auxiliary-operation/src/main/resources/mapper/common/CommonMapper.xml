<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.common.mapper.CommonMapper">
    <sql id="paxInfoSql">
        select
        pass.id as paxId,
        fo.ID as flightId,
        CASE
        when pass.PASSENGER_NAME is null or pass.PASSENGER_NAME='' then pass.PASSENGER_NAME_EN
        else pass.PASSENGER_NAME
        end
        as paxName,
        pass.TICKET_NUMBER as tktNo,
        pass.SEAT_NUMBER as seatNumber,
        pass.COUPON_NUMBER as couponNumber,
        case
        when pass.VIP_TYPE is null or pass.VIP_TYPE='' then ''
        else '要客'
        end
        as paxType,

        ifnull(
        (CASE pass.LOYAL_LEVEL WHEN 'C' THEN '普通卡' WHEN 'S' THEN '银卡' WHEN 'G' THEN '金卡' WHEN 'P' THEN '白金卡'
        WHEN 'V' THEN '贵宾卡'  WHEN 'T' THEN '终身白金卡' END ),
        ifnull(
         (
         select a.SPECIAL_NAME from  sys_dict sd
         INNER join flt_passenger_special_service a on  a.SPECIAL_CODE=sd.DATA_CODE
         where  a.PASSR_ID=pass.ID and  sd.DATA_TYPE_CODE='ao_pax_card_type' and sd.DATA_STATUS='0' order by sd.DATA_VALUE asc limit 1
         ) ,'普通卡')
        ) as cardType,
        pass.ID_TYPE as idType,
        pass.ID_NUMBER as paxNo,
        pass.PNR_REF as pnr,
        if(pass.MAIN_CABIN='C', '公务舱', '经济舱')
        as subCabin,
        ifnull(pass.CARRY_CABIN,pass.SUB_CABIN) as subCabinCode,
        (case fo.FLIGHT_TYPE when 'D' then '国内' when 'I' then '国际' when 'R' then '地区' end) as segmentType,
        DATE_FORMAT(fo.STD,'%Y-%m-%d %H:%i:%S')as std,
        pass.org as org,
        pass.dst as dst,
        pass.FLIGHT_SEGMENT as segment,
        get_city_name(pass.org) as orgName,
        get_city_name(pass.dst) as dstName,
        pass.TICKET_PRICE,
        fo.flt_gate as gate,
        pass.FLIGHT_NUMBER as flightNo,
        fo.FLIGHT_MODEL as flightModel,
        if(
        (SELECT DATA_CODE  FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0' AND (DATA_CODE = pass.org OR DATA_CODE = pass.dst) limit 1 ) is null,'N','Y'
        ) as isGat,
        pass.BOARDING_NUMBER bordNo,
        DATE_FORMAT(pass.FLIGHT_DATE,'%Y-%m-%d') as FLIGHT_DATE,
        DATE_FORMAT(pass.TICKET_ISSUE_DATE,'%Y%m%d') as ticketIssueDate,
        DATE_FORMAT(pass.BIRTH_DATE,'%Y-%m-%d') as birthDate,
        ifnull((select FFP_NO from sa_o2002_ffp_basic_info sofbi where sofbi.ID_NUMBER=pass.ID_NUMBER  and sofbi.IS_DEL='N' order by UPDATE_TIME desc limit 1),'-') as ffpNo
        from flt_passenger_real_info  pass
        left join flt_flight_real_info fo on pass.flight_date=fo.flight_date and pass.flight_number=fo.flight_number and pass.org=fo.org
</sql>


    <select id="getPaxInfoByTktNo" parameterType="String"
            resultType="com.swcares.psi.ao.common.vo.PaxInfoVo">
        <include refid="paxInfoSql"></include>
        where 1=1
        and (pass.IS_CANCEL != 'Y' or pass.IS_CANCEL is null)
        <if test="tktNo != null and tktNo!='' ">
            and  pass.TICKET_NUMBER=#{tktNo}
        </if>
        <if test="flitghtDate != null and flitghtDate!='' ">
            and  pass.FLIGHT_DATE=DATE_FORMAT(#{flitghtDate},'%Y-%m-%d')
        </if>
        <if test="flitghtNo != null and flitghtNo!='' ">
            and  pass.FLIGHT_NUMBER=#{flitghtNo}
        </if>
        <if test="boardingNumber != null and boardingNumber!='' ">
            and  pass.BOARDING_NUMBER=#{boardingNumber}
        </if>
        <if test="org != null and org!='' ">
            and  pass.org=#{org}
        </if>
    </select>

    <select id="getPaxInfoById" parameterType="String"
            resultType="com.swcares.psi.ao.common.vo.PaxInfoVo">
        <include refid="paxInfoSql"></include>
        where pass.id=#{paxId}
    </select>

    <select id="getAoCardTypeList" resultType="java.lang.String">
    select
        aa.cardTypeName
        from
        (

               (select
                        CASE pass.LOYAL_LEVEL WHEN 'C' THEN '普通卡' WHEN 'S' THEN '银卡' WHEN 'G' THEN '金卡' WHEN 'P' THEN '白金卡'
                    WHEN 'V' THEN '贵宾卡'  WHEN 'T' THEN '终身白金卡'  else  '普通卡'  END  as cardTypeName,
                        CASE pass.LOYAL_LEVEL WHEN 'C' THEN 70 WHEN 'S' THEN 60 WHEN 'G' THEN 40 WHEN 'P' THEN 30
                    WHEN 'V' THEN 10  WHEN 'T' THEN 20  else 70 END  as cardTypeOrder

                    from flt_passenger_real_info  pass
                    where pass.id=#{paxId}
                ) union
                (
                    select
                     '集团协议' as cardTypeName,
                      50 as cardTypeOrder
                    FROM
                    (
                        select
                        fpss.SPECIAL_CODE as code
                        from flt_passenger_special_service  fpss where fpss.passr_id=#{paxId} order by id limit 1
                    )  xx
                    WHERE xx.code ='JTXY'
                )
        ) aa  order by aa.cardTypeOrder asc

    </select>


    <select id="unDisposeOrder" resultType="java.lang.Integer">
        SELECT
        IFNULL(SUM(1), 0)
        FROM ao_order_info
        <where>
            <if test=" chargeType != null">
                AND CHARGE_TYPE in
                <foreach collection="chargeType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test=" orderStatus != null and orderStatus != ''">
                and ORDER_STATUS in
                <foreach collection="orderStatus" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test=" userNo != null and userNo != '' ">
                AND ( USER_NO=#{userNo}
                <!-- # 指定用户只能查看的数据-->
                <if test=" userId != null and userId != '' ">
                    OR USER_NO in (
                    select DISTINCT(emy.TUNO) from data_permissions dp
                    LEFT JOIN user_role ur on dp.ROLE_ID=ur.ROLE_ID
                    LEFT JOIN user_role ur2 on ur2.ROLE_ID=dp.PERMISSIONS_ROLE_ID
                    LEFT JOIN employee emy on ur2.EMPLOYEE_ID=emy.id
                    where ur.EMPLOYEE_ID=#{userId}
                    )
                </if>
                )
            </if>
        </where>
    </select>


    <select id="isDirector" resultType="java.lang.Boolean">
    select count(*)>0 from  data_permissions dp
    inner join user_role ur on ur.role_id=dp.role_id
    where ur.EMPLOYEE_ID=#{userId}
    </select>
</mapper>