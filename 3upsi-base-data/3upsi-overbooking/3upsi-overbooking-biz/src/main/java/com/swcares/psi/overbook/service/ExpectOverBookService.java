package com.swcares.psi.overbook.service;

import com.swcares.psi.common.utils.query.QueryResults;
import com.swcares.psi.overbook.dto.ExpectOverBookDto;
import com.swcares.psi.overbook.dto.ExpectOverBookInfoDto;
import com.swcares.psi.overbook.dto.PushExpectOverBookInfoDto;
import com.swcares.psi.overbook.vo.ActOverBookInfoVo;
import com.swcares.psi.overbook.vo.ExpectOverBookVo;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * Title：ExpectOverBookDao
 * Package：com.swcares.psi.overbook.dao
 * Description：预计超售信息Service
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2020-10-10
 * @version v1.0
 */
public interface ExpectOverBookService {

    /**
     * Title: expectOverBookPage
     * param: [expectOverBookDto]
     * @return com.swcares.psi.common.utils.query.QueryResults
     * Description: 查询预超信息列表
     * author: makai
     * date: 2020-10-10
     */
    QueryResults findExpectOverBookPage(ExpectOverBookDto expectOverBookDto);

    /**
     * Title: saveExpectOverBook
     * param: [expectOverBook]
     * @return void
     * Description: 保存
     * author: makai
     * date: 2020-10-10
     */
    void saveExpectOverBook(ExpectOverBookInfoDto expectOverBook);


    List<PushExpectOverBookInfoDto.PushData> insetExpectOverBook(PushExpectOverBookInfoDto expectOverBook);

    /**
     * Title: delExpectOverBook
     * param: [ids]
     * @return void
     * Description: 删除
     * author: makai
     * date: 2020-10-10
     */
    void delExpectOverBook(String ids);

    /**
     * Title: updateExpectOverBook
     * param: [expectOverBook]
     * @return void
     * Description: 更新
     * author: makai
     * date: 2020-10-10
     */
    void updateExpectOverBook(ExpectOverBookInfoDto expectOverBook);

    /**
     * Title: getExpectOverBookInfo
     * param: [expectOverBookDto]
     * @return java.util.List<com.swcares.psi.overbook.entity.ExpectOverBook>
     * Description: 查询预超信息List
     * author: makai
     * date: 2020-10-10
     */
    List<ExpectOverBookVo> getExpectOverBookInfo(ExpectOverBookDto expectOverBookDto);

    /**
     * Title: getActOverBookInfo
     * param: [expectOverBookDto]
     * @return java.util.List<com.swcares.psi.overbook.vo.ActOverBookInfoVo>
     * Description: 定时任务获取当日之前所有超售信息
     * author: makai
     * date: 2020-11-30
     */
    List<ActOverBookInfoVo> getActOverBookInfo(ExpectOverBookDto expectOverBookDto);
}
