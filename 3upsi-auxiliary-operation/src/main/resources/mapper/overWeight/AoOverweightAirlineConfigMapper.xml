<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.overweightbkg.mapper.AoOverweightAirlineConfigMapper">


    <resultMap id="resultType" type="com.swcares.psi.ao.overweightbkg.vo.AoOverweightAirlineConfigPageVo">
        <result column="id" property="id" />
        <result column="orgPlace" property="orgPlace" />
        <result column="dstPlace" property="dstPlace" />
        <result column="spaceDivision" property="spaceDivision" />
        <collection property="itemConfigs" ofType="com.swcares.psi.ao.overweightbkg.vo.AoOverweightAirlineConfigPageVo$ItemConfig" javaType="java.util.List"
                    select="getItem" column="id">

        </collection>
    </resultMap>



    <select id="getList" resultMap="resultType" parameterType="com.swcares.psi.ao.overweightbkg.dto.AoOverweightConfigDto">
        <include refid="listSql"/>
    </select>

    <select id="getPage" resultMap="resultType" parameterType="com.swcares.psi.ao.overweightbkg.dto.AoOverweightConfigDto">
           <include refid="listSql"/>
    </select>


    <sql id="listSql">
        select
        aoac.id as id,
        aoac.ORG_PLACE as orgPlace,
        aoac.DST_PLACE as dstPlace,
        aoac.SPACE_DIVISION as spaceDivision
        from  ao_overweight_airline_config aoac
        <where>
            aoac.STATUS='1'
            <if test="dto.id != null  and dto.id !='' ">
                and aoac.ID=#{dto.id}
            </if>
            <if test="dto.orgPlace != null  and dto.orgPlace !='' ">
                and aoac.ORG_PLACE=#{dto.orgPlace}
            </if>
            <if test="dto.dstPlace != null  and dto.dstPlace !='' ">
                and aoac.DST_PLACE=#{dto.dstPlace}
            </if>
        </where>
    </sql>



    <resultMap id="resultItem" type="com.swcares.psi.ao.overweightbkg.vo.AoOverweightAirlineConfigPageVo$ItemConfig">
        <result column="itemId" property="itemId"/>
        <result column="spaceType" property="spaceType"/>
        <result column="chargeItem" property="chargeItem"/>
        <result column="chargeOrder" property="chargeOrder"/>
        <result column="absoluteStandardItem" property="absoluteStandardItem"/>
        <result column="scopeStandardItemStart" property="scopeStandardItemStart"/>
        <result column="scopeStandardItemEnd" property="scopeStandardItemEnd"/>
        <collection property="pricesConfig" ofType="com.swcares.psi.ao.overweightbkg.vo.AoOverweightAirlineConfigPageVo$PriceConfig" javaType="java.util.List"
                    select="getPrice" column="itemId">

        </collection>
    </resultMap>

    <select id="getItem" resultMap="resultItem" >
        select
        aoic.id as itemId,
        aoic.SPACE_TYPE as spaceType,
        aoic.CHARGE_ITEM as chargeItem,
        aoic.CHARGE_ORDER as chargeOrder,
        aoic.ABSOLUTE_STANDARD_ITEM as absoluteStandardItem,
        aoic.SCOPE_STANDARD_ITEM_START as scopeStandardItemStart,
        aoic.SCOPE_STANDARD_ITEM_END as scopeStandardItemEnd
        from  ao_overweight_item_config aoic
        where aoic.STATUS='1' and aoic.OVERWEIGHT_AIRLINE_ID=#{id}
    </select>




<resultMap id="priceMap" type="com.swcares.psi.ao.overweightbkg.vo.AoOverweightAirlineConfigPageVo$PriceConfig">
    <result column="currencyType" property="currencyType"/>
    <result column="currencyTypeName" property="currencyTypeName"/>
    <result column="currencyPrice" property="currencyPrice"/>
</resultMap>

    <select id="getPrice" resultMap="priceMap">
            select
                cc.CURRENCY_TYPE as currencyType,
                cc.CURRENCY_TYPE_NAME as currencyTypeName,
                cc.CURRENCY_PRICE as currencyPrice
            from ao_common_price_config cc
            where cc.BUSINESS_TABLE_ID=#{itemId}

    </select>

</mapper>