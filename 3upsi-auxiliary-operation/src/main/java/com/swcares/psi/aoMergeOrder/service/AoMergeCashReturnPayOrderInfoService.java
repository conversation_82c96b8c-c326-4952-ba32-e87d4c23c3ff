package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashReturnPayOrderInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AoMergeCashReturnPayOrderInfoService extends IService<AoMergeCashReturnPayOrderInfo> {
    Integer validateTurnOrder( List<String> subOrderIdList);
    List<String> turnNotifyOrderNoLock( String turnOrderNo);
}
