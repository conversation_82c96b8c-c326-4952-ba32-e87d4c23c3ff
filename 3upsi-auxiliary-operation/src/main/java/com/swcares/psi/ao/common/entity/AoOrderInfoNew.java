package com.swcares.psi.ao.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.psi.common.utils.encryption.Encryption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 辅营订单表 加_new 的新设计
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AoOrderInfoNew对象", description="辅营订单表 加_new 的新设计")
@TableName("AO_ORDER_INFO_NEW")
public class AoOrderInfoNew implements Serializable {
    @ApiModelProperty(value = "订单表主键")
    @TableId(value="ID",type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "航班日期")
    @TableField("FLIGHT_DATE")
    private LocalDate flightDate;

    @ApiModelProperty(value = "航班号")
    @TableField("FLIGHT_NO")
    private String flightNo;

    @ApiModelProperty(value = "订单类型(一人多坐,现场座位销售,逾重行李,贵宾室,柜台升舱,机上升舱)")
    @TableField("ORDER_TYPE")
    private String orderType;

    @ApiModelProperty(value = "订单号")
    @TableField("ORDER_NO")
    private String orderNo;

    @ApiModelProperty(value = "订单日期")
    @TableField("ORDER_DATE")
    private LocalDateTime orderDate;



    @ApiModelProperty(value = "出发航站3字码")
    @TableField("ORG")
    private String org;

    @ApiModelProperty(value = "到达航站3字码")
    @TableField("DST")
    private String dst;

    @ApiModelProperty(value = "客票号")
    @TableField("TKT_NO")
    private String tktNo;

    @ApiModelProperty(value = "航线性质(国内,国际,地区)")
    @TableField("SEGMENT_TYPE")
    private String segmentType;

    @ApiModelProperty(value = "机型")
    @TableField("FLIGHT_TYPE")
    private String flightType;

    @ApiModelProperty(value = "订单类型编码(1一人多坐,2现场座位销售,3逾重行李,4贵宾室,5柜台升舱,6机上升舱)")
    @TableField("ORDER_TYPE_CODE")
    private String orderTypeCode;

    @ApiModelProperty(value = "订单状态(0未支付，1支付失败，2支付完成，3部分退款失成功，4全部退款成功,5取消)")
    @TableField("ORDER_STATUS")
    private String orderStatus;

    @Encryption
    @ApiModelProperty(value = "下订单旅客证件号")
    @TableField("PAX_NO")
    private String paxNo;

    @ApiModelProperty(value = "购买产品旅客对应客票号乘机人")
    @TableField("PAX_NAME")
    private String paxName;


    @ApiModelProperty(value = "操作人号")
    @TableField("USER_NO")
    private String userNo;

    @ApiModelProperty(value = "操作人")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "部门ID")
    @TableField("DEPARTMENT_ID")
    private String departmentId;

    @ApiModelProperty(value = "部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "旅客ID")
    @TableField("PAX_ID")
    private String paxId;

    @ApiModelProperty(value = "支付单id  以此确定最终支付的那笔交易单记录")
    @TableField("PAY_ORDER_ID")
    private String payOrderId;

    @ApiModelProperty(value = "署名URL")
    @TableField("SIGNATURE_URL")
    private String signatureUrl;
}
