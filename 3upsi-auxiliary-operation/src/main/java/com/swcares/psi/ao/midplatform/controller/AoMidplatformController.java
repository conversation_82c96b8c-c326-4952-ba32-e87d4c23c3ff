package com.swcares.psi.ao.midplatform.controller;

import com.swcares.psi.ao.midplatform.dto.AoMidplatformDto;
import com.swcares.psi.ao.midplatform.service.IAoMidplatformService;
import com.swcares.psi.ao.midplatform.service.IAoOrderToMidplatformService;
import com.swcares.psi.ao.midplatform.service.impl.AoMidplatformServiceImpl;
import com.swcares.psi.ao.midplatform.vo.AoMidplatformDetailVo;
import com.swcares.psi.ao.midplatform.vo.AoMidplatformVo;
import com.swcares.psi.ao.midplatform.vo.AoOrderToMidplatformVoWrapper;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.ExcelExportUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@AllArgsConstructor
@Api(tags = "辅营数智中台订单控制器")
@RequestMapping("/api/ao/midplat")
@RestController
public class AoMidplatformController {

    private final IAoMidplatformService aoMidplatformService;

    private final IAoOrderToMidplatformService aoOrderToMidplatformService;

    @ApiOperation(value = "数智中台订单列表")
    @PostMapping("/query")
    public RenderResult<PsiPage<AoMidplatformVo>> query(@RequestBody AoMidplatformDto dto) {
        return RenderResult.success(aoMidplatformService.page(dto));
    }

    @ApiOperation(value = "数智中台订单列表导出")
    @PostMapping("/export")
    public void export(@RequestBody AoMidplatformDto dto, HttpServletResponse response) {
        List<AoMidplatformVo> list = aoMidplatformService.list(dto);

        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "数智中台订单明细";
            String fileNameEocode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEocode + ".csv");
            ExcelExportUtils.export(response.getOutputStream(), AoMidplatformVo.class, list, AuthenticationUtil.getUserNo(), fileName);
        } catch (Exception e) {
            log.error("数智中台订单明细导出出错！", e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
    }

    @ApiOperation(value = "数智中台订单详情")
    @GetMapping("/detail")
    public RenderResult<AoMidplatformDetailVo> queryOrderDetail(String orderNo) {
        return RenderResult.success(aoMidplatformService.orderDetail(orderNo));
    }

    @ApiOperation(value = "推送辅营订单数据到消息队列待数智中台消费")
    @GetMapping("/syncOrderToMidplatform")
    public RenderResult syncOrderToMidplatform(String orderNos, String isUpgradeOnboard) {
        aoOrderToMidplatformService.syncOrderToMidplatform(Arrays.asList(orderNos.split(",")), "1".equals(isUpgradeOnboard));
        return RenderResult.success();
    }

    @ApiOperation(value = "推送辅营订单同步数智中台数据结构")
    @GetMapping("/aoOrderToMidplatformVo")
    public RenderResult<AoOrderToMidplatformVoWrapper> aoOrderToMidplatformVo() {
        return RenderResult.success(null);
    }

}
