package com.swcares.psi.aoMergeOrder.service;

import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeViproomAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeViproomItemSell;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeViproomItemSellAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

/**
 * <p>
 * 合并支付贵宾厅套餐销售产品详情订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeViproomItemSellService extends IService<AoMergeViproomItemSell> {
    List<AoMergeViproomItemSellAccountsReportVo> getAccountsList(AoMergeViproomAccountsReportDto dto);
    PsiPage<AoMergeViproomItemSellAccountsReportVo> getAccountsListPage(AoMergeViproomAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeViproomAccountsReportDto dto);
}
