<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.register.mapper.CountVipRegisterMapper">

  <select id="selectVipRegisterInfosByParems" resultType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoVO" parameterType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto">
    SELECT ID,
           PASSENGER_NAME,
           CARD_NUMBER,
           CERTIFIC_TYPE,
           CERTIFIC_NO,
           PHONE_NUMBER,
           REGISTER_TYPE,
           DATE_FORMAT(avr.CREATE_DATE,'%Y-%m-%d %H:%i:%s') as CREATE_DATE
    from ao_vip_register avr
    WHERE avr.IS_DEL = '1'
      <if test="paremsVo.userName != null and paremsVo.userName != ''">
          and avr.USER_NAME = #{paremsVo.userName}
      </if>

      <if test="paremsVo.userNumber != null and paremsVo.userNumber != ''">
        and avr.USER_NUMBER = #{paremsVo.userNumber}
      </if>

      <if test="paremsVo.userDopt != null and paremsVo.userDopt != ''">
        and avr.USER_DOPT like concat('%',#{paremsVo.userDopt},'%')
      </if>

      <if test="paremsVo.userDoptNo != null and paremsVo.userDoptNo != ''">
        and avr.USER_DOPT_NO = #{paremsVo.userDoptNo}
      </if>

      <if test="paremsVo.registerType != null and paremsVo.registerType != ''">
          and avr.REGISTER_TYPE = #{paremsVo.registerType}
      </if>

      <if test="paremsVo.startTime != null and paremsVo.startTime != ''">
          and avr.CREATE_DATE &gt;= #{paremsVo.startTime}
          and avr.CREATE_DATE &lt; DATE_ADD(#{paremsVo.startTime}, INTERVAL 1 DAY)
      </if>



  </select>

  <select id="countVipRegisterInfo" resultType="com.swcares.psi.ao.register.vo.CountVIPNumberVo" parameterType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto">
      SELECT avr.USER_NAME,
             avr.USER_NUMBER,
             avr.USER_DOPT,
             avr.USER_DOPT_NO,
             avr.REGISTER_TYPE,
             avr.CREATE_DATE,
             avr.CERTIFIC_TYPE,
             COUNT(id) as registerNumber
      FROM ao_vip_register avr
      <where>
          avr.IS_DEL = '1'
          <if test="paremsVo.userName != null and paremsVo.userName != ''">
              and avr.USER_NAME like CONCAT('%',#{paremsVo.userName},'%')
          </if>

          <if test="paremsVo.userNumber != null and paremsVo.userNumber != ''">
              and avr.USER_NUMBER = #{paremsVo.userNumber}
          </if>

          <if test="paremsVo.userDopt != null and paremsVo.userDopt != ''">
              and avr.USER_DOPT like concat('%',#{paremsVo.userDopt},'%')
          </if>

          <if test="paremsVo.registerType != null and paremsVo.registerType != ''">
              and avr.REGISTER_TYPE = #{paremsVo.registerType}
          </if>

          <if test="paremsVo.startTime != null and paremsVo.startTime != ''">
              and avr.CREATE_DATE &gt;= #{paremsVo.startTime}
          </if>

          <if test="paremsVo.stopTime != null and paremsVo.stopTime != ''">
              and avr.CREATE_DATE &lt; DATE_ADD(#{paremsVo.stopTime}, INTERVAL 1 DAY)
          </if>

      </where>

      GROUP BY avr.USER_NAME, avr.USER_NUMBER,avr.USER_DOPT, avr.REGISTER_TYPE, DATE_FORMAT(CREATE_DATE,'%Y%m%d')
      ORDER BY avr.CREATE_DATE DESC
  </select>

    <select id="selectVipRegisterInfos" resultType="com.swcares.psi.ao.register.vo.VIPRegisterInfoVo" parameterType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto">
        <include refid="selectVipRegisterInfo"></include>
    </select>

    <select id="selectVipRegisterInfosList" resultType="com.swcares.psi.ao.register.vo.VIPRegisterInfoVo" parameterType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto">
        <include refid="selectVipRegisterInfo"></include>
    </select>

    <sql id="selectVipRegisterInfo">
        SELECT avr.PASSENGER_NAME,
        avr.CARD_NUMBER,
        avr.CERTIFIC_TYPE,
        avr.CERTIFIC_NO,
        avr.PHONE_NUMBER,
        avr.USER_NAME,
        avr.USER_NUMBER,
        avr.USER_DOPT,
        avr.REGISTER_TYPE,
        case avr.REGISTER_TYPE when '1' then '现场办理入会' when '0' then '短信办理入会' when '2' then '平板办理入会' else '' end as registerTypeName,
        DATE_FORMAT(avr.CREATE_DATE,'%Y-%m-%d %H:%i:%s') as CREATE_DATE
        FROM ao_vip_register avr
        <where>
            avr.IS_DEL = '1'
            <if test="paremsVo.passengerName != null and paremsVo.passengerName != ''">
                and avr.PASSENGER_NAME like CONCAT('%', #{paremsVo.passengerName}, '%')
            </if>

            <if test="paremsVo.phoneNumber != null and paremsVo.phoneNumber != ''">
                and avr.PHONE_NUMBER = #{paremsVo.phoneNumber}
            </if>

            <if test="paremsVo.userName != null and paremsVo.userName != ''">
                and avr.USER_NAME like CONCAT('%',#{paremsVo.userName},'%')
            </if>

            <if test="paremsVo.userDopt != null and paremsVo.userDopt != ''">
                and avr.USER_DOPT like concat('%',#{paremsVo.userDopt},'%')
            </if>

            <if test="paremsVo.registerType != null and paremsVo.registerType != ''">
                and avr.REGISTER_TYPE = #{paremsVo.registerType}
            </if>

            <if test="paremsVo.startTime != null and paremsVo.startTime != ''">
                and avr.CREATE_DATE &gt;= #{paremsVo.startTime}
            </if>

            <if test="paremsVo.stopTime != null and paremsVo.stopTime != ''">
                and avr.CREATE_DATE &lt; DATE_ADD(#{paremsVo.stopTime}, INTERVAL 1 DAY)
            </if>

            <if test="paremsVo.certificType != null and paremsVo.certificType != ''">
                and avr.CERTIFIC_TYPE = #{paremsVo.certificType}
            </if>
        </where>
    </sql>

    <select id="countMemVipRegisterInfo" resultType="com.swcares.psi.ao.register.vo.CountVIPNumberVo" parameterType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto">
        SELECT
               avr.USER_NUMBER,
               avr.REGISTER_TYPE,
               DATE_FORMAT(avr.CREATE_DATE,'%Y-%m-%d') as CREATE_DATE,
               COUNT(ID) as registerNumber
        FROM ao_vip_register avr
        <where> avr.IS_DEL = '1'
            <if test="paremsVo.userNumber != null and paremsVo.userNumber != ''">
                and avr.USER_NUMBER = #{paremsVo.userNumber}
            </if>

            <if test="paremsVo.registerType != null and paremsVo.registerType != ''">
                and avr.REGISTER_TYPE = #{paremsVo.registerType}
            </if>

            <if test="paremsVo.startTime != null and paremsVo.startTime != ''">
                and avr.CREATE_DATE &gt;= #{paremsVo.startTime}
            </if>

            <if test="paremsVo.stopTime != null and paremsVo.stopTime != ''">
                and avr.CREATE_DATE &lt; DATE_ADD(#{paremsVo.stopTime}, INTERVAL 1 DAY)
            </if>
        </where>

        GROUP BY avr.USER_NUMBER, avr.REGISTER_TYPE, DATE_FORMAT(CREATE_DATE,'%Y%m%d')
        ORDER BY CREATE_DATE DESC
    </select>

    <select id="countVipRegisterTotal" resultType="com.swcares.psi.ao.register.vo.CountVIPNumberVo" parameterType="com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto">
        SELECT
            COUNT(avr.ID) as registerSumNumber
        FROM ao_vip_register avr
        <where>
            avr.IS_DEL = '1'
            <if test="paremsVo.passengerName != null and paremsVo.passengerName != ''">
                and avr.PASSENGER_NAME = #{paremsVo.passengerName}
            </if>

            <if test="paremsVo.phoneNumber != null and paremsVo.phoneNumber != ''">
                and avr.PHONE_NUMBER = #{paremsVo.phoneNumber}
            </if>

            <if test="paremsVo.userName != null and paremsVo.userName != ''">
                and avr.USER_NAME = #{paremsVo.userName}
            </if>

            <if test="paremsVo.userNumber != null and paremsVo.userNumber != ''">
                and avr.USER_NUMBER = #{paremsVo.userNumber}
            </if>

            <if test="paremsVo.userDopt != null and paremsVo.userDopt != ''">
                and avr.USER_DOPT like concat('%',#{paremsVo.userDopt},'%')
            </if>

            <if test="paremsVo.registerType != null and paremsVo.registerType != ''">
                and avr.REGISTER_TYPE = #{paremsVo.registerType}
            </if>

            <if test="paremsVo.startTime != null and paremsVo.startTime != ''">
                and avr.CREATE_DATE &gt;= #{paremsVo.startTime}
            </if>

            <if test="paremsVo.stopTime != null and paremsVo.stopTime != ''">
                and avr.CREATE_DATE &lt; DATE_ADD(#{paremsVo.stopTime}, INTERVAL 1 DAY)
            </if>

        </where>
    </select>
</mapper>