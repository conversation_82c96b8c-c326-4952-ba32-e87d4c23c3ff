/*
 * Copyright (c) ACCA Corp.
 * All Rights Reserved.
 */
package com.swcares.psi.permission.row.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.permission.entity.RowPermissionData;
import com.swcares.psi.common.permission.impl.row.vo.RowPermissionDataRedisVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> Zheng, 2021/11/13
 *
 */
@Mapper
public interface RowPermissionDataMapper extends BaseMapper<RowPermissionData> {

    @Delete("delete from row_permission where role_id = #{roleId}")
    public void deleteByRoleId(Long roleId);

    @Select("<script> select cc.airport_3code airportCode,rp.* from row_permission rp " +
            "LEFT JOIN city_code cc on rp.type_value = cc.city_ch_name where rp.role_id in " +
            "<foreach item='item' index='index' collection='roleIds' open='(' separator=',' close=')'> " +
            "#{item} </foreach></script> ")
    public List<RowPermissionDataRedisVO> selectRowPermissionByRoleIds(List<String> roleIds);
}
