package com.swcares.psi.aoMergeOrder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.mapper.CommonMapper;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.cons.RedisLockKeyCons;
import com.swcares.psi.aoMergeOrder.dto.AoRefundDto;
import com.swcares.psi.aoMergeOrder.entity.*;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.aoMergeOrder.vo.CashTurnSaveVo;
import com.swcares.psi.collect.h5.yee.NotifyRequestDTO;
import com.swcares.psi.combine.constant.CacheConstants;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.redis.RedisService;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.worldpay.vo.WorldpayAuthorisedVo;
import com.swcares.psi.common.worldpay.vo.WorldpayCancelledVo;
import com.swcares.psi.common.worldpay.vo.WorldpayCapturedVo;
import com.swcares.psi.common.worldpay.vo.WorldpayRefundedVo;
import com.swcares.psi.stimulate.utils.StimulateOrderUtils;
import com.swcares.psi.utils.WxPayUtils;
import com.yeepay.g3.sdk.yop.encrypt.CertTypeEnum;
import com.yeepay.g3.sdk.yop.encrypt.DigitalEnvelopeDTO;
import com.yeepay.g3.sdk.yop.utils.DigitalEnvelopeUtils;
import com.yeepay.g3.sdk.yop.utils.InternalConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AoMegerOrderNotifyLockService {

    @Autowired
    IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    AoCommonService aoCommonService;
    @Autowired
    private Redisson redisson;
    @Autowired
    RedisService redisService;
    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;

    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;

    @Autowired
    IAoMergePayOrderInfoService aoMergePayOrderInfoService;
    @Autowired
    AoMegerOrderNotifyService aoMegerOrderNotifyService;
    @Autowired
    AoMergeCashReturnPayOrderInfoService aoMergeCashReturnPayOrderInfoService;

    @Autowired
    private IAoMergeRefundOrderInfoService aoMergeRefundOrderInfoService;

    @Autowired
    private StimulateOrderUtils stimulateOrderUtils;
    @Autowired
    CommonMapper dao;

    /**
     * 升舱加锁
     *
     * @param id 子订单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void upgradeThreadFunction(String id) {
        // 查询当前升舱订单结果：
        AoMergeCounterUpgrade upgradeOrder = aoMergeCounterUpgradeService.lambdaQuery()
                .eq(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, id)
                .one();
        if (null == upgradeOrder) {
            log.info("升舱订单不存在{}",id);
            return;
        }

        RLock lock = redisson.getLock(RedisLockKeyCons.ORDER_UPGRADE + upgradeOrder.getUpgradeOrder());
        try {
            // 处理当前的订单并且加锁
            if (!lock.tryLock()) {
                log.info("已有服务器执行当前升舱订单任务!!!");
                return;
            }
            // 查询当前订单状态，并且将当前的订单状态处理为升舱中
            aoMergeCounterUpgradeService.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate()
                    .set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_ING)
                    .eq(AoMergeCounterUpgrade::getId, upgradeOrder.getId()));
            // 执行当前的升舱操作
            aoMergeCounterUpgradeService.upgradeUser(upgradeOrder);
        } catch (Exception e) {
            log.error("升舱错误！", e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    /**
     * 微信订单支付回调订单加锁
     *
     * @param request
     */
    public void wxpayNotifyUrl(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> streamToMap = WxPayUtils.getStreamToMap(request, response);
        AoMergePayOrderInfo mergeOrderInfo = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo, streamToMap.get("out_trade_no"))
                .one();
        if (mergeOrderInfo == null) {
            log.info("worldPay支付回调订单触发,订单{}不存在 不计算激励", streamToMap.get("out_trade_no"));
            return;
        }

        AoMergeOrderInfo byId = aoMergeOrderInfoService.getById(mergeOrderInfo.getAoMergeOrderInfoId());


        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + byId.getOrderNo());
        try {
            while (!lock.tryLock()) {
                log.info("微信支付回调，支付状态已经有线程操作,订单号{}", byId.getOrderNo());
                Thread.sleep(300);
            }
            aoMegerOrderNotifyService.wxPayNotify(streamToMap,byId);

            List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                    .list();
            for (AoMergeSuborderInfo ele : list) {
                stimulateOrderUtils.stimulate(mergeOrderInfo.getId(), ele.getId());
            }

        } catch (Exception e) {
            log.error("微信回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    /**
     * 微信缴纳支付回调订单加锁
     *
     * @param request
     */
    public void wxpayTurnNotifyUrl(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> streamToMap = WxPayUtils.getStreamToMap(request, response);
        String out_trade_no = streamToMap.get("out_trade_no");
        List<String> strings = aoMergeCashReturnPayOrderInfoService.turnNotifyOrderNoLock(out_trade_no);


        List<RLock> deleteLock = new ArrayList<>();
        try {
            for (String odr : strings) {
                RLock odrLock = null;
                odrLock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + odr);
                if (!odrLock.tryLock()) {
                    log.info("缴纳回调订单加锁失败:订单号{}", odr);
                    return;
                }

                deleteLock.add(odrLock);
            }
            aoMegerOrderNotifyService.wxpayTurnNotifyUrl(streamToMap);
        } catch (Exception e) {
            log.error("微信回调加锁异常:", e.getMessage(), e);
        } finally {
            for (RLock lock : deleteLock) {
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }


    /**
     * 支付宝订单支付回调订单加锁
     *
     * @param request
     */
    public void alipayNotifyUrl(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        AoMergePayOrderInfo payOrder = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo, parameterMap.get("out_trade_no"))
                .one();
        if (payOrder == null) {
            log.info("支付宝支付回调订单触发,订单{}不存在 不计算激励", parameterMap.get("out_trade_no"));
            return;
        }

        AoMergeOrderInfo byId = aoMergeOrderInfoService.getById(payOrder.getAoMergeOrderInfoId());

        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + byId.getOrderNo());
        try {
            while (!lock.tryLock()) {
                log.info("支付宝支付回调，支付状态已经有线程操作,订单号{}", byId.getOrderNo());
                Thread.sleep(300);
            }
            aoMegerOrderNotifyService.alipayNotifyUrl(request);
            List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, payOrder.getId())
                    .list();
            for (AoMergeSuborderInfo ele : list) {
                stimulateOrderUtils.stimulate(payOrder.getId(), ele.getId());
            }

        } catch (Exception e) {
            log.error("支付宝回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }


    /**
     * 支付宝订单支付回调订单加锁
     *
     * @param request
     */
    public void alipay_turn_notify_url(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        List<String> strings = aoMergeCashReturnPayOrderInfoService.turnNotifyOrderNoLock(out_trade_no);


        List<RLock> deleteLock = new ArrayList<>();
        try {
            for (String odr : strings) {
                RLock odrLock = null;
                odrLock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + odr);
                if (!odrLock.tryLock()) {
                    log.info("缴纳回调订单加锁失败:订单号{}", odr);
                    return;
                }

                deleteLock.add(odrLock);
            }
            aoMegerOrderNotifyService.alipay_turn_notify_url(request);
        } catch (Exception e) {
            log.error("微信回调加锁异常:", e.getMessage(), e);
        } finally {
            for (RLock lock : deleteLock) {
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }

    }


    public void yeePayNotify(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        NotifyRequestDTO notify = new NotifyRequestDTO();
        notify.setResponse(parameterMap.get("response")[0]);
        notify.setCustomerIdentification(parameterMap.get("customerIdentification")[0]);
        DigitalEnvelopeDTO dto = new DigitalEnvelopeDTO();
        dto.setCipherText(notify.getResponse());
        PrivateKey privateKey = InternalConfig.getISVPrivateKey(CertTypeEnum.RSA2048);
        PublicKey publicKey = InternalConfig.getYopPublicKey(CertTypeEnum.RSA2048);
        dto = DigitalEnvelopeUtils.decrypt(dto, privateKey, publicKey);
        JSONObject jsonObject = JSON.parseObject(dto.getPlainText());
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo,jsonObject.get("customerRequestNo").toString())
                .one();
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(payOrderInfo.getAoMergeOrderInfoId());
        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderInfo.getOrderNo());
        try {
            while (!lock.tryLock()) {
                log.info("易宝支付回调，支付状态已经有线程操作,订单号{}", jsonObject.get("customerRequestNo").toString());
                Thread.sleep(300);
            }
            aoMegerOrderNotifyService.yeePayNotify(jsonObject,mergeOrderInfo);
            List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                    .list();
            for (AoMergeSuborderInfo ele : list) {
                stimulateOrderUtils.stimulate(mergeOrderInfo.getId(), ele.getId());
            }
        } catch (Exception e) {
            log.error("易宝支付回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public void worldpayNotifyUrl(WorldpayAuthorisedVo notification) {
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo, notification.getOrderNo())
                .one();
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(payOrderInfo.getAoMergeOrderInfoId());
        if (mergeOrderInfo == null) {
            log.info("worldPay支付回调订单触发,订单{}不存在 不计算激励", mergeOrderInfo.getOrderNo());
            return;
        }
        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderInfo.getOrderNo());
        try {
            if (!lock.tryLock()) {
                log.info("worldPay支付回调，支付状态已经有线程操作,总订单号{}", mergeOrderInfo.getOrderNo());
                return;
            }
            aoMegerOrderNotifyService.worldpayNotify(mergeOrderInfo.getOrderNo());
            List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                    .list();
            for (AoMergeSuborderInfo ele : list) {
                stimulateOrderUtils.stimulate(mergeOrderInfo.getId(), ele.getId());
            }

        } catch (Exception e) {
            log.error("worldPay支付回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void worldpayCapturedNotifyUrl(WorldpayCapturedVo notification) {
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.lambdaQuery().eq(AoMergePayOrderInfo::getBankOrderNo, notification.getOrderNo())
                .one();
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(payOrderInfo.getAoMergeOrderInfoId());
        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderInfo.getOrderNo());
        try {
            if (!lock.tryLock()) {
                log.info("worldPay支付captured事件回调，支付状态已经有线程操作,总订单号{}", mergeOrderInfo.getOrderNo());
                return;
            }
            aoMegerOrderNotifyService.worldpayCapturedNotifyUrl(mergeOrderInfo.getOrderNo());
        } catch (Exception e) {
            log.error("worldPay支付回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void worldpayRefundNotifyUrl(WorldpayRefundedVo notification) {
        log.info("合并订单退款回调>>{}",JSON.toJSONString(notification));
        AoMergePayOrderInfo one = aoMergePayOrderInfoService.lambdaQuery()
                .eq(AoMergePayOrderInfo::getBankOrderNo, notification.getOrderNo())
                .one();
        AoMergeOrderInfo byId = aoMergeOrderInfoService.getById(one.getAoMergeOrderInfoId());
        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + byId.getOrderNo());
        try {
            if (!lock.tryLock()) {
                log.info("worldPay退款回调，支付状态已经有线程操作,退款子订单号{}", notification.getReference());
                return;
            }
            aoMegerOrderNotifyService.worldpayRefundNotifyUrl(byId.getOrderNo(),notification.getReference());
            AoMergeSuborderInfo sub = aoMergeSuborderInfoService.lambdaQuery().eq(AoMergeSuborderInfo::getSuborderOrderNo, notification.getReference()).one();
            stimulateOrderUtils.stimulate(byId.getId(), sub.getId());

        } catch (Exception e) {
            log.error("worldPay退款回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public String worldpayResultAffirm(String orderNo) {

        RLock lock = null;
        try {
            AoMergePayOrderInfo one = aoMergePayOrderInfoService.lambdaQuery()
                    .eq(AoMergePayOrderInfo::getBankOrderNo, orderNo)
                    .one();
            AoMergeOrderInfo byId;
            if (one == null) {
                byId = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getOrderNo, orderNo).one();
                if (byId == null) {
                    log.info("worldPay前端支付确认订单不存在{}", byId.getOrderNo());
                    throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
                }
            } else {

                byId = aoMergeOrderInfoService.getById(one.getAoMergeOrderInfoId());
                if (byId == null) {
                    log.info("worldPay前端支付确认订单不存在{}", byId.getOrderNo());
                    throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
                }
            }
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + byId.getOrderNo());
            while (!lock.tryLock()) {
                log.info("worldpay支付结果确认，支付状态已经有线程操作,订单号{}", byId.getOrderNo());
                Thread.sleep(200);
            }
            return aoMegerOrderNotifyService.worldpayResultAffirm(byId.getOrderNo());
        } catch (Exception e) {
            log.error("worldpay支付确认异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    public void orderCancel(String mergeOrderNo) {
        AoMergeOrderInfo one = aoMergeOrderInfoService.getOne((Wrappers.<AoMergeOrderInfo>lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, mergeOrderNo))
        .eq(AoMergeOrderInfo::getMergeOrderStatus,AoMergeCons.MERGE_ORDER_STATUS_UNPAY));
        if (one == null) {
            throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_CANCEL.getCode());
        }
        RLock lock = null;
        try {
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderNo);
            while (!lock.tryLock()) {
                log.info("订单取消，支付状态已经有线程操作,订单号{}", mergeOrderNo);
                Thread.sleep(200);
            }
            aoCommonService.orderCancel(one);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("订单取消订单异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public void startRefund(String mergeOrderNo, String subOrderNo) {
        log.info("子订单发起退款,总订单{}子订单号{}", mergeOrderNo, subOrderNo);
        RLock lock = null;
        try {
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderNo);
            while (!lock.tryLock()) {
                log.info("子订单发起退款，已经有线程在操作,总订单{}子订单号{}", mergeOrderNo, subOrderNo);
                Thread.sleep(200);
            }
            aoCommonService.startRefund(subOrderNo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("子订单发起退款加锁异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void cancelRefund(String mergeOrderNo, String subOrderNo) {
        log.info("子订单取消退款申请,总订单{}子订单号{}", mergeOrderNo, subOrderNo);
        RLock lock = null;
        try {
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderNo);
            while (!lock.tryLock()) {
                log.info("子订单取消退款申请，已经有线程在操作,总订单{}子订单号{}", mergeOrderNo, subOrderNo);
                Thread.sleep(200);
            }
            aoCommonService.cancelRefund(subOrderNo);
        } catch (Exception e) {
            log.error("子订单取消退款申请异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void refund(AoRefundDto dto) {
        log.info("子订单退款,总订单{}子订单号{}", dto.getMergeOrderNo(), dto.getSubOrderNo());
        RLock lock = null;
        try {
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + dto.getMergeOrderNo());
            while (!lock.tryLock()) {
                log.info("子订单退款，已经有线程在操作总订单,子订单号{}", dto.getSubOrderNo());
                Thread.sleep(200);
            }
            aoCommonService.refund(dto);
            AoMergeSuborderInfo suborderInfo = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getSuborderOrderNo, dto.getSubOrderNo())
                    .one();
            stimulateOrderUtils.stimulate(suborderInfo.getAoMergeOrderInfoId(),suborderInfo.getId());

        } catch (BusinessException e) {
            throw e;
        }catch (Exception e) {
            log.error("子订单退款异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

        }
    }

    public void cash(String orderNo, String posOrderNo){
        log.info("现金收款,总订单{}", orderNo);
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        if(!dao.isDirector(authentication.getId())){
            throw new BusinessException(MessageCode.DIRECTOR_BLANK_RESOURCES.getCode());
        }
        RLock lock = null;
        try {
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + orderNo);
            while (!lock.tryLock()) {
                log.info("现金收款,总订单{}，已经有线程在操作总订单", orderNo);
                Thread.sleep(200);
            }
            aoCommonService.cash(orderNo,posOrderNo);
            AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getOrderNo, orderNo)
                    .one();
            if (mergeOrderInfo == null) {
                log.info("worldPay支付回调订单触发,订单{}不存在 不计算激励", orderNo);
                return;
            }
            List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                    .list();
            for (AoMergeSuborderInfo ele : list) {
                stimulateOrderUtils.stimulate(mergeOrderInfo.getId(), ele.getId());
            }

        } catch (BusinessException e) {
            throw e;
        }catch (Exception e) {
            log.error("现金收款异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

        }
    }

    public CashTurnSaveVo cashTurn(String servicePort, String payType, String mergeOrderIds) {
        String[] split = mergeOrderIds.split(",");
        List<AoMergeOrderInfo> list = aoMergeOrderInfoService.list((Wrappers.<AoMergeOrderInfo>lambdaQuery()
                .in(AoMergeOrderInfo::getId, split))
                .eq(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_SUCCESS));
        List<String> collect = list.stream().map(AoMergeOrderInfo::getOrderNo).collect(Collectors.toList());
        log.info("现金缴纳,总订单{}", StringUtils.join(collect, ","));
        List<RLock> arrayList = new ArrayList<>();
        try {
            for (String orderNo : collect) {
                RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + orderNo);
                while (!lock.tryLock()) {
                    log.info("现金缴纳,总订单{}，已经有线程在操作总订单", orderNo);
                    throw new BusinessException(MessageCode.SYSTEM_BUSY.getCode());
                }
                arrayList.add(lock);
            }
            return aoCommonService.cashTurn(servicePort, payType, mergeOrderIds);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("现金缴纳异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        } finally {
            for (RLock lock : arrayList) {
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }

        }
    }

    public void wxpayRefundNotifyUrl(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> streamToMap = WxPayUtils.getStreamToMap(request, response);
        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + streamToMap.get("out_trade_no"));
        try {
            if (!lock.tryLock()) {
                log.info("微信退款回调，微信退款状态已经有线程操作,订单号{}-退款单号{}", streamToMap.get("out_trade_no"), streamToMap.get("out_refund_no"));
                return;
            }
            String out_refund_no = aoCommonService.wxpayRefundNotifyUrl(streamToMap);
            AoMergeRefundOrderInfo one = aoMergeRefundOrderInfoService.lambdaQuery()
                    .eq(AoMergeRefundOrderInfo::getRefundNo, out_refund_no)
                    .one();
            stimulateOrderUtils.stimulate(one.getAoMergeOrderInfoId(), one.getAoMergeSuborderInfoId());
        } catch (Exception e) {
            log.error("微信退款回调加锁异常:", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public String updateOrderPayType(String mergeOrderNo,String payType,String currencySpecies){
        RLock lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_ORDER_LOOK_ + mergeOrderNo);
        try {
            while (!lock.tryLock()) {
                log.info("切换支付方式,总订单{}，已经有线程在操作总订单", mergeOrderNo);
                throw new BusinessException(MessageCode.SYSTEM_BUSY.getCode());
            }
            String payPath = aoCommonService.updateOrderPayType(mergeOrderNo,payType,currencySpecies);
           return payPath;
        } catch (BusinessException e) {
            throw e;
        }catch (Exception e) {
            log.error("切换支付方式加锁异常:", e.getMessage(), e);
            throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

}
