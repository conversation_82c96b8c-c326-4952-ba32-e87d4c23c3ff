package com.swcares.psi.irregular.flight.service;

import com.swcares.psi.irregular.flight.dto.PayOrderBaseDto;
import com.swcares.psi.irregular.flight.vo.PaxPayInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 赔付信息获取类
 * <AUTHOR>
 * @date 2020/9/11 11:00
 */
public interface PayInfoService {

    List<PaxPayInfoVO> getPayInfoAboutPax(String flightDate, String flightNo, String paxIdNo, String payType,String receiveStatus);
    Map<String,List<PaxPayInfoVO>> getPayInfoAboutAct(PayOrderBaseDto dto);

}
