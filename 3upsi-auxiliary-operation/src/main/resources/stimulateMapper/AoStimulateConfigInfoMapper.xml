<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.stimulate.mapper.AoStimulateConfigInfoMapper">
    <select id="pageList" resultType="com.swcares.psi.stimulate.vo.AoStimulateConfigInfoPageListVo">
        select * from (
            select
            asci.id,
            CASE
            WHEN asci.ORDER_TYPE = '1' THEN '一人多座'
            WHEN asci.ORDER_TYPE = '2' THEN '付费选座'
            WHEN asci.ORDER_TYPE = '3' THEN '逾重行李'
            WHEN asci.ORDER_TYPE = '4' THEN '贵宾室'
            when asci.ORDER_TYPE = '5' then '柜台升舱'
            when asci.ORDER_TYPE = '6' then '机上升舱'
            END AS orderTypeName,
            asci.ORDER_TYPE,
            asci.INTERNAL_TAX_RATE,
            asci.INTERNATIONAL_TAX_RATE,
            asci.STIMULATE,
            asci.PERSON_STIMULATE,
            asci.DEPARTMENT_STIMULATE,
            asci.STATUS AS status,
            CASE
            WHEN asci.ORDER_TYPE = '3' THEN 1
            when asci.ORDER_TYPE = '5' then 2
            when asci.ORDER_TYPE = '6' then 3
            WHEN asci.ORDER_TYPE = '2' THEN 4
            WHEN asci.ORDER_TYPE = '1' THEN 5
            WHEN asci.ORDER_TYPE = '4' THEN 6
            END AS orderValue,
            date_format(asci.CREATE_TIME, "%Y-%m-%d %H:%i:%s") as createTime,
           (select  CONCAT(TU_CNAME,tuno)   from employee  where tuno=asci.CREATE_USER limit 1) as createUser
            from AO_STIMULATE_CONFIG_INFO asci
            where asci.HISTORY = '0'
            <if test="dto.orderType != null and dto.orderType != ''">
                AND find_in_set(asci.ORDER_TYPE, #{dto.orderType})
            </if>

            <if test="dto.status != null and dto.status != ''">
                AND asci.STATUS=#{dto.status}
            </if>
        ) a
        order by a.orderValue asc
    </select>



</mapper>
