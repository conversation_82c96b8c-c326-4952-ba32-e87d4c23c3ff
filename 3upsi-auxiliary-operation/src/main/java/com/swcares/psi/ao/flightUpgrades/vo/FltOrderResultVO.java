package com.swcares.psi.ao.flightUpgrades.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：$.$
 * Description：(返回前端的结果类，base)
 * Copyright © 2022$ xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/2/14 10:34
 * @version v1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FltOrderResultVO<T> {
    @ApiModelProperty(value = "状态码")
   private String code;
    @ApiModelProperty(value = "返回信息")
    private String message;
    @ApiModelProperty(value = "返回数据集合")
    private List<T> resultData;
    @ApiModelProperty(value = "错误数据，后端报错排查用，前段不用管")
    private String errMessage;


}
