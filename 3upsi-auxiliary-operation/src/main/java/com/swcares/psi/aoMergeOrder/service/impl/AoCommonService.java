package com.swcares.psi.aoMergeOrder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.wxpay.sdk.WXPayUtil;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.mapper.CommonMapper;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradeConfigPriceDto;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgradesPriceConfig;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradesPriceConfigService;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeConfigPriceH5Vo;
import com.swcares.psi.ao.manySeats.entity.AoManySeatsPrice;
import com.swcares.psi.ao.manySeats.service.AoManySeatsPriceService;
import com.swcares.psi.ao.manySeats.vo.AoManySeatsPriceInfoVo;
import com.swcares.psi.ao.model.vo.AoViproomItemConfVO;
import com.swcares.psi.ao.sceneSeats.service.AoSceneSeatsPriceConfigService;
import com.swcares.psi.ao.sceneSeats.vo.AoSceneSeatsPriceConfigInfoVo;
import com.swcares.psi.ao.service.AoViproomItemConfService;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.ao.utils.DepartmentUtil;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeQueryDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderValidataDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSubOrderValidataDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSuborderInfoDto;
import com.swcares.psi.aoMergeOrder.dto.AoRefundDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashReturnPayOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashTurnList;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCounterUpgrade;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderDiscountList;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergePayOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeRefundOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeReportEventLog;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSubOrderJoinCashReturnPay;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.enums.AoMergeOrderType;
import com.swcares.psi.aoMergeOrder.exception.AoMergeOrderException;
import com.swcares.psi.aoMergeOrder.payHander.AliMergePayClientConfig;
import com.swcares.psi.aoMergeOrder.payHander.WxMergePayClientConfig;
import com.swcares.psi.aoMergeOrder.payHander.YeeMergeOrderPayConfiguration;
import com.swcares.psi.aoMergeOrder.service.AoMergeCashReturnPayOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.AoMergeReportEventLogService;
import com.swcares.psi.aoMergeOrder.service.AoMergeSubOrderJoinCashReturnPayService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCardTypeDiscountListService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCashTurnListService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCounterUpgradeService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeManySeatsService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderDiscountListService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOverweightBkgService;
import com.swcares.psi.aoMergeOrder.service.IAoMergePayOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeRefundOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSceneSeatsService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeViproomItemSellService;
import com.swcares.psi.aoMergeOrder.utils.I18nMsgUtils;
import com.swcares.psi.aoMergeOrder.utils.MergeOrderUtils;
import com.swcares.psi.base.base.SpringUtil;
import com.swcares.psi.base.data.api.entity.*;
import com.swcares.psi.base.data.api.enums.FlightTypeEnum;
import com.swcares.psi.base.data.service.*;
import com.swcares.psi.combine.constant.CacheConstants;
import com.swcares.psi.common.enums.OrderTypeEnum;
import com.swcares.psi.common.enums.PayTypeToPayCodeByOrderMergeEnum;
import com.swcares.psi.common.enums.PayTypeToPayCodeEnum;
import com.swcares.psi.common.utils.AoCommonUtils;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.aoMergeOrder.utils.SortUtils;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo;
import com.swcares.psi.aoMergeOrder.vo.CashTurnOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.CashTurnQueryVo;
import com.swcares.psi.aoMergeOrder.vo.CashTurnSaveVo;
import com.swcares.psi.aoMergeOrder.vo.InvoiceWarnOrderPaxInfoVo;
import com.swcares.psi.aoMergeOrder.vo.RefundOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.RefundSendTaskVo;
import com.swcares.psi.aoMergeOrder.vo.VaidatePayOrderVo;
import com.swcares.psi.base.data.api.common.SmsBusinessTypeEnum;
import com.swcares.psi.base.data.api.common.SmsSendType;
import com.swcares.psi.base.util.DateUtils;
import com.swcares.psi.collect.h5.yee.YeeProcessUtils;
import com.swcares.psi.collect.h5.yee.YeeRequestCommonParam;
import com.swcares.psi.collect.qrCode.alipay.AliNativeProcess;
import com.swcares.psi.collect.qrCode.alipay.AliRequestCommonParam;
import com.swcares.psi.collect.qrCode.alipay.AliReturnConstans;
import com.swcares.psi.collect.qrCode.wx.WxNativeProcess;
import com.swcares.psi.collect.qrCode.wx.WxRequestCommonParam;
import com.swcares.psi.collect.qrCode.wx.WxReturnConstans;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.retrievephone.PaxerPhoneUtil;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.common.worldpay.WorldpayUtil;
import com.swcares.psi.stimulate.utils.StimulateOrderUtils;
import com.travelsky.hub.model.peentity.CancelOrderOutPutBean;
import com.travelsky.hub.model.peentity.UpgOrderDetail;
import com.travelsky.hub.svc.ICheckInService;
import com.travelsky.hub.util.APISvcException;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class AoCommonService {

    @Autowired
    private FltPassengerRealInfoService fltPassengerRealInfoService;

    @Autowired
    private IAoMergeCardTypeDiscountListService aoMergeCardTypeDiscountListService;
    @Autowired
    private IAoMergeOrderDiscountListService aoMergeOrderDiscountListService;

    @Autowired
    AoMegerOrderNotifyLockService aoMegerOrderNotifyLockService;
    @Autowired
    private AoManySeatsPriceService aoManySeatsPriceService;

    @Autowired
    private AoSceneSeatsPriceConfigService aoSceneSeatsPriceConfigService;

    @Autowired
    private AoViproomItemConfService aoViproomItemConfService;

    @Autowired
    AoCounterUpgradesPriceConfigService aoCounterUpgradesPriceConfigService;

    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;
    @Autowired
    IAoMergeManySeatsService aoMergeManySeatsService;
    @Autowired
    IAoMergeSceneSeatsService aoMergeSceneSeatsService;
    @Autowired
    IAoMergeOverweightBkgService aoMergeOverweightBkgService;
    @Autowired
    IAoMergeViproomItemSellService aoMergeViproomItemSellService;
    @Autowired
    IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;

    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private AoMergeSubOrderSaveServiceImpl subOrderSaveService;
    @Autowired
    private IAoMergePayOrderInfoService aoMergePayOrderInfoService;
    @Autowired
    private IAoMergeRefundOrderInfoService aoMergeRefundOrderInfoService;

    @Autowired
    AoMergeCashReturnPayOrderInfoService aoMergeCashReturnPayOrderInfoService;

    @Autowired
    AoMergeSubOrderJoinCashReturnPayService aoMergeSubOrderJoinCashReturnPayService;
    @Autowired
    IAoMergeCashTurnListService aoMergeCashTurnListService;

    @Autowired
    private PaxerPhoneUtil paxerPhoneUtil;

    @Autowired
    private SmsTemplateService smsTemplateService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private ICheckInService iCheckInService;


    @Resource(name = "mergeOrderWxPayConfig")
    WxMergePayClientConfig wxpayClientConfig;
    @Resource(name = "mergeOrderWxTrunPayConfig")
    WxMergePayClientConfig mergeOrderWxTrunPayConfig;
    @Autowired
    WxNativeProcess wxNativeProcess;

    @Resource(name = "mergeOrderYeePayConfig")
    YeeMergeOrderPayConfiguration yeeConfig;


    @Autowired
    YeeProcessUtils yeeProcess;


    @Resource(name = "mergeOrderAliPayConfig")
    AliMergePayClientConfig alipayClientConfig;

    @Resource(name = "mergeOrderTrunAliPayConfig")
    AliMergePayClientConfig mergeOrderTrunAliPayConfig;
    @Autowired
    AliNativeProcess aliNativeProcess;

    @Autowired
    private WorldpayUtil worldpayUtil;

    @Value("${aopayconfig.worldpay.mergeNotify_front}")
    private String mergeNotifyFront;


    @Autowired
    IAoCityAreaCompanyConfigService aoCityAreaCompanyConfigService;

    @Autowired
    private AoMegerOrderNotifyService aoMegerOrderNotifyService;


    @Autowired
    private SysAirportInfoService sysAirportInfoService;

    @Autowired
    private AoMergeReportEventLogService aoMergeReportEventLogService;

    @Autowired
    private StimulateOrderUtils stimulateOrderUtils;
    @Autowired
    private CommonMapper dao;

    public AoMergeOrderValidataDto validata(AoMergeOrderValidataDto dto) {
        Map<String, List<AoMergeSubOrderValidataDto>> paxDataMap = new HashMap<>();
        //先按照旅客分组
        for (AoMergeSubOrderValidataDto ele : dto.getSubOrderList()) {
            if (paxDataMap.get(ele.getPayPaxId()) == null) {
                List<AoMergeSubOrderValidataDto> list = new ArrayList<>();
                list.add(ele);
                paxDataMap.put(ele.getPayPaxId(), list);
            } else {
                paxDataMap.get(ele.getPayPaxId()).add(ele);
            }
        }

        dto.setPaxSize(paxDataMap.size());
        //去重判断
        Map<String, Map<String, String>> filtrationRepeatDataMap = new HashMap<>();
        Iterator<Map.Entry<String, List<AoMergeSubOrderValidataDto>>> iterator = paxDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<AoMergeSubOrderValidataDto>> next = iterator.next();
            String key = next.getKey();
            if (filtrationRepeatDataMap.get(key) == null) {
                Map<String, String> hashMap = new HashMap<>();
                filtrationRepeatDataMap.put(key, hashMap);
            }
            List<AoMergeSubOrderValidataDto> value = next.getValue();
            for (AoMergeSubOrderValidataDto ele : value) {
                if (filtrationRepeatDataMap.get(key).get(ele.getOrderType()) != null) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getPaxName()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                } else {
                    filtrationRepeatDataMap.get(key).put(ele.getOrderType(), ele.getOrderType());
                }
            }
        }
        //价格校验
        validatePrice(dto);
        if (dto.getCodes() != null && dto.getCodes().size() > 0) {
            return dto;
        }
        //所有卡级别折扣  查出备用
        List<AoMergeCardTypegetListVo> cardTypeList = aoMergeCardTypeDiscountListService.getList(new AoMergeCardTypeQueryDto());
        //所有组合折扣  查出备用
        List<AoMergeOrderDiscountList> discountLists = aoMergeOrderDiscountListService.lambdaQuery()
                .eq(AoMergeOrderDiscountList::getIsDelete, AoMergeOrderDiscountList.DELETE_DISABLE)
                .eq(AoMergeOrderDiscountList::getStatus, AoMergeOrderDiscountList.STATUS_ENABLE)
                .list();
        //识别产品组合编码
        Iterator<Map.Entry<String, Map<String, String>>> filtrationRepeatDataIterator = filtrationRepeatDataMap.entrySet().iterator();
        BigDecimal totalAmount = new BigDecimal(0);
        while (filtrationRepeatDataIterator.hasNext()) {
            Map.Entry<String, Map<String, String>> next = filtrationRepeatDataIterator.next();
            Map<String, String> value = next.getValue();
            String join = StringUtils.join(Arrays.asList(value.keySet().toArray()), "");
            String itemsCode = SortUtils.orderTypeSort(join);
            //符合产品组合全部折扣 组合编码  折扣
            Map<String, BigDecimal> bigDecimalOrderDis = new HashMap<>();
            //识别产品组合折扣
            itepl:
            for (AoMergeOrderDiscountList dic : discountLists) {
                String[] split = dic.getItemsCode().split("");
                for (String s : split) {
                    if (!itemsCode.contains(s)) {
                        continue itepl;
                    }
                }
                bigDecimalOrderDis.put(dic.getItemsCode(), new BigDecimal(dic.getDiscount()));
            }

            // 识别卡级别加组合折扣最优惠金额
            BigDecimal paxCardTypeDisCountPirce = null;
            String paxCardType = null;
            String paxCardTypeDisCount = null;
            List<AoMergeSubOrderValidataDto> aoMergeSubOrderValidataDtos = paxDataMap.get(next.getKey());
            for (String ctele : aoMergeSubOrderValidataDtos.get(0).getCardType()) {
                for (AoMergeCardTypegetListVo ele : cardTypeList) {
                    if (ele.getCardType().equals(ctele)) {
                        BigDecimal bigDecimal = computeAmount(ctele, new BigDecimal(ele.getDiscount()), bigDecimalOrderDis, aoMergeSubOrderValidataDtos);
                        if (paxCardTypeDisCountPirce == null || paxCardTypeDisCountPirce.compareTo(bigDecimal) > 0) {
                            paxCardTypeDisCountPirce=bigDecimal;
                            paxCardTypeDisCount = ele.getDiscount();
                            paxCardType = ctele;
                            break;
                        }
                    }
                }
            }
            BigDecimal bigDecimal = computeAmount(paxCardType, new BigDecimal(paxCardTypeDisCount), bigDecimalOrderDis, aoMergeSubOrderValidataDtos);
            totalAmount = totalAmount.add(bigDecimal);

        }
        dto.setInitOrderPrice(dto.getOrderPrice());
        dto.setOrderPrice(totalAmount.toString());
        BigDecimal subtract = new BigDecimal(dto.getInitOrderPrice()).subtract(totalAmount);
        dto.setDiscountPrice(subtract.toString());
        return dto;
    }


    //价格校验
    private void validatePrice(AoMergeOrderValidataDto dto) {
        List<AoMergeSubOrderValidataDto> subOrderList = dto.getSubOrderList();
        String currencySpecies = dto.getCurrencySpecies();
        BigDecimal orderPrice = new BigDecimal(0);
        for (AoMergeSubOrderValidataDto ele : subOrderList) {
            if (AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS.equals(ele.getOrderType())) {
                AoManySeatsPriceInfoVo one = aoManySeatsPriceService.getOne(ele.getOrg(), ele.getDst(), AoManySeatsPrice.STATUS_ENABLE);
                if (one == null) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                BigDecimal priceConfig = null;
                if ("CNY".equals(currencySpecies)) {
                    priceConfig = new BigDecimal(one.getCnyPrice());
                } else {
                    AoManySeatsPriceInfoVo.PriceTypeClass price = one.getPrice(currencySpecies);
                    if (price == null) {
                        AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                        i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                        i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                        i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                        dto.getCodes().add(i18nCodeInfo);
                        continue;
                    }
                    Integer currencyPrice = price.getCurrencyPrice();
                    priceConfig = new BigDecimal(currencyPrice);
                }

                BigDecimal payPrice = new BigDecimal(ele.getOrderPrice());
                BigDecimal seatNumber = new BigDecimal(ele.getAoMergeManySeatsDetailDto().getSeatsNumber());

                BigDecimal multiply = seatNumber.multiply(priceConfig);
                if (multiply.compareTo(payPrice) != 0) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConformMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getPaxName() + ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    i18nCodeInfo.setErrorCode(I18nMsgUtils.ERROR_CODE_PRICE);
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }

                orderPrice = orderPrice.add(multiply);

            } else if (AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS.equals(ele.getOrderType())) {
                List<AoSceneSeatsPriceConfigInfoVo> priceInfo = aoSceneSeatsPriceConfigService.getPriceInfo();
                AoSceneSeatsPriceConfigInfoVo one = null;
                for (AoSceneSeatsPriceConfigInfoVo p : priceInfo) {
                    if (p.getCode().equals(ele.getAoMergeSceneSeatsDetailDtos().getSeatsType())) {
                        one = p;
                        break;
                    }
                }
                if (one == null) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                BigDecimal priceConfig = null;
                if ("CNY".equals(currencySpecies)) {
                    priceConfig = new BigDecimal(one.getCnyPrice());
                } else {
                    AoSceneSeatsPriceConfigInfoVo.PriceTypeClass price = one.getPrice(currencySpecies);
                    if (price == null) {
                        AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                        i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                        i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                        i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                        dto.getCodes().add(i18nCodeInfo);
                        continue;
                    }
                    priceConfig = new BigDecimal(price.getCurrencyPrice());
                }
                BigDecimal payPrice = new BigDecimal(ele.getOrderPrice());
                if (priceConfig.compareTo(payPrice) != 0) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConformMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getPaxName() + ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    i18nCodeInfo.setErrorCode(I18nMsgUtils.ERROR_CODE_PRICE);
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                orderPrice = orderPrice.add(priceConfig);
            } else if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
                orderPrice = orderPrice.add(new BigDecimal(ele.getOrderPrice()));
            } else if (AoMergeCons.ORDER_TYPE_CODE_VIP.equals(ele.getOrderType())) {
                List<AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto> aoMergeViproomItemSellDetailDtos = ele.getAoMergeViproomItemSellDetailDtos();
                if (aoMergeViproomItemSellDetailDtos.size() < 1) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(MessageCode.UN_KNOWN.getCode());
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto detailDto = aoMergeViproomItemSellDetailDtos.get(0);
                String portCode = detailDto.getPortCode();
                List<AoViproomItemConfVO> list = aoViproomItemConfService.item(portCode);
                if (list.size() < 1) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                AoViproomItemConfVO conf = null;
                for (AoViproomItemConfVO p : list) {
                    if (p.getId().equals(detailDto.getItemId())) {
                        conf = p;

                        for (AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto vipDetail : aoMergeViproomItemSellDetailDtos) {
                            vipDetail.setItemName(p.getName());
                        }

                        break;
                    }
                }
                if (conf == null) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }

                AoViproomItemConfVO.PriceTypeClass price = conf.getPrice(currencySpecies);
                if (price == null) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                BigDecimal priceConfig = new BigDecimal(price.getCurrencyPrice());
                BigDecimal payPrice = new BigDecimal(ele.getOrderPrice());
                if (priceConfig.compareTo(payPrice) != 0) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConformMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getPaxName() + ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    i18nCodeInfo.setErrorCode(I18nMsgUtils.ERROR_CODE_PRICE);
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                orderPrice = orderPrice.add(priceConfig);
            } else if (AoMergeCons.ORDER_TYPE_CODE_UPGRADE.equals(ele.getOrderType())) {
                AoCounterUpgradeConfigPriceDto upDto = new AoCounterUpgradeConfigPriceDto();
                upDto.setOrgPlace(ele.getOrg());
                upDto.setDstPlace(ele.getDst());
                upDto.setStatus(AoCounterUpgradesPriceConfig.STATUS_ENABLE);
                AoCounterUpgradeConfigPriceH5Vo one = aoCounterUpgradesPriceConfigService.getPrice(upDto);
                if (one == null) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }
                BigDecimal priceConfig = null;
                if ("CNY".equals(currencySpecies)) {
                    priceConfig = new BigDecimal(one.getCurrentPrice());
                } else {
                    AoCounterUpgradeConfigPriceH5Vo.PriceTypeClass price = one.getPrice(currencySpecies);
                    if (price == null) {
                        AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                        i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConfigMessageCode().getCode());
                        i18nCodeInfo.setParam(new String[]{ele.getOrg() + "-" + ele.getDst()});
                        i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                        dto.getCodes().add(i18nCodeInfo);
                        continue;
                    }
                    priceConfig = new BigDecimal(price.getCurrencyPrice());
                }
                BigDecimal payPrice = new BigDecimal(ele.getOrderPrice());
                if (priceConfig.compareTo(payPrice) != 0) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(AoMergeOrderType.getType(ele.getOrderType()).getPriceConformMessageCode().getCode());
                    i18nCodeInfo.setParam(new String[]{ele.getPaxName() + ele.getOrg() + "-" + ele.getDst()});
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    i18nCodeInfo.setErrorCode(I18nMsgUtils.ERROR_CODE_PRICE);
                    dto.getCodes().add(i18nCodeInfo);
                    continue;
                }

                //2022-02-18 增加了判断升舱时间的问题
                FltFlightRealInfo flightRealInfo = null;
                Date currentDate = new Date(System.currentTimeMillis());
                if (org.apache.commons.lang.StringUtils.isNotBlank(ele.getOrg()) && org.apache.commons.lang.StringUtils.isNotBlank(ele.getDst()) && org.apache.commons.lang.StringUtils.isNotBlank(ele.getFlightDate())
                        && org.apache.commons.lang.StringUtils.isNotBlank(ele.getFlightNo())) {
                    flightRealInfo = fltFlightRealInfoService.getOne(Wrappers.<FltFlightRealInfo>lambdaQuery()
                            .eq(FltFlightRealInfo::getFlightNumber, ele.getFlightNo())
                            .eq(FltFlightRealInfo::getFlightDate, ele.getFlightDate())
                            .eq(FltFlightRealInfo::getOrg, ele.getOrg())
                            .eq(FltFlightRealInfo::getDst, ele.getDst())
                    );
                } else {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(MessageCode.DATA_NOT_EXIST.getCode());
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                }
                if (null != flightRealInfo) {
                    Boolean isGat = false;
                    List<SysDict> gatList = sysDictService.lambdaQuery().eq(SysDict::getDataTypeCode, "GAT")
                            .eq(SysDict::getDataStatus, "0")
                            .list();
                    for (SysDict gat : gatList) {
                        if (flightRealInfo.getOrg().equals(gat.getDataCode()) || flightRealInfo.getDst().equals(gat.getDataCode())) {
                            isGat = true;
                            break;
                        }
                    }

                    if (flightRealInfo.getFlightType().equals("D") && !isGat) {
                        if (null != flightRealInfo.getEtd()) {
                            Date startDate = new Date(flightRealInfo.getEtd().getTime() - 180 * 60 * 1000);
                            Date endDate = new Date(flightRealInfo.getEtd().getTime());
                            if (currentDate.before(startDate) || currentDate.after(endDate)) {
                                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                                i18nCodeInfo.setCode(MessageCode.UPGRADE_PE_TIME_ERROR.getCode());
                                i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                                i18nCodeInfo.setErrorCode(I18nMsgUtils.UPGRADE_TIME_ERROR);
                                dto.getCodes().add(i18nCodeInfo);
                            }
                        } else if (null != flightRealInfo.getStd()) {
                            Date startDate = new Date(flightRealInfo.getStd().getTime() - 180 * 60 * 1000);
                            Date endDate = new Date(flightRealInfo.getStd().getTime());
                            if (currentDate.before(startDate) || currentDate.after(endDate)) {
                                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                                i18nCodeInfo.setCode(MessageCode.UPGRADE_PE_TIME_ERROR.getCode());
                                i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                                i18nCodeInfo.setErrorCode(I18nMsgUtils.UPGRADE_TIME_ERROR);

                                dto.getCodes().add(i18nCodeInfo);
                            }
                        }
                    } else {
                        if (null != flightRealInfo.getEtd()) {
                            Date startDate = new Date(flightRealInfo.getEtd().getTime() - 360 * 60 * 1000);
                            if (currentDate.before(startDate) && currentDate.before(flightRealInfo.getEtd())) {
                                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                                i18nCodeInfo.setCode(MessageCode.UPGRADE_PE_TIME_ERROR.getCode());
                                i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                                i18nCodeInfo.setErrorCode(I18nMsgUtils.UPGRADE_TIME_ERROR);
                                dto.getCodes().add(i18nCodeInfo);
                            }
                        } else if (null != flightRealInfo.getStd()) {
                            Date startDate = new Date(flightRealInfo.getStd().getTime() - 360 * 60 * 1000);
                            if (currentDate.before(startDate) && currentDate.before(flightRealInfo.getStd())) {
                                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                                i18nCodeInfo.setCode(MessageCode.UPGRADE_PE_TIME_ERROR.getCode());
                                i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                                i18nCodeInfo.setErrorCode(I18nMsgUtils.UPGRADE_TIME_ERROR);
                                dto.getCodes().add(i18nCodeInfo);

                            }
                        }
                    }
                } else {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(MessageCode.PAD_FLIGHT_NOT_EXIST.getCode());
                    i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                    dto.getCodes().add(i18nCodeInfo);
                }
                orderPrice = orderPrice.add(priceConfig);

            } else {
                log.info("{}存在未知订单类型>>{}", ele.getPaxName(), ele.getOrderType());
                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                i18nCodeInfo.setCode(MessageCode.UN_KNOWN.getCode());
                i18nCodeInfo.setProductId(ele.getPayPaxId() + "_" + ele.getOrderType());
                dto.getCodes().add(i18nCodeInfo);
                continue;
            }
        }
        if (dto.getCodes().size() == 0) {
            dto.setOrderPrice(orderPrice.setScale(0, BigDecimal.ROUND_UP).toString());
        }
    }

    /**
     * 一个旅客 单卡级别+组合订单金额计算
     *
     * @param paxTypeDiscount
     * @param bigDecimalOrderDis
     * @param paxOrderData
     * @param paxCardType
     * @return
     */
    private BigDecimal computeAmount(String paxCardType, BigDecimal paxTypeDiscount, Map<String, BigDecimal> bigDecimalOrderDis, List<AoMergeSubOrderValidataDto> paxOrderData) {
        BigDecimal orderDis = new BigDecimal(0);//单名旅客实付金额
        String minimumDiscount = null;//最低折扣组合
        String miniOrderDiscount = null;//最低折扣组合折扣率
        BigDecimal itemOrderDis = null;//最低折扣组合实付总金额
        BigDecimal miniDiscount = new BigDecimal("1");//旅客实际享受的最低折扣
        //筛选最低折扣
        if (bigDecimalOrderDis.size() != 0) {
            Iterator<Map.Entry<String, BigDecimal>> iterator = bigDecimalOrderDis.entrySet().iterator();
            while (iterator.hasNext()) {
                BigDecimal cprice = null;//当前折扣金额

                Map.Entry<String, BigDecimal> next = iterator.next();
                String key = next.getKey();
                BigDecimal value = next.getValue();
                for (AoMergeSubOrderValidataDto po : paxOrderData) {
                    BigDecimal poPrice = new BigDecimal(po.getOrderPrice());
                    //乘以卡级别折扣和订单组合折扣
                    if (key.contains(po.getOrderType())) {
                        poPrice = poPrice.multiply(value);
                    }
                    poPrice = poPrice.multiply(paxTypeDiscount);
                    if (cprice == null) {
                        cprice = poPrice;
                    } else {
                        cprice = cprice.add(poPrice);
                    }

                }

                if (itemOrderDis == null || itemOrderDis.compareTo(cprice) > 0) {
                    miniDiscount = value.multiply(paxTypeDiscount);
                    miniOrderDiscount = value.toString();
                    itemOrderDis = cprice;
                    minimumDiscount = key;
                }
            }
        } else {
            BigDecimal cprice = null;//当前折扣金额
            for (AoMergeSubOrderValidataDto po : paxOrderData) {
                BigDecimal poPrice = new BigDecimal(po.getOrderPrice());
                poPrice = poPrice.multiply(paxTypeDiscount);
                if (cprice == null) {
                    cprice = poPrice;
                } else {
                    cprice = cprice.add(poPrice);
                }

            }
            miniDiscount = paxTypeDiscount.multiply(new BigDecimal("1"));
            itemOrderDis = cprice;
        }

        //设置折扣金额
        for (AoMergeSubOrderValidataDto po : paxOrderData) {
            BigDecimal practicalOrderPrice = new BigDecimal("0"); //子订单实收金额
            po.setIdentityDiscountDetails("身份折扣-" + (paxTypeDiscount.multiply(new BigDecimal(10)).toString()) + "折"+(miniOrderDiscount==null?"":"|产品打包折扣"+new BigDecimal(miniOrderDiscount).multiply(new BigDecimal(10)).toString()+"折"));
            po.setItemsCode(minimumDiscount);
            po.setDisCountCardType(paxCardType);
            //设置实际享受的卡级别优惠
            if (AoMergeCons.ORDER_TYPE_CODE_VIP.equals(po.getOrderType())) {
                for (AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto xx : po.getAoMergeViproomItemSellDetailDtos()) {
                    xx.setCardType(paxCardType);
                }
            }
            if (AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS.equals(po.getOrderType())) {
                po.getAoMergeManySeatsDetailDto().setCardType(paxCardType);
            }
            if (AoMergeCons.ORDER_TYPE_CODE_UPGRADE.equals(po.getOrderType())) {
                po.getAoMergeCounterUpgradeDetailDto().setCardType(paxCardType);
            }
            if (AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS.equals(po.getOrderType())) {
                po.getAoMergeSceneSeatsDetailDtos().setCardType(paxCardType);
            }
            if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(po.getOrderType())) {
                po.getAoMergeOverweightBkg().setCardType(paxCardType);
            }
            if (po.getOrderPrice().equals("0")) {
                po.setPracticalOrderPrice("0");
                po.setDiscountTotalPrice("0");
                po.setDiscount("10");
            } else {
                BigDecimal poPrice = new BigDecimal(po.getOrderPrice());
                BigDecimal discount = new BigDecimal(1); //默认折扣率
                if (StringUtils.isNotEmpty(minimumDiscount) && minimumDiscount.contains(po.getOrderType())) {
                    practicalOrderPrice = practicalOrderPrice.add(poPrice.multiply(bigDecimalOrderDis.get(minimumDiscount)).setScale(2, BigDecimal.ROUND_UP));
                    AoMergeSubOrderValidataDto.DiscountDetail discountDetail = new AoMergeSubOrderValidataDto.DiscountDetail();
                    discountDetail.setDisCountType(AoMergeSubOrderValidataDto.DiscountDetail.PACK);
                    discountDetail.setDiscountPrice(poPrice.subtract(practicalOrderPrice).toString());
                    po.getDiscountDetail().add(discountDetail);
                    discount = bigDecimalOrderDis.get(minimumDiscount);

                } else {
                    practicalOrderPrice = practicalOrderPrice.add(poPrice);
                }

                //设置订单折扣
                if (AoMergeCons.ORDER_TYPE_CODE_VIP.equals(po.getOrderType())) {
                    for (AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto xx : po.getAoMergeViproomItemSellDetailDtos()) {
                        xx.setOrderDiscountDetails(po.getIdentityDiscountDetails());
                    }
                }
                if (AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS.equals(po.getOrderType())) {
                    po.getAoMergeManySeatsDetailDto().setOrderDiscountDetails(po.getIdentityDiscountDetails());
                }
                if (AoMergeCons.ORDER_TYPE_CODE_UPGRADE.equals(po.getOrderType())) {
                    po.getAoMergeCounterUpgradeDetailDto().setOrderDiscountDetails(po.getIdentityDiscountDetails());
                }
                if (AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS.equals(po.getOrderType())) {
                    po.getAoMergeSceneSeatsDetailDtos().setOrderDiscountDetails(po.getIdentityDiscountDetails());
                }
                if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(po.getOrderType())) {
                    po.getAoMergeOverweightBkg().setOrderDiscountDetails(po.getIdentityDiscountDetails());
                }

                if (paxTypeDiscount != null) {
                    BigDecimal paxTypeDiscountPrice = practicalOrderPrice.multiply(paxTypeDiscount).setScale(2, BigDecimal.ROUND_UP);
                    discount = discount.multiply(paxTypeDiscount);

                    AoMergeSubOrderValidataDto.DiscountDetail discountDetail = new AoMergeSubOrderValidataDto.DiscountDetail();
                    discountDetail.setDisCountType(AoMergeSubOrderValidataDto.DiscountDetail.DIFFERENCE);
                    discountDetail.setDiscountPrice(practicalOrderPrice.subtract(paxTypeDiscountPrice).toString());
                    po.getDiscountDetail().add(discountDetail);
                    orderDis = orderDis.add(paxTypeDiscountPrice);
                    po.setDiscountTotalPrice(poPrice.subtract(paxTypeDiscountPrice).toString());
                    po.setPracticalOrderPrice(paxTypeDiscountPrice.toString());
                } else {
                    orderDis = orderDis.add(practicalOrderPrice);
                    po.setDiscountTotalPrice(poPrice.subtract(practicalOrderPrice).toString());
                    po.setPracticalOrderPrice(practicalOrderPrice.toString());
                }
                po.setDiscount(discount.toString());
            }
        }

        return orderDis;
    }


    /**
     * 创建合并订单数据
     *
     * @param dto
     * @return
     * @Autowired IAoMergeOrderInfoService aoMergeOrderInfoService;
     * @Autowired IAoMergeSuborderInfoService aoMergeSuborderInfoService;
     */


    @Transactional(rollbackFor = Exception.class)
    public AoMergeOrderValidataDto saveOrder(AoMergeOrderValidataDto dto) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        String deptId = authentication.getDeptId();
        LocalDateTime now = LocalDateTime.now();
        validata(dto);
        //确定航站区域归属
        AoCityAreaCompanyConfig cityArea = aoCityAreaCompanyConfigService.lambdaQuery()
                .eq(AoCityAreaCompanyConfig::getCityCode, dto.getServicePort())
                .one();
        AoMergeOrderInfo mergeOrderInfo = null;
        if (dto.getCodes() == null || dto.getCodes().size() < 1) {
            try {
                mergeOrderInfo = new AoMergeOrderInfo();
                mergeOrderInfo.setCreateNo(userNo);
                mergeOrderInfo.setCreateUser(realName);
                DepartmentEntityService bean = SpringUtil.getBean(DepartmentEntityService.class);
                DepartmentEntity byId = bean.getById(deptId);
                if (byId != null && StringUtils.isNotEmpty(byId.getDepLocate())) {
                    int i = byId.getDepLocate().indexOf("-");
                    if(i>=0){
                        String substring = byId.getDepLocate().substring(i+1);
                        String replace = substring.replace("-", "/");
                        mergeOrderInfo.setCreateUserDepartment(replace);
                    }else{
                        mergeOrderInfo.setCreateUserDepartment(byId.getDepLocate());
                    }

                }
                mergeOrderInfo.setCreateDate(now);
                mergeOrderInfo.setServicePort(dto.getServicePort());
                mergeOrderInfo.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_UNPAY);
                String orderNo = AoCommonUtils.getMergeOrderNo();
                mergeOrderInfo.setOrderNo(orderNo);
                mergeOrderInfo.setAreaCompany(cityArea.getAreaCompany());
                SysAirportInfoEntity one = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, dto.getServicePort()).one();
                if (one == null) {
                    mergeOrderInfo.setServicePortType("d");
                } else if (StringUtils.isEmpty(one.getType())) {
                    mergeOrderInfo.setServicePortType("d");
                } else {
                    mergeOrderInfo.setServicePortType(one.getType());
                }
                aoMergeOrderInfoService.save(mergeOrderInfo);
                dto.setOrderNo(orderNo);
                List<AoMergeSubOrderValidataDto> subOrderList = dto.getSubOrderList();
                CountDownLatch latch = new CountDownLatch(subOrderList.size());
                for (AoMergeSubOrderValidataDto ele : subOrderList) {
                    subOrderSaveService.subOrderSave(ele, dto, mergeOrderInfo, latch);
                }
                latch.await();
            } catch (Exception e) {
                log.error("合并订单保存报错:{}", e.getMessage(), e);
                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                i18nCodeInfo.setCode(MessageCode.UN_KNOWN.getCode());
                dto.getCodes().add(i18nCodeInfo);
            }

            try {
                AoMergePayOrderInfo aoMergePayOrderInfo = new AoMergePayOrderInfo();
                aoMergePayOrderInfo.setAoMergeOrderInfoId(mergeOrderInfo.getId());

                aoMergePayOrderInfo.setPayOrderNo(mergeOrderInfo.getOrderNo());
                aoMergePayOrderInfo.setCurrencySpecies(dto.getCurrencySpecies());
                aoMergePayOrderInfo.setCurrencySpeciesName(dto.getCurrencySpeciesName());
                aoMergePayOrderInfo.setPayType(dto.getPayType());
                aoMergePayOrderInfo.setCreateDate(now);
                aoMergePayOrderInfo.setPrice(new BigInteger(AoUtils.yuanToPoints(dto.getOrderPrice()).toString()));
                String precreate = null;

                //现金支付
                if (dto.getPayType().equals(AoOrderConstant.ORDER_CHARGE_TYPE_CASH) || dto.getPayType().equals(AoOrderConstant.ORDER_CHARGE_TYPE_POS)) {
                    aoMergePayOrderInfo.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_UNPAY);
                    aoMergePayOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT);
                    aoMergePayOrderInfoService.save(aoMergePayOrderInfo);
                    mergeOrderInfo.setOrderPayId(aoMergePayOrderInfo.getId());
                    aoMergeOrderInfoService.saveOrUpdate(mergeOrderInfo);//回填支付单Id
                } else {

                    String mergeOrderBankOrderNo = AoCommonUtils.getMergeOrderBankOrderNo(
                            mergeOrderInfo.getServicePortType(),
                            PayTypeToPayCodeByOrderMergeEnum.findEnumByPayType(dto.getPayType()).getPayTypeOrderCode()
                    );

                    aoMergePayOrderInfo.setBankOrderNo(mergeOrderBankOrderNo);
                    //线上支付方式
                    if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(dto.getPayType()) && "CNY".equals(dto.getCurrencySpecies())) {
                        WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
                        wxrequestParam.setOutTradeNo(mergeOrderBankOrderNo);
                        wxrequestParam.setTotalFee(AoUtils.yuanToPoints(dto.getOrderPrice()).toString());
                        wxrequestParam.setBody(wxrequestParam.getBody());
                        precreate = wxNativeProcess.precreate(wxrequestParam, wxpayClientConfig);
                    }
                    if (AoMergeCons.ORDER_CHARGE_TYPE_YEE.equals(dto.getPayType()) && "CNY".equals(dto.getCurrencySpecies())) {
                        YeeRequestCommonParam param = new YeeRequestCommonParam();
                        param.setAmount(dto.getOrderPrice());
                        param.setOrderNo(mergeOrderBankOrderNo);
                        param.setGoodsName(param.getGoodsName());
                        precreate = yeeProcess.precreate(param, yeeConfig);
                    }
                    if (AoMergeCons.ORDER_CHARGE_TYPE_ALIPAY.equals(dto.getPayType()) && "CNY".equals(dto.getCurrencySpecies())) {
                        AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                        requestCommonParam.setOutTradeNo(mergeOrderBankOrderNo);
                        requestCommonParam.setTotalAmount(dto.getOrderPrice());
                        requestCommonParam.setSubject(requestCommonParam.getSubject());
                        precreate = aliNativeProcess.precreate(requestCommonParam, alipayClientConfig);

                    }
                    if (AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY.equals(dto.getPayType())) {
                        precreate = worldpayUtil.orderCreateHosted(mergeOrderBankOrderNo, dto.getCurrencySpecies(), dto.getOrderPrice(), "ALL");
                        if (StringUtils.isNotEmpty(precreate)) {
                            precreate = precreate + mergeNotifyFront;
                        }
                        log.info("合并订单worldPay三方返回地址{}", precreate);

                    }
                    if (StringUtils.isNotEmpty(precreate)) {
                        aoMergePayOrderInfo.setPayPath(precreate);
                        aoMergePayOrderInfo.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_UNPAY);
                        aoMergePayOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT);
                        aoMergePayOrderInfoService.save(aoMergePayOrderInfo);
                        mergeOrderInfo.setOrderPayId(aoMergePayOrderInfo.getId());
                        aoMergeOrderInfoService.saveOrUpdate(mergeOrderInfo);//回填支付单Id
                        dto.setPayPath(precreate);
                    } else {
                        log.info("合并订单支付订单建单失败 支付类型-{} 币种-{} 金额-{}",dto.getPayType(),dto.getCurrencySpecies(),dto.getOrderPrice());
                        AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                        i18nCodeInfo.setCode(MessageCode.CREATE_PAY_ORDER_FAIL.getCode());
                        dto.getCodes().add(i18nCodeInfo);
                        //todo 支付订单创建失败
                    }
                }

            } catch (Exception e) {
                log.error("合并订单保存报错:{}", e.getMessage(), e);
                AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                i18nCodeInfo.setCode(MessageCode.UN_KNOWN.getCode());
                dto.getCodes().add(i18nCodeInfo);
            }


        }
        if (dto.getCodes().size() > 0) {
            List<AoMergeOrderValidataDto.ReasonRefusal> reasonRefusals = I18nMsgUtils.messageConvert(dto.getCodes());
            dto.setReasonRefusal(reasonRefusals);
            throw new AoMergeOrderException(dto);
        }
        return dto;
    }


    /**
     * 支付结果验证
     *
     * @param lockOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void vaidatePayResults(List<VaidatePayOrderVo> lockOrder) {


        LocalDateTime now = LocalDateTime.now();
        for (VaidatePayOrderVo ele : lockOrder) {
            AoMergeOrderInfo byId = aoMergeOrderInfoService.getById(ele.getOrderId());
            if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(ele.getPayType())) {
                WxRequestCommonParam param = new WxRequestCommonParam();
                param.setOutTradeNo(ele.getBankOrderNo());
                Map<String, String> query = wxNativeProcess.query(param, wxpayClientConfig);
                if (query == null) {
                    log.info("微信支付结果查询失败,订单号:{}", ele.getOrderNo());
                    continue;
                }
                log.info("微信支付结果验证,订单号:{}请求结果:{},订单状态:{}", ele.getOrderNo(), query.get("return_code"), query.get("trade_state"));
                //订单不存在不做处理
                if (WxReturnConstans.ORDER_NOT_EXIST.equals(query.get("err_code"))) {
                    log.info("微信支付订单不存在,订单号:{}", ele.getBankOrderNo());
                    continue;
                }
                //查询结果为支付成功
                if (WxReturnConstans.REQUEST_SUCCESS.equals(query.get("return_code")) && WxReturnConstans.RESULT_CODE_SUCCESS.equals(query.get("result_code")) && WxReturnConstans.RESULT_CODE_SUCCESS.equals(query.get("trade_state"))) {
                    aoMegerOrderNotifyService.paySuccess(byId, DateUtils.parseStringToLocalDateTime(query.get("time_end"), DateUtils.YYYYMMDDHHMMSS), now, "订单支付结果查询定时任务");
                    //默认支付失败
                } else {
                    //支付失败不作处理
                    log.info("微信支付失败,订单号:{}", ele.getOrderNo());

                }

            } else if (AoMergeCons.ORDER_CHARGE_TYPE_YEE.equals(ele.getPayType())) {
                YopResponse query = yeeProcess.query(ele.getBankOrderNo(), yeeConfig);
                JSONObject jsonObject = JSON.parseObject(query.getStringResult());
                log.info("易宝订单查询:{}", jsonObject.toString());
                String retCode = jsonObject.get("retCode").toString();
                if ("1001".equals(retCode)) {
                    log.info("定时任务,易宝订单不存在,订单号{}", ele.getOrderNo());
                    continue;
                }
                String orderStatus = jsonObject.get("orderStatus").toString();
                //支付成功
                if ("SUCCESS".equals(orderStatus)) {
                    aoMegerOrderNotifyService.paySuccess(byId, now, now, "订单支付结果查询定时任务");
                }
                //支付失败
                else {
                    //支付失败不作处理
                    log.info("易宝支付失败,订单号:{}", ele.getOrderNo());
                }

            } else {
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(ele.getBankOrderNo());
                AlipayTradeQueryResponse query = aliNativeProcess.query(requestCommonParam, alipayClientConfig);
                if (query == null) {
                    log.info("支付宝支付结果查询失败,订单号:{}", ele.getOrderNo());
                    continue;
                }
                log.info("支付宝支付结果验证,订单号:{}请求结果:{},支付结果:{}", ele.getOrderNo(), query.isSuccess(), query.getTradeStatus());
                log.info("支付宝支付结果查询返回结果:" + JSON.toJSONString(query));
                //查询请求发送成功
                if (query.isSuccess()) {

                    //等待支付状态不与处理（扫了二维码却没有支付的情况）
                    if (AliReturnConstans.WAIT_BUYER_PAY.equals(query.getTradeStatus())) {
                        continue;
                    }
                    //查询结果为支付成功或者（交易结束，不可退款）
                    if (AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus())) {
                        LocalDateTime dateToLocalDateTime = DateUtils.getDateToLocalDateTime(query.getSendPayDate());
                        aoMegerOrderNotifyService.paySuccess(byId, dateToLocalDateTime, now, "订单支付结果查询定时任务");
                    }
                    //未付款交易超时关闭，或支付完成后全额退款  不与处理  已退款交予退款定时任务处理
                    else if (AliReturnConstans.TRADE_CLOSED.equals(query.getTradeStatus())) {
                        log.info("支付宝支付异常，关闭>>{}", ele.getOrderNo());
                        continue;
                    }
                    //支付失败
                    else {
                        log.info("支付宝支付失败>>>{}", ele.getOrderNo());
                    }
                } else {
                    //交易不存在(包含生成了二维码却没有扫码支付的情况(过期了))
                    if (AliReturnConstans.ORDER_NOT_EXIST.equals(query.getSubCode())) {
                        log.info("支付宝支付订单不存在>>>{}", ele.getOrderNo());
                        continue;
                    }
                }
            }
        }
    }


    /**
     * 退款结果验证
     *
     * @param ele
     */
    @Transactional(rollbackFor = Exception.class)
    public void vaidateRefundResults(RefundOrderTaskVo ele) {
            log.info("订单开始退款{}", ele.getRefundNo());
            if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(ele.getPayType())) {
                WxRequestCommonParam param = new WxRequestCommonParam();
                param.setOutTradeNo(ele.getBankOrderNo());
                param.setOutRequestNo(ele.getRefundNo());
                Map<String, String> query = wxNativeProcess.refund_query(param, wxpayClientConfig);
                if (query == null) {
                    log.info("微信退款结果查询失败,订单号:{}", ele.getRefundNo());
                    throw new RuntimeException();
                }
                String return_code = query.get("return_code");
                String result_code = query.get("result_code");
                log.info("微信退款验证请求结果:{},转账单号:{},返回结果{}", return_code, ele.getRefundNo(), result_code);
                if (WxReturnConstans.REQUEST_SUCCESS.equals(return_code)) {
                    //退款结果为成功
                    if (WxReturnConstans.RESULT_CODE_SUCCESS.equals(result_code)) {
                        log.info("微信退款成功:" + ele.getRefundNo());
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)
                                .set(AoMergeRefundOrderInfo::getRefundEndTime, LocalDateTime.now())
                                .update();
                        aoMergeSuborderInfoService.lambdaUpdate()
                                .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                                .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND)
                                .update();

                        AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
                        aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
                        aoMergeReportEventLog.setAoMergeSuborderInfoId(ele.getSubOrderId());
                        aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
                        aoMergeReportEventLog.setEventExplain("退款完成");
                        aoMergeReportEventLogService.save(aoMergeReportEventLog);

                        refundStatusUpdate(ele.getMergeOrderId());


                    }else
                    if (WxReturnConstans.RESULT_CODE_FAIL.equals(result_code)) {
                        String err_code_des = query.get("err_code_des");
                        String err_code = query.get("err_code");
                        log.info("微信退款失败:" + ele.getRefundNo() + ">>" + "{}-退款失败原因:{},错误代码:{}", err_code_des, err_code);
                        //不存在的退款  不做操作
                        if (WxReturnConstans.REFUND_NOT_EXIST.equals(err_code)) {
                            return;
                        }
                        //退款失败
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                                .update();
                        aoMergeSuborderInfoService.lambdaUpdate()
                                .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                                .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                                .update();
                    }

                }

            } else if (AoMergeCons.ORDER_CHARGE_TYPE_YEE.equals(ele.getPayType())) {
                YeeRequestCommonParam param = new YeeRequestCommonParam();
                param.setOrderNo(ele.getBankOrderNo());
                param.setRefundOrderNo(ele.getRefundNo());
                log.info("易宝退款查询参数:{}",JSON.toJSONString(param));
                YopResponse refund = yeeProcess.refundQuery(param, yeeConfig);
                log.info("易宝退款结果返回:{}",refund.toString());
                JSONObject jsonObject = JSON.parseObject(refund.getStringResult());
                String status = jsonObject.get("status").toString();
                if ("DEBIT_SUCCESS".equals(status) || "ACCEPT_PROCESSING".equals(status) || "ACCEPT_SUCCESS".equals(status)) {
                    log.info("易宝退款成功:" + ele.getMergeOrderNo() + ">>" + ele.getRefundNo());
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)
                            .set(AoMergeRefundOrderInfo::getRefundEndTime, LocalDateTime.now())
                            .update();
                    aoMergeSuborderInfoService.lambdaUpdate()
                            .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                            .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                            .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND)
                            .update();

                    AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
                    aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
                    aoMergeReportEventLog.setAoMergeSuborderInfoId(ele.getSubOrderId());
                    aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
                    aoMergeReportEventLog.setEventExplain("退款完成");
                    aoMergeReportEventLogService.save(aoMergeReportEventLog);

                    refundStatusUpdate(ele.getMergeOrderId());
                } else if ("REFUND_FAILED".equals(status)) {
                    //退款失败
                    log.info("易宝退款失败:" + ele.getMergeOrderNo() + ">>" + ele.getRefundNo());
                    //退款失败
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .update();
                    aoMergeSuborderInfoService.lambdaUpdate()
                            .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                            .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                            .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                            .update();
                }

            } else {
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(ele.getBankOrderNo());
                requestCommonParam.setOutRequestNo(ele.getRefundNo());
                AlipayTradeFastpayRefundQueryResponse query = aliNativeProcess.refund_query(requestCommonParam, alipayClientConfig);
                if (query == null) {
                    log.info("退款查询失败 订单号{}", ele.getMergeOrderNo() + ">>" + ele.getRefundNo());
                    return;
                }

                log.info("支付宝退款结果返回:{}",JSON.toJSONString(query));
                log.info("支付宝退款验证请求结果:{},转账单号:{},返回结果{}", query.isSuccess(), ele.getRefundNo(), query.getRefundStatus());
                //不存在的退款  不做操作
                if (StringUtils.isNotEmpty(query.getSubCode())) {
                    log.info("支付宝退款失败:" + JSON.toJSONString(query));
                    return;
                }
                //查询请求发送成功
                if (query.isSuccess()) {
                    //退款成功
                    if (null == query.getRefundStatus() || "REFUND_SUCCESS".equals(query.getRefundStatus())) {
                        log.info("支付宝退款成功:" + ele.getMergeOrderNo() + ">>" + ele.getRefundNo());
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)
                                .set(AoMergeRefundOrderInfo::getRefundEndTime, LocalDateTime.now())
                                .update();
                        aoMergeSuborderInfoService.lambdaUpdate()
                                .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                                .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND)
                                .update();

                        AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
                        aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
                        aoMergeReportEventLog.setAoMergeSuborderInfoId(ele.getSubOrderId());
                        aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
                        aoMergeReportEventLog.setEventExplain("退款完成");
                        aoMergeReportEventLogService.save(aoMergeReportEventLog);

                        refundStatusUpdate(ele.getMergeOrderId());
                    } else {
                        //退款失败
                        log.info("支付宝退款失败:" + ele.getMergeOrderNo() + ">>" + ele.getRefundNo());
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                                .update();
                        aoMergeSuborderInfoService.lambdaUpdate()
                                .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                                .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                                .update();

                    }
                }
            }

    }


    /**
     * 过期订单识别
     */
    @Transactional(rollbackFor = Exception.class)
    public void validateExpiredResults(List<AoMergeOrderInfo> lockEntity) {
        for (AoMergeOrderInfo ele : lockEntity) {
            ele.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_EXPIRED);
            aoMergeSuborderInfoService.lambdaUpdate()
                    .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_EXPIRED)
                    .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_PAY_FAIL)
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, ele.getId())
                    .update();
            aoMergeOrderInfoService.saveOrUpdate(ele);
            aoMergePayOrderInfoService.lambdaUpdate()
                    .set(AoMergePayOrderInfo::getStatus, AoMergePayOrderInfo.PAY_ORDER_STATUS_EXPIRED)
                    .eq(AoMergePayOrderInfo::getId, ele.getOrderPayId())
                    .update();
            List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, ele.getId())
                    .eq(AoMergeSuborderInfo::getOrderType, AoMergeCons.ORDER_TYPE_CODE_UPGRADE)
                    .list();
            //todo-houchuan 开发测试过渡  上线恢复
            for (AoMergeSuborderInfo sub:list){
                AoMergeCounterUpgrade one = aoMergeCounterUpgradeService.lambdaQuery()
                        .eq(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, sub.getId())
                        .eq(AoMergeCounterUpgrade::getAoMergeOrderInfoId, ele.getId())
                        .one();
                if (one != null) {
                    try {
                        CancelOrderOutPutBean cancelOrderOutPutBean = iCheckInService.cancelOrder(one.getUpgradeOrder());
                        log.info("订单{}-{}取消pe订单结果{}", ele.getOrderNo(), sub.getSuborderOrderNo(), cancelOrderOutPutBean.getIsSuccess());
                        log.info("当前订单取消结果详情{}", JSON.toJSONString(cancelOrderOutPutBean));
                    } catch (APISvcException e) {
                        log.info("订单{}-{}取消pe订单异常{}", ele.getOrderNo(), sub.getSuborderOrderNo(), e.getMessage(), e);
                    }
                }
            }
            log.info("{}订单过期,修改总订单,子订单,支付订单状态为过期", ele.getOrderNo());
        }
    }

    /**
     * 退款请求发送
     */
    @Transactional(rollbackFor = Exception.class)
    public void refundSend(List<RefundSendTaskVo> lockEntity) {
        for (RefundSendTaskVo ele : lockEntity) {


            if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(ele.getPayType())) {
                WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
                wxrequestParam.setTotalFee(ele.getPrice());
                wxrequestParam.setRefundAmount(ele.getRefundPrice());
                wxrequestParam.setOutRequestNo(ele.getSuborderOrderNo());
                wxrequestParam.setOutTradeNo(ele.getBankOrderNo());
                Map<String, String> refund = wxNativeProcess.refund(wxrequestParam, wxpayClientConfig);
                if (refund == null) {
                    log.info("微信退款失败 总订单号{}-子订单号{}", ele.getMergeOrderNo(), ele.getSuborderOrderNo());
                    aoMergeSuborderInfoService.lambdaUpdate()
                            .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                            .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                            .update();
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .update();
                } else {
                    String result_code = refund.get("result_code");
                    //微信接收退款业务成功
                    if (WxReturnConstans.REQUEST_SUCCESS.equals(result_code)) {
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                                .update();
                    } else {
                        //退款失败
                        aoMergeSuborderInfoService.lambdaUpdate()
                                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                                .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                                .update();
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                                .update();
                    }
                }
            } else if (AoMergeCons.ORDER_CHARGE_TYPE_ALIPAY.equals(ele.getPayType())) {

                //支付宝退款
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(ele.getBankOrderNo());
                requestCommonParam.setTotalAmount(AoUtils.pointsToYuan(ele.getPrice()));
                requestCommonParam.setRefundAmount(AoUtils.pointsToYuan(ele.getRefundPrice()));
                requestCommonParam.setOutRequestNo(ele.getSuborderOrderNo());
                AlipayTradeRefundResponse refund = aliNativeProcess.refund(requestCommonParam, alipayClientConfig);
                if (refund == null) {
                    log.info("支付宝退款失败 总订单号{}-子订单号{}", ele.getMergeOrderNo(), ele.getSuborderOrderNo());
                    aoMergeSuborderInfoService.lambdaUpdate()
                            .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                            .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                            .update();
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .update();
                }

                //退款请求成功
                if (refund.isSuccess()) {
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                            .update();
                } else {
                    //退款失败
                    aoMergeSuborderInfoService.lambdaUpdate()
                            .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                            .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                            .update();
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .update();
                }

            } else if (AoMergeCons.ORDER_CHARGE_TYPE_YEE.equals(ele.getPayType())) {
                YeeRequestCommonParam param = new YeeRequestCommonParam();
                param.setOrderNo(ele.getBankOrderNo());
                param.setAmount(AoUtils.pointsToYuan(ele.getPrice()));
                param.setRefundOrderNo(ele.getSuborderOrderNo());
                param.setRefundAmount(AoUtils.pointsToYuan(ele.getRefundPrice()));
                YopResponse refund = yeeProcess.refund(param, yeeConfig);
                JSONObject jsonObject = JSON.parseObject(refund.getStringResult());
                if (refund == null) {
                    log.info("易宝退款失败 总订单号{}-子订单号{}", ele.getMergeOrderNo(), ele.getSuborderOrderNo());
                    aoMergeSuborderInfoService.lambdaUpdate()
                            .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                            .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                            .update();
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .update();
                } else {
                    if ("0000".equals(jsonObject.get("retCode"))) {
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                                .update();
                    } else {
                        //退款失败
                        aoMergeSuborderInfoService.lambdaUpdate()
                                .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                                .eq(AoMergeSuborderInfo::getId, ele.getSubOrderId())
                                .update();
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                                .update();


                    }
                }


            } else if (AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY.equals(ele.getPayType())) {
                AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(ele.getMergeOrderId());
                if (!(AoMergeCons.MERGE_ORDER_STATUS_SUCCESS.equals(mergeOrderInfo.getMergeOrderStatus()) || AoMergeCons.MERGE_ORDER_STATUS_PART_REFUND.equals(mergeOrderInfo.getMergeOrderStatus()))) {
                    continue;
                }
                worldpayUtil.orderRefund(ele.getBankOrderNo(), ele.getCurrencySpecies(), new BigDecimal("100").multiply(new BigDecimal(ele.getRefundPrice())).toString(), ele.getSuborderOrderNo());
                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getId, ele.getRefundOrderId())
                        .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                        .update();


            }
        }
    }


    /**
     * 现金收款
     *
     * @param orderNo    订单号
     * @param posOrderNo pos机交易号
     */
    @Transactional(rollbackFor = Exception.class)
    public void cash(String orderNo, String posOrderNo) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        LocalDateTime now = LocalDateTime.now();
        if(!dao.isDirector(authentication.getId())){
            throw new BusinessException(MessageCode.BLANK_RESOURCES.getCode());
        }
        log.info("{}-{}合并订单现金确认收款{}-银行订单号{}", userNo, realName, orderNo, posOrderNo);
        AoMergeOrderInfo one = aoMergeOrderInfoService.getOne((Wrappers.<AoMergeOrderInfo>lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, orderNo)));
        if (one == null) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        one.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_SUCCESS);
        one.setCashReceiveUser(userNo);
        LambdaUpdateChainWrapper<AoMergePayOrderInfo> aoMergePayOrderInfoLambdaUpdateChainWrapper = aoMergePayOrderInfoService.lambdaUpdate();
        if (StringUtils.isEmpty(posOrderNo)) {
            one.setTurnStatus(AoMergeCons.ORDER_TURN_STATUS_UNPAY);
        }else{
            aoMergePayOrderInfoLambdaUpdateChainWrapper.set(AoMergePayOrderInfo::getBankOrderNo, posOrderNo);
        }
        one.setCashReceiveUserDepartment(authentication.getDeptName());
        one.setCashReceiveUserName(realName);
        aoMergeOrderInfoService.saveOrUpdate(one);
        aoMergePayOrderInfoLambdaUpdateChainWrapper.set(AoMergePayOrderInfo::getPayStatus, AoMergeCons.PAY_ORDER_STATUS_SUCCESS)
                .set(AoMergePayOrderInfo::getPayTime, now)
                .set(AoMergePayOrderInfo::getPayType, StringUtils.isEmpty(posOrderNo) ? AoMergeCons.ORDER_CHARGE_TYPE_CASH : AoMergeCons.ORDER_CHARGE_TYPE_POS)
                .eq(AoMergePayOrderInfo::getId, one.getOrderPayId())
                .update();
        List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                .list();
        for (AoMergeSuborderInfo ele : list) {
            aoMergeSuborderInfoService.lambdaUpdate()
                    .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                    .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS)
                    .eq(AoMergeSuborderInfo::getId, ele.getId())
                    .update();

            AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
            aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
            aoMergeReportEventLog.setAoMergeSuborderInfoId(ele.getId());
            aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS);
            aoMergeReportEventLog.setEventExplain("支付完成");
            aoMergeReportEventLogService.save(aoMergeReportEventLog);


            //给国内逾重旅客发送短信
            if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
                aoMegerOrderNotifyService.smsOverWeighLectronicVoucher(ele.getPayPaxId(), ele.getSuborderOrderNo());
            }
            //处理支付成功之后的升舱功能处理
            if (ele.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_UPGRADE)) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        aoMegerOrderNotifyLockService.upgradeThreadFunction(ele.getId());
                    }
                }).start();
            }
        }


    }


    /**
     * 取消订单
     */
    @Transactional(rollbackFor = Exception.class)
    public BusinessException orderCancel(AoMergeOrderInfo one) throws Exception {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        log.info("{}-{}取消合并订单{}", userNo, realName, one.getOrderNo());

        if (!one.getMergeOrderStatus().equals(AoMergeCons.MERGE_ORDER_STATUS_UNPAY)) {
            log.info("{}订单状态发生变化不能取消", one.getOrderNo());
            throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_CANCEL.getCode());
        }

        AoMergePayOrderInfo payOrder = aoMergePayOrderInfoService.lambdaQuery()
                .eq(AoMergePayOrderInfo::getId, one.getOrderPayId())
                .one();
        List<AoMergeSuborderInfo> suborderInfos = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                .eq(AoMergeSuborderInfo::getOrderType,AoMergeCons.ORDER_TYPE_CODE_UPGRADE)
                .list();
        for (AoMergeSuborderInfo ele : suborderInfos) {
                doBeforeCancle(ele.getId());
//              doBeforeRefund(ele.getId());
        }

        //收款方式 0微信、1易宝、2支付宝、3现金，4pos机 5worldPay
        if (payOrder.getPayType().equals(AoMergeCons.ORDER_CHARGE_TYPE_WX)) {
            WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
            wxrequestParam.setOutTradeNo(payOrder.getPayOrderNo());
            Map<String, String> close = wxNativeProcess.clase(wxrequestParam, wxpayClientConfig);
            String result = close.get("result_code");
            log.info("合并订单取消微信支付取消,orderNo:{},payOrderNo:{},result:{}", one.getOrderNo(), payOrder.getPayOrderNo(), JSON.toJSONString(close));
            if ("SUCCESS".equals(result)) {
                aoMergeOrderInfoService.lambdaUpdate()
                        .eq(AoMergeOrderInfo::getOrderNo, one.getOrderNo())
                        .eq(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_UNPAY)
                        .set(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_CANCEL)
                        .update();
                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_PAY_FAIL)
                        .update();
            } else {
                return new BusinessException(MessageCode.ORDER_CANCEL_FAIL.getCode());
            }

        } else if (payOrder.getPayType().equals(AoMergeCons.ORDER_CHARGE_TYPE_YEE)) {
            aoMergeOrderInfoService.lambdaUpdate()
                    .eq(AoMergeOrderInfo::getOrderNo, one.getOrderNo())
                    .eq(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_UNPAY)
                    .set(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_CANCEL)
                    .update();
            aoMergeSuborderInfoService.lambdaUpdate()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                    .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL)
                    .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_PAY_FAIL)
                    .update();
        } else if (payOrder.getPayType().equals(AoMergeCons.ORDER_CHARGE_TYPE_ALIPAY)) {
            AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
            requestCommonParam.setOutTradeNo(payOrder.getPayOrderNo());

            AlipayTradeCloseResponse close = aliNativeProcess.close(requestCommonParam, alipayClientConfig);
            log.info("合并订单取消支付宝支付取消-orderNo:{},payOrderNo:{},result:{}", one.getOrderNo(), payOrder.getPayOrderNo(), JSON.toJSONString(close));
            if (close.isSuccess() || "ACQ.TRADE_NOT_EXIST".equals(close.getSubCode())) { // 扫码未支付关闭  未扫码和不存在订单
                aoMergeOrderInfoService.lambdaUpdate()
                        .eq(AoMergeOrderInfo::getOrderNo, one.getOrderNo())
                        .eq(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_UNPAY)
                        .set(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_CANCEL)
                        .update();
                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_PAY_FAIL)
                        .update();
            } else {
                return new BusinessException(MessageCode.ORDER_CANCEL_FAIL.getCode());
            }
        } else if (payOrder.getPayType().equals(AoMergeCons.ORDER_CHARGE_TYPE_CASH) || payOrder.getPayType().equals(AoMergeCons.ORDER_CHARGE_TYPE_POS)) {
            aoMergeOrderInfoService.lambdaUpdate()
                    .eq(AoMergeOrderInfo::getOrderNo, one.getOrderNo())
                    .eq(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_UNPAY)
                    .set(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_CANCEL)
                    .update();
            aoMergeSuborderInfoService.lambdaUpdate()
                    .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                    .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL)
                    .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_PAY_FAIL)
                    .update();
        } else if (payOrder.getPayType().equals(AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY)) {
            try {
                worldpayUtil.orderCancel(payOrder.getPayOrderNo());
                worldpayUtil.orderRefund(one.getOrderNo(), payOrder.getCurrencySpecies(), new BigDecimal(payOrder.getPrice().toString()).multiply(new BigDecimal("100")).toString(), null);
                aoMergeOrderInfoService.lambdaUpdate()
                        .eq(AoMergeOrderInfo::getOrderNo, one.getOrderNo())
                        .eq(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_UNPAY)
                        .set(AoMergeOrderInfo::getMergeOrderStatus, AoMergeCons.MERGE_ORDER_STATUS_CANCEL)
                        .update();
                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_PAY_FAIL)
                        .update();
            } catch (Exception e) {
                log.info("合并订单取消支付宝支付取消-orderNo:{},payOrderNo:{}", one.getOrderNo(), payOrder.getPayOrderNo(), e);
                return new BusinessException(MessageCode.ORDER_CANCEL_FAIL.getCode());
            }

        }

        return null;

    }

    @Transactional(rollbackFor = Exception.class)
    public void startRefund(String subOrderNo) {
        //判断状态
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        String[] split = subOrderNo.split(",");
        log.info("{}-{}发起子订单退款,订单号{}", userNo, realName, subOrderNo);
        List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                .in(AoMergeSuborderInfo::getSuborderOrderNo, split)
                .eq(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                .eq(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS)
                .list();
        if (list == null || list.size()<1) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }

        for (AoMergeSuborderInfo one : list) {
            one.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_AWAIT_REFUND);
            aoMergeSuborderInfoService.saveOrUpdate(one);
            AoMergeRefundOrderInfo refundOrderInfo = new AoMergeRefundOrderInfo();
            refundOrderInfo.setAoMergeOrderInfoId(one.getAoMergeOrderInfoId());
            refundOrderInfo.setAoMergeSuborderInfoId(one.getId());
            refundOrderInfo.setRefundNo(one.getSuborderOrderNo());
            refundOrderInfo.setRefundPrice(one.getPracticalOrderPrice().multiply(new BigDecimal("100")));
            refundOrderInfo.setRefundStatus(AoMergeCons.SUBORDER_REFUND_STATUS_START);
            refundOrderInfo.setCreateUser(realName);
            refundOrderInfo.setCreateNo(userNo);
            refundOrderInfo.setCreateDate(LocalDateTime.now());
            AoMergeOrderInfo byId = aoMergeOrderInfoService.getById(one.getAoMergeOrderInfoId());
            refundOrderInfo.setAoMergeOrderPayId(byId.getOrderPayId());
            refundOrderInfo.setStatus(AoMergeRefundOrderInfo.STATUS_YES);
            aoMergeRefundOrderInfoService.save(refundOrderInfo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelRefund(String subOrderNo) {
        //判断状态
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        log.info("{}-{}子订单取消退款申请,订单号{}", userNo, realName, subOrderNo);
        AoMergeSuborderInfo one = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getSuborderOrderNo, subOrderNo)
                .eq(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_AWAIT_REFUND)
                .eq(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS)
                .one();
        if (one == null) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        aoMergeSuborderInfoService.lambdaUpdate()
                .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS)
                .eq(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_AWAIT_REFUND)
                .eq(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS)
                .eq(AoMergeSuborderInfo::getId, one.getId())
                .update();
        aoMergeRefundOrderInfoService.lambdaUpdate()
                .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, one.getId())
                .set(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_NO)
                .update();
    }

    @Transactional(rollbackFor = Exception.class)
    public void refund(AoRefundDto dto) {
        //判断状态
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        String deptName = authentication.getDeptName();
        LocalDateTime now = LocalDateTime.now();
        log.info("{}-{}子订单退款,订单号{}", userNo, realName, dto.getSubOrderNo());
        AoMergeSuborderInfo suborderInfo = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getSuborderOrderNo, dto.getSubOrderNo())
                .eq(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_AWAIT_REFUND)
                .in(AoMergeSuborderInfo::getSuborderOrderPayStatus, new String[]{AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL, AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS})
                .one();
        if (suborderInfo == null) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(suborderInfo.getAoMergeOrderInfoId());
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.getById(mergeOrderInfo.getOrderPayId());

        if (!mergeOrderInfo.getOrderNo().equals(dto.getMergeOrderNo())) {
            log.info("参数总订单号与子订单号关系不匹配{}-{}", dto.getMergeOrderNo(), dto.getSubOrderNo());
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }

        if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(dto.getPayType())) {
            List<AoMergeRefundOrderInfo> list = aoMergeRefundOrderInfoService.lambdaQuery()
                    .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                    .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                    .isNotNull(AoMergeRefundOrderInfo::getRefundSendTime)
                    .orderByDesc(AoMergeRefundOrderInfo::getRefundSendTime)
                    .list();


            if (list.size() > 0) {
                AoMergeRefundOrderInfo aoMergeRefundOrderInfo = list.get(0);
                LocalDateTime refundSendTime = aoMergeRefundOrderInfo.getRefundSendTime();
                LocalDateTime localDateTime = refundSendTime.plusSeconds(60L);

                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                        .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                        .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                        .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_REFUND_ING)
                        .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                        .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                        .set(AoMergeRefundOrderInfo::getApproveTime, now)
                        .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                        .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                        .set(AoMergeRefundOrderInfo::getRefundSendTime, localDateTime)
                        .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                        .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                        .update();
            } else {
                WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
                wxrequestParam.setTotalFee(payOrderInfo.getPrice().toString());
                wxrequestParam.setRefundAmount(AoUtils.yuanToPoints(suborderInfo.getPracticalOrderPrice().toString()).toString());
                wxrequestParam.setOutRequestNo(suborderInfo.getSuborderOrderNo());
                wxrequestParam.setOutTradeNo(payOrderInfo.getBankOrderNo());
                Map<String, String> refund = wxNativeProcess.refund(wxrequestParam, wxpayClientConfig);
                if (refund == null) {
                    log.info("微信退款失败 总订单号{}-子订单号{}", mergeOrderInfo.getOrderNo(), suborderInfo.getSuborderOrderNo());
                    suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL);
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                            .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                            .set(AoMergeRefundOrderInfo::getApproveTime, now)
                            .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                            .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                            .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                            .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                            .update();
                } else {
                    String result_code = refund.get("result_code");
                    //微信接收退款业务成功
                    if (WxReturnConstans.REQUEST_SUCCESS.equals(result_code)) {
                        //退款中  微信的退款结果要通过退款回调 或者 主动发起退款查询
                        suborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND_ING);
                        aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                                .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                                .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                                .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_REFUND_ING)
                                .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                                .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                                .set(AoMergeRefundOrderInfo::getApproveTime, now)
                                .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                                .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                                .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                                .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                                .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                                .update();
                    } else {
                        //退款失败
                        suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL);
                        aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                        aoMergeRefundOrderInfoService.lambdaUpdate()
                                .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                                .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                                .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                                .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                                .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                                .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                                .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                                .set(AoMergeRefundOrderInfo::getApproveTime, now)
                                .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                                .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                                .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                                .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                                .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                                .update();
                    }
                }
            }
        } else if (AoMergeCons.ORDER_CHARGE_TYPE_ALIPAY.equals(dto.getPayType())) {
            List<AoMergeRefundOrderInfo> list = aoMergeRefundOrderInfoService.lambdaQuery()
                    .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                    .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                    .isNotNull(AoMergeRefundOrderInfo::getRefundSendTime)
                    .orderByDesc(AoMergeRefundOrderInfo::getRefundSendTime)
                    .list();


            if (list.size() > 0) {
                AoMergeRefundOrderInfo aoMergeRefundOrderInfo = list.get(0);
                LocalDateTime refundSendTime = aoMergeRefundOrderInfo.getRefundSendTime();
                LocalDateTime localDateTime = refundSendTime.plusSeconds(3L);

                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                        .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                        .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                        .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_REFUND_ING)
                        .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                        .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                        .set(AoMergeRefundOrderInfo::getApproveTime, now)
                        .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                        .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                        .set(AoMergeRefundOrderInfo::getRefundSendTime, localDateTime)
                        .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                        .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                        .update();
            } else {
                //支付宝退款
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(payOrderInfo.getBankOrderNo());
                requestCommonParam.setTotalAmount(AoUtils.pointsToYuan(payOrderInfo.getPrice().toString()));
                requestCommonParam.setRefundAmount(suborderInfo.getPracticalOrderPrice().toString());
                requestCommonParam.setOutRequestNo(suborderInfo.getSuborderOrderNo());
                AlipayTradeRefundResponse refund = aliNativeProcess.refund(requestCommonParam, alipayClientConfig);
                if (refund == null) {
                    log.info("支付宝退款失败 总订单号{}-子订单号{}", mergeOrderInfo.getOrderNo(), suborderInfo.getSuborderOrderNo());
                    suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL);
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                            .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                            .set(AoMergeRefundOrderInfo::getApproveTime, now)
                            .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                            .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                            .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                            .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                            .update();
                    return;
                }

                //退款请求成功
                if (refund.isSuccess()) {
                    //退款中
                    suborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND_ING);
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_REFUND_ING)
                            .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                            .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                            .set(AoMergeRefundOrderInfo::getApproveTime, now)
                            .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                            .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                            .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                            .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                            .update();
                } else {
                    //退款失败
                    suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL);
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                            .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                            .set(AoMergeRefundOrderInfo::getApproveTime, now)
                            .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                            .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                            .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                            .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                            .update();

                }
            }

        } else if (AoMergeCons.ORDER_CHARGE_TYPE_YEE.equals(dto.getPayType())) {
            YeeRequestCommonParam param = new YeeRequestCommonParam();
            param.setOrderNo(payOrderInfo.getBankOrderNo());
            param.setRefundOrderNo(suborderInfo.getSuborderOrderNo());
            param.setRefundAmount(suborderInfo.getPracticalOrderPrice().toString());
            log.info("易宝退款请求参数:{}", JSON.toJSONString(param));
            YopResponse refund = yeeProcess.refund(param, yeeConfig);
            JSONObject jsonObject = JSON.parseObject(refund.getStringResult());
            if (refund == null) {
                log.info("易宝退款失败 总订单号{}-子订单号{}", mergeOrderInfo.getOrderNo(), suborderInfo.getSuborderOrderNo());
                suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL);
                aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                        .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                        .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                        .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                        .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                        .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                        .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                        .set(AoMergeRefundOrderInfo::getApproveTime, now)
                        .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                        .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                        .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                        .update();
            } else {
                if ("0000".equals(jsonObject.get("retCode"))) {
                    //退款中  微信的退款结果要通过退款回调 或者 主动发起退款查询
                    suborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND_ING);
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_REFUND_ING)
                            .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                            .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                            .set(AoMergeRefundOrderInfo::getApproveTime, now)
                            .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                            .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                            .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                            .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                            .update();
                } else {
                    //退款失败
                    suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL);
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
                    aoMergeRefundOrderInfoService.lambdaUpdate()
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                            .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                            .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                            .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                            .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                            .set(AoMergeRefundOrderInfo::getApproveTime, now)
                            .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                            .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO)
                            .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                            .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                            .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                            .update();
                }
            }


        } else if (AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY.equals(dto.getPayType())) {

            if (!(AoMergeCons.MERGE_ORDER_STATUS_SUCCESS.equals(mergeOrderInfo.getMergeOrderStatus()) || AoMergeCons.MERGE_ORDER_STATUS_PART_REFUND.equals(mergeOrderInfo.getMergeOrderStatus()))) {
                log.info("worrldPay支付的订单状态发生变化不能退款{}-{}", dto.getMergeOrderNo(), dto.getSubOrderNo());
                throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_REFUND.getCode());
            }
            LambdaUpdateChainWrapper<AoMergeRefundOrderInfo> aoMergeRefundOrderInfoLambdaUpdateChainWrapper = aoMergeRefundOrderInfoService.lambdaUpdate();
            if (AoMergeCons.SUBORDER_PERMIT_REFUND_YES.equals(suborderInfo.getPermitRefund())) {
                worldpayUtil.orderRefund(payOrderInfo.getBankOrderNo(), payOrderInfo.getCurrencySpecies(), suborderInfo.getPracticalOrderPrice().toString(), suborderInfo.getSuborderOrderNo());
                aoMergeRefundOrderInfoLambdaUpdateChainWrapper.set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                    .set(AoMergeRefundOrderInfo::getRefundSendTime, now);
            } else {
                aoMergeRefundOrderInfoLambdaUpdateChainWrapper.set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_NO);
            }
            suborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND_ING);
            aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
            aoMergeRefundOrderInfoLambdaUpdateChainWrapper.eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                    .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                    .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                    .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                    .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_REFUND_ING)
                    .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                    .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                    .set(AoMergeRefundOrderInfo::getApproveTime, now)
                    .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                    .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                    .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                    .update();


        } else {

            suborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND);
            suborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
            aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);
            aoMergeRefundOrderInfoService.lambdaUpdate()
                    .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                    .eq(AoMergeRefundOrderInfo::getAoMergeSuborderInfoId, suborderInfo.getId())
                    .eq(AoMergeRefundOrderInfo::getAoMergeOrderPayId, payOrderInfo.getId())
                    .eq(AoMergeRefundOrderInfo::getStatus, AoMergeRefundOrderInfo.STATUS_YES)
                    .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)
                    .set(AoMergeRefundOrderInfo::getApproveUserNo, userNo)
                    .set(AoMergeRefundOrderInfo::getApproveUserName, realName)
                    .set(AoMergeRefundOrderInfo::getApproveTime, now)
                    .set(AoMergeRefundOrderInfo::getApproveUserDepartment, deptName)
                    .set(AoMergeRefundOrderInfo::getRefundRequestSend, AoMergeRefundOrderInfo.REFUND_REQUEST_SEND_YES)
                    .set(AoMergeRefundOrderInfo::getRefundSendTime, now)
                    .set(AoMergeRefundOrderInfo::getRefundSause, dto.getRefundSause())
                    .set(AoMergeRefundOrderInfo::getRefundExplain, dto.getRefundExplain())
                    .update();

            AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
            aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
            aoMergeReportEventLog.setAoMergeSuborderInfoId(suborderInfo.getId());
            aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
            aoMergeReportEventLog.setEventExplain("退款完成");
            aoMergeReportEventLogService.save(aoMergeReportEventLog);
            refundStatusUpdate(suborderInfo.getAoMergeOrderInfoId());
        }

        doBeforeRefund(suborderInfo.getId());
    }

    /**
     * 子订单退款成功后的总订单状态更新
     *
     * @param mergeOrderId 总订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void refundStatusUpdate(String mergeOrderId) {
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.lambdaQuery()
                .eq(AoMergeOrderInfo::getId, mergeOrderId)
                .in(AoMergeOrderInfo::getMergeOrderStatus, new String[]{AoMergeCons.MERGE_ORDER_STATUS_SUCCESS, AoMergeCons.MERGE_ORDER_STATUS_PART_REFUND})
                .one();
        if (mergeOrderInfo == null) {
            log.info("{}总订单状态为支付完成或部分退款的总订单不存在,总订单状态更新不执行", mergeOrderId);
            return;
        }
        List<AoMergeSuborderInfo> subOrderList = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderId)
                .list();

        if (ObjectUtils.isEmpty(subOrderList)) {
            log.info("子订单不存在,总订单{}状态更新不执行", mergeOrderId);
            return;
        }
        long count = subOrderList.stream().filter(e -> e.getSuborderOrderStatus().equals(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND))
                .count();
        BigDecimal subSize = new BigDecimal(subOrderList.size());
        BigDecimal paySize = new BigDecimal(count);
        if (paySize.compareTo(subSize) == 0) {
            mergeOrderInfo.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_ALL_REFUND);
        } else if (paySize.compareTo(new BigDecimal(0)) == 0) {
            mergeOrderInfo.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_SUCCESS);
        } else {
            mergeOrderInfo.setMergeOrderStatus(AoMergeCons.MERGE_ORDER_STATUS_PART_REFUND);
        }
        aoMergeOrderInfoService.saveOrUpdate(mergeOrderInfo);
    }

    /**
     * 微信退款回调
     *
     * @param map
     */
    @Transactional(rollbackFor = Exception.class)
    public String wxpayRefundNotifyUrl(Map<String, String> map) {
        log.info("微信退款回调:{}", JSON.toJSONString(map));
        String return_code = map.get("return_code");
        if ("SUCCESS".equals(return_code)) {
            String req_info = AoUtils.wxRefundNotifyDecrypt(wxpayClientConfig.getKey(), map.get("req_info"));
            if (req_info == null) {
                log.info("微信退款回调返回值解密失败:{}", JSON.toJSONString(map));
                return null;
            }
            Map<String, String> map1 = null;
            try {
                map1 = WXPayUtil.xmlToMap(req_info);
            } catch (Exception e) {
                log.info(e.getMessage(), e);
                throw new RuntimeException();
            }
            String out_trade_no = map1.get("out_trade_no");
            String out_refund_no = map1.get("out_refund_no");
            String refund_status = map1.get("refund_status");

            AoMergeRefundOrderInfo one = aoMergeRefundOrderInfoService.lambdaQuery()
                    .eq(AoMergeRefundOrderInfo::getRefundNo, out_refund_no)
                    .one();

            if ("SUCCESS".equals(refund_status)) {
                log.info("微信退款回调成功,订单号{}-退款单号{}", out_trade_no, out_refund_no);
                LocalDateTime successTime = DateUtils.parseStringToLocalDateTime(map1.get("success_time"), DateUtils.YYYY_MM_DD_HH_MM_SS);
                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getId, one.getId())
                        .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)
                        .set(AoMergeRefundOrderInfo::getRefundEndTime, successTime)
                        .update();
                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getId, one.getAoMergeSuborderInfoId())
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND)
                        .update();

                AoMergeReportEventLog aoMergeReportEventLog = new AoMergeReportEventLog();
                aoMergeReportEventLog.setCreateTime(LocalDateTime.now());
                aoMergeReportEventLog.setAoMergeSuborderInfoId(one.getId());
                aoMergeReportEventLog.setPayEvent(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
                aoMergeReportEventLog.setEventExplain("退款完成");
                aoMergeReportEventLogService.save(aoMergeReportEventLog);

                refundStatusUpdate(one.getAoMergeOrderInfoId());


            } else if ("CHANGE".equals(refund_status)) {
                log.info("微信退款回调异常,订单号{}-退款单号{}", out_trade_no, out_refund_no);
                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getId, one.getId())
                        .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                        .update();
                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getId, one.getAoMergeSuborderInfoId())
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                        .update();
            } else {
                log.info("微信退款回调失败,订单号{}-退款单号{}", out_trade_no, out_refund_no);
                aoMergeRefundOrderInfoService.lambdaUpdate()
                        .eq(AoMergeRefundOrderInfo::getId, one.getId())
                        .set(AoMergeRefundOrderInfo::getRefundStatus, AoMergeCons.SUBORDER_REFUND_STATUS_FAIL)
                        .update();
                aoMergeSuborderInfoService.lambdaUpdate()
                        .eq(AoMergeSuborderInfo::getId, one.getAoMergeSuborderInfoId())
                        .set(AoMergeSuborderInfo::getSuborderOrderStatus, AoMergeCons.SUBORDER_ORDER_STATUS_REFUND)
                        .set(AoMergeSuborderInfo::getSuborderOrderPayStatus, AoMergeCons.SUBORDER_PAY_STATUS_REFUND_FAIL)
                        .update();
            }
            return out_refund_no;
        } else {
            log.info("微信退款状态未成功:" + JSON.toJSONString(map));
        }
        return null;
    }

    /**
     * 现金缴纳
     *
     * @param servicePort
     * @param mergeOrderIds
     */
    @Transactional(rollbackFor = Exception.class)
    public CashTurnSaveVo cashTurn(String servicePort, String payType, String mergeOrderIds) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        LocalDateTime now = LocalDateTime.now();

        List<CashTurnQueryVo> cashTurnQueryVos = aoMergeOrderInfoService.cashTurnQuery(servicePort, StringUtils.isEmpty(mergeOrderIds) ? null : Arrays.asList(mergeOrderIds.split(",")));
        if (cashTurnQueryVos.size() <1) {
            throw new BusinessException(MessageCode.REPEAT_TURN.getCode());
        }

        BigDecimal tprice = new BigDecimal(0);
        List<AoMergeSubOrderJoinCashReturnPay> joinList = new ArrayList<>();
        for (CashTurnQueryVo ele : cashTurnQueryVos) {
            AoMergeSubOrderJoinCashReturnPay joinCashReturnPay = new AoMergeSubOrderJoinCashReturnPay();
            joinCashReturnPay.setMergeOrderId(ele.getMergeOrderId());
            joinCashReturnPay.setSuborderOrderId(ele.getSubOrderId());
            joinList.add(joinCashReturnPay);
            tprice = tprice.add(ele.getSubPrice());
        }
        if(tprice.compareTo(new BigDecimal("0"))==0){
            throw new BusinessException(MessageCode.RETURN_ORDER_NOT_EXIST.getCode());
        }
        AoMergeCashReturnPayOrderInfo payOrderInfo = new AoMergeCashReturnPayOrderInfo();
        payOrderInfo.setPayType(payType);
        payOrderInfo.setPayStatus(AoMergeCashReturnPayOrderInfo.PAY_ORDER_PAY_STATUS_UNPAY);
        payOrderInfo.setStatus(AoMergeCashReturnPayOrderInfo.PAY_ORDER_STATUS_EFFECT);
        payOrderInfo.setCreateDate(now);
        SysAirportInfoEntity one = sysAirportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, servicePort)
                .one();
        if (one == null) {
            throw new BusinessException(MessageCode.NOT_FIND_AIR_LINE.getCode(), new String[]{servicePort});
        }
        String turnOrderNo = MergeOrderUtils.createTurnOrderNo(one.getType(), payType);
        payOrderInfo.setPayOrderNo(turnOrderNo);
        String precreate = null;

        if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(payType)) {
            WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
            wxrequestParam.setOutTradeNo(turnOrderNo);
            wxrequestParam.setTotalFee(MergeOrderUtils.yuanConversionPointsToStr(tprice));
            wxrequestParam.setBody(wxrequestParam.getBody() + "现金缴纳");
            precreate = wxNativeProcess.precreate(wxrequestParam, mergeOrderWxTrunPayConfig);
        } else {
            AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
            requestCommonParam.setOutTradeNo(turnOrderNo);
            requestCommonParam.setTotalAmount(MergeOrderUtils.conversionToString(tprice));
            requestCommonParam.setSubject(requestCommonParam.getSubject() + "现金缴纳");
            precreate = aliNativeProcess.precreate(requestCommonParam, mergeOrderTrunAliPayConfig);
        }
        payOrderInfo.setPayPath(precreate);
        payOrderInfo.setPrice(new Integer(MergeOrderUtils.yuanConversionPointsToStr(tprice)));
        aoMergeCashReturnPayOrderInfoService.save(payOrderInfo);
        Set<String> set = new HashSet<>();


        AoMergeCashTurnList aoMergeCashTurnList = new AoMergeCashTurnList();
        aoMergeCashTurnList.setCreateTime(now);
        aoMergeCashTurnList.setQrcodePath(precreate);
        aoMergeCashTurnList.setAmount(tprice);
        aoMergeCashTurnList.setCallbackPayNo(turnOrderNo);
        aoMergeCashTurnList.setCashReceiveUserId(userNo);
        aoMergeCashTurnList.setChargeType(payType);
        aoMergeCashTurnList.setTurnStatus(AoMergeCons.ORDER_TURN_STATUS_REFUND_ING);
        aoMergeCashTurnListService.save(aoMergeCashTurnList);
        for (AoMergeSubOrderJoinCashReturnPay ele : joinList) {
            ele.setCashReturnPayId(aoMergeCashTurnList.getId());
            set.add(ele.getMergeOrderId());
        }
        aoMergeOrderInfoService.lambdaUpdate()
                .in(AoMergeOrderInfo::getId, set)
                .set(AoMergeOrderInfo::getTurnStatus, AoMergeCons.ORDER_TURN_STATUS_REFUND_ING)
                .set(AoMergeOrderInfo::getTurnNumber, turnOrderNo)
                .update();
        aoMergeSubOrderJoinCashReturnPayService.saveBatch(joinList);
        CashTurnSaveVo cashTurnSaveVo = new CashTurnSaveVo();
        cashTurnSaveVo.setPath(precreate);
        cashTurnSaveVo.setTurnPrice(tprice.toString());
        cashTurnSaveVo.setTurnOrderNo(turnOrderNo);
        cashTurnSaveVo.setCreateDate(now);
        cashTurnSaveVo.setChargeType(payType);
        return cashTurnSaveVo;

    }


    public CashTurnSaveVo cashTurnPrice(String servicePort, String mergeOrderIds) {
        List<CashTurnQueryVo> cashTurnQueryVos = aoMergeOrderInfoService.cashTurnQuery(servicePort, StringUtils.isEmpty(mergeOrderIds) ? null : Arrays.asList(mergeOrderIds.split(",")));
        if (cashTurnQueryVos.size() < 1) {
            throw new BusinessException(MessageCode.REPEAT_TURN.getCode());
        }
        BigDecimal tprice = new BigDecimal(0);
        List<AoMergeSubOrderJoinCashReturnPay> joinList = new ArrayList<>();
        for (CashTurnQueryVo ele : cashTurnQueryVos) {
            AoMergeSubOrderJoinCashReturnPay joinCashReturnPay = new AoMergeSubOrderJoinCashReturnPay();
            joinCashReturnPay.setMergeOrderId(ele.getMergeOrderId());
            joinCashReturnPay.setSuborderOrderId(ele.getSubOrderId());
            joinList.add(joinCashReturnPay);
            tprice = tprice.add(ele.getSubPrice());
        }
        if(tprice.compareTo(new BigDecimal("0"))==0){
            throw new BusinessException(MessageCode.RETURN_ORDER_NOT_EXIST.getCode());
        }
        CashTurnSaveVo cashTurnSaveVo = new CashTurnSaveVo();
        cashTurnSaveVo.setTurnPrice(tprice.toString());
        return cashTurnSaveVo;
    }


    public String orderPayResultQuery(String orderNo) {
        AoMergeOrderInfo one = aoMergeOrderInfoService.lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, orderNo)
                .one();
        return one.getMergeOrderStatus();
    }

    /**
     * 缴纳结果查询
     *
     * @param collect
     */
    @Transactional(rollbackFor = Exception.class)
    public void vaidateTurnResults(Map<String, List<CashTurnOrderTaskVo>> collect) {
        Iterator<Map.Entry<String, List<CashTurnOrderTaskVo>>> iterator = collect.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<CashTurnOrderTaskVo>> next = iterator.next();
            List<CashTurnOrderTaskVo> value = next.getValue();
            String key = next.getKey();
            List<String> ids = value.stream().map(CashTurnOrderTaskVo::getMergeOrderId).collect(Collectors.toList());
            if (value.get(0).getChargeType().equals(AoMergeCons.ORDER_CHARGE_TYPE_WX)) {
                WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
                wxrequestParam.setOutTradeNo(key);
                Map<String, String> query = wxNativeProcess.query(wxrequestParam, mergeOrderWxTrunPayConfig);
                log.info("合并订单微信缴纳支付结果:{}", JSON.toJSONString(query));
                //支付已成功
                if (WxReturnConstans.REQUEST_SUCCESS.equals(query.get("return_code")) && WxReturnConstans.RESULT_CODE_SUCCESS.equals(query.get("result_code")) && WxReturnConstans.RESULT_CODE_SUCCESS.equals(query.get("trade_state"))) {
                    aoMergeOrderInfoService.lambdaUpdate()
                            .set(AoMergeOrderInfo::getTurnStatus, AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                            .set(AoMergeOrderInfo::getTurnTime, DateUtils.parseStringToLocalDateTime(query.get("time_end"), DateUtils.YYYYMMDDHHMMSS))
                            .in(AoMergeOrderInfo::getId, ids)
                            .update();
                    aoMergeCashTurnListService.lambdaUpdate()
                            .set(AoMergeCashTurnList::getTurnStatus, AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                            .eq(AoMergeCashTurnList::getCallbackPayNo, key)
                            .update();
                }
            } else {
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(key);
                AlipayTradeQueryResponse query = aliNativeProcess.query(requestCommonParam, mergeOrderTrunAliPayConfig);
                log.info("合并订单支付宝缴纳支付结果:{}", JSON.toJSONString(query));
                //支付已成功
                if (query.isSuccess() && AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus())) {
                    aoMergeOrderInfoService.lambdaUpdate()
                            .set(AoMergeOrderInfo::getTurnStatus, AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                            .set(AoMergeOrderInfo::getTurnTime, DateUtils.getDateToLocalDateTime(query.getSendPayDate()))
                            .in(AoMergeOrderInfo::getId, ids)
                            .update();
                    aoMergeCashTurnListService.lambdaUpdate()
                            .set(AoMergeCashTurnList::getTurnStatus, AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                            .eq(AoMergeCashTurnList::getCallbackPayNo, key)
                            .update();
                }
            }
        }

    }


    public List<InvoiceWarnOrderPaxInfoVo> getInvoiceOrderInfo(String mergeOrderNo) {
        List<InvoiceWarnOrderPaxInfoVo> dataList = new ArrayList<>();
        AoMergeOrderInfo one = aoMergeOrderInfoService.lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, mergeOrderNo)
                .one();
        List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery().eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, one.getId())
                .list();
        for (AoMergeSuborderInfo e : list) {
            InvoiceWarnOrderPaxInfoVo vo = new InvoiceWarnOrderPaxInfoVo();
            vo.setSubMergeOrderId(e.getId());

            List<String> objects = new ArrayList<>();
            objects.add(e.getPayPaxId());
            Map<String, String> paxerPhones = paxerPhoneUtil.getPaxersPhones(objects);
            String s = paxerPhones.get(e.getPayPaxId());
//            String s = "13281234564,13284567897,1327891231";
            List<String> phones = new ArrayList<>();
            if (StringUtils.isNotEmpty(s)) {
                phones.addAll(Arrays.asList(s.split(",")));
            }
            BeanUtils.copyProperties(e, vo);
            vo.setPhones(phones);
            vo.setPracticalOrderPrice(e.getPracticalOrderPrice().toString());
            vo.setInvoiceId(e.getInvoiceJoinId());
            vo.setMergeOrderId(e.getAoMergeOrderInfoId());
            dataList.add(vo);
        }
        return dataList;
    }

    /**
     * 发送订单发开开具提示短信
     *
     * @param invoiceSmsSendDtos
     */
    @Transactional(rollbackFor = Exception.class)
    public void invoiceSms(List<InvoiceWarnOrderPaxInfoVo> invoiceSmsSendDtos) {
        log.info("合并辅营开票提醒短信通知请求：{}, user:{}", JSON.toJSONString(invoiceSmsSendDtos), AuthenticationUtil.getUserNo());
        Map<String, List<InvoiceWarnOrderPaxInfoVo>> collect = invoiceSmsSendDtos.stream().collect(Collectors.groupingBy(data -> data.getPayPaxId()));
        Iterator<Map.Entry<String, List<InvoiceWarnOrderPaxInfoVo>>> iterator = collect.entrySet().iterator();
        SmsTemplate oneSmsTemplateByCode = null;
        try {
            oneSmsTemplateByCode = smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.AO_INVOICE_EXPRESS.getCode());
        } catch (BusinessException bue) {
            log.info("发票开具提示短信发送失败：模板不存在");
            return;
        }
        final String smsContent = oneSmsTemplateByCode.getTemplateContent();
        while (iterator.hasNext()) {
            Map.Entry<String, List<InvoiceWarnOrderPaxInfoVo>> next = iterator.next();
            List<InvoiceWarnOrderPaxInfoVo> value = next.getValue();
            InvoiceWarnOrderPaxInfoVo ele = value.get(0);
            Set<String> sendPhones = new HashSet<>();
            for (InvoiceWarnOrderPaxInfoVo xx : value) {
                sendPhones.addAll(xx.getPhones());
            }

            try {
                SmsSendRecord smsSendRecord = smsService.send(oneSmsTemplateByCode, smsContent, sendPhones.toArray(new String[sendPhones.size()]), false, SmsSendType.MANUAL, sendRecord -> {
                    sendRecord.setReciverName(ele.getPaxName());
                    sendRecord.setSendUser(AuthenticationUtil.getUserNo());
                    sendRecord.setFlightNo(ele.getFlightNo());
                    sendRecord.setFlightDate(ele.getFlightDate().atStartOfDay());
                    sendRecord.setPnrRef(ele.getPnr());
                    sendRecord.setSegment(ele.getOrg() + "-" + ele.getDst());
                    sendRecord.setTktNo(ele.getTktNo());
                    sendRecord.setPaxId(ele.getPayPaxId());
                });
                if (smsSendRecord == null || !"0".equals(smsSendRecord.getSendState())) {
                    log.info("发票短信发送失败:{}-{}", ele.getPaxName(), sendPhones);
                }
            } catch (Exception e) {
                log.error("发票开具短信提示发送异常：{}", e.getMessage(), e);
            }

        }


    }



    /**
     * 退款前判断升舱订单是否支持退款
     *
     * @param subOrderId
     * @return
     */
    private void doBeforeRefund(String subOrderId) {

        // 查询当前升舱订单结果：
        AoMergeCounterUpgrade upgradeOrder = aoMergeCounterUpgradeService.getOne(Wrappers.<AoMergeCounterUpgrade>lambdaQuery()
                .eq(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, subOrderId));
        if (null == upgradeOrder) {
            return;
        }
        String orderNum = upgradeOrder.getUpgradeOrder();
        UpgOrderDetail orderTetail = PeUpgradeUtils.getOrderTetail(orderNum);
        if (null != orderTetail && orderTetail.getOrderStatus().equals(PeUpgradeUtils.INIT_SUCCESS_UNPAY)) {
            //取消订单
            PeUpgradeUtils.cancelOrder(upgradeOrder.getUpgradeOrder());
        } else  if (null != orderTetail && orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL)){
            throw new BusinessException(MessageCode.SYSTEM_BUSY.getCode());

        }else{
            //判断当前的客票状态是否为Used
            FltPassengerRealInfo passengerRealInfo = fltPassengerRealInfoService.getOne(Wrappers.<FltPassengerRealInfo>lambdaQuery()
                    .eq(FltPassengerRealInfo::getId, upgradeOrder.getPaxId())
                    .eq(FltPassengerRealInfo::getIsCancel, "N"));
            if (null != passengerRealInfo) {
                if (StringUtils.equals(passengerRealInfo.getPnrActionCode(), "USED")) {
                    log.info("当前客票为USED状态，无法发起退款-{}", orderNum);
                    throw new BusinessException(MessageCode.UPGRADE_ORDER_TKT_STATUS_USED.getCode());
                }
            }
            if (null != orderTetail && (orderTetail.getOrderStatus().equals(PeUpgradeUtils.PE_OPERATION_SUCCESS) || orderTetail.getOrderStatus().equals(PeUpgradeUtils.PE_OPERATION_FAIL) || orderTetail.getOrderStatus().equals(PeUpgradeUtils.REFUND_TICKET_FAIL))) {
                try {
                    PeUpgradeUtils.refundOrder(orderNum);
                } catch (Exception e) {
                    log.info("PE退票异常：{}", e.getMessage(), e);
                }
            } else {
                log.info("PE订单{}状态{}不做操作", orderNum, orderTetail==null?"null":orderTetail.getOrderStatus());
            }
        }
        aoMergeCounterUpgradeService.update(Wrappers.<AoMergeCounterUpgrade>lambdaUpdate()
                .set(AoMergeCounterUpgrade::getUpgradeStatus, AoMergeCons.UPGRADE_STATUS_FAIL)
                .eq(AoMergeCounterUpgrade::getUpgradeOrder, orderNum));

    }


    private void doBeforeCancle(String subOrderId){

        // 查询当前升舱订单结果：
        AoMergeCounterUpgrade upgradeOrder = aoMergeCounterUpgradeService.getOne(Wrappers.<AoMergeCounterUpgrade>lambdaQuery()
                .eq(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, subOrderId));
        if (null == upgradeOrder) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        String orderNum = upgradeOrder.getUpgradeOrder();
        UpgOrderDetail orderTetail = PeUpgradeUtils.getOrderTetail(orderNum);


        if(null == orderTetail){
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }

        if(orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL)){
            throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_APPLY.getCode());
        }else if(orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL_SUCCESS) ){
            return;
        }else if(!(orderTetail.getOrderStatus().equals(PeUpgradeUtils.INIT_SUCCESS_UNPAY) || orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL_FAIL) )){
            throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_AGAIN.getCode());
        }

        try {
            CancelOrderOutPutBean cancelOrderOutPutBean = PeUpgradeUtils.cancelOrder(upgradeOrder.getUpgradeOrder());
            if(cancelOrderOutPutBean.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL)){
                throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_APPLY.getCode());
            }else if(cancelOrderOutPutBean.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL_FAIL)){
                throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_AGAIN.getCode());
            }
        }catch (BusinessException e) {
            throw e;
        }catch (Exception e) {
            log.info("PE取消异常：{}", e.getMessage(), e);
            throw new BusinessException(MessageCode.UPGRADE_CUSTOMIZE.getCode(),new String[]{e.getMessage()});
        }


    }

    /**
     * 切换支付方式
     * @param mergeOrderNo
     * @param payType
     * @param currencySpecies
     * @return
     */
    public String updateOrderPayType(String mergeOrderNo, String payType, String currencySpecies) {
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, mergeOrderNo)
                .one();
        if (mergeOrderInfo == null) {
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }

        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.getById(mergeOrderInfo.getOrderPayId());
        if (!payOrderInfo.getPayStatus().equals(AoMergeCons.PAY_ORDER_STATUS_UNPAY)) {
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }
        if (payType.equals(payOrderInfo.getPayType())) {
            return payOrderInfo.getPayPath();
        }


        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String username = authentication.getRealName();
        log.info(username + ">修改订单" + mergeOrderNo + "收款方式:" + payOrderInfo.getPayType() + ">>>" + payType);
        AoMergePayOrderInfo aoMergePayOrderInfo = new AoMergePayOrderInfo();
        BeanUtils.copyProperties(payOrderInfo, aoMergePayOrderInfo);
        aoMergePayOrderInfo.setPayType(payType);
        aoMergePayOrderInfo.setId(null);
        String precreate = null;

        //现金支付
        if (payType.equals(AoOrderConstant.ORDER_CHARGE_TYPE_CASH) || payType.equals(AoOrderConstant.ORDER_CHARGE_TYPE_POS)) {
            aoMergePayOrderInfo.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_UNPAY);
            aoMergePayOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT);
            aoMergePayOrderInfo.setPayPath(null);
            aoMergePayOrderInfo.setBankOrderNo(null);
            aoMergePayOrderInfoService.save(aoMergePayOrderInfo);
            mergeOrderInfo.setOrderPayId(aoMergePayOrderInfo.getId());
            aoMergeOrderInfoService.saveOrUpdate(mergeOrderInfo);//回填支付单Id
        } else {

            String mergeOrderBankOrderNo = AoCommonUtils.getMergeOrderBankOrderNo(
                    mergeOrderInfo.getServicePortType(),
                    PayTypeToPayCodeEnum.findEnumByPayType(payType).getPayTypeOrderCode()
            );

            aoMergePayOrderInfo.setBankOrderNo(mergeOrderBankOrderNo);
            //线上支付方式
            if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(payType) && "CNY".equals(currencySpecies)) {
                WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
                wxrequestParam.setOutTradeNo(mergeOrderBankOrderNo);
                wxrequestParam.setTotalFee(aoMergePayOrderInfo.getPrice().toString());
                wxrequestParam.setBody(wxrequestParam.getBody());
                precreate = wxNativeProcess.precreate(wxrequestParam, wxpayClientConfig);
            }
            if (AoMergeCons.ORDER_CHARGE_TYPE_YEE.equals(payType) && "CNY".equals(currencySpecies)) {
                YeeRequestCommonParam param = new YeeRequestCommonParam();
                param.setAmount(AoUtils.pointsToYuan(aoMergePayOrderInfo.getPrice().toString()));
                param.setOrderNo(mergeOrderBankOrderNo);
                param.setGoodsName(param.getGoodsName());
                precreate = yeeProcess.precreate(param, yeeConfig);
            }
            if (AoMergeCons.ORDER_CHARGE_TYPE_ALIPAY.equals(payType) && "CNY".equals(currencySpecies)) {
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(mergeOrderBankOrderNo);
                requestCommonParam.setTotalAmount(AoUtils.pointsToYuan(aoMergePayOrderInfo.getPrice().toString()));
                requestCommonParam.setSubject(requestCommonParam.getSubject());
                precreate = aliNativeProcess.precreate(requestCommonParam, alipayClientConfig);

            }
            if (AoMergeCons.ORDER_CHARGE_TYPE_WORLD_PAY.equals(payType)) {
                precreate = worldpayUtil.orderCreateHosted(mergeOrderBankOrderNo, currencySpecies, AoUtils.pointsToYuan(aoMergePayOrderInfo.getPrice().toString()), "ALL");
                if (StringUtils.isNotEmpty(precreate)) {
                    precreate = precreate + mergeNotifyFront;
                }
                log.info("合并订单worldPay三方返回地址{}", precreate);

            }
            if (StringUtils.isNotEmpty(precreate)) {
                aoMergePayOrderInfo.setPayPath(precreate);
                aoMergePayOrderInfo.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_UNPAY);
                aoMergePayOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT);
                aoMergePayOrderInfoService.save(aoMergePayOrderInfo);
                mergeOrderInfo.setOrderPayId(aoMergePayOrderInfo.getId());
                aoMergeOrderInfoService.saveOrUpdate(mergeOrderInfo);//回填支付单Id

            } else {
                throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
            }
        }
        return precreate;
    }
}



