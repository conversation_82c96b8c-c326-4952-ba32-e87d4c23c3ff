<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.overweightbkg.mapper.VwFdY3uMapper">

  <select id="getInternalOverweightBkgConfig" resultType="java.lang.String">
    SELECT
    PRICE_ONEWAY
    FROM
    VW_FD_Y_3U v
    <where>
      AND
      v.UP_LOCATION = #{org}
      AND
      v.DIS_LOCATION = #{dst}
      AND
      (
      #{localDate} &lt;= date_format(v.END_DATE,'%Y-%m-%d')
      AND #{localDate} &gt;= date_format(v.START_DATE,'%Y-%m-%d')
      )
    </where>


  </select>

  <delete id="deleteVwFdY3us">
    DELETE  FROM VW_FD_Y_3U
  </delete>

</mapper>