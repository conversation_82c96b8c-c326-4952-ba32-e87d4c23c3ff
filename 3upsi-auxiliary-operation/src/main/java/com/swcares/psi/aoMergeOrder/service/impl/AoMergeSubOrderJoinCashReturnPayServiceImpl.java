package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSubOrderJoinCashReturnPay;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeSubOrderJoinCashReturnPayMapper;
import com.swcares.psi.aoMergeOrder.service.AoMergeSubOrderJoinCashReturnPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AoMergeSubOrderJoinCashReturnPayServiceImpl extends ServiceImpl<AoMergeSubOrderJoinCashReturnPayMapper, AoMergeSubOrderJoinCashReturnPay> implements AoMergeSubOrderJoinCashReturnPayService {
}
