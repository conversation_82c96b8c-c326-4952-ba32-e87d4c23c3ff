<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.stimulate.mapper.AoStimulateOrderInfoMapper">

    <select id="reportOrderStimulatePage" resultType="com.swcares.psi.stimulate.vo.ReportOrderStimulatePageVo">
        <include refid="reportOrderStimulateSql"></include>
    </select>
    <select id="reportOrderStimulateList" resultType="com.swcares.psi.stimulate.vo.ReportOrderStimulatePageVo">
        <include refid="reportOrderStimulateSql"></include>

    </select>
    <select id="stimulate" resultType="com.swcares.psi.stimulate.vo.ReportStimulateCountVo">
        select
        sum(a.employeeStimulate) as employee,
        sum(a.departmentStimulate) as department
        from (
        <include refid="reportOrderStimulateSql"></include>
        ) a

    </select>
    <sql id="reportOrderStimulateSql">
    select
        asoi.id,
        date_format(asoi.ORDER_TIME, '%Y-%m-%d') AS orderDate,
        date_format(asoi.FLIGHT_DATE, '%Y-%m-%d') AS flightDate,
        asoi.FLIGHT_NO as flightNo,
        asoi.SEGMENT as segment,
        asoi.ORDER_PRICE as orderPrice,
        CASE
            WHEN asoi.ORDER_TYPE = '1' THEN '一人多座'
            WHEN asoi.ORDER_TYPE = '2' THEN '付费选座'
            WHEN asoi.ORDER_TYPE = '3' THEN '逾重行李'
            WHEN asoi.ORDER_TYPE = '4' THEN '贵宾室'
            when asoi.ORDER_TYPE = '5' then '柜台升舱'
            else '机上升舱'
        END AS orderType,

       'CNY(人民币)'  as currencySpecies,
       case  asoi.FLIGHT_TYPE
           when 'D' THEN '国内'
           when 'I' THEN '国际'
           when 'R' THEN '地区'
       END as flightType,
       asoi.ORDER_CREATE_USER as userName,
       asoi.ORDER_CREATE_NO as userNo,
       asoi.ORDER_CREATE_USER_DEPARTMENT_NAME as stimulateDepartmentName,
       asoi.ORDER_CREATE_ADMINISTRATIVE_NAME as stimulateAdministrativeName,
       sum(CASE dt.stimulate_type when '1' then dt.STIMULATE_PRICE else 0 end ) as employeeStimulate,
       sum(CASE dt.stimulate_type when '2' then dt.STIMULATE_PRICE else 0 end ) as departmentStimulate
    from AO_STIMULATE_ORDER_INFO asoi
    left join AO_STIMULATE_ORDER_DETAILS dt  on dt.STIMULATE_ORDER_ID=asoi.id

    <where>
        asoi. STATUS='1' and asoi.ORDER_STATUS='1' and asoi.IS_CANCEL='N'  and dt.HISTORY='0'
        <if test="dto.userName != null and dto.userName!='' ">
           and asoi.ORDER_CREATE_USER like CONCAT('%',#{dto.userName},'%')
        </if>

        <if test="dto.userNo != null and dto.userNo!='' ">
          and  asoi.ORDER_CREATE_NO like CONCAT('%',#{dto.userNo},'%')
        </if>

        <if test="dto.orderCreateUserDepartment != null and dto.orderCreateUserDepartment!='' ">
          and  asoi.ORDER_CREATE_USER_DEPARTMENT = #{dto.orderCreateUserDepartment}
        </if>

        <if test="dto.orderCreateAdministrative != null and dto.orderCreateAdministrative!='' ">
            and  asoi.ORDER_CREATE_ADMINISTRATIVE = #{dto.orderCreateAdministrative}
        </if>

        <if test="dto.orderDateStart != null and dto.orderDateStart !=''">
            and asoi.ORDER_TIME  <![CDATA[ >= ]]> date_format(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.orderDateEnd != null and dto.orderDateEnd !=''">
            and asoi.ORDER_TIME  <![CDATA[ <= ]]> date_format(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.flightDateStart != null and dto.flightDateStart !=''">
            and asoi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d')
        </if>

        <if test="dto.flightDateEnd != null and dto.flightDateEnd !=''">
            and asoi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d')
        </if>
        <if test="dto.currencySpeciesCode != null and dto.currencySpeciesCode !=''">

        AND find_in_set(asoi.CURRENCY_SPECIES_CODE, #{dto.currencySpeciesCode})
        </if>

        <if test="dto.orderType != null and dto.orderType !=''">

            AND find_in_set(asoi.ORDER_TYPE, #{dto.orderType})
        </if>

        <if test="dto.flightType != null and dto.flightType !=''">

            AND find_in_set(asoi.FLIGHT_TYPE, #{dto.flightType})
        </if>

    </where>
        group by  asoi.id
        order by asoi.ORDER_TIME desc ,asoi.FLIGHT_DATE desc
    </sql>




    <select id="employeeStimulatePage" resultType="com.swcares.psi.stimulate.vo.ReportEmployeeStimulatePageVo">
        <include refid="employeeStimulateSql"></include>
    </select>

    <select id="employeeStimulateList" resultType="com.swcares.psi.stimulate.vo.ReportEmployeeStimulatePageVo">
        <include refid="employeeStimulateSql"></include>
    </select>
    <sql id="employeeStimulateWhereSql">
        <where>

            details.STIMULATE_TYPE='1'  and details.HISTORY='0'  and asoi.IS_CANCEL='N' and asoi.ORDER_STATUS='1'
            <if test="dto.userName != null and dto.userName != ''">
                and details.STIMULATE_USER like CONCAT('%',#{dto.userName},'%')
            </if>
            <if test="dto.userNo != null and dto.userNo != ''">
                and details.STIMULATE_USER_NO like CONCAT('%',#{dto.userNo},'%')
            </if>
            <if test="dto.stimulateDepartment != null and dto.stimulateDepartment != ''">
                and  details.STIMULATE_DEPARTMENT = #{dto.stimulateDepartment}
            </if>
            <if test="dto.stimulateAdministrative != null and dto.stimulateAdministrative != ''">
                and  details.STIMULATE_ADMINISTRATIVE = #{dto.stimulateAdministrative}
            </if>
            <if test="dto.flightDateStart != null and dto.flightDateStart != ''">
                and asoi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d')
            </if>
            <if test="dto.flightDateEnd != null and dto.flightDateEnd != ''">
                and asoi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d')
            </if>
            <if test="dto.orderType != null and dto.orderType != ''">


                AND find_in_set(asoi.ORDER_TYPE, #{dto.orderType})
            </if>


            <if test="dto.segmentType != null and dto.segmentType != ''">
                AND find_in_set(asoi.FLIGHT_TYPE, #{dto.segmentType})

            </if>
        </where>
    </sql>
    <sql id="employeeStimulateSql">
        select xx.*  from (
            select
            GROUP_CONCAT( asoi.id) as ids,
            details.STIMULATE_USER as employeeName,
            details.STIMULATE_USER_NO as employeeNo,
            details.STIMULATE_DEPARTMENT_NAME as stimulateDepartmentName,
            details.STIMULATE_ADMINISTRATIVE_NAME as stimulateAdministrativeName,
            count(distinct asoi.ORDER_ID) as stimulateOrderCount,
            sum(asoi.ORDER_PRICE) as  orderStimulate,
            sum(details.STIMULATE_PRICE) as employeeStimulate
            from AO_STIMULATE_ORDER_INFO asoi
            left join AO_STIMULATE_ORDER_DETAILS details   on asoi.id=details.STIMULATE_ORDER_ID
      <include refid="employeeStimulateWhereSql"></include>

            group by details.STIMULATE_USER_NO
        ) xx  order by xx.employeeStimulate desc
    </sql>

<select id="employeeStimulate" resultType="com.swcares.psi.stimulate.vo.ReportStimulateCountVo">


    select
    (

    select

    count(distinct asoi.ORDER_ID)
    from AO_STIMULATE_ORDER_INFO asoi
    left join AO_STIMULATE_ORDER_DETAILS details   on asoi.id=details.STIMULATE_ORDER_ID
    <include refid="employeeStimulateWhereSql"></include>

    )as  orderCount,
    (

    select
    sum(details.STIMULATE_PRICE) as employeeStimulate
    from AO_STIMULATE_ORDER_INFO asoi
    left join AO_STIMULATE_ORDER_DETAILS details   on asoi.id=details.STIMULATE_ORDER_ID
    <include refid="employeeStimulateWhereSql"></include>

    )as employee

    from  dual

</select>



<select id="productStimulatePage" resultType="com.swcares.psi.stimulate.vo.ReportProductStimulatePageVo">
    <include refid="productStimulateSql"></include>
</select>
<select id="productStimulateListData" resultType="com.swcares.psi.stimulate.vo.ReportProductStimulatePageVo">
    <include refid="productStimulateSql"></include>
</select>
<select id="productStimulate" resultType="com.swcares.psi.stimulate.vo.ReportStimulateCountVo">
        select
        sum(a.employeeStimulate) as employee,
        sum(a.departmentStimulate) as department
        from (
        <include refid="productStimulateSql"></include>
        ) a

    </select>

<sql id="productStimulateSql" >
    select xx.orderType ,
    xx.productName ,
    count(distinct xx.id ) as stimulateOrderCount,
    sum(xx.stimulateOrderPrice) as stimulateOrderPrice ,
    sum(xx.employeeStimulate) as  employeeStimulate ,
    sum(xx.departmentStimulate) as  departmentStimulate
    from (
            select
            asoi.ORDER_TYPE as orderType,
            case
            when asoi.ORDER_TYPE = '1' then '一人多座'
            when asoi.ORDER_TYPE = '2' then '付费选座'
            when asoi.ORDER_TYPE = '3' then '逾重行李'
            when asoi.ORDER_TYPE = '4' then '贵宾室'
            when asoi.ORDER_TYPE = '5' then '柜台升舱'
            else '机上升舱'
            end as productName,
            sum(CASE detail.stimulate_type when '1' then detail.STIMULATE_PRICE else 0 end ) as employeeStimulate,
            sum(CASE detail.stimulate_type when '2' then detail.STIMULATE_PRICE else 0 end ) as departmentStimulate,
            asoi.id,
            asoi.ORDER_PRICE as stimulateOrderPrice
            from
            AO_STIMULATE_ORDER_INFO asoi
            left join ao_stimulate_order_details  detail  on detail.STIMULATE_ORDER_ID=asoi.id
            <where>

                asoi.IS_CANCEL='N' and asoi.STATUS='1'  and asoi.ORDER_STATUS='1'
                <if test="dto.stimulateDepartment != null and dto.stimulateDepartment != ''">
                    and  detail.STIMULATE_DEPARTMENT = #{dto.stimulateDepartment}
                </if>
                <if test="dto.stimulateAdministrative != null and dto.stimulateAdministrative != ''">
                    and  detail.STIMULATE_ADMINISTRATIVE = #{dto.stimulateAdministrative}
                </if>
                <if test="dto.flightDateStart != null and dto.flightDateStart != ''">
                    and asoi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d')
                </if>
                <if test="dto.flightDateEnd != null and dto.flightDateEnd != ''">
                    and asoi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d')
                </if>
                <if test="dto.segmentType != null and dto.segmentType != ''">
                    AND find_in_set(asoi.FLIGHT_TYPE, #{dto.segmentType})

                </if>
                <if test="dto.orderType != null and dto.orderType != ''">
                    AND find_in_set( asoi.ORDER_TYPE , #{dto.orderType})
                </if>
            </where>

            group by  asoi.id
    ) xx
    group by  xx.orderType
    order by sum(xx.employeeStimulate)  desc


    </sql>



<select id="departmentStimulatePage" resultType="com.swcares.psi.stimulate.vo.ReportDepartmentStimulatePageVo">
    <include refid="departmentStimulateSql"></include>
</select>
<select id="departmentStimulateListData" resultType="com.swcares.psi.stimulate.vo.ReportDepartmentStimulatePageVo">
    <include refid="departmentStimulateSql"></include>
</select>

<select id="departmentStimulate" resultType="com.swcares.psi.stimulate.vo.ReportStimulateCountVo">
        select
        sum(a.employeeStimulate) as employee,
        sum(a.departmentStimulate) as department
        from (
        <include refid="departmentStimulateSql"></include>
        ) a

    </select>

<sql id="departmentStimulateSql">



select xx.* from (
    select
    count(distinct asoi.id) as stimulateOrderCount,
    emp.STIMULATE_DEPARTMENT as stimulateDepartment,
    emp.STIMULATE_DEPARTMENT_NAME AS stimulateDepartmentName,
    emp.STIMULATE_ADMINISTRATIVE_NAME AS stimulateAdministrativeName,
    sum(asoi.ORDER_PRICE) as stimulatePrice,
    sum(CASE emp.stimulate_type when '1' then emp.STIMULATE_PRICE else 0 end ) as employeeStimulate,
    sum(CASE emp.stimulate_type when '2' then emp.STIMULATE_PRICE else 0 end ) as departmentStimulate
    from AO_STIMULATE_ORDER_DETAILS emp
    inner join AO_STIMULATE_ORDER_INFO asoi on asoi.id=emp.STIMULATE_ORDER_ID
    where   asoi.IS_CANCEL='N' and asoi.STATUS='1'  and asoi.ORDER_STATUS='1'
    <if test="dto.stimulateDepartment != null and dto.stimulateDepartment != ''">
        and  emp.STIMULATE_DEPARTMENT = #{dto.stimulateDepartment}
    </if>
    <if test="dto.stimulateAdministrative != null and dto.stimulateAdministrative != ''">
        and  emp.STIMULATE_ADMINISTRATIVE = #{dto.stimulateAdministrative}
    </if>
    <if test="dto.flightDateStart != null and dto.flightDateStart != ''">
        and asoi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d')
    </if>
    <if test="dto.flightDateEnd != null and dto.flightDateEnd != ''">
        and asoi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d')
    </if>
    <if test="dto.orderType != null and dto.orderType != ''">
        AND find_in_set( asoi.ORDER_TYPE , #{dto.orderType})
    </if>
    <if test="dto.segmentType != null and dto.segmentType != ''">
        AND find_in_set(asoi.FLIGHT_TYPE, #{dto.segmentType})
    </if>
    group by  emp.STIMULATE_DEPARTMENT_NAME, emp.STIMULATE_ADMINISTRATIVE_NAME
    ) xx
    order by  xx.departmentStimulate desc

</sql>

</mapper>
