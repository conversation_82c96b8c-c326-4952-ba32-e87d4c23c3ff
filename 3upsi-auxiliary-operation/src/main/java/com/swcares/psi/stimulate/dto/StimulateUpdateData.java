package com.swcares.psi.stimulate.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class StimulateUpdateData {

//    sid  接口请求 ID
// message  处理信息
// status  处理结果状态 success/error
// resData  返回数据包

    private String sid;
    private String message;
    private String status;
    private List<ResData> resData;


    @Data
    public static class ResData{
// pk_rate 汇率主键 共享平台汇率唯一标识
// pk_curr 外币主键
// currcode 币种编码 国际币种编码，如 CNY
// ratevalue 汇率值（中间价）
// ratedate 汇率日期 YYYY-MM-DD
// dr 删除标识 1 = 删除， 0 = 未删除
// ts 时间戳 当前汇率最后修改时间
// rowno 数据行号 本次条件请求的数据行号
    private String rowno;
    private String pk_rate;
    private String pk_curr;
    private String currcode;
    private String ratevalue;
    private LocalDate ratedate;
    private String dr;
    private LocalDateTime ts;

    }
}
