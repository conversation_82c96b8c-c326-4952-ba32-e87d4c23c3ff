##############SDKéç½®æä»¶ï¼è¯ä¹¦æ¹å¼ç­¾åï¼################
# è¯´æï¼
# 1. ä½¿ç¨æ¶è¯·å°æ­¤æä»¶å¤å¶å°srcæä»¶å¤¹ä¸æ¿æ¢åæ¥çacp_sdk.propertiesã
# 2. å·ä½éç½®é¡¹è¯·æ ¹æ®æ³¨éä¿®æ¹ã
#
################################################

##########################å¥ç½æµè¯ç¯å¢äº¤æåéå°åï¼çº¿ä¸æµè¯éè¦ä½¿ç¨çäº§ç¯å¢äº¤æè¯·æ±å°åï¼#############################

##äº¤æè¯·æ±å°å 
acpsdk.frontTransUrl=https://gateway.test.95516.com/gateway/api/frontTransReq.do
acpsdk.backTransUrl=https://gateway.test.95516.com/gateway/api/backTransReq.do
acpsdk.singleQueryUrl=https://gateway.test.95516.com/gateway/api/queryTrans.do
acpsdk.batchTransUrl=https://gateway.test.95516.com/gateway/api/batchTrans.do
acpsdk.fileTransUrl=https://filedownload.test.95516.com/
acpsdk.appTransUrl=https://gateway.test.95516.com/gateway/api/appTransReq.do
acpsdk.cardTransUrl=https://gateway.test.95516.com/gateway/api/cardTransReq.do

#ä»¥ä¸ç¼´è´¹äº§åä½¿ç¨ï¼å¶ä½äº§åç¨ä¸å°
acpsdk.jfFrontTransUrl=https://gateway.test.95516.com/jiaofei/api/frontTransReq.do
acpsdk.jfBackTransUrl=https://gateway.test.95516.com/jiaofei/api/backTransReq.do
acpsdk.jfSingleQueryUrl=https://gateway.test.95516.com/jiaofei/api/queryTrans.do
acpsdk.jfCardTransUrl=https://gateway.test.95516.com/jiaofei/api/cardTransReq.do
acpsdk.jfAppTransUrl=https://gateway.test.95516.com/jiaofei/api/appTransReq.do

########################################################################

# æ¥æçæ¬å·ï¼åºå®5.1.0ï¼è¯·å¿æ¹å¨
acpsdk.version=5.1.0

# ç­¾åæ¹å¼ï¼è¯ä¹¦æ¹å¼åºå®01ï¼è¯·å¿æ¹å¨
acpsdk.signMethod=01

# æ¯å¦éªè¯éªç­¾è¯ä¹¦çCNï¼æµè¯ç¯å¢è¯·è®¾ç½®falseï¼çäº§ç¯å¢è¯·è®¾ç½®trueãéfalseçå¼é»è®¤é½å½trueå¤çã
acpsdk.ifValidateCNName=false

# æ¯å¦éªè¯httpsè¯ä¹¦ï¼æµè¯ç¯å¢è¯·è®¾ç½®falseï¼çäº§ç¯å¢å»ºè®®ä¼åå°è¯trueï¼ä¸è¡åfalseãétrueçå¼é»è®¤é½å½falseå¤çã
acpsdk.ifValidateRemoteCert=false

#åå°éç¥å°åï¼å¡«åæ¥æ¶é¶èåå°éç¥çå°åï¼å¿é¡»å¤ç½è½è®¿é®
acpsdk.backUrl=http://***************:8080/ACPSample_DaiShou/backRcvResponse

#åå°éç¥å°åï¼å¡«åå¤çé¶èåå°éç¥çå°åï¼å¿é¡»å¤ç½è½è®¿é®
acpsdk.frontUrl=http://localhost:8080/ACPSample_DaiShou/frontRcvResponse

#########################å¥ç½æµè¯ç¯å¢ç­¾åè¯ä¹¦éç½® ################################
# å¤è¯ä¹¦çæåµè¯ä¹¦è·¯å¾ä¸ºä»£ç æå®ï¼å¯ä¸å¯¹æ­¤ååéç½®ã
# ç­¾åè¯ä¹¦è·¯å¾ï¼å¿é¡»ä½¿ç¨ç»å¯¹è·¯å¾ï¼å¦æä¸æ³ä½¿ç¨ç»å¯¹è·¯å¾ï¼å¯ä»¥èªè¡å®ç°ç¸å¯¹è·¯å¾è·åè¯ä¹¦çæ¹æ³ï¼æµè¯è¯ä¹¦ææåæ·å±ç¨å¼ååä¸­çæµè¯ç­¾åè¯ä¹¦ï¼çäº§ç¯å¢è¯·ä»cfcaä¸è½½å¾å°ã
# windowsæ ·ä¾ï¼
acpsdk.signCert.path=D:/certs/acp_test_sign.pfx
# linuxæ ·ä¾ï¼æ³¨æï¼å¨linuxä¸è¯»åè¯ä¹¦éè¦ä¿è¯è¯ä¹¦æè¢«åºç¨è¯»çæéï¼ï¼åç»­å¶ä»è·¯å¾éç½®ä¹åæ­¤æ¡è¯´æï¼
#acpsdk.signCert.path=/SERVICE01/usr/ac_frnas/conf/ACPtest/acp700000000000001.pfx

# ç­¾åè¯ä¹¦å¯ç ï¼æµè¯ç¯å¢åºå®000000ï¼çäº§ç¯å¢è¯·ä¿®æ¹ä¸ºä»cfcaä¸è½½çæ­£å¼è¯ä¹¦çå¯ç ï¼æ­£å¼ç¯å¢è¯ä¹¦å¯ç ä½æ°éå°äºç­äº6ä½ï¼å¦åä¸ä¼ å°åæ·æå¡ç½ç«ä¼å¤±è´¥
acpsdk.signCert.pwd=000000
# ç­¾åè¯ä¹¦ç±»åï¼åºå®ä¸éè¦ä¿®æ¹
acpsdk.signCert.type=PKCS12

##########################å å¯è¯ä¹¦éç½®################################
# ææä¿¡æ¯å å¯è¯ä¹¦è·¯å¾(åæ·å·å¼éäºåæ·å¯¹ææä¿¡æ¯å å¯çæéï¼éè¦å¯¹ å¡å·accNoï¼pinåphoneNoï¼cvn2ï¼expiredå å¯ï¼å¦æè¿äºä¸éçè¯ï¼ï¼å¯¹ææä¿¡æ¯å å¯ä½¿ç¨)
acpsdk.encryptCert.path=d:/certs/acp_test_enc.cer

##########################éªç­¾è¯ä¹¦éç½®################################
# éªç­¾ä¸­çº§è¯ä¹¦è·¯å¾(é¶èæä¾)
acpsdk.middleCert.path=D:/certs/acp_test_middle.cer
# éªç­¾æ ¹è¯ä¹¦è·¯å¾(é¶èæä¾)
acpsdk.rootCert.path=D:/certs/acp_test_root.cer

