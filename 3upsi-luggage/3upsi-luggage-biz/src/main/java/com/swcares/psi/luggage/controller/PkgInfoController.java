package com.swcares.psi.luggage.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.component.core.exception.ColumnAuthException;
import com.component.core.processor.FieldPermissionsProcessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.swcares.psi.base.fileuploadanddownload.UploadAndDownload;
import com.swcares.psi.base.util.DateUtils;
import com.swcares.psi.base.web.BaseController;
import com.swcares.psi.common.freemarker.FreemarkerUtils;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.luggage.dto.PkgAccidentCloseListDto;
import com.swcares.psi.luggage.dto.PkgImgDto;
import com.swcares.psi.luggage.dto.PkgInfoQueryParamDto;
import com.swcares.psi.luggage.dto.PkgOrderPageDto;
import com.swcares.psi.luggage.dto.PkgQuickSearchDto;
import com.swcares.psi.luggage.dto.PkgUpdateInfoDto;
import com.swcares.psi.luggage.entity.PkgImg;
import com.swcares.psi.luggage.mapper.PkgImgMapper;
import com.swcares.psi.luggage.service.PkgInfoService;
import com.swcares.psi.luggage.vo.CommunicateVo;
import com.swcares.psi.luggage.vo.OrderPrintVo;
import com.swcares.psi.luggage.vo.PkgAccidentDetailInfoVos;
import com.swcares.psi.luggage.vo.PkgExportInfoVo;
import com.swcares.psi.luggage.vo.PkgOrderPageVo;
import com.swcares.psi.luggage.vo.PkgPageInfoVo;
import com.swcares.psi.luggage.vo.PkgQuickSearchVos;
import com.swcares.psi.base.data.api.entity.EmployeeJpa;
import com.swcares.psi.base.data.mapper.UserJpaDao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

/**
 * ClassName：com.swcares.psi.modules.pkg <br>
 * Description：异常行李 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月22日 9:32 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/luggage/pkg")
@Api(tags = "web异常行李")
@Slf4j
@Validated
public class PkgInfoController extends BaseController {

    private static final String FILE_NAME = "pkgInfoTemplate.xlsx";

    @Autowired
    private PkgInfoService pkgInfoService;

    @Autowired
    private UserJpaDao userJpaDao;

    @Autowired
    private UploadAndDownload uploadAndDownload;

    @Autowired
    private PkgImgMapper pkgImgMapper;

    @Autowired
    private FreemarkerUtils freemarkerUtils;

    @Autowired
    private FieldPermissionsProcessor fieldPermissionsProcessor;

    /**
     * Title：pkgMatch <br>
     * Description： 异常行李H5行李匹配<br>
     * author：王建文 <br>
     * date：2020-3-22 11:19 <br>
     *
     * @param queryParamDto 参数接收
     */
    @GetMapping("pkgMatch")
    @ApiOperation(value = "异常行李H5行李匹配")
    public RenderResult pkgMatch(PkgInfoQueryParamDto queryParamDto) {
        return RenderResult.success(pkgInfoService.getPkgInfo(queryParamDto));
    }

    /**
     * Title：detailInfo <br>
     * Description： 行李异常详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:40 <br>
     *
     * @param accidentId 事故单号
     */
    @GetMapping("detailInfo")
    @ApiOperation(value = "行李异常详情")
    public RenderResult detailInfo(String accidentId) {
        return RenderResult.success(pkgInfoService.getPkgDetailInfo(accidentId));
    }

    /**
     * Title：pkgCase <br>
     * Description： 事故单结案<br>
     * author：王建文 <br>
     * date：2020-3-22 15:48 <br>
     *
     * @param accidentId 事故单号
     */
    @PostMapping("pkgCase")
    @ApiOperation(value = "事故单结案")
    public RenderResult pkgCase(String accidentId) {
        pkgInfoService.pkgCase(accidentId.split(","));
        return RenderResult.success();
    }

    /**
     * Title：getPkgInfoPage <br>
     * Description： 分页查询异常行李列表,分页查询事故单列表<br>
     * author：王建文 <br>
     * date：2020-3-22 17:31 <br>
     *
     * @param queryParamDto 查询参数
     */
    @GetMapping("getPkgInfoPage")
    @ApiOperation(value = "分页查询异常行李（事故单）列表")
    public RenderResult<PsiPage> getPkgInfoPage(PkgInfoQueryParamDto queryParamDto) {
        PsiPage<PkgPageInfoVo> psiPage = pkgInfoService.getPkgInfoPage(queryParamDto);
        return RenderResult.success(psiPage);
    }

    /**
     * Title：importDataByExcel <br>
     * Description： 异常行李导入<br>
     * author：王建文 <br>
     * date：2020-4-20 13:34 <br>
     *
     * @param
     */
    @PostMapping("/importDataByExcel")
    @ApiOperation(value = "导入", notes = "异常行李数据导入")
    public RenderResult<Object> importDataByExcel(HttpServletRequest request) throws Exception {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        Map<String, Object> resultData = new HashMap<>();
        int totalExitCount = 0;
        String totalExitContent = "";
        int totalFailureCount = 0;
        String totalFailureContent = "";
        int totalFailParamCount = 0;
        String totalFailParamContent = "";
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        EmployeeJpa employee = userJpaDao.findByTuNo(createUser);
        String workAirport = employee.getAirport3code();
        if (files.size() > 0) {
            //解析破损行李
            List<Map<String, Object>> result = null;
            try (InputStream inputStream = files.get(0).getInputStream()) {
                result = ExcelUtils.read(inputStream, 0);
            }
            for (Map<String, Object> map : result) {
                if (null == map.get("航班号") || null == map.get("航班日期")) {
                    return new RenderResult("1", "模板导入有误!", "模板导入有误");
                }
            }
            if (result.size() > 0) {
                Map<String, Object> resultMap = pkgInfoService.parseData(result, "1", workAirport);
                int exitCount = Integer.valueOf(resultMap.get("exitCount").toString());
                if (exitCount > 0) {
                    totalExitCount += exitCount;
                    totalExitContent += resultMap.get("exitContent").toString();
                }
                int failureCount = Integer.valueOf(resultMap.get("failureCount").toString());
                if (failureCount > 0) {
                    totalFailureCount += failureCount;
                    totalFailureContent += resultMap.get("failureContent").toString();
                }
                int failParamCount = Integer.valueOf(resultMap.get("failParamCount").toString());
                if (failParamCount > 0) {
                    totalFailParamCount += failParamCount;
                    totalFailParamContent += resultMap.get("failParamContent").toString();
                }
            }
            //解析少收行李
            List<Map<String, Object>> littlePkgInfo = null;
            try (InputStream inputStream = files.get(0).getInputStream()) {
                littlePkgInfo = ExcelUtils.read(inputStream, 1);
            }
            if (littlePkgInfo.size() > 0) {
                Map<String, Object> resultMap = pkgInfoService.parseData(littlePkgInfo, "2", workAirport);
                int exitCount = Integer.valueOf(resultMap.get("exitCount").toString());
                if (exitCount > 0) {
                    totalExitCount += exitCount;
                    totalExitContent += resultMap.get("exitContent").toString();
                }
                int failureCount = Integer.valueOf(resultMap.get("failureCount").toString());
                if (failureCount > 0) {
                    totalFailureCount += failureCount;
                    totalFailureContent += resultMap.get("failureContent").toString();
                }
                int failParamCount = Integer.valueOf(resultMap.get("failParamCount").toString());
                if (failParamCount > 0) {
                    totalFailParamCount += failParamCount;
                    totalFailParamContent += resultMap.get("failParamContent").toString();
                }
            }
            List<Map<String, Object>> morePkgInfo = null;
            try (InputStream inputStream = files.get(0).getInputStream()) {
                morePkgInfo = ExcelUtils.read(inputStream, 2);
            }
            if (morePkgInfo.size() > 0) {
                Map<String, Object> resultMap = pkgInfoService.parseData(morePkgInfo, "3", workAirport);
                int exitCount = Integer.valueOf(resultMap.get("exitCount").toString());
                if (exitCount > 0) {
                    totalExitCount += exitCount;
                    totalExitContent += resultMap.get("exitContent").toString();
                }
                int failureCount = Integer.valueOf(resultMap.get("failureCount").toString());
                if (failureCount > 0) {
                    totalFailureCount += failureCount;
                    totalFailureContent += resultMap.get("failureContent").toString();
                }
                int failParamCount = Integer.valueOf(resultMap.get("failParamCount").toString());
                if (failParamCount > 0) {
                    totalFailParamCount += failParamCount;
                    totalFailParamContent += resultMap.get("failParamContent").toString();
                }
            }
            //处理返回值
            resultData.put("totalExitCount", totalExitCount);
            resultData.put("totalExitContent", totalExitContent);
            resultData.put("totalFailureCount", totalFailureCount);
            resultData.put("totalFailureContent", totalFailureContent);
            resultData.put("totalFailParamCount", totalFailParamCount);
            resultData.put("totalFailParamContent", totalFailParamContent);
        }
        return RenderResult.success(resultData);
    }

    /**
     * Title：exportData <br>
     * Description： 异常行李导出<br>
     * author：王建文 <br>
     * date：2020-4-21 16:58 <br>
     *
     * @param queryParamDto 查询参数
     * @return
     */
    @GetMapping("exportData")
    public void exportData(PkgInfoQueryParamDto queryParamDto, HttpServletResponse response) throws Exception {
        List<PkgExportInfoVo> pkgPageInfoVoList = pkgInfoService.getExportPkgInfo(queryParamDto);
//        String[] columnNames = { "状态", "类型", "行李事故单号", "航班号", "航班日期", "航段", "服务航站", "旅客姓名", "行李号", "补偿总额", "赔付单数量", "尺寸", "数据导入", "发放人", "申请时间" };
//        String[] keys = { "status", "accidentType", "accidentId", "flightNo", "flightDate", "segment", "serviceCity", "paxName", "pkgNo", "totalPay",
//                "payCount", "pkgSize", "dataType", "grantUser", "createTime" };
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("异常行李报表" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        //ExcelImportExportUtil.exportExcel(response, fileName, pkgPageUInfoVoList, columnNames, keys);
        ExcelUtils.writeExcel(response.getOutputStream(), pkgPageInfoVoList, PkgExportInfoVo.class, "", null);
    }

    /**
     * Title：downLoadExcel <br>
     * Description：异常行李导入模板下载<br>
     * author：王建文 <br>
     * date：2020-4-22 13:59 <br>
     *
     * @param
     * @return
     */
    @GetMapping("downLoadExcel")
    public void downLoadExcel(HttpServletResponse response) throws Exception {
        // 配置文件下载
        response.setContentType("multipart/form-data");
        // 下载文件能正常显示中文
        response.addHeader(
                "Content-Disposition",
                "attachment;fileName=" + new String(FILE_NAME.getBytes("UTF-8"), "iso-8859-1"));
        uploadAndDownload.ftpDownload(response.getOutputStream(), FILE_NAME, uploadAndDownload.getRemotePath());
    }

    @ApiOperation(value = "创建事故单的行李图片上传(已废弃)")
    @PostMapping(value = "/uploadImg")
    public RenderResult uploadImg(@RequestBody @ApiParam(name = "PkgImgDto", value = "行李图片上传对象") PkgImgDto pkgImgDto) {
        StringBuffer sb = new StringBuffer();
        List<MultipartFile> files = pkgImgDto.getFiles();
        if (files.size() == 0) {
            return RenderResult.fail();
        }
        files.forEach(file -> {
            Date date = new Date();
            if (StringUtils.isNotEmpty(file.getOriginalFilename())) {
                String fileName = UUID.randomUUID().toString();
                String fileSuffix =
                        file.getOriginalFilename().substring(
                                file.getOriginalFilename().lastIndexOf("."));
                // 为防止重名文件需加上时间戳后缀
                String remote = fileName + "-" + date.getTime() + fileSuffix;
                try {
                    sb.append(uploadAndDownload.
                            ftpUpload(file, remote)).append(",");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        String url = sb.toString().substring(0, sb.length() - 1);
        PkgImg pkgImg = pkgImgDto.getPkgImg();
        pkgImg.setImgUrl(url);
        pkgImg.setStatus("1");
        pkgImgMapper.insert(pkgImg);
        return RenderResult.success(url);
    }

    @GetMapping("/getPkgAccidentDetail")
    @ApiOperation(value = "PC端获取事故单完整详情")
    public RenderResult<PkgAccidentDetailInfoVos> getPkgAccidentDetail(String accidentId) {
        return RenderResult.success(pkgInfoService.getPkgAccidentDetail(accidentId));
    }

    @GetMapping("/getPkgOrderInfo")
    @ApiOperation(value = "获取赔偿单列表")
    public RenderResult<PsiPage<PkgOrderPageVo>> getPkgOrderInfo(PkgOrderPageDto pkgOrderPageDto) {
        PsiPage<PkgOrderPageVo> psiPage = new PsiPage<>();
        return RenderResult.success(pkgInfoService.getOrderList(psiPage, pkgOrderPageDto));
    }

    @GetMapping("/listQuickSearch")
    @ApiOperation(value = "快速检索信息-列表")
    public RenderResult<PkgQuickSearchVos> listQuickSearch(PkgQuickSearchDto pkgQuickSearchDto) {
        PsiPage<PkgQuickSearchVos> page = new PsiPage<>(pkgQuickSearchDto);
        return RenderResult.success(pkgInfoService.listPkgQuickSearch(page, pkgQuickSearchDto));
    }

    @GetMapping("/downloadOrderInfo")
    @ApiOperation(value = "打印赔偿单")
    public void downloadOrderInfo(HttpServletRequest request, HttpServletResponse response, String orderId, Integer imgWidthPx, Integer imgHeightPx) {
        try {
            Map<String, Object> returnMap = pkgInfoService.getOrderPrintInfo(orderId);
            ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
            OrderPrintVo orderPrintVo = new OrderPrintVo();
            BeanUtil.fillBeanWithMap(returnMap, orderPrintVo, false);
            try {
                fieldPermissionsProcessor.process(serverHttpRequest, orderPrintVo);
            } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
                log.error("数据脱敏失败", e);
                throw new ColumnAuthException(e);
            }
            response.setHeader("Content-Type", "application/json");
            response.getWriter().write(JSONObject.toJSONString(RenderResult.success(freemarkerUtils.turnImage(imgWidthPx, 3200, true, "luggageCompensateVoucherPayment.ftl", orderPrintVo, response)), SerializerFeature.WriteMapNullValue));
        } catch (Exception e) {
            log.error("打印赔偿单信息：", e);
            try {
                response.getWriter().write(JSONObject.toJSONString(RenderResult.fail()));
            } catch (IOException ex) {
                log.error("打印赔偿单信息返回失败",ex);
            }
        }
    }

    @GetMapping("/downloadImgList")
    @ApiOperation(value = "打印事故单关联图片")
    public RenderResult downloadImgList(HttpServletResponse response, String accidentId, Integer imgWidthPx, Integer imgHeightPx,HttpServletRequest request) {
        try {
            List<Map<String, Object>> returnMapList = pkgInfoService.getImgPrintInfo(accidentId,request);
            if (null != returnMapList && !returnMapList.isEmpty()) {
                List<String> resultString = new ArrayList<>();
                for (Map<String, Object> resultMap : returnMapList) {
                    String s = freemarkerUtils.turnImage(imgWidthPx, imgHeightPx, true, "luggageCompensateVoucherPayment.ftl", resultMap, response);
                    resultString.add(s);
                }
                return RenderResult.success(resultString);
            }
            return RenderResult.success();
        } catch (Exception e) {
            log.error("打印事故单关联图片信息：", e);
            return RenderResult.fail();
        }
    }

    @GetMapping("/downLoadCommunicate")
    @ApiOperation(value = "打印事故单关联联系记录")
    public void downLoadCommunicate(HttpServletRequest request,HttpServletResponse response, String accidentId, Integer imgWidthPx, Integer imgHeightPx) {
        try {
            Map<String, Object> returnMap = pkgInfoService.downLoadCommunicate(accidentId);
            CommunicateVo communicateVo = new CommunicateVo();
            BeanUtil.fillBeanWithMap(returnMap, communicateVo, false);
            ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
            try {
                fieldPermissionsProcessor.process(serverHttpRequest, communicateVo);
            } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
                log.error("数据脱敏失败", e);
                throw new ColumnAuthException(e);
            }
            response.setHeader("Content-Type", "application/json");
            response.getWriter().write(JSONObject.toJSONString(RenderResult.success(freemarkerUtils.turnImage(imgWidthPx, imgHeightPx, true, "recordCommunication.ftl", communicateVo, response)), SerializerFeature.WriteMapNullValue));
        } catch (Exception e) {
            log.error("打印事故单关联联系记录：", e);
            try {
                response.getWriter().write(JSONObject.toJSONString(RenderResult.fail()));
            } catch (IOException ex) {
                log.error("打印事故单关联联系记录返回失败",ex);
            }
        }
    }

    /**
     * author：like
     *
     * @param pkgInfoDto
     * @return
     * @throws Exception
     */
    @PostMapping("/updateAccident")
    @ApiOperation(value = "修改异常行李事故单（包含作废）")
    public RenderResult<String> updateAccident(@RequestBody PkgUpdateInfoDto pkgInfoDto) throws Exception {
       //手工录入
        if("2".equals(pkgInfoDto.getDataType())){
            return RenderResult.success(pkgInfoService.updateAccidentArtificial(pkgInfoDto));
        }
        //系统录入
        return RenderResult.success(pkgInfoService.updateAccident(pkgInfoDto));
    }

    /**
     * 事故单批量结案
     * 2021-07-07
     * @param pkgAccidentCloseDto
     * @return
     */
    @PostMapping("/accidentClose")
    @ApiOperation(value = "批量事故单结案")
    public RenderResult<Boolean> pkgCase(@RequestBody PkgAccidentCloseListDto pkgAccidentCloseDto) {
        return RenderResult.success(pkgInfoService.accidentCloseList(pkgAccidentCloseDto));
    }
    @GetMapping("/haveOnLineBelow")
    @ApiOperation(value = "查询事故单是否有线下审核 accidentId 事故单ID")
    public RenderResult<Boolean> haveOnLineBelow (String accidentId) {
        Boolean aBoolean = pkgInfoService.haveOnLineBelow(accidentId);
        return RenderResult.success(aBoolean);
    }


}
