package com.swcares.psi;

import com.component.core.annotation.EnableColumnPermissions;
import com.swcares.psi.base.base.ScgsiRepositoryImpl;
import com.swcares.psi.common.security.annotation.EnablePsiFeignClients;
import com.swcares.psi.common.security.annotation.EnablePsiResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Description: []
 * Created on 2020-11-20 10:54
 *
 * <AUTHOR>
 * @version 1.0
 */
@EnablePsiResourceServer
@EnablePsiFeignClients
@SpringCloudApplication
@EnableSwagger2
@EnableAutoConfiguration
@EnableJpaRepositories(repositoryBaseClass= ScgsiRepositoryImpl.class)
@EnableColumnPermissions
public class AoApplication {
    public static void main(String[] args) {
        SpringApplication.run(AoApplication.class, args);
    }
}
