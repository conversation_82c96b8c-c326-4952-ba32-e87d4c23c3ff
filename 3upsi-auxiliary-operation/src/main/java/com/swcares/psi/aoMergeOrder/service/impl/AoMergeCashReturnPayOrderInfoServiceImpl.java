package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashReturnPayOrderInfo;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeCashReturnPayOrderInfoMapper;
import com.swcares.psi.aoMergeOrder.service.AoMergeCashReturnPayOrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 缴纳支付
 */
@Slf4j
@Service
public class AoMergeCashReturnPayOrderInfoServiceImpl extends ServiceImpl<AoMergeCashReturnPayOrderInfoMapper, AoMergeCashReturnPayOrderInfo> implements AoMergeCashReturnPayOrderInfoService {

    @Override
    public Integer validateTurnOrder(List<String> subOrderIdList) {
        return baseMapper.validateTurnOrder(subOrderIdList);
    }

    @Override
    public List<String> turnNotifyOrderNoLock(String turnOrderNo) {
        return baseMapper.turnNotifyOrderNoLock(turnOrderNo);
    }
}
