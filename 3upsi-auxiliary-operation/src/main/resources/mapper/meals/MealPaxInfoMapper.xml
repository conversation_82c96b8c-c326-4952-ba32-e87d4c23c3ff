<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.mapper.MealPaxInfoMapper">

    <select id="queryPaxInfo" resultType="com.swcares.psi.base.data.api.vo.PaxBaseInfo" parameterType="com.swcares.psi.base.data.api.vo.PaxBaseInfo">
		select
		    id,
			flight_date,
			flight_date as flightDateStr,
			flight_number,
			org,
			dst,
			ticket_number,
			pnr_ref,
			seat_number,
			boarding_number,
			ifnull(passenger_name, passenger_name_en) as name,
			id_type,
			id_number,
			is_cancel,
			case when is_cancel='Y' then 1 else 0 end as cancelFlag
		from
			flt_passenger_real_info
		where
			1 = 1
        <if test="dto.id != null and dto.id!=''">
            and id=#{dto.id}
        </if>
        <if test="dto.flightDate != null">
            and flight_date=#{dto.flightDate}
        </if>
        <if test="dto.flightDateStr != null and dto.flightDateStr!=''">
            and flight_date=#{dto.flightDateStr}
        </if>
        <if test="dto.flightNumber != null and dto.flightNumber!=''">
            and flight_number=#{dto.flightNumber}
        </if>
        <if test="dto.org != null and dto.org!=''">
            and org=#{dto.org}
        </if>
        <if test="dto.dst != null and dto.dst!=''">
            and dst=#{dto.dst}
        </if>
        <if test="dto.ticketNumber != null and dto.ticketNumber!=''">
            and ticket_number=#{dto.ticketNumber}
        </if>
        <if test="dto.pnrRef != null and dto.pnrRef!=''">
            and pnr_ref=#{dto.pnrRef}
        </if>
        <if test="dto.seatNumber != null and dto.seatNumber!=''">
            and seat_number=#{dto.seatNumber}
        </if>
        <if test="dto.boardingNumber != null and dto.boardingNumber!=''">
            and boarding_number=#{dto.boardingNumber}
        </if>
        <if test="dto.name != null and dto.name!=''">
            and (passenger_name=#{dto.name} or passenger_name_en=#{dto.name})
        </if>
        <if test="dto.idType != null and dto.idType!=''">
            and id_type=#{dto.idType}
        </if>
        <if test="dto.idNumber != null and dto.idNumber!=''">
            and id_number=#{dto.idNumber}
        </if>
        <if test="dto.isCancel != null and dto.isCancel!=''">
            and is_cancel=#{dto.isCancel}
        </if>
        limit 1
    </select>

    <select id="queryMealPaxInfoPage" parameterType="com.swcares.psi.ao.model.dto.MealPaxInfoDto"
            resultType="com.swcares.psi.ao.model.vo.MealPaxInfoVO">
        <include refid="queryMealPaxInfo"></include>
    </select>
    
    <sql id="queryMealPaxInfo">
        select
        temp.paxName paxName,
        temp.gender,
        temp.pnr pnr,
        case temp.idType
        when 'NI' then '身份证'
        when 'PP' or 'PSPT' then '护照'
        when 'CC' then '信用卡'
        when 'ID' then '军官证'
        when 'UU' then '其他'
        else temp.idType
        end idType,
        temp.idNo idNo,
        temp.flightNo flightNo,
        temp.org org,
        temp.dst dst,
        case
        when length(temp.flightSegment) = 11 then
        (concat(case
        when ft.AIRPORT_3CODE = 'TFU' then '成都天府'
        when ft.AIRPORT_3CODE = 'CTU' then '成都双流'
        when ft.AIRPORT_3CODE = 'CKG' then '重庆'
        else ft.CITY_CH_NAME end,
        '-',
        case
        when substring(temp.flightSegment, 5, 3) = 'TFU' then '成都天府'
        when substring(temp.flightSegment, 5, 3) = 'CTU' then '成都双流'
        when substring(temp.flightSegment, 5, 3) = 'CKG' then '重庆'
        else (select ft3.CITY_CH_NAME
        from foc50_t7001 ft3
        where ft3.AIRPORT_3CODE = substring(temp.flightSegment, 5, 3)) end,
        '-',
        case
        when ft2.AIRPORT_3CODE = 'TFU' then '成都天府'
        when ft2.AIRPORT_3CODE = 'CTU' then '成都双流'
        when ft2.AIRPORT_3CODE = 'CKG' then '重庆'
        else ft2.CITY_CH_NAME end))
        else
        (concat(case
        when ft.AIRPORT_3CODE = 'TFU' then '成都天府'
        when ft.AIRPORT_3CODE = 'CTU' then '成都双流'
        when ft.AIRPORT_3CODE = 'CKG' then '重庆'
        else ft.CITY_CH_NAME end,
        '-',
        case
        when ft2.AIRPORT_3CODE = 'TFU' then '成都天府'
        when ft2.AIRPORT_3CODE = 'CTU' then '成都双流'
        when ft2.AIRPORT_3CODE = 'CKG' then '重庆'
        else ft2.CITY_CH_NAME end))
        end flightLine,
        date_format(temp.std, '%Y-%m-%d %H:%i:%s') std,
        date_format(temp.flightDate, '%Y-%m-%d') flightDate,
        temp.mainClass mainClass,
        concat(temp.mealsCode, temp.mealsName) mealsName,
        temp.approveUser,
        temp.approveRet,
        date_format(temp.approveTime, '%Y-%m-%d %H:%i:%s') approveTime,
        temp.mealsSource mealsSource,
        case ffri2.flight_state when 'D' then '延误'
        when 'C' then '取消'
        else '正常' end flightStatus,
        case
        when temp.psgStatus = 'Y' then '已退改'
        else '正常'
        end psgStatus,
        temp.mealsType mealsCategory,
        temp.specialStatus specialStatus,
        temp.mealsId,
        temp.remark
        from
        (
        select
        case when fpri2.PASSENGER_NAME is null then fpri2.PASSENGER_NAME_EN else fpri2.PASSENGER_NAME end paxName,
        case when fpri2.GENDER = 'M' and fpri2.PASSENGER_TYPE = 'ADT' then '男'
        when fpri2.GENDER = 'F' and fpri2.PASSENGER_TYPE = 'ADT' then '女'
        when fpri2.PASSENGER_TYPE = 'CHD' then '儿童'
        when fpri2.PASSENGER_TYPE = 'INF' then '婴儿'
        end gender,
        mpi.pnr pnr,
        mpi.id_type idType,
        mpi.id_no idNo,
        mpi.flight_no flightNo,
        mpi.org org,
        mpi.dst dst,
        mpi.std,
        mpi.flight_date flightDate,
        fpri2.MAIN_CABIN mainClass,
        mpi.meals_name mealsName,
        '系统' approveUser,
        case
        mpi.meals_status when 200 then '审核通过'
        when 20 then '审核未通过'
        end approveRet,
        mpi.approve_time approveTime,
        mpi.meals_source mealsSource,
        mpi.meals_code mealsCode,
        mpi.meals_type mealsType,
        fpri2.FLIGHT_SEGMENT flightSegment,
        fpri2.IS_CANCEL psgStatus,
        case
        mpi.meals_status when 200 then '正常'
        when 20 then '取消'
        end specialStatus,
        mpi.id mealsId,
        mpi.remark
        from
        meal_pax_info mpi,flt_passenger_real_info fpri2
        where
        1 = 1 and mpi.flight_no=fpri2.flight_number and mpi.flight_date=fpri2.flight_date and mpi.org=fpri2.org and mpi.dst=fpri2.dst and mpi.ticket_number=fpri2.ticket_number
        <if test="dto.paxName!=null and dto.paxName!=''">
            and (fpri2.PASSENGER_NAME like concat('%',#{dto.paxName},'%') or fpri2.PASSENGER_NAME_EN like concat('%',#{dto.paxName},'%'))
        </if>
        <if test="dto.idNo!=null and dto.idNo!=''">
            and mpi.id_no = #{dto.idNo}
        </if>
        <if test="dto.pnr!=null and dto.pnr!=''">
            and mpi.pnr = #{dto.pnr}
        </if>
        <if test="dto.flightNo!=null and dto.flightNo!=''">
            and mpi.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.endDate!=null and dto.endDate!='' and dto.startDate!=null and dto.startDate!='' ">
            and mpi.flight_date between #{dto.startDate} and #{dto.endDate}
        </if>
        <if test="dto.approveStatus == 'AT'">
            and mpi.meals_status = 200
        </if>
        <if test="dto.approveStatus == 'AF'">
            and mpi.meals_status = 20
        </if>
        <if test="dto.approveStatus == 'PR'">
            and 1 != 1
        </if>
        <if test="dto.org != null and dto.org != ''">
            and mpi.org = #{dto.org}
        </if>
        <if test="dto.dst != null and dto.dst != ''">
            and mpi.dst = #{dto.dst}
        </if>
        <if test='dto.gender == "M" or dto.gender == "F"'>
            and fpri2.gender = #{dto.gender} and fpri2.PASSENGER_TYPE = 'ADT'
        </if>
        <if test= 'dto.gender == "CHD" or dto.gender == "INF"'>
            and fpri2.PASSENGER_TYPE = #{dto.gender}
        </if>
        <if test="dto.mealsSource != null and dto.mealsSource != '' and dto.mealsSource == 'APP' ">
            and mpi.meals_source = '机供品系统'
        </if>
        <if test="dto.mealsSource != null and dto.mealsSource != '' and dto.mealsSource == 'ASlink' ">
            and mpi.meals_source = '黑屏系统'
        </if>
        <if test="dto.depPoint != null and dto.depPoint.size() == 1 and dto.depPoint.get(0) == 'CDCQ'">
            and mpi.org in ('TFU', 'CTU', 'CKG')
        </if>
        <if test="dto.depPoint != null and dto.depPoint.size() == 1 and dto.depPoint.get(0) == 'OTHER'">
            and mpi.org not in ('TFU', 'CTU', 'CKG')
        </if>
        union
        select
        case when fpri.PASSENGER_NAME is null then fpri.PASSENGER_NAME_EN else fpri.PASSENGER_NAME end paxName,
        case when fpri.GENDER = 'M' and fpri.PASSENGER_TYPE = 'ADT' then '男'
        when fpri.GENDER = 'F' and fpri.PASSENGER_TYPE = 'ADT' then '女'
        when fpri.PASSENGER_TYPE = 'CHD' then '儿童'
        when fpri.PASSENGER_TYPE = 'INF' then '婴儿'
        end gender,
        fpri.PNR_REF pnr,
        fpri.ID_TYPE idType,
        fpri.ID_NUMBER idNo,
        fpri.FLIGHT_NUMBER flightNo,
        fpri.ORG org,
        fpri.DST dst,
        ffri.STD std,
        fpri.FLIGHT_DATE flightDate,
        fpri.MAIN_CABIN mainClass,
        fpss.SPECIAL_NAME mealsName,
        fpss.APPROVE_USER approveUser,
        case
        when fpss.APPROVE_STATUS = '1' and fpri.IS_CANCEL = 'N' then '审核通过'
        when fpss.APPROVE_STATUS = '2' or fpss.APPROVE_STATUS = '3' then '审核未通过'
        when fpss.APPROVE_STATUS = '1' and fpri.IS_CANCEL = 'Y' then '已取消'
        end approveRet,
        fpss.APPROVE_DATE approveTime,
        '黑屏系统' mealsSource,
        fpss.SPECIAL_CODE mealsCode,
        '免费点餐' mealsType,
        fpri.FLIGHT_SEGMENT flightSegment,
        fpri.IS_CANCEL psgStatus,
        case
        when fpss.ACTION_CODE in ('XX', 'NO', 'UC', 'UN', 'HX') then '取消'
        when fpss.ACTION_CODE in ('HK', 'RR', 'KK','NN') then '正常'
        end specialStatus,
        fpss.ID mealsId,
        fpss.APPROVE_DESCRIBE remark
        from
        flt_passenger_real_info fpri
        inner join flt_passenger_special_service fpss on
        fpss.PASSR_ID = fpri.ID
        left join flt_flight_real_info ffri on
        ffri.FLIGHT_NUMBER = fpri.FLIGHT_NUMBER
        and ffri.ORG = fpri.ORG
        and ffri.DST = fpri.DST
        and ffri.FLIGHT_DATE = fpri.FLIGHT_DATE
        where
        1 = 1
        and fpss.APPROVE_STATUS is not null
        and fpss.SPECIAL_CODE in ('BLML','CHML','DBML','FPML','GFML','HFML','KSML','LCML','LFBRS','LFML','LPML','LSML','MOML','NLML','PRML','VGML','VLML','VOML','HNML','RVML','SFML','SPML','VJML','AVML','BBML')
        <if test="dto.paxName!=null and dto.paxName!=''">
            and (fpri.PASSENGER_NAME like concat('%',#{dto.paxName},'%') or fpri.PASSENGER_NAME_EN like concat('%',#{dto.paxName},'%'))
        </if>
        <if test="dto.idNo!=null and dto.idNo!=''">
            and fpri.ID_NUMBER = #{dto.idNo}
        </if>
        <if test="dto.pnr!=null and dto.pnr!=''">
            and fpri.PNR_REF = #{dto.pnr}
        </if>
        <if test="dto.flightNo!=null and dto.flightNo!=''">
            and fpri.FLIGHT_NUMBER = #{dto.flightNo}
        </if>
        <if test="dto.endDate!=null and dto.endDate!='' and dto.startDate!=null and dto.startDate!='' ">
            and fpri.FLIGHT_DATE between #{dto.startDate} and #{dto.endDate}
        </if>
        <if test="dto.approveStatus == 'AT'">
            and (fpss.APPROVE_STATUS = '1' or fpss.INFO_SOURCE = 'C')
        </if>
        <if test="dto.approveStatus == 'AF'">
            and fpss.APPROVE_STATUS in ('2','3')
        </if>
        <if test="dto.approveStatus == 'PR'">
            and fpss.APPROVE_STATUS = '1' and fpri.IS_CANCEL = 'Y'
        </if>
        <if test="dto.org != null and dto.org != ''">
            and fpri.ORG = #{dto.org}
        </if>
        <if test="dto.dst != null and dto.dst != ''">
            and fpri.DST = #{dto.dst}
        </if>
        <if test='dto.gender == "M" or dto.gender == "F"'>
            and fpri.gender = #{dto.gender} and fpri.PASSENGER_TYPE = 'ADT'
        </if>
        <if test= 'dto.gender == "CHD" or dto.gender == "INF"'>
            and fpri.PASSENGER_TYPE = #{dto.gender}
        </if>
        <if test="dto.mealsSource == 'APP'">
            and 1 != 1
        </if>
        <if test="dto.depPoint != null and dto.depPoint.size() == 1 and dto.depPoint.get(0) == 'CDCQ'">
            and fpri.ORG in ('TFU', 'CTU', 'CKG')
        </if>
        <if test="dto.depPoint != null and dto.depPoint.size() == 1 and dto.depPoint.get(0) == 'OTHER'">
            and fpri.ORG not in ('TFU', 'CTU', 'CKG')
        </if>
        ) temp
        left join flt_flight_real_info ffri2 on
        ffri2.FLIGHT_NUMBER = temp.flightNo
        and ffri2.ORG = temp.org
        and ffri2.DST = temp.dst
        and ffri2.FLIGHT_DATE = temp.flightDate
        left join foc50_t7001 ft on
        ft.AIRPORT_3CODE = temp.org
        left join foc50_t7001 ft2 on
        ft2.AIRPORT_3CODE = temp.dst
        order by temp.flightDate desc,temp.flightNo
    </sql>

    <select id="queryMealPaxInfoList" parameterType="com.swcares.psi.ao.model.dto.MealPaxInfoDownLoadDto"
            resultType="com.swcares.psi.ao.model.vo.MealPaxInfoDownLoadVO">
        select
        (@i:=@i+1) as serialNumber,a.*
        from
        (SELECT @i:=0) as i,
        (<include refid="queryMealPaxInfo"></include>) a
    </select>

    <select id="queryMealPaxInfoByMealsId" parameterType="String" resultType="com.swcares.psi.ao.model.MealPaxInfo">
        select
        mpi.*
        from meal_pax_info mpi
        where
        1 = 1
        <if test="mealsId != null and mealsId != ''">
            and mpi.meals_id = #{mealsId}
        </if>
        order by mpi.update_date desc limit 1
    </select>

    <select id="queryByPaxId" parameterType="String" resultType="com.swcares.psi.ao.model.MealPaxInfo">
        select
        mpi.*
        from meal_pax_info mpi,flt_passenger_real_info fpri
        where
        1 = 1 and mpi.flight_no=fpri.flight_number and mpi.flight_date=fpri.flight_date and mpi.org=fpri.org and mpi.dst=fpri.dst and mpi.ticket_number=fpri.ticket_number
        <if test="paxId != null and paxId != ''">
            and fpri.id = #{paxId}
        </if>
        order by mpi.update_date desc limit 1
    </select>
</mapper>