package com.swcares.psi.aoMergeOrder.utils;

import com.swcares.psi.ao.i18n.RequestAcceptHeaderLocaleResolver;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderValidataDto;
import com.swcares.psi.base.base.SpringUtil;
import com.swcares.psi.combine.intlang.ResourceBundleMessageSourceFactory;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class I18nMsgUtils {

    public static final String ERROR_CODE_PRICE="1";
    public static final String UPGRADE_TIME_ERROR="2";


    public static String getMsgByCode(String code){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        RequestAcceptHeaderLocaleResolver localeResolver = SpringUtil.getBean(RequestAcceptHeaderLocaleResolver.class);
        // 国际化处理
        Locale local = localeResolver.resolveLocale(request);
        // 设置meesage
        ResourceBundleMessageSource rbms =
                ResourceBundleMessageSourceFactory.get(local.toString());
        // 设置i18n
        String i18nMsg = rbms.getMessage(code + "", null, local);

        return i18nMsg;
    }


    public static List<AoMergeOrderValidataDto.ReasonRefusal> messageConvert(List<AoMergeOrderValidataDto.I18nCodeInfo> codes){
       List<AoMergeOrderValidataDto.ReasonRefusal> list= new ArrayList<>();
       for(AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfos :codes){
           String msgByCode = getMsgByCode(i18nCodeInfos.getCode());
           String[] param = i18nCodeInfos.getParam();
           if(param!=null && param.length>0){
               for (int i=0;i<param.length;i++){
                   msgByCode = msgByCode.replace("{" + i + "}", param[i]);
               }
           }
           AoMergeOrderValidataDto.ReasonRefusal reasonRefusal = new AoMergeOrderValidataDto.ReasonRefusal();
           reasonRefusal.setProductId(i18nCodeInfos.getProductId());
           reasonRefusal.setErrorCode(i18nCodeInfos.getErrorCode());
           reasonRefusal.setRefusal(msgByCode);
           list.add(reasonRefusal );
       }
        return list;
    }
}
