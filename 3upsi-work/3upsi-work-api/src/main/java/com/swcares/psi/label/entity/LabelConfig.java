package com.swcares.psi.label.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：. <br>
 * Description：(LabelConfig)表实体类<br>
 * Copyright ©  xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022/2/18 13:17<br>
 * @version v1.0 <br>
 */
@Data
@TableName("label_config")
@ApiModel(value = "LabelConfig对象", description = "")
public class LabelConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "标签ID", required = true)
    private String id;

    @ApiModelProperty(value = "idList")
    @TableField(exist = false)
    private List<String> idList;

    @ApiModelProperty(value = "部门名称")
    @TableField(exist = false)
    private String departmentName;

    @ApiModelProperty(value = "标签名称", required = true)
    @TableField("`NAME`")
    private String name;

    @ApiModelProperty(value = "父级标签ID, 一级标签为@ROOT", required = true)
    @TableField("PID")
    private String pid;

    @ApiModelProperty(value = "标签等级(1:一级标签;2:二级标签;3:三级标签)", required = true)
    @TableField("`LEVEL`")
    private String level;

    @ApiModelProperty(value = "标签状态(0:启用;1禁用)", required = true)
    @TableField("`STATUS`")
    private String status;

    @ApiModelProperty(value = "备注", required = true)
    @TableField("`DESCRIBE`")
    private String describe;

    @ApiModelProperty(value = "创建人工号", required = true)
    @TableField("CREATOR")
    private String creator;

    @ApiModelProperty(value = "创建时间", required = true)
    @TableField(fill = FieldFill.INSERT, value = "CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "修改人工号", required = true)
    @TableField("UPDATER")
    private String updater;

    @ApiModelProperty(value = "修改时间", required = true)
    @TableField(fill = FieldFill.UPDATE, value = "UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "归属部门ID", required = true)
    @TableField("DEPARTMENT_ID")
    private String departmentId;

    @ApiModelProperty(value = "归属部门多级ID", required = true)
    @TableField("DEPARTMENT_ID_LIST")
    private String departmentIdList;

    @ApiModelProperty(value = "是否删除(0:未删除;1:已删除)", required = true)
    @TableField("IS_DELETE")
    private String isDelete;

    @ApiModelProperty(value = "标签属性主键ID", required = true)
    @TableField("LABEL_TYPE_ID")
    private String labelTypeId;

}

