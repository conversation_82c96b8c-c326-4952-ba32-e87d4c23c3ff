package com.swcares.psi.aoMergeOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.dto.AoMergeViproomAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeViproomItemSell;
import com.swcares.psi.aoMergeOrder.vo.AoMergeViproomItemSellAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付贵宾厅套餐销售产品详情订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeViproomItemSellMapper extends BaseMapper<AoMergeViproomItemSell> {
    List<AoMergeViproomItemSellAccountsReportVo> getAccountsList(@Param("dto") AoMergeViproomAccountsReportDto dto);
    PsiPage<AoMergeViproomItemSellAccountsReportVo> getAccountsListPage(PsiPage<AoMergeViproomItemSellAccountsReportVo> page, @Param("dto") AoMergeViproomAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(@Param("dto") AoMergeViproomAccountsReportDto dto);
}
