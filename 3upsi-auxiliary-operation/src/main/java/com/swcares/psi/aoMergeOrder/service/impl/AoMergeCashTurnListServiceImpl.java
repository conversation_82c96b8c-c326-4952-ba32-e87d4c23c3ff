package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashTurnList;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeCashTurnListMapper;
import com.swcares.psi.aoMergeOrder.payHander.AliMergePayClientConfig;
import com.swcares.psi.aoMergeOrder.payHander.WxMergePayClientConfig;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCashTurnListService;
import com.swcares.psi.aoMergeOrder.utils.MergeOrderUtils;
import com.swcares.psi.aoMergeOrder.vo.CashTurnOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.CashTurnSaveVo;
import com.swcares.psi.collect.qrCode.alipay.AliNativeProcess;
import com.swcares.psi.collect.qrCode.alipay.AliRequestCommonParam;
import com.swcares.psi.collect.qrCode.wx.WxNativeProcess;
import com.swcares.psi.collect.qrCode.wx.WxRequestCommonParam;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 合并支付现金缴纳清单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeCashTurnListServiceImpl extends ServiceImpl<AoMergeCashTurnListMapper, AoMergeCashTurnList> implements IAoMergeCashTurnListService {


    @Resource(name = "mergeOrderWxTrunPayConfig")
    WxMergePayClientConfig mergeOrderWxTrunPayConfig;
    @Autowired
    WxNativeProcess wxNativeProcess;




    @Resource(name = "mergeOrderTrunAliPayConfig")
    AliMergePayClientConfig mergeOrderTrunAliPayConfig;
    @Autowired
    AliNativeProcess aliNativeProcess;
    @Override
    public CashTurnSaveVo cashTurnResult(String turnNo) {
        AoMergeCashTurnList one = this.lambdaQuery().eq(AoMergeCashTurnList::getCallbackPayNo, turnNo).one();
        CashTurnSaveVo cashTurnSaveVo = new CashTurnSaveVo();
        cashTurnSaveVo.setCreateDate(one.getCreateTime());
        cashTurnSaveVo.setTurnOrderNo(one.getCallbackPayNo());
        cashTurnSaveVo.setPath(one.getQrcodePath());
        cashTurnSaveVo.setTurnPrice(one.getAmount().toString());
        cashTurnSaveVo.setTurnStatus(one.getTurnStatus());
        cashTurnSaveVo.setChargeType(one.getChargeType());
        LocalDateTime now = LocalDateTime.now().minusSeconds(7201L);
        if(one.getTurnStatus().equals(AoMergeCons.ORDER_TURN_STATUS_REFUND_ING) && one.getCreateTime().isBefore(now)){
            cashTurnSaveVo.setOverdue(true);
        }
        return cashTurnSaveVo;
    }

    @Override
    public CashTurnSaveVo refreshCashTurnResult(String turnNo) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String realName = authentication.getRealName();
        AoMergeCashTurnList one = this.lambdaQuery()
                .eq(AoMergeCashTurnList::getCallbackPayNo, turnNo)
                .ne(AoMergeCashTurnList::getTurnStatus,AoMergeCons.ORDER_TURN_STATUS_SUCCESS)
                .one();
        if(one==null){
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.minusSeconds(7202L);
        CashTurnSaveVo cashTurnSaveVo = new CashTurnSaveVo();
        cashTurnSaveVo.setCreateDate(one.getCreateTime());
        cashTurnSaveVo.setTurnOrderNo(one.getCallbackPayNo());
        cashTurnSaveVo.setPath(one.getQrcodePath());
        cashTurnSaveVo.setTurnPrice(one.getAmount().toString());
        cashTurnSaveVo.setChargeType(one.getChargeType());
        if(!one.getCreateTime().isBefore(localDateTime)){
            return cashTurnSaveVo;
        }

         String precreate=null;
        if (AoMergeCons.ORDER_CHARGE_TYPE_WX.equals(one.getChargeType())) {
            WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
            wxrequestParam.setOutTradeNo(one.getCallbackPayNo());
            wxrequestParam.setTotalFee(MergeOrderUtils.yuanConversionPointsToStr(one.getAmount()));
            wxrequestParam.setBody(wxrequestParam.getBody() + "现金缴纳");
            precreate = wxNativeProcess.precreate(wxrequestParam, mergeOrderWxTrunPayConfig);
        } else {
            AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
            requestCommonParam.setOutTradeNo(one.getCallbackPayNo());
            requestCommonParam.setTotalAmount(one.getAmount().toString());
            requestCommonParam.setSubject(requestCommonParam.getSubject() + "现金缴纳");
            precreate = aliNativeProcess.precreate(requestCommonParam, mergeOrderTrunAliPayConfig);
        }
        one.setQrcodePath(precreate);
        one.setCreateTime(now);
        this.saveOrUpdate(one);
        cashTurnSaveVo.setPath(precreate);
        return cashTurnSaveVo;
    }


    @Override
    public List<CashTurnOrderTaskVo> getCashTurnOrderAll() {
        LocalDateTime localDateTime = LocalDateTime.now().minusHours(2L);
        String createTime = DateUtils.parseLocalDateTimeToString(localDateTime,DateUtils.YYYY_MM_DD_HH_MM_SS);
        return baseMapper.getCashTurnOrderAll(createTime);
    }
}
