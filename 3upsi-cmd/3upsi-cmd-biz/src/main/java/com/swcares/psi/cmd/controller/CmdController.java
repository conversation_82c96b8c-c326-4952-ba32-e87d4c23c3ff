package com.swcares.psi.cmd.controller;

import com.swcares.psi.base.data.api.cmdReqBean.ResvSeatDto;
import com.swcares.psi.base.data.api.cmdReturnBean.AvCmdReturnBean;
import com.swcares.psi.base.data.api.cmdReturnBean.DetrCmdReturnBean;
import com.swcares.psi.base.data.api.cmdReturnBean.SimpleCmdReturnBean;
import com.swcares.psi.cmd.config.source.CmdPoolUtil;
import com.swcares.psi.cmd.service.AvCmdHanderService;
import com.swcares.psi.cmd.service.DetrCmdHanderService;
import com.swcares.psi.cmd.service.ResvSeatHandlerService;
import com.swcares.psi.cmd.service.RocCmdHanderService;
import com.swcares.psi.cmd.service.RtHandlerService;
import com.swcares.psi.cmd.service.SimpleCmdHanderService;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2021/8/11 14:38
 */
@Slf4j
@Controller
@RequestMapping("/cmd")
public class CmdController {

    @Autowired
    AvCmdHanderService avCmdHanderService;
    @Autowired
    RocCmdHanderService rocCmdHanderService;
    @Autowired
    DetrCmdHanderService detrCmdHanderService;
    @Autowired
    SimpleCmdHanderService simpleCmdHanderService;
    @Autowired
    RtHandlerService rtHandlerService;
    @Autowired
    ResvSeatHandlerService resvSeatHandlerService;

    /**
     * 资源占据
     * @param source
     * @return
     */
    @PostMapping("/getCmdSource")
    @ResponseBody
    public RenderResult getCmdSource(@RequestParam("source") String source) {
        try {
            String clientId = CmdPoolUtil.occupyAccount(source);
            log.info("获取资源{}-返回clientId:{}",source, clientId);
            return RenderResult.success(clientId);
        } catch (RuntimeException e) {
            log.info("获取资源{}-异常{}",source,e.getMessage(),e);
        }
        return RenderResult.fail();
    }


    @PostMapping("/simpleCmd")
    @ResponseBody
    public RenderResult simpleCmd(@RequestParam("cmd") String cmd,@RequestParam("source") String source,@RequestParam("clientId") String clientId) {
        SimpleCmdReturnBean send = (SimpleCmdReturnBean)simpleCmdHanderService.send(cmd, source, clientId);
        return RenderResult.success(send.getCmdRetrunStr());
    }

    /**
     * 手动释放指令账号资源  慎用
     * @param source
     * @param clientId
     * @return
     */
    @PostMapping("/releaseCmdSource")
    @ResponseBody
    public RenderResult releaseCmdSource(@RequestParam("source") String source,@RequestParam("clientId") String clientId) {
        try {
            log.info("归还资源{}-{}",source,clientId);
            if (CmdPoolUtil.releaseCmdSource(source, clientId)) {
                log.info("归还资源成功，clientId:{}", clientId);
                return RenderResult.success();
            }
        }catch (RuntimeException e){
            log.error("归还资源出错{}-{}异常:{}",source,clientId,e.getMessage(), e);
        }
        return RenderResult.fail();
    }

    @PostMapping("/avFligtSeatNumInfo")
    @ResponseBody
    public RenderResult getAvFligtSeatNumInfo(@RequestParam("source") String source,@RequestParam("flightNo") String flightNo,
                                              @RequestParam("org") String org,@RequestParam("dst") String dst,
                                              @RequestParam("date") String date,@RequestParam("clientId") String clientId) {
        AvCmdReturnBean info = (AvCmdReturnBean)avCmdHanderService.getAvFligtSeatNumInfo(flightNo, date, org, dst, source, clientId);
        return RenderResult.success(info);
    }
    @PostMapping("/analyMixed")
    @ResponseBody
    public RenderResult analyMixed(@RequestParam("org") String org,@RequestParam("flightNo") String flightNo,@RequestParam("date") String date,
                                   @RequestParam("source")String source,@RequestParam("clientId") String clientId) {
        Boolean isMixed = (Boolean)rocCmdHanderService.analyMixed(flightNo, date, org,source,clientId);
        return RenderResult.success(isMixed);
    }

    @PostMapping("/getDetrCmdInfo")
    @ResponseBody
    public RenderResult getDetrCmdInfo(@RequestParam("tktNo") String tktNo,@RequestParam("source") String source,@RequestParam("clientId") String clientId) {
        DetrCmdReturnBean detr = (DetrCmdReturnBean)detrCmdHanderService.getDetrCmdInfo(tktNo,source,clientId);
        return RenderResult.success(detr);
    }

    @PostMapping("/getPnrInfo")
    @ResponseBody
    public RenderResult<String> getPnr(@RequestParam("pnr") String pnr,@RequestParam("source") String source,@RequestParam("clientId") String clientId) {
        String ret = rtHandlerService.getPnrCompletely(pnr, source, clientId);
        if (ret == null) {
            return RenderResult.fail();
        }
        return RenderResult.success(ret);
    }

    @ApiOperation(value = "机组占座")
    @ResponseBody
    @PostMapping("/resvSeat")
    public RenderResult<String> resvSeat(@RequestParam("departCode") String departCode,
                                         @RequestParam("empNum") String empNum,
                                         @RequestParam("flightNo") String flightNo,
                                         @RequestParam("flightDate") String flightDate,
                                         @RequestParam("orgCityAirp") String orgCityAirp,
                                         @RequestParam("dstCityAirp") String dstCityAirp,
                                         @RequestParam("applyType") String applyType,
                                         @RequestParam("remark") String remark) {
        return RenderResult.success(resvSeatHandlerService.resvSeat(departCode, empNum, flightNo, flightDate, orgCityAirp, dstCityAirp, applyType, remark));
    }

    @ApiOperation(value = "机组占座取消")
    @ResponseBody
    @PostMapping("/resvSeatCancel")
    public RenderResult<String> cancelResvSeat(String pnr) {
        return RenderResult.success(resvSeatHandlerService.cancelResvSeat(pnr));
    }

}
