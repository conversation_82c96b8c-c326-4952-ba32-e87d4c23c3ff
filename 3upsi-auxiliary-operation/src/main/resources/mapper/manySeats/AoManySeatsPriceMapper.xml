<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.manySeats.mapper.AoManySeatsPriceMapper">

    <sql id="listSql">
        select
        up.id,
        air.id as airLineId,
        concat(p1.AIRPORT_NAME,'-',p1.CODE) orgName,
        concat(p2.AIRPORT_NAME,'-',p2.CODE) dstName,
        case air.TYPE
        when 'D' then '国内'
        when 'I' then '国际'
        when 'R' then '地区'
        end as lineType,

        (select com.CURRENCY_PRICE from ao_common_price_config com where com.BUSINESS_TABLE_ID =up.id and com.STATUS
        ='1' and com.CURRENCY_TYPE ='CNY' and NOW() <![CDATA[ >= ]]>com.EFFECT_DATE_TIME order by com.CREATE_TIME
        desc,com.EFFECT_DATE_TIME DESC limit 1 ) as cnyPirce,

        (
        select
        group_concat(
        concat(com.CURRENCY_PRICE, com.CURRENCY_TYPE, '(',com.CURRENCY_TYPE_NAME, ')')
        order by com.BUSINESS_TABLE_ID separator '\n'
        ) as xx
        from
        ao_common_price_config com
        where
        com.STATUS = '1'
        and com.CURRENCY_TYPE  <![CDATA[ <> ]]> 'CNY'
        and up.id=com.BUSINESS_TABLE_ID
        and NOW() <![CDATA[ >= ]]> com.EFFECT_DATE_TIME
        and com.EFFECT_DATE_TIME = (
        select B.EFFECT_DATE_TIME from ao_common_price_config B WHERE B.BUSINESS_TABLE_ID=com.BUSINESS_TABLE_ID and NOW() <![CDATA[ >= ]]>  B.EFFECT_DATE_TIME AND B.STATUS = '1'  order by B.CREATE_TIME DESC  ,B.EFFECT_DATE_TIME desc limit 1
        )
        order by
        com.BUSINESS_TABLE_ID
        ) as foreignCurrencyPrice,

        DATE_FORMAT(up.UPDATE_DATE ,'%Y-%m-%d %H:%i:%S') as updateTime,
        case up.status
        when '0' then '启用'
        when '1' then '禁用'
        end as 'status',
        emp.TU_CNAME as updateUser
        from
        sys_air_line_info air
        left join ao_many_seats_price up on up.ARI_LINE_ID =air.ID
        left join sys_airport_info p1 on p1.code=air.org and p1.is_use='0'
        left join sys_airport_info p2 on p2.code=air.DST and p2.is_use='0'
        left join employee emp on emp.TUNO =up.UPDATE_NO
        <where>
            <if test="dto.isUse != null  and dto.isUse != '' ">
                up.STATUS=#{dto.isUse}
            </if>
            <if test="dto.org != null  and dto.org != '' ">
                and air.org=#{dto.org}
            </if>
            <if test="dto.dst != null  and dto.dst != '' ">
                and air.dst=#{dto.dst}
            </if>
             <if test="dto.priceType != null  and dto.priceType != '' ">
                and air.type=#{dto.priceType}
            </if>
            <if test="dto.orgCountry!= null and dto.orgCountry != ''">
                and p1.COUNTRY_NAME=#{dto.orgCountry}
            </if>
            <if test="dto.dstCountry!= null and dto.dstCountry != ''">
                and p2.COUNTRY_NAME=#{dto.dstCountry}
            </if>
        </where>

    </sql>

    <select id="getPage" parameterType="com.swcares.psi.ao.manySeats.dto.QueryManySeatsPriceDto" resultType="com.swcares.psi.ao.manySeats.vo.AoManySeatsPriceVo">
        <include refid="listSql"></include>
    </select>
    <select id="getList" parameterType="com.swcares.psi.ao.manySeats.dto.QueryManySeatsPriceDto"  resultType="com.swcares.psi.ao.manySeats.vo.AoManySeatsPriceVo">
        <include refid="listSql"></include>
    </select>

</mapper>