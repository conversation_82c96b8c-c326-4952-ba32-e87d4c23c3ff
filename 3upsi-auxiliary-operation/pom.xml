<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>3upsi-msapi</artifactId>
        <groupId>com.swcares.3upsi</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>3upsi-auxiliary-operation</artifactId>
    <packaging>jar</packaging>
    <description>辅营模块</description>

    <dependencies>
        <!--安全模块-->
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-security</artifactId>
        </dependency>
        <!--公共配置文件提取  -->
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-yml</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-quartz</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--mybatis 模块-->
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-invoice</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-base</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-retrievephone</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.travelsky</groupId>
            <artifactId>ebuildapi-emd</artifactId>
            <version>3.9.6.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/ebuildapi-emd-3.9.6.8.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.travelsky.hub</groupId>
            <artifactId>TravelskySvc</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/TravelskySvc.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.agent</groupId>
            <artifactId>agent-cfg</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/agent-cfg-1.0.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-base-data-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-sharding-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-upms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-permission</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-coupon</artifactId>
        </dependency>
        <!--中文转拼音-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.3-RC1-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <version>1.3-RC1-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-message-biz</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-timediff</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.3upsi</groupId>
            <artifactId>3upsi-common-worldpay</artifactId>
        </dependency>
        <!--国密4加密工具类-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.66</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.swcares.psi.AoApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding><!-- 过滤后缀为pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>