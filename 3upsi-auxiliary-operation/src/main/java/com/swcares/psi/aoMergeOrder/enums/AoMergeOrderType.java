package com.swcares.psi.aoMergeOrder.enums;

import com.swcares.psi.combine.constant.MessageCode;

public enum AoMergeOrderType {
    //1一人多座,2现场座位销售(选座),3逾重行李,4贵宾室,5柜台升舱
    MANY_SEATS(1,"一人多座","1",MessageCode.REPEAT_ADD_MANY_SEATS,MessageCode.PRICE_NOT_EXIST_MANY_SEATS,MessageCode.PRICE_NOT_CONFORM_MANY_SEATS),
    SCENE_SEATS(2,"付费选座","2",MessageCode.REPEAT_ADD_SCENE_SEATS,MessageCode.PRICE_NOT_EXIST_SCENE_SEATS,MessageCode.PRICE_NOT_CONFORM_SCENE_SEATS),
    OVERWEIGHTBKG(3,"逾重行李","3",MessageCode.REPEAT_ADD_OVERWEIGHTBKG,MessageCode.PRICE_NOT_EXIST_OVERWEIGHTBKG,MessageCode.PRICE_NOT_CONFORM_OVERWEIGHTBKG),
    VIP_ROOM(4,"贵宾室购买","4",MessageCode.REPEAT_ADD_VIP_ROOM,MessageCode.PRICE_NOT_EXIST_VIP_ROOM,MessageCode.PRICE_NOT_CONFORM_VIP_ROOM),
    COUNTER_UPGRADE(5,"柜台升舱","5",MessageCode.REPEAT_ADD_COUNTER_UPGRADE,MessageCode.PRICE_NOT_EXIST_COUNTER_UPGRADE,MessageCode.PRICE_NOT_CONFORM_COUNTER_UPGRADE),
    ;
    private int value;
    private String name;
    private String code;
    private MessageCode messageCode;
    private MessageCode priceConfigMessageCode;
    private MessageCode priceConformMessageCode;
    AoMergeOrderType(int value, String name, String code,MessageCode messageCode,MessageCode priceConfigMessageCode,MessageCode priceConformMessageCode) {
        this.value = value;
        this.name = name;
        this.code = code;
        this.messageCode = messageCode;
        this.priceConfigMessageCode = priceConfigMessageCode;
        this.priceConformMessageCode = priceConformMessageCode;
    }
    public static AoMergeOrderType getType( String code) {
        for (int i = 0; i < AoMergeOrderType.values().length; i++) {
            if(code.equals(AoMergeOrderType.values()[i].getCode())){
               return AoMergeOrderType.values()[i];
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public MessageCode getMessageCode() {
        return messageCode;
    }

    public void setMessageCode(MessageCode messageCode) {
        this.messageCode = messageCode;
    }

    public MessageCode getPriceConfigMessageCode() {
        return priceConfigMessageCode;
    }

    public void setPriceConfigMessageCode(MessageCode priceConfigMessageCode) {
        this.priceConfigMessageCode = priceConfigMessageCode;
    }

    public MessageCode getPriceConformMessageCode() {
        return priceConformMessageCode;
    }

    public void setPriceConformMessageCode(MessageCode priceConformMessageCode) {
        this.priceConformMessageCode = priceConformMessageCode;
    }
}
