package com.swcares.psi.modules.cip.service;

import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.modules.imporListener.imporEntity.CipPaxImporEntity;
import com.swcares.psi.modules.sys.dto.CipQueryDto;
import com.swcares.psi.modules.sys.dto.CipSaveDto;
import com.swcares.psi.modules.sys.entity.CipPassengerRegister;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.modules.sys.vo.CipPassengerRegisterVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * cip旅客登记表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
public interface ICipPassengerRegisterService extends IService<CipPassengerRegister> {

    void saveEntity(CipSaveDto dto);

    void delete(List<String> list);
    PsiPage<CipPassengerRegisterVo> getListPage(CipQueryDto dto);
    List<CipPassengerRegisterVo> getList(CipQueryDto dto);
    List<CipPaxImporEntity> importData(List<CipPaxImporEntity> dataList);
    void cipPaxSendSmsValidata(String paxId,String idNo,String event,String changeContent);
    void cipFlightSendSmsValidata( String flightDate, String flightNo, String event, String org, String dst);
}
