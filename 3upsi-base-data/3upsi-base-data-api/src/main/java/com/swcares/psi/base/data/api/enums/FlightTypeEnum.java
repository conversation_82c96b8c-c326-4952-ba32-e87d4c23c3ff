package com.swcares.psi.base.data.api.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Iterator;

@Getter
public enum FlightTypeEnum {
    INTERNATION("I","国际"),
    INTERNAL("D","国内"),
    DISTRICT("R","地区")
    ;
    private String code;
    private String chinese;

    FlightTypeEnum(String code, String chinese) {
        this.code = code;
        this.chinese = chinese;
    }

    public static String findCode(String chinese){
        Iterator<FlightTypeEnum> iterator = Arrays.stream(FlightTypeEnum.values()).iterator();
        while (iterator.hasNext()){
            FlightTypeEnum next = iterator.next();
            if (next.getChinese().equals(chinese)){
                return next.getCode();
            }
        }
        return null;
    }

    public static String findChinese(String code){
        Iterator<FlightTypeEnum> iterator = Arrays.stream(FlightTypeEnum.values()).iterator();
        while (iterator.hasNext()){
            FlightTypeEnum next = iterator.next();
            if (next.getCode().equals(code)){
                return next.getChinese();
            }
        }
        return null;
    }
}
