package com.swcares.psi.ground.assurance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName：UpdateCommonAssuranceOrderReqDto
 * @Description：旅客普通保障单编辑接口请求参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/4/24 15:25
 * @version： v1.0
 */
@Data
@ApiModel(value = "旅客普通保障单编辑接口请求参数")
public class UpdateCommonAssuranceOrderReqDto {
    @ApiModelProperty(value = "保障单编号",required = true)
    @NotEmpty(message = "保障单编号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "服务类别项场站ID",required = true)
    @NotNull(message = "服务类别项场站ID能为空")
    private List<String> categoryItemAirportIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "职务")
    private String position;

}
