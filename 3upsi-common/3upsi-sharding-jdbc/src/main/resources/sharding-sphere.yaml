#数据源配置
dataSources:
  ds_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.jdbc.Driver
    jdbcUrl: ****************************************************************************************************************
    username: psi
    password: 3Upsi@123
  ds_1:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.jdbc.Driver
    jdbcUrl: ****************************************************************************************************************
    username: psi_reader
    password: v@Y9dxYW
  ds_2:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.jdbc.Driver
    jdbcUrl: ****************************************************************************************************************
    username: psi_reader
    password: v@Y9dxYW

###路由规则配置###
# 航班表、旅客主表基于FLIGHT_DATE列进行表路由（com.swcares.psi.shardingjdbc.StandardShardingTableAlgorithm）
# 数据库则未配置路由规则即进行全库路由（org.apache.shardingsphere.sharding.route.strategy.type.none.NoneShardingStrategy）
# 副表则通过与各自主表的绑定关系找到主表所在数据库下的对应表
rules:
  - !SHARDING
    tables:
      FLT_ASSOCIATED_SEGMENT:
      FLT_EMD_TICKET_INFO:
      FLT_PASSENGER_BAGGAGE_INFO:
      FLT_PASSENGER_CONTACTS:
      FLT_PASSENGER_EVENT_RECORD:
      FLT_PASSENGER_FREETEXT_INFO:
      FLT_PASSENGER_REAL_INFO:
      FLT_PASSENGER_SPECIAL_SERVICE:
      FLT_TICKET_FARE_GROUP:
      FLT_TICKET_TAXS:
      FLT_FLIGHT_REAL_INFO:
      FLT_FLIGHT_SEGMENT_REAL_INFO:
      FOC50_T2001:
      FOC50_T3005:

    defaultTableStrategy:
      standard:
        shardingColumn: FLIGHT_DATE
        shardingAlgorithmName: standard_tbl
    shardingAlgorithms:
      standard_tbl:
        type: STANDARD_TBL

props:
  sql-show: false
  #加载表列信息时不区分大小写（优化数据源初始化效率org.apache.shardingsphere.infra.metadata.schema.ShardingSphereSchema)
  column-case: false
  #main-db支持读写，非main-db只支持读
  main-db: ds_0
  #DML强制路由到main-db，不包含需路由的表的DQL也强制路由到main-db
  force-route-to-main-db-except-dql: true
  #数据源分片信息（格式：数据源@年份后两位_起始月份-终止月份）
  sharding-nodes: ds_1@20_1-12,ds_1@21_1-3,ds_1@19_3-12,ds_2@19_1-2,ds_2@18_1-12