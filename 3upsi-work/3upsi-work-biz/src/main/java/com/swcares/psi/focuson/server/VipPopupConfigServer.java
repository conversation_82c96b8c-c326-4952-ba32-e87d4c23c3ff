package com.swcares.psi.focuson.server;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.common.utils.encryption.DecryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.focuson.dto.VipPopupConfigDto;
import com.swcares.psi.focuson.pojo.VipPopupConfig;
import com.swcares.psi.focuson.vo.VipPopupConfigVo;

import java.util.List;

/**
 * ClassName：com.swcares.psi.focuson.server.VipPopupConfigServer <br>;
 * Description：vip弹窗配置服务 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/3/29 15:31 <br>;
 * @version v1.0 <br>;
 */
public interface VipPopupConfigServer extends IService<VipPopupConfig> {
    /**
     * Title：getVipPopupConfigPage <br>;
     * Description：获取vip弹窗配置 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/3/29 16:05 <br>;
     * @throws  <br>;
     */
    PsiPage<VipPopupConfigVo> getVipPopupConfigPage(VipPopupConfigDto vipPopupConfigDto);

    /**
     * Title：addVipPopupConfig <br>;
     * Description：增加vip弹窗配置 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/3/29 16:45 <br>;
     * @throws  <br>;
     */
    void addVipPopupConfig(VipPopupConfigDto vipPopupConfigDto);

    /**
     * Title：updateVipPopupConfig <br>;
     * Description：修改vip弹窗配置 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/3/29 16:45 <br>;
     * @throws  <br>;
     */
    void updatevipPopupConfig(VipPopupConfigDto vipPopupConfigDto) throws Exception;

    /**
     * Title：getVipPopupConfigByParems <br>;
     * Description：更具条件查询配置 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/3/29 19:39 <br>;
     * @throws  <br>;
     */
    @DecryptMethod
    List<VipPopupConfig> getVipPopupConfigByParems(VipPopupConfigDto vipPopupConfigDto);
}
