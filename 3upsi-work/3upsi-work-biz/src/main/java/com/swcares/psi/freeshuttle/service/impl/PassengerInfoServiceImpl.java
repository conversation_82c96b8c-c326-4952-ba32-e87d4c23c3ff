package com.swcares.psi.freeshuttle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.psi.freeshuttle.constant.Constant;
import com.swcares.psi.freeshuttle.dto.CheckInfoDto;
import com.swcares.psi.freeshuttle.dto.OrderReturnDto;
import com.swcares.psi.freeshuttle.dto.TieHangCheckDto;
import com.swcares.psi.freeshuttle.entity.FltFlightRealInfo;
import com.swcares.psi.freeshuttle.entity.PvOrder;
import com.swcares.psi.freeshuttle.vo.CheckResultVo;
import com.swcares.psi.freeshuttle.vo.FltPassengerInfoVo;
import com.swcares.psi.freeshuttle.vo.OrderReturnVo;
import com.swcares.psi.freeshuttle.vo.PassengerInfoInterfaceVo;
import com.swcares.psi.base.data.api.entity.FltPassengerRealInfo;
import com.swcares.psi.common.redis.RedisService;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.MD5EncryptUtils;
import com.swcares.psi.freeshuttle.mapper.PassengerInfoDao;
import com.swcares.psi.freeshuttle.mapper.PvOrderDao;
import com.swcares.psi.freeshuttle.handler.order.QueryOrderStateHandler;
import com.swcares.psi.freeshuttle.handler.rule.CheckRuleHandler;
import com.swcares.psi.freeshuttle.interfaces.TiehangOrderCheck;
import com.swcares.psi.freeshuttle.service.FlightInfoService;
import com.swcares.psi.freeshuttle.service.PassengerInfoService;
import com.swcares.psi.freeshuttle.service.PvOrderService;
import com.swcares.psi.freeshuttle.service.PvRuleService;
import com.swcares.psi.utils.DesensitizationUtil;
import com.swcares.psi.utils.RSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className: PassengerInfoServiceImpl
 * @description: 关于川航微信和第三个接口服务
 * @create 2021-11-17-9:35
 */
@Slf4j
@Service("passengerInfoService")
public class PassengerInfoServiceImpl extends ServiceImpl<PassengerInfoDao, FltPassengerRealInfo> implements PassengerInfoService {
    private final static String FLIGHT_SEGMENT_MARK_ONE = "A-B";
    private final static String FLIGHT_SEGMENT_MARK_TWO = "B-C";

    @Value("${rsa.encrypt.privateKey}")
    private String privateKey;

    @Value(("${plane.order.expiration}"))
    private Integer orderExpiration;

    @Resource
    private FlightInfoService flightInfoService;

    @Resource
    private PvOrderDao pvOrderDao;

    @Resource
    private PassengerInfoDao passengerInfoDao;

    @Resource
    private PvRuleService pvRuleService;

    @Resource
    private PvOrderService pvOrderService;

    @Resource
    private RedisService redisService;

    @Resource
    private TiehangOrderCheck tiehangOrderCheck;

    @Resource
    private QueryOrderStateHandler queryOrderStateHandler;


    /**
     * @Description: 获取满足申请免费车旅客的信息
     * @Author: 杜树涛
     * @Date: 2021/11/18 10:36
     * @param id:
     * @return: java.util.List<com.swcares.psi.freeshuttle.vo.FltPassengerInfoVo>
     **/
    @Override
    public List<FltPassengerInfoVo> queryAllPassengerInfo(String id, String segmentMark) {
        //获取航班信息
        FltFlightRealInfo fltFlightRealInfo = flightInfoService.getById(id);


        if (fltFlightRealInfo == null) {
            return null;
        }

        if (FLIGHT_SEGMENT_MARK_ONE.equals(segmentMark)) {
            fltFlightRealInfo.setDst(null);
        }else if (FLIGHT_SEGMENT_MARK_TWO.equals(segmentMark)) {
            fltFlightRealInfo.setOrg(null);
        }

        //获取所有旅客信息
        List<FltPassengerInfoVo> fltPassengerRealInfos = passengerInfoDao.queryAllPassengerInfoByFlightInfoId(fltFlightRealInfo);

        if (fltPassengerRealInfos.isEmpty()) {
            return fltPassengerRealInfos;
        }
        //装配规则链
        CheckRuleHandler checkRuleHandler = new CheckRuleHandler();
        checkRuleHandler.load(pvRuleService);

        //规则校验
        List<FltPassengerInfoVo> fltPassengerInfoVos = checkRuleHandler.checkList(fltPassengerRealInfos);

        fltPassengerInfoVos.forEach(o -> {
            log.debug("旅客校验情况"+o);
        });

        //过滤掉不满足的旅客
        fltPassengerInfoVos = fltPassengerInfoVos.stream().filter(FltPassengerInfoVo::isResult).collect(Collectors.toList());

        log.debug("满足校验规则旅客" + fltPassengerInfoVos);

        //数据脱敏操作
        if (DesensitizationUtil.isDesensitization()) {
            fltPassengerInfoVos.forEach(e -> {
                e.setIdNumber(DesensitizationUtil.identificationNum(e.getIdNumber()));
                e.setPhoneNumber(DesensitizationUtil.mobilePhone(e.getPhoneNumber()));
            });
        }

        return fltPassengerInfoVos;
    }

    /**
     * @Description: 下单申请获取id
     * @Author: 杜树涛
     * @Date: 2021/11/18 10:37
     * @param id:
     * @return: java.lang.String
     **/
    @Override
    public String getOrderId(String id, String remarks) throws Exception {

        //获取操作人
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();

        //查询旅客信息
        List<FltPassengerInfoVo> fltPassengerInfos = passengerInfoDao.queryPassengerInfoById(id);

        if (fltPassengerInfos.isEmpty()) {
            log.info(userId + "下单时旅客id:" + id +"为空");
            throw new Exception("未找到该旅客");
        }

        FltPassengerInfoVo fltPassengerInfo = fltPassengerInfos.get(fltPassengerInfos.size() - 1);
        if (StringUtils.isNotEmpty(fltPassengerInfo.getState())) {
            if ("2".equals(fltPassengerInfo.getState())) {
                TieHangCheckDto tieHangCheckDto = tiehangOrderCheck.tiehangOrderCheck(fltPassengerInfo.getFlightNumber(), fltPassengerInfo.getFlightDate(), fltPassengerInfo.getIdNumber());
                if (tieHangCheckDto != null
                        && "1".equals(tieHangCheckDto.getStatus())
                        && "1".equals(tieHangCheckDto.getServerStatus())
                        && tieHangCheckDto.isData()) {
                    log.info(userId + "下单时旅客id:" + id +"已申请");
                    throw new Exception("该该旅客已申请");
                 }

            }else if ("3".equals(fltPassengerInfo.getState())) {
                log.info(userId + "下单时旅客id:" + id +"已申请");
                throw new Exception("该该旅客已申请");
            }
        }

        //加载规则链
        CheckRuleHandler checkRuleHandler = new CheckRuleHandler();
        checkRuleHandler.load(pvRuleService);
        //校验
        FltPassengerInfoVo checkList = checkRuleHandler.checkOne(fltPassengerInfo);

        if (!checkList.isResult()) {
            log.error(id + "H5下单时校验不通过：" + checkList.getMessage());
            throw new Exception("该旅客不满足免费车乘坐要求");
        }

        fltPassengerInfo.setCreateUser(userId);
        fltPassengerInfo.setRemarks(remarks);

        //对旅客姓名:+时间戳:+航班id:进行MD5加密形成key
        String passwordKey = fltPassengerInfo.getPassengerName() + ":" + new Date() + ":" +fltPassengerInfo.getId();
        String outId = MD5EncryptUtils.encrypt(passwordKey);

        if (!redisService.set(outId, fltPassengerInfo, orderExpiration)) {
            log.error("，免费车申请失败：申请id为" + id);
            return null;
        }
        log.info("，免费车申请成功：申请id为" + id);

        return outId;
    }


    @Override
    @Transactional
    public PassengerInfoInterfaceVo getPassengerInfoById(String id) throws Exception {

        try {
             id = RSAUtil.decryptByPrivateKey(id, privateKey);
        }catch (Exception e) {
            log.error(id + "解密失败");
           throw new Exception("解密失败");
        }

        Object passengerInfo = redisService.get(id);

        if (passengerInfo instanceof  FltPassengerInfoVo) {
            FltPassengerInfoVo redisGetPassengerInfo = (FltPassengerInfoVo) passengerInfo;

            //创建免费车订单对象
            PvOrder newPvOrder = new PvOrder();

            BeanUtil.copyProperties(redisGetPassengerInfo, newPvOrder);

            //是否进港航班班
            if (Constant.AIRPORTADAPTATION.contains(redisGetPassengerInfo.getOrg())) {
                newPvOrder.setIsIncomming("N");
            }else if (Constant.AIRPORTADAPTATION.contains (redisGetPassengerInfo.getDst())) {
                newPvOrder.setIsIncomming("Y");
            }

            //航段
            newPvOrder.setLogic(redisGetPassengerInfo.getOrg() + "-" + redisGetPassengerInfo.getDst());

            //旅客表id
            newPvOrder.setPsgId(redisGetPassengerInfo.getId());

            //证件号
            newPvOrder.setIdNumber(StringUtils.isEmpty(redisGetPassengerInfo.getIdNumber()) ? null : AesEncryptUtil.encrypt(redisGetPassengerInfo.getIdNumber()));

            //手机号
            newPvOrder.setPhoneNumber(StringUtils.isEmpty(redisGetPassengerInfo.getPhoneNumber()) ? null : AesEncryptUtil.encrypt(redisGetPassengerInfo.getPhoneNumber()));

           //渠道
            newPvOrder.setChannel("川航小程序");

            //是否自动
            newPvOrder.setIsAuto("Y");

            //是否团队
            newPvOrder.setIsTeam(redisGetPassengerInfo.getGroupTag());

            //创建时间
            newPvOrder.setCreateTime(LocalDateTime.now());

            //状态
            newPvOrder.setState("1");

            //备注
            newPvOrder.setRemarks(redisGetPassengerInfo.getRemarks());

            try {
                List<PvOrder> pvOrderServiceOne = pvOrderService.list(new QueryWrapper<PvOrder>()
                        .eq("PSG_ID", redisGetPassengerInfo.getId())
                        .ne("STATE", "4"));

                if (pvOrderServiceOne.size() < 1){
                    pvOrderService.save(newPvOrder);
                }

            }catch (Exception e) {
                log.error("川航下单后获取旅客信息入库失败，但旅客信息还是会传回铁航。错误原因："+ e);
            }

            PassengerInfoInterfaceVo passengerInfoInterfaceVo = new PassengerInfoInterfaceVo();

            BeanUtil.copyProperties(redisGetPassengerInfo, passengerInfoInterfaceVo);

            //设置舱位
            passengerInfoInterfaceVo.setCabin(redisGetPassengerInfo.getSubCabin());

            return passengerInfoInterfaceVo;
        }

        PassengerInfoInterfaceVo passengerInfoInterfaceVo = new PassengerInfoInterfaceVo();
        passengerInfoInterfaceVo.setPassengerName("id不正确或已过期");

        return passengerInfoInterfaceVo;
    }

    @Override
    public List<CheckResultVo> checkOrderInfo(String info) throws Exception {


        List<CheckResultVo> checkResultVos = new ArrayList<>();

        //解密参数，转为对象
        String infoJson = "";
        CheckInfoDto checkInfoDto = null;

        try {
            infoJson = RSAUtil.decryptByPrivateKey(info, privateKey);
            checkInfoDto = new ObjectMapper().readValue(infoJson, CheckInfoDto.class);
        } catch (Exception e) {
            log.error(info + ": 解密失败 :" + e);
            throw new Exception("解密失败");
        }

        log.error("铁航校验接口解析数据为：" + checkInfoDto);

        List<CheckInfoDto> checkInfoDtos = new ArrayList<>();

        //对身份证号加密，进行查询
        CheckInfoDto finalCheckInfoDto = checkInfoDto;
        checkInfoDto.getIdNumber().forEach(o -> {
            o = StringUtils.isEmpty(o) ? null : AesEncryptUtil.encrypt(o);
            CheckInfoDto newCheckInfoDto = new CheckInfoDto();

            newCheckInfoDto.setFlightDate(finalCheckInfoDto.getFlightDate());
            newCheckInfoDto.setFlightNumber(finalCheckInfoDto.getFlightNumber());
            newCheckInfoDto.setIdNumber(Arrays.asList(o));
            checkInfoDtos.add(newCheckInfoDto);
        });

        //加载规则链
        CheckRuleHandler checkRuleHandler = new CheckRuleHandler();
        checkRuleHandler.load(pvRuleService);

        checkInfoDtos.forEach(o -> {
            //赋值转换
            CheckResultVo checkResultVo = new CheckResultVo();

            //根据参数查询旅客信息
            List<FltPassengerInfoVo> fltPassengerInfoVo = passengerInfoDao.queryPassengerInfoByCheckInfosDto(o);
            if (fltPassengerInfoVo.isEmpty()) {
                BeanUtil.copyProperties(o, checkResultVo);
                checkResultVo.setResult(false);
                checkResultVo.setMessage("旅客未找到");
                checkResultVo.setIdNumber(o.getIdNumber().isEmpty() ? null : AesEncryptUtil.decryption(o.getIdNumber().get(0)));

            }else {
                //校验
                List<FltPassengerInfoVo> checkList = checkRuleHandler.checkList(fltPassengerInfoVo);
                checkList.forEach(oe -> {

                    BeanUtil.copyProperties(oe, checkResultVo);
                    if ("1".equals(oe.getState())) {
                        queryOrderStateHandler.queryOrderState(oe.getFlightNumber(), oe.getFlightDate(), oe.getIdNumber(), oe.getId());
                    }
                    //设置舱位
                    checkResultVo.setCabin(oe.getSubCabin());
                });
            }

            checkResultVos.add(checkResultVo);

        });

        log.info("铁航校验接口返回数据为-》{}", checkResultVos);
        return checkResultVos;
    }

    /**
     * @Description: 第三方信息范返回处理
     * @Author: 杜树涛
     * @Date: 2021/11/26 10:15
     * @param info:
     * @return: com.swcares.psi.common.utils.query.RenderResult
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ArrayList<OrderReturnVo> orderInfoReturn(String info) throws Exception {

        //保存返回解密后的集合对象
        List<OrderReturnDto> orderReturnDtos = new ArrayList();

        //对班次编号进行分组，好校验是否超载
        HashMap<String, List<PvOrder>> orderReturnMap = new HashMap<>();

        //保存校验完成后的对象
        List<FltPassengerInfoVo> allVerifiedObj = new ArrayList<>();

        try {
            //字符串解密，获得json字符串
            String orderReturnJson = RSAUtil.decryptByPrivateKey(info, privateKey);
            //获取参数
            orderReturnDtos = JSONObject.parseArray(orderReturnJson, OrderReturnDto.class);
        }catch (Exception e) {
            log.error(info + "使用：" + privateKey + "解密失败：" + e);
            throw new Exception("解密失败");
        }

        if (orderReturnDtos.isEmpty()) {
            log.error(info + "：解密成功，但json转回的对象集合为空");
            throw new Exception("传输的对象为空");
        }

        //把传回的旅客信息转为pvOrder对象
        orderReturnDtos.forEach(o -> {
            PvOrder newPvOrder = new PvOrder();

            newPvOrder.setFlightNumber(o.getFlightNo());
            newPvOrder.setFlightDate(o.getFlightDate());

            //是否进离港航班
            if (Constant.AIRPORTADAPTATION.contains(o.getTakeOffStation())) {
                newPvOrder.setIsIncomming("N");
            }else if (Constant.AIRPORTADAPTATION.contains (o.getArriveStation())) {
                newPvOrder.setIsIncomming("Y");
            }
            newPvOrder.setOrg(o.getTakeOffStation());
            newPvOrder.setDst(o.getArriveStation());
            newPvOrder.setPassengerName(o.Name);
            newPvOrder.setPhoneNumber(StringUtils.isEmpty(o.getPhone()) ? null : AesEncryptUtil.encrypt(o.getPhone()));
            newPvOrder.setIdNumber(StringUtils.isEmpty(o.getIDCardNo()) ? null : AesEncryptUtil.encrypt(o.getIDCardNo()));
            newPvOrder.setSubCabin(o.CabinSeat);
            newPvOrder.setSeqNo(o.SeqNo);
            newPvOrder.setCarLicense(o.CarLicense);
            newPvOrder.setDriverNo(o.DriverNo);
            newPvOrder.setScheduleNo(o.ScheduleNo);
            newPvOrder.setChannel(o.Channel);
            newPvOrder.setBookingTime(o.BookingTime);
            newPvOrder.setServiceType(o.ServiceType);
            newPvOrder.setPickupAddress(o.PickupAddress);
            newPvOrder.setAlightingAddress(o.AlightingAddress);
            newPvOrder.setPickupTime(o.PickupTime);
            newPvOrder.setDriverConfirmedTime(o.DriverConfirmedTime);
            newPvOrder.setConfirmedTime(o.ConfirmedTime);
            newPvOrder.setMapImg(o.MapImg);

            newPvOrder.setUpdateTime(LocalDateTime.now());
            newPvOrder.setUpdateUser("铁航");
            newPvOrder.setIsAuto("Y");
            newPvOrder.setState("3");

            if (orderReturnMap.get(o.getScheduleNo()) == null) {
                ArrayList<PvOrder> pvOrders = new ArrayList<>();
                pvOrders.add(newPvOrder);
                orderReturnMap.put(o.getScheduleNo(), pvOrders);
            }else {
                orderReturnMap.get(o.getScheduleNo()).add(newPvOrder);
            }
        });

        CheckRuleHandler checkRuleHandler = new CheckRuleHandler();
        checkRuleHandler.load(pvRuleService);

        orderReturnMap.values().forEach(o -> {
            for (PvOrder pvOrder : o) {
                CheckInfoDto checkInfoDto = new CheckInfoDto();

                checkInfoDto.setFlightDate(pvOrder.getFlightDate());
                checkInfoDto.setFlightNumber(pvOrder.getFlightNumber());
                checkInfoDto.setIdNumber(Collections.singletonList(pvOrder.getIdNumber()));

                //根据航班号，航班日期，旅客姓名，旅客证件号查询旅客信息
                List<FltPassengerInfoVo> fltPassengerInfoVos = passengerInfoDao.queryPassengerInfoByCheckInfosDto(checkInfoDto);

                if (fltPassengerInfoVos.isEmpty()) {
                    log.error("航班号：" + pvOrder.getFlightNumber()
                            + "航班日期 : " + pvOrder.getFlightDate()
                            + "证件号 ： " + pvOrder.getIdNumber()
                            + "未找到旅客信息");
                    pvOrder.setSubCabin(null);
                    pvOrder.setState("0");
                    pvOrder.setReson("未查询到该旅客信息");
                }else {
                    FltPassengerInfoVo fltPassengerInfoVo = fltPassengerInfoVos.get(fltPassengerInfoVos.size() - 1);
                    pvOrder.setPsgId(fltPassengerInfoVo.getId());
                    pvOrder.setIsTeam(fltPassengerInfoVo.getGroupTag());
                    pvOrder.setOrg(fltPassengerInfoVo.getOrg());
                    pvOrder.setDst(fltPassengerInfoVo.getDst());
                    pvOrder.setTicketPrice(fltPassengerInfoVo.getTicketPrice());
                    pvOrder.setSubCabin(fltPassengerInfoVo.getSubCabin());
                    pvOrder.setLogic(fltPassengerInfoVo.getLogic());
                    pvOrder.setPriceOneway(fltPassengerInfoVo.getPriceOneway());
                    pvOrder.setIdType(fltPassengerInfoVo.getIdType());
                    pvOrder.setDepartureTime(fltPassengerInfoVo.getDepartureTime());
                    //校验旅客是否有权限
                    FltPassengerInfoVo checkOne = checkRuleHandler.checkOne(fltPassengerInfoVo);

                    if (checkOne.isResult()) {
                        pvOrder.setReson(checkOne.getMessage());
                        pvOrder.setState("3");
                    }else {
                        pvOrder.setReson(checkOne.getMessage());
                        pvOrder.setState("0");
                    }
                }
            }
        });

        //根据航程号查询数据库校验是否超6个
        orderReturnMap.forEach((key, value) -> {
            int scheduleNoNumber = pvOrderService.count(new QueryWrapper<PvOrder>().eq("SCHEDULE_NO", key).ne("STATE", "4"));
            for (PvOrder pvOrder : value) {
                if ("0".equals(pvOrder.getState())) {
                   continue;
                }

                if (scheduleNoNumber + value.size() > 6) {
                    QueryWrapper<PvOrder> wrapper = new QueryWrapper<>();
                    Map<String, Object> hashMap = new HashMap<>();
                    hashMap.put("FLIGHT_NUMBER", pvOrder.getFlightNumber());
                    hashMap.put("FLIGHT_DATE", pvOrder.getFlightDate());
                    hashMap.put("ORG", pvOrder.getOrg());
                    hashMap.put("DST", pvOrder.getDst());
                    hashMap.put("ID_NUMBER", pvOrder.getIdNumber());
                    wrapper.allEq(hashMap).ne("STATE", "4");

                    List<PvOrder> pvOrder1 = new ArrayList<>();
                    try {
                        pvOrder1 = pvOrderDao.selectList(wrapper);
                    }catch (Exception e) {
                        log.error("车辆承载人数超过限制, 查询订单出错，e-》{}", e);
                    }

                    if (pvOrder1.isEmpty()) {
                        log.error("车辆承载人数超过限制, key-》{}, scheduleNoNumber-》{}, value的长度-》{}", key, scheduleNoNumber, value.size());
                        pvOrder.setState("0");
                        pvOrder.setReson("车辆承载人数超过限制");
                    }
                }
            }
        });

        ArrayList<OrderReturnVo> errorObjects = new ArrayList<>();
        orderReturnMap.forEach((key, value) -> {
            for (PvOrder pvOrder : value) {
                QueryWrapper<PvOrder> wrapper = new QueryWrapper<>();
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("FLIGHT_NUMBER", pvOrder.getFlightNumber());
                hashMap.put("FLIGHT_DATE", pvOrder.getFlightDate());
                hashMap.put("ORG", pvOrder.getOrg());
                hashMap.put("DST", pvOrder.getDst());
                hashMap.put("ID_NUMBER", pvOrder.getIdNumber());
                wrapper.allEq(hashMap).ne("STATE", "4");

                try {
                    List<PvOrder> pvOrder1 = pvOrderDao.selectList(wrapper);
                    if (pvOrder1.size() != 0) {
                        pvOrder.setId(pvOrder1.get(0).getId());
                        //更新
                        pvOrderDao.updateById(pvOrder);
                    } else {
                        pvOrder.setCreateTime(LocalDateTime.now());
                        pvOrder.setCreateUser("铁航");
                        //增加
                        pvOrderDao.insert(pvOrder);
                    }
                } catch (Exception e) {
                    log.error("铁航回传数据插入异常：" + pvOrder);
                    OrderReturnVo orderReturnVo = new OrderReturnVo();
                    BeanUtil.copyProperties(pvOrder, orderReturnVo);
                    //身份证号解密
                    orderReturnVo.setIdNumber(StringUtils.isEmpty(pvOrder.getIdNumber()) ? null : AesEncryptUtil.decryption(pvOrder.getIdNumber()));
                    orderReturnVo.setStatus("-1");
                    errorObjects.add(orderReturnVo);
                }

            }
        });

        return errorObjects;
    }
}
