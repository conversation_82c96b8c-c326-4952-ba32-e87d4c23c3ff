package com.swcares.psi.quartz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.quartz.entity.SysQuartzJob <br>
 * Description：系统定时任务结构体<br>
 * Copyright © 2021/11/22 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021/11/22 15:53<br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SysQuartzJob对象", description="")
public class SysQuartzJob implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "任务所属bean类")
    @TableField("BEAN_NAME")
    private String beanName;

    @ApiModelProperty(value = "cro表达式")
    @TableField("CRON_EXPRESSION")
    private String cronExpression;

    @ApiModelProperty(value = "job运行状态1停止0运行")
    @TableField("IS_PAUSE")
    private Integer isPause;

    @ApiModelProperty(value = "任务名称")
    @TableField("JOB_NAME")
    private String jobName;

    @ApiModelProperty(value = "执行方法")
    @TableField("METHOD_NAME")
    private String methodName;

    @ApiModelProperty(value = "执行方法参数可选")
    @TableField("PARAMS")
    private String params;

    @ApiModelProperty(value = "job描述")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "定时任务作用域web,pssn")
    @TableField("RUN_SCOPE")
    private String runScope;


}
