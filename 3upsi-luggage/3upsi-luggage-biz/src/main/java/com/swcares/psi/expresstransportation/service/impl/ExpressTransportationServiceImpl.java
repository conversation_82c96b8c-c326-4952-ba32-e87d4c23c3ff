package com.swcares.psi.expresstransportation.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.encryption.DecryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.cons.LuggageCons;
import com.swcares.psi.expresstransportation.dto.ExpressTransportationDto;
import com.swcares.psi.expresstransportation.entity.ExpressTransportation;
import com.swcares.psi.expresstransportation.mapper.ExpressTransportationMapper;
import com.swcares.psi.expresstransportation.service.ExpressTransportationService;
import com.swcares.psi.expresstransportation.vo.ExpressTransportationExportVo;
import com.swcares.psi.expresstransportation.vo.ExpressTransportationVo;
import com.swcares.psi.terminal.entity.DpPkgTerminal;
import com.swcares.psi.terminal.mapper.DpPkgTerminalMapper;
import com.swcares.psi.terminal.service.IDpPkgTerminalService;
import com.swcares.psi.base.data.api.entity.EmployeeJpa;
import com.swcares.psi.base.data.mapper.UserJpaDao;
import com.swcares.psi.utlis.LuggageUtils;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * Description: []
 * Created on 2021-3-9 15:18
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class ExpressTransportationServiceImpl extends ServiceImpl<ExpressTransportationMapper, ExpressTransportation> implements ExpressTransportationService {

    @Autowired
    UserJpaDao userJpaDao;
    @Autowired
    ExpressTransportationMapper expressTransportationMapper;
    @Autowired
    DpPkgTerminalMapper dpPkgTerminalMapper;
    @Autowired
    private IDpPkgTerminalService pkgTerminalService;

    @Override
    public void saveExpressTransportation(ExpressTransportation expressTransportation) {
//        Date newFlightDate = expresstransportation.getNewFlightDate();
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-DD");
//        simpleDateFormat.format(newFlightDate);
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        PsiUser user = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNO = (String) AuthenticationUtil.getAuthentication().getCredentials();
        String name = user.getRealName();
        EmployeeJpa employee = userJpaDao.findByTuNo(createUser);
        String workAirport = employee.getAirport3code();
        DpPkgTerminal dpPkgTerminal = dpPkgTerminalMapper.selectOne(
                Wrappers.<DpPkgTerminal>lambdaQuery().eq(DpPkgTerminal::getAirport3code, workAirport));
        if (StringUtils.isEmpty(expressTransportation.getId())) {
            String orderNo = LuggageUtils.createOrderNo(LuggageCons.EXPRESS_TRANSPORTATION_SUFFIX);
            expressTransportation.setExpressTransportationNo(orderNo);
            expressTransportation.setCreateUser(name);
            expressTransportation.setCreateUserNo(userNO);
            expressTransportation.setCreateDate(new Date());
            expressTransportation.setCreateTerminal(workAirport);
            expressTransportation.setIsDel("0");
            if (ObjectUtil.isNotNull(dpPkgTerminal)) {
                expressTransportation.setTerminalContact(dpPkgTerminal.getPhoneFirst());
            }
        } else {
            expressTransportation.setUpdateUserNo(userNO);
            expressTransportation.setUpdateDate(new Date());
        }

        this.saveOrUpdate(expressTransportation);
    }

    @Override
    public PsiPage<ExpressTransportationVo> getExpressTransportationPage(PsiPage<ExpressTransportation> page, ExpressTransportationDto expressTransportationDto) {
        return expressTransportationMapper.getExpressTransportationPage(page, expressTransportationDto);
    }

    @Override
    public List<ExpressTransportationExportVo> getExpressTransportationList(ExpressTransportationDto expressTransportationDto) {
        return expressTransportationMapper.getExpressTransportationList(expressTransportationDto);
    }

    @Override
    public void deleteExpressTransportationByIds(List<String> ids) {
//        Authentication authentication = AuthenticationUtil.getAuthentication();
//        for(String id : ids){
//            ExpressTransportation expressTransportation = new ExpressTransportation();
//            expressTransportation.setUpdateUserNo((String)authentication.getCredentials());
//            expressTransportation.setUpdateDate(new Date());
//            expressTransportation.setIsDel("1");
//            expressTransportation.setId(id);
//            expressTransportationList.add(expressTransportation);
//        }
        List<ExpressTransportation> expressTransportationList = this.listByIds(ids);
        expressTransportationList.forEach(e -> {
            if (StringUtils.equals("6", e.getType())) {
                throw new BusinessException(MessageCode.EXPRESS_TRANSPORTATION_DELETE_ERROR.getCode());
            }
        });
        expressTransportationMapper.deleteExpressTransportationByIds(ids);
    }

    @Override
    @DecryptMethod
    public ExpressTransportation getDetail(String id) {
        return this.getOne(Wrappers.<ExpressTransportation>lambdaQuery().eq(ExpressTransportation::getId, id));
    }

    @Override
    public Boolean judgePkgNo(ExpressTransportation expressTransportation) {
        List<ExpressTransportation> expressTransportationList = this.list(Wrappers.<ExpressTransportation>lambdaQuery()
                .eq(ExpressTransportation::getNewPkgNo, expressTransportation.getNewPkgNo())
                .eq(ExpressTransportation::getNewFlightDate, expressTransportation.getNewFlightDate())
                .eq(ExpressTransportation::getNewFlightNo, expressTransportation.getNewFlightNo())
                .eq(ExpressTransportation::getIsDel, "0"));
        if (null != expressTransportationList && !expressTransportationList.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    @DecryptMethod
    public Map<String, Object> getDetailByAccidentId(String accidentId) {
        ExpressTransportation expressTransportation = this.getDetail(accidentId);
        Map<String, Object> result = new HashMap<>();
        result.put("fileReferenceNo", expressTransportation.getExpressTransportationNo());
        if (StringUtils.isNotBlank(expressTransportation.getCreateTerminal())) {
            result.put("phoneNo", pkgTerminalService.getTerminalPhone(expressTransportation.getCreateTerminal()));
        }
        result.put("oldPkgNo", expressTransportation.getOldPkgNo());
        result.put("oldFlightDate", expressTransportation.getOldFlightDate());
        result.put("oldFlightNo", expressTransportation.getOldFlightNo());
        result.put("newPkgNo", expressTransportation.getNewPkgNo());
        result.put("newFlightDate", expressTransportation.getNewFlightDate());
        result.put("newFlightNo", expressTransportation.getNewFlightNo());
        result.put("org", expressTransportation.getOrg());
        result.put("dst", expressTransportation.getDst());
        result.put("voyage", expressTransportation.getVoyage());
        result.put("weight", expressTransportation.getWeight());
        result.put("createUser", expressTransportation.getCreateUser());
        if (null != expressTransportation.getCreateDate()) {
            result.put("createDate", DateUtils.parseDateToStr(expressTransportation.getCreateDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        result.put("createTerminal", expressTransportation.getCreateTerminal());
        String terminalContact = AesEncryptUtil.decryption(expressTransportation.getTerminalContact());
        result.put("terminalContact", StringUtils.isBlank(terminalContact) ? null : terminalContact);
        if (StringUtils.isNotBlank(expressTransportation.getType())) {
            switch (expressTransportation.getType()) {
                case "1":
                    result.put("type", "掉条");
                    break;
                case "2":
                    result.put("type", "漏运");
                    break;
                case "3":
                    result.put("type", "错运");
                    break;
                case "4":
                    result.put("type", "退回始发站");
                    break;
                case "5":
                    result.put("type", "内部托运");
                    break;
                case "6":
                    result.put("type", "多收转速运");
                    break;
                case "7":
                    result.put("type", "其他");
                    break;
                default:
                    break;
            }
        }
        result.put("latestNotificationTime", expressTransportation.getLatestNotificationTime());
        result.put("accidentId", expressTransportation.getAccidentId());
        if (StringUtils.isNotBlank(expressTransportation.getIsNotice())) {
            switch (expressTransportation.getIsNotice()) {
                case "0":
                    result.put("isNotice", "否");
                    break;
                case "1":
                    result.put("isNotice", "是");
                    break;
                default:
                    break;
            }
        }
        result.put("pkgColor", expressTransportation.getPkgColor());
        result.put("pkgType", expressTransportation.getPkgType());
        result.put("pkgContent", expressTransportation.getPkgContent());
        result.put("reason", expressTransportation.getReason());
        result.put("currentDate", DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        return result;
    }
}
