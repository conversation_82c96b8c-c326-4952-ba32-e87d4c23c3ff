<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.invoice.mapper.AoInvoiceDetailInfoMapper">
    <select id="selectProductId" resultType="String">
        select id from
        <choose>
            <when test="orderType=='1'">
                ao_many_seats
                WHERE ORDER_NO=#{orderNo}
            </when>
            <when test="orderType=='2'">
                ao_scene_seats
                WHERE ORDER_NO=#{orderNo}
            </when>
            <when test="orderType=='3'">
                ao_overweight_bkg
                WHERE ORDER_NO=#{orderNo}
            </when>
            <when test="orderType=='4'">
                ao_viproom_item_sell
                where ORDER_ID=#{orderNo}
            </when>
            <otherwise>
                ao_counter_upgrade
                WHERE ORDER_NO=#{orderNo}
            </otherwise>
        </choose>
    </select>
  </mapper>
