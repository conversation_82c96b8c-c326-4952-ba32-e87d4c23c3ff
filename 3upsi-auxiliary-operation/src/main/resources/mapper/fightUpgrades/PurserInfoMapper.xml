<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.flightUpgrades.mapper.PurserInfoMapper">
<select id="selectFlightCrew" resultType="com.swcares.psi.ao.flightUpgrades.entity.PurserInfo">
    SELECT
	c.P_CODE `code`,
	c.C_NAME `name`,
	c.SEX,
	c.PHONE
FROM
	flt_flight_real_info AS a
	LEFT JOIN foc50_t3005 AS b ON a.FOC_ID = b.CREW_LINK_LINE
	LEFT JOIN foc50_t3017 AS c ON c.P_CODE = b.P_CODE
WHERE
	a.FLIGHT_NUMBER = #{no}
	AND a.FLIGHT_DATE = #{date}
	AND b.RANK_NO = 'MPS'
</select>

</mapper>
