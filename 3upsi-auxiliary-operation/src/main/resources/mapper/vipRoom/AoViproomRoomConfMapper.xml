<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.mapper.AoViproomRoomConfMapper">

    <select id="queryPaxInfo" parameterType="com.swcares.psi.ao.model.dto.AoViproomPaxInfoDto"
            resultType="com.swcares.psi.ao.model.vo.AoViproomPaxInfoVO">
        SELECT
        CASE
        E.STATUS
        WHEN '1' THEN
        '已迎接'
        WHEN '2' THEN
        '已送别'
        WHEN '3' THEN
        '已拒绝'
        WHEN '4' THEN
        '撤销' ELSE '待使用'
        END AS STATUS,
        E.ENTRY_TIME,
        E.LEAVE_TIME,
        F.*
        FROM
        (
        SELECT
        K.*,
        IFNULL( G<PERSON>UP_CONCAT(K.remoteRoom1), <PERSON><PERSON>remoteRoom2 ) AS remoteRoom
        FROM
        (
        SELECT
        I.*,
        IF
        ( FIND_IN_SET( I.GATE, J.GATES ) &gt; 0, J.ROOM_NAME, NULL ) AS remoteRoom1,
        IF
        ( I.GATE IS NULL OR FIND_IN_SET( I.GATE, J.GATES ) &lt;= 0, J.ROOM_NAME, NULL ) AS remoteRoom2
        FROM
        (
        SELECT
        D.ID AS dID,
        A.PAX_ID,
        D.PAX_NAME,
        A.PAX_NO,
        C.NAME AS 'itemName',
        A.ORDER_PRICE AS price,
        A.ORDER_NO,
        DATE_FORMAT( A.CREATE_TIME, '%Y-%m-%d %H:%i:%s' ) AS sellDate,
        A.FLIGHT_NO,
        D.GATE,
        A.USER_NO AS seller,
        A.FLIGHT_DATE,
        A.ORG,
        A.DST,
        C.PORT_CODE
        FROM
        AO_ORDER_INFO A,
        AO_VIPROOM_ITEM_CONF C,
        AO_VIPROOM_ITEM_SELL D
        WHERE
        1 = 1
        AND D.ORDER_ID = A.ORDER_NO
        AND D.ITEM_ID = C.ID
        ) I
        LEFT JOIN (
        SELECT
        G.PORT_CODE,
        G.GATES,
        H.ROOM_NAME
        FROM
        AO_VIPROOM_ROOM_CONF G,
        VIP_ROOM_INFO H
        WHERE
        G.ROOM_ID = H.ID
        ) J ON I.PORT_CODE = J.PORT_CODE
        ) K
        GROUP BY
        K.dID
        ) F
        LEFT JOIN VIP_ENTRY_STATUS E ON F.FLIGHT_NO = E.FLIGHT_NO
        AND F.FLIGHT_DATE = E.FLIGHT_DATE
        AND F.ORG = E.ORG
        AND F.DST = E.DST
        WHERE
        1 = 1
        <if test="p.orderNo != null and p.orderNo != ''">
            AND F.ORDER_NO = #{p.orderNo}
        </if>
        <if test="p.paxNo != null and p.paxNo != ''">
            AND F.PAX_NO = #{p.paxNo}
        </if>
        <if test="p.sellDateBegin != null and p.sellDateBegin != ''">
            AND F.sellDate &gt;= #{p.sellDateBegin}
        </if>
        <if test="p.sellDateEnd != null and p.sellDateEnd != ''">
            AND F.sellDate &lt;= date_format(#{p.sellDateEnd},'%Y-%m-%d 23:59:59')
        </if>
        <if test="p.paxName != null and p.paxName != ''">
            AND F.PAX_NAME like concat('%',#{p.paxName},'%')
        </if>
        <if test="p.status != null and p.status != ''">
            <choose>
                <when test="p.status != 0">
                    AND E.STATUS = #{p.status}
                </when>
                <otherwise>
                    AND E.STATUS is null
                </otherwise>
            </choose>
        </if>
        <if test="p.flightDateBegin != null and p.flightDateBegin != ''">
            AND F.FLIGHT_DATE &gt;= #{p.flightDateBegin}
        </if>
        <if test="p.flightDateEnd != null and p.flightDateEnd != ''">
            AND F.FLIGHT_DATE &lt;= date_format(#{p.flightDateEnd},'%Y-%m-%d 23:59:59')
        </if>
        <if test="p.flightNo != null and p.flightNo != ''">
            AND F.FLIGHT_NO = #{p.flightNo}
        </if>
        ORDER BY
        E.UPDATE_TIME DESC
    </select>

    <select id="queryRoomsPage" parameterType="com.swcares.psi.ao.model.dto.AoViproomRoomDto"
            resultType="com.swcares.psi.ao.model.vo.AoViproomRoomConfVO">
        select *,(d.SELL_NUM-ifnull(d.stayNum, 0))
        as avaliableNum from (
        select a.*,b.ROOM_NAME as name,count(c.STATUS='1') as stayNum
        from
        AO_VIPROOM_ROOM_CONF a,VIP_ROOM_INFO b left join VIP_ENTRY_STATUS c
        on b.ID = c.VIP_ROOM_ID
        and (c.STATUS is null or c.STATUS='1')
        where a.ROOM_ID = b.ID
        and b.STATUS='1'
        <if test="dto.portCode!=null and dto.portCode!=''">
            and a.PORT_CODE = #{dto.portCode}
        </if>
        <if test="dto.capacity!=null">
            and a.CAPACITY = #{dto.capacity}
        </if>
        <if test="dto.sellNum!=null">
            and a.SELL_NUM = #{dto.sellNum}
        </if>
        <if test="dto.name!=null and dto.name!=''">
            and b.ROOM_NAME like CONCAT('%',#{dto.name},'%')
        </if>
        <if test="dto.roomId!=null and dto.roomId!=''">
            and a.ROOM_ID = #{dto.roomId}
        </if>
        group by b.ID
        ) d
    </select>

    <resultMap id="aoViproomRoomConfResultMap" type="com.swcares.psi.ao.model.AoViproomRoomConf">
        <id column="ID" property="id"/>
        <result column="ROOM_ID" property="roomId"/>
        <result column="PORT_CODE" property="portCode"/>
        <result column="PORT_NAME" property="portName"/>
        <result column="CAPACITY" property="capacity"/>
        <result column="SELL_NUM" property="sellNum"/>
        <result column="GATES" property="gates"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>

</mapper>