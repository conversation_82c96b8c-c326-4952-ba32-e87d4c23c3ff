<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.counterUpgrade.mapper.AoCounterUpgradeOrderMapper">

    <select id="getOrderByOrderNo" resultType="com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeDetailsVo">
        SELECT
        ao_counter_upgrade.APPLY_UPDATE_STATUS_REASON,
        ao_counter_upgrade.APPLY_USER_NO,
        ao_counter_upgrade.APPROVE_USER_NO,
        ao_counter_upgrade.UPGRADE_UPDATE_STATUS,
        date_format(ao_counter_upgrade.APPLY_DATE,'%Y-%m-%d %H:%i:%s') as applyDate,
        date_format(ao_counter_upgrade.APPROVE_DATE,'%Y-%m-%d %H:%i:%s') as approveDate,
            ao_counter_upgrade.ID as productId,
            ao_counter_upgrade.NEW_SEAT,
            ao_order_info.PAX_NAME,
            ao_order_info.ORDER_PRICE as price,
            ao_order_info.PAX_NO
        FROM
            ao_counter_upgrade
        LEFT JOIN ao_order_info
            ON ao_counter_upgrade.ORDER_NO = ao_order_info.ORDER_NO
        WHERE
            ao_counter_upgrade.ORDER_NO = #{orderNo}
    </select>

    <select id="applyPage" resultType="com.swcares.psi.ao.counterUpgrade.vo.UpgradeStatusUpDateVo">
        SELECT
        aoi.ORDER_NO,
        date_format(aoi.ORDER_DATE,'%Y-%m-%d') as orderDate,
        case
        when aoi.ORDER_STATUS = '0' then '未支付'
        when aoi.ORDER_STATUS = '1' then '支付失败'
        when aoi.ORDER_STATUS = '2' then '支付完成'
        when aoi.ORDER_STATUS = '3' then '退款中'
        when aoi.ORDER_STATUS = '4' then '退款失败'
        when aoi.ORDER_STATUS = '5' then '退款成功'
        when aoi.ORDER_STATUS = '6' then '已取消'
        else '发起退款'
        end                                                 as ORDER_STATUS,
        case
        when upgrade.UPGRADE_STATUS = '0' then '初始化'
        when upgrade.UPGRADE_STATUS = '1' then '升舱中'
        when upgrade.UPGRADE_STATUS = '2' then '升舱成功'
        when upgrade.UPGRADE_STATUS = '3' then '升舱失败'
        end                                                 as upgradeStatus,
        aoi.ORDER_PRICE,
        case
        when aoi.CHARGE_TYPE = '0' then '微信'
        when aoi.CHARGE_TYPE = '1' then '易宝'
        when aoi.CHARGE_TYPE = '2' then '支付宝'
        when aoi.CHARGE_TYPE = '3' then '现金'
        when aoi.CHARGE_TYPE = '5' then 'worldPay'
        else '现金'
        end                                                 as  chargeType,
        date_format(aoi.PAY_TIME,'%Y-%m-%d %H:%i:%s') as payTime,
        upgrade.APPLY_USER_NO,
        date_format(upgrade.APPLY_DATE,'%Y-%m-%d %H:%i:%s') as applyDate
        FROM
        ao_counter_upgrade upgrade
        LEFT JOIN ao_order_info aoi
        ON upgrade.ORDER_NO = aoi.ORDER_NO
        <where>
            upgrade.UPGRADE_UPDATE_STATUS is not null
            <if test="dto.startDate != null and dto.startDate != ''">
                and upgrade.APPLY_DATE <![CDATA[ >= ]]> CONCAT(#{dto.startDate},' 00:00:00')
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                and upgrade.APPLY_DATE <![CDATA[ <= ]]> CONCAT(#{dto.endDate},' 23:59:59')
            </if>
            <if test="dto.status != null and dto.status != ''">
                and upgrade.UPGRADE_UPDATE_STATUS = #{dto.status}
            </if>
        </where>
    </select>

</mapper>