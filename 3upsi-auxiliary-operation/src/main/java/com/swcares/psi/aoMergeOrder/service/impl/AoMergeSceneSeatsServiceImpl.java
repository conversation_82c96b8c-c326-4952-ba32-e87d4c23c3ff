package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSceneSeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSceneSeats;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeSceneSeatsMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSceneSeatsService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSceneSeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 合并支付现场座位销售产品详情订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeSceneSeatsServiceImpl extends ServiceImpl<AoMergeSceneSeatsMapper, AoMergeSceneSeats> implements IAoMergeSceneSeatsService {
    @Override
    public List<AoMergeSceneSeatsAccountsReportVo> getAccountsList(AoMergeSceneSeatsAccountsReportDto dto) {
        return baseMapper.getAccountsList(dto);
    }

    @Override
    public PsiPage<AoMergeSceneSeatsAccountsReportVo> getAccountsListPage(AoMergeSceneSeatsAccountsReportDto dto) {
        PsiPage<AoMergeSceneSeatsAccountsReportVo> page = new PsiPage<>(dto);
        return baseMapper.getAccountsListPage(page,dto);
    }

    @Override
    public List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeSceneSeatsAccountsReportDto dto) {
        return baseMapper.payTypesSummaryMoney(dto);
    }
}
