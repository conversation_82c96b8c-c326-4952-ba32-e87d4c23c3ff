package com.swcares.psi.aoMergeOrder.controller;


import com.component.core.exception.ColumnAuthException;
import com.component.core.processor.FieldPermissionsProcessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSceneSeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSceneSeatsService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSceneSeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSceneSeatsPageVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合并支付现场座位销售 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Api(tags = "合并支付现场座位销售")
@RestController
@RequestMapping("/api/ao/mergeOrder/sceneSeats")
public class AoMergeSceneSeatsController {

    @Autowired
    private IAoMergeSceneSeatsService aoMergeSceneSeatsService;

    @Autowired
    private FieldPermissionsProcessor fieldPermissionsProcessor;

    @GetMapping("/accounts/page")
    @ApiOperation(value = "现场座位销售结算报表分页查询")
    public RenderResult<AoMergeSceneSeatsPageVo> getSceneSeatsAccountsReportPage(AoMergeSceneSeatsAccountsReportDto dto){
        PsiPage<AoMergeSceneSeatsAccountsReportVo> page = aoMergeSceneSeatsService.getAccountsListPage(dto);
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVo = aoMergeSceneSeatsService.payTypesSummaryMoney(dto);
        AoMergeSceneSeatsPageVo pageVo = new AoMergeSceneSeatsPageVo();
        pageVo.setPage(page);
        pageVo.setPayTypesSummaryMoneyVo(payTypesSummaryMoneyVo);
        return RenderResult.success(pageVo);
    }

    @GetMapping("/accounts/report")
    @ApiOperation(value = "现场座位销售结算报表导出")
    public void aoSceneSeatsAccountExport(AoMergeSceneSeatsAccountsReportDto dto, HttpServletResponse response, HttpServletRequest request) throws Exception {
        response.setContentType("application/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("现场座位销售结算报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeSceneSeatsAccountsReportVo> list = aoMergeSceneSeatsService.getAccountsList(dto);

        AoMergeSceneSeatsPageVo pageVo = new AoMergeSceneSeatsPageVo();
        PsiPage<AoMergeSceneSeatsAccountsReportVo> page = new PsiPage<>();
        page.setRecords(list);
        pageVo.setPage(page);
        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            pageVo = (AoMergeSceneSeatsPageVo)fieldPermissionsProcessor.process(serverHttpRequest, pageVo);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            throw new ColumnAuthException(e);
        }
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVos = aoMergeSceneSeatsService.payTypesSummaryMoney(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), Arrays.asList("现场座位销售结算", "合计"), Arrays.asList(pageVo.getPage().getRecords(),payTypesSummaryMoneyVos), Arrays.asList(AoMergeSceneSeatsAccountsReportVo.class, PayTypesSummaryMoneyVo.class), null);

    }
}

