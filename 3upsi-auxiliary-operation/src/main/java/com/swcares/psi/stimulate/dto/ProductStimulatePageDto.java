package com.swcares.psi.stimulate.dto;

import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductStimulatePageDto extends BaseDto {

    @ApiModelProperty(value = "产品名称  订单类型1一人多座,2现场座位销售(选座),3逾重行李,4贵宾室,5柜台升舱,6机上升舱  多选逗号分割")
    private String orderType;

    @ApiModelProperty(value = "部门")
    private String stimulateDepartment;

    @ApiModelProperty(value = "科室")
    private String stimulateAdministrative;

    @ApiModelProperty(value = "航班日期开始")
    private String flightDateStart;

    @ApiModelProperty(value = "航班日期结束")
    private String flightDateEnd;

    @ApiModelProperty(value = "航线性质")
    private String segmentType;
}
