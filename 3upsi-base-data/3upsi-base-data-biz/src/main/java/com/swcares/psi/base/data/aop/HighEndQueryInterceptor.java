package com.swcares.psi.base.data.aop;

import com.swcares.psi.base.data.api.highend.HighEndQueryDto;
import com.swcares.psi.base.data.api.highend.HighEndQueryParamField;
import com.swcares.psi.base.data.api.highend.HighEndServiceEnum;
import com.swcares.psi.combine.config.ScheduleConfiguration;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;
/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.base.data.aop.HighEndQueryInterceptor <br>
 * Description：高端旅客查询拦截注入参数<br>
 * Copyright © 2022/8/31 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * @date 2022/8/31 <br>
 * @version v1.0 <br>
 */
@Intercepts(@Signature(type = Executor.class, method = "query",
        args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}))
@Slf4j
@RequiredArgsConstructor
@Component
public class HighEndQueryInterceptor implements Interceptor {

    private final ScheduleConfiguration scheduleConfiguration;

    @Override
    public Object intercept(final Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        Object params = args[1];
        if (params instanceof MapperMethod.ParamMap) {
            HighEndQueryDto.HighEndQueryDtoBuilder builder = HighEndQueryDto.builder();
            for (Object param : ((MapperMethod.ParamMap) params).values()) {
                if (param == null) {
                    continue;
                }
                Class<?> cls = param.getClass();
                HighEndQueryParamField highEndQueryParamField = cls.getAnnotation(HighEndQueryParamField.class);
                if (highEndQueryParamField != null) {
                    if (highEndQueryParamField.filterService() != HighEndServiceEnum.DEF_NULL) {
                        builder.filterService(highEndQueryParamField.filterService().name());
                    }
                    if (highEndQueryParamField.serviceSort() != HighEndServiceEnum.DEF_NULL) {
                        builder.serviceSort(highEndQueryParamField.filterService().name());
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.flightDateField())) {
                        Object fieldOrMethod = getInheritedField(cls, highEndQueryParamField.flightDateField());
                        if (fieldOrMethod != null) {
                            builder.flightDate((String) ((Field) fieldOrMethod).get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.flightDateBeginField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.flightDateBeginField());
                        if (fieldOrMethod != null) {
                            builder.beginDate((String) fieldOrMethod.get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.flightDateEndField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.flightDateEndField());
                        if (fieldOrMethod != null) {
                            builder.endDate((String) fieldOrMethod.get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.orgsField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.orgsField());
                        if (fieldOrMethod != null) {
                            builder.orgs((String) fieldOrMethod.get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.dstsField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.dstsField());
                        if (fieldOrMethod != null) {
                            builder.dsts((String) fieldOrMethod.get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.flightNoField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.flightNoField());
                        if (fieldOrMethod != null) {
                            builder.flightNo((String) fieldOrMethod.get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.paxTypesField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.paxTypesField());
                        if (fieldOrMethod != null) {
                            builder.paxTypes((String) fieldOrMethod.get(param));
                        }
                    }
                    if (StringUtils.isNotBlank(highEndQueryParamField.paxTypeCodesField())) {
                        Field fieldOrMethod = getInheritedField(cls, highEndQueryParamField.paxTypeCodesField());
                        if (fieldOrMethod != null) {
                            builder.paxTypeCodes((String) fieldOrMethod.get(param));
                        }
                    }
                    boolean ffpFilter = highEndQueryParamField.ffpFilter();
                    HighEndQueryDto highEndQueryDto = builder.build();
                    // 旅客类别PCC_CODES在查询旅客时作为过滤条件使用
                    if (scheduleConfiguration.isPsgPccCodesQueryFilterEnable() && StringUtils.isNotBlank(highEndQueryDto.getEndDate())) {
                        String endDate = highEndQueryDto.getEndDate();
                        LocalDate localDate = LocalDate.parse(endDate.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        if (!localDate.isAfter(LocalDate.now().minusDays(scheduleConfiguration.getPsgPccCodesUpdateBackwardDays()))) {
                            highEndQueryDto.setFilterByPccCodes(true);
                        }
                    }
                    ((MapperMethod.ParamMap) params).put("_hed", highEndQueryDto);
                    ((MapperMethod.ParamMap) params).put("_ffpFilter", ffpFilter);
                    break;
                }
            }
            if (!((MapperMethod.ParamMap) params).containsKey("_hed")) {
                ((MapperMethod.ParamMap) params).put("_hed", null);
            }
            if (!((MapperMethod.ParamMap) params).containsKey("_ffpFilter")) {
                ((MapperMethod.ParamMap) params).put("_ffpFilter", false);
            }
        }

        return invocation.proceed();
    }

    private Field getInheritedField(Class cls, String fieldName) {
        try {
            Field field = cls.getDeclaredField(fieldName);
            if (!field.isAccessible()) {
                field.setAccessible(true);
                return field;
            }
        } catch (NoSuchFieldException e) {
            if (cls.getSuperclass() != null) {
                return getInheritedField(cls.getSuperclass(), fieldName);
            }
        }
       return null;
    }

}