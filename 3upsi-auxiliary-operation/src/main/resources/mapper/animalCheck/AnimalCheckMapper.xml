<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.animal.mapper.AnimalCheckMapper">


    <select id="getAnimalCheckPage" resultType="com.swcares.psi.ao.animal.vo.AnimalCheckPageVo">
        SELECT

            ID ,
            STATUS ,
            sum(USAGE_num)as total,
            RESERVE_TIME ,
            UPDATE_TIME ,
            TICKET_NUM ,
            FLIGHT_DATE ,
            FLIGHT_NO as flightNo,
            PHONE_NO as phoneNo,
            PASSENGER_NAME ,
            ID_NUMBER ,
            RESERVE_ID ,
            ORG ,
            DST ,
            FLIGHT_SEGMENT ,
            sum(if(PET_TYPE = 'cat',USAGE_num,0)) catNum,
            sum(if(PET_TYPE = 'dog',USAGE_num,0)) dogNum,
            IF (LENGTH(FLIGHT_SEGMENT)  <![CDATA[ <= ]]> 7,
            CONCAT(
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),'-',
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', -1 ))),
            CONCAT(
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),'-',
            get_city_name (SUBSTRING_INDEX( SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 2 ), '-',- 1 )),'-',
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', - 1 )))
            ) AS segment,
            IF (LENGTH(FLIGHT_SEGMENT)  <![CDATA[ <= ]]> 7,
            CONCAT(
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 ),'-',
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', -1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', -1 )),
            CONCAT(
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 ),'-',
            get_city_name (SUBSTRING_INDEX( SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 2 ), '-',- 1 )),SUBSTRING_INDEX( SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 2 ), '-',- 1 ),'-',
            get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', - 1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', - 1 ))
            ) AS segmentStr,

           ( case when STATUS = '1' then '1'
                when STATUS = '2' then '3'
            when STATUS = '3' then '2' end ) status_sort

        from ao_animal_check aac


        <where>
            and RESERVE_TIME = (select max(RESERVE_TIME) from ao_animal_check where FLIGHT_DATE = aac.FLIGHT_DATE
            and FLIGHT_NO =aac.FLIGHT_NO and ID_NUMBER =aac.ID_NUMBER)
            <if test="param.flightStartDate != null and param.flightStartDate != ''">
                AND FLIGHT_DATE <![CDATA[ >=  ]]> #{param.flightStartDate}
            </if>
            <if test="param.flightEndDate != null and param.flightEndDate != ''">
                AND FLIGHT_DATE <![CDATA[ <= ]]> #{param.flightEndDate}
            </if>
            <if test="param.flightNumber != null and param.flightNumber != ''">
                AND FLIGHT_NO = #{param.flightNumber}
            </if>

            <if test="param.org != null and param.org != ''">
                AND ORG = #{param.org}
            </if>
            <if test="param.dst != null and param.dst != ''">
                AND DST = #{param.dst}
            </if>
            <if test="param.passengerName != null and param.passengerName != ''">
                AND PASSENGER_NAME like CONCAT('%',UPPER(#{param.passengerName}),'%')
            </if>
            <if test="param.idNumber != null and param.idNumber != ''">
                AND ID_NUMBER = #{param.idNumber}
            </if>
            <if test="param.ticketNum != null and param.ticketNum != ''">
                AND TICKET_NUM = #{param.ticketNum}
            </if>

            <if test="null != param.status">
                AND STATUS in
                <foreach collection="param.status" item="at" open="(" close=")" separator=",">
                    #{at}
                </foreach>
            </if>
        </where>

        GROUP BY  FLIGHT_DATE ,FLIGHT_NO ,ID_NUMBER
        order by status_sort asc ,FLIGHT_DATE asc,STD asc,RESERVE_TIME asc

    </select>


    <select id="getAnimalCheckList" resultType="com.swcares.psi.ao.animal.vo.AnimalCheckExportVo">
        SELECT

        ID ,
        case when STATUS = '1' then '预约成功'
        when STATUS = '2' then '已取消'
        when STATUS = '3' then '已完成'
        else '' end status ,
        sum(USAGE_num)as total,
        RESERVE_TIME ,
        UPDATE_TIME ,
        TICKET_NUM ,
        FLIGHT_DATE ,
        FLIGHT_NO as flightNo,
        PHONE_NO ,
        PASSENGER_NAME ,
        ID_NUMBER ,
        RESERVE_ID ,
        ORG ,
        DST ,
        FLIGHT_SEGMENT ,
        sum(if(PET_TYPE = 'cat',USAGE_num,0)) catNum,
        sum(if(PET_TYPE = 'dog',USAGE_num,0)) dogNum,
        IF (LENGTH(FLIGHT_SEGMENT)  <![CDATA[ <= ]]> 7,
        CONCAT(
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),'-',
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', -1 ))),
        CONCAT(
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),'-',
        get_city_name (SUBSTRING_INDEX( SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 2 ), '-',- 1 )),'-',
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', - 1 )))
        ) AS segment,
        IF (LENGTH(FLIGHT_SEGMENT)  <![CDATA[ <= ]]> 7,
        CONCAT(
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 ),'-',
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', -1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', -1 )),
        CONCAT(
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 1 ),'-',
        get_city_name (SUBSTRING_INDEX( SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 2 ), '-',- 1 )),SUBSTRING_INDEX( SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', 2 ), '-',- 1 ),'-',
        get_city_name (SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', - 1 )),SUBSTRING_INDEX(FLIGHT_SEGMENT, '-', - 1 ))
        ) AS segmentStr

        from ao_animal_check aac


        <where>
            and RESERVE_TIME = (select max(RESERVE_TIME) from ao_animal_check where FLIGHT_DATE = aac.FLIGHT_DATE
            and FLIGHT_NO =aac.FLIGHT_NO and ID_NUMBER =aac.ID_NUMBER)
            <if test="param.flightStartDate != null and param.flightStartDate != ''">
                AND FLIGHT_DATE <![CDATA[ >=  ]]> #{param.flightStartDate}
            </if>
            <if test="param.flightEndDate != null and param.flightEndDate != ''">
                AND FLIGHT_DATE <![CDATA[ <= ]]> #{param.flightEndDate}
            </if>
            <if test="param.flightNumber != null and param.flightNumber != ''">
                AND FLIGHT_NO = #{param.flightNumber}
            </if>

            <if test="param.org != null and param.org != ''">
                AND ORG = #{param.org}
            </if>
            <if test="param.dst != null and param.dst != ''">
                AND DST = #{param.dst}
            </if>
            <if test="param.passengerName != null and param.passengerName != ''">
                AND PASSENGER_NAME like CONCAT('%',#{param.passengerName},'%')
            </if>
            <if test="param.idNumber != null and param.idNumber != ''">
                AND ID_NUMBER = #{param.idNumber}
            </if>
            <if test="param.ticketNum != null and param.ticketNum != ''">
                AND TICKET_NUM = #{param.ticketNum}
            </if>

            <if test="null != param.status">
                AND STATUS in
                <foreach collection="param.status" item="at" open="(" close=")" separator=",">
                    #{at}
                </foreach>
            </if>
        </where>

        GROUP BY  FLIGHT_DATE ,FLIGHT_NO ,ID_NUMBER
        order by FLIGHT_DATE asc,STD asc,RESERVE_TIME asc
    </select>

    <select id="getAnimalCheckH5FltPage" resultType="com.swcares.psi.ao.animal.vo.AnimalCheckH5Vo">
        SELECT
            ID ,
            FLIGHT_DATE ,
            FLIGHT_NO ,
            CONCAT(ORG,'-',DST) as segment ,
            STD,
            PLANECODE,
        ifnull(sum(if(STATUS = '1',USAGE_num,null)),0)  successNum,
        ifnull(sum(if(STATUS = '2',USAGE_num,null)),0)  cancelNum,
        ifnull(sum(if(STATUS = '3',USAGE_num,null)),0)  completeNum
        from ao_animal_check aac
        where 1=1
        and	RESERVE_TIME = (select max(RESERVE_TIME) from ao_animal_check where FLIGHT_DATE = aac.FLIGHT_DATE
        and FLIGHT_NO =aac.FLIGHT_NO and ID_NUMBER =aac.ID_NUMBER)
        <if test="param.keySearch != null and param.keySearch != ''">
            AND (FLIGHT_NO like CONCAT('%',#{param.keySearch},'%') or ORG = #{param.keySearch} or DST = #{param.keySearch})
        </if>
        <if test="param.flightDate != null and param.flightDate != ''">
            AND FLIGHT_DATE = #{param.flightDate}
        </if>
        GROUP BY  FLIGHT_DATE ,FLIGHT_NO , ORG ,DST
        order by FLIGHT_DATE asc ,STD asc
    </select>

    <select id="getAnimalCheckH5PaxList" resultType="com.swcares.psi.ao.animal.vo.AnimalCheckH5PaxListVo">
        SELECT
            FLIGHT_DATE ,
            FLIGHT_NO ,
            FLIGHT_SEGMENT as segment ,
            PASSENGER_NAME,
            ID_NUMBER,
            PHONE_NO,
            sum(if(PET_TYPE = 'cat',USAGE_num,0)) catNum,
            sum(if(PET_TYPE = 'dog',USAGE_num,0)) dogNum,
            RESERVE_TIME,UPDATE_TIME,STATUS
        from ao_animal_check aac
        where 1=1
        and	RESERVE_TIME = (select max(RESERVE_TIME) from ao_animal_check where FLIGHT_DATE = aac.FLIGHT_DATE
        and FLIGHT_NO =aac.FLIGHT_NO and ID_NUMBER =aac.ID_NUMBER)

        AND FLIGHT_DATE = #{param.flightDate}
            AND FLIGHT_NO = #{param.flightNo}
            AND ORG = #{param.org}
            AND DST = #{param.dst}

        <if test="param.keySearch != null and param.keySearch != ''">
            AND (PASSENGER_NAME like CONCAT('%',#{param.keySearch},'%') or ID_NUMBER = #{param.keySearch})
        </if>

        GROUP BY  ID_NUMBER
        ORDER BY STATUS asc ,RESERVE_TIME asc
    </select>
</mapper>