package com.swcares.psi.ao.overweightbkg.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.swcares.psi.ao.utils.AmountConvers;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Description: [国内逾重行李票录入模板]
 * Created on 2020-12-10 11:04
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AoOverweightInputTemplateReportVo {

    @ExcelIgnore
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**
     * 值为空
     */
    @ExcelProperty(value = "批组号")
    @ApiModelProperty(value = "批组号")
    private String batchGroupNumber;

    /**
     * 固定值1
     */
    @ExcelProperty(value = "子批号")
    @ApiModelProperty(value = "子批号")
    private String subBatchNumber;

    /**
     * 值为空
     */
    @ExcelProperty(value = "代理人号")
    @ApiModelProperty(value = "代理人号")
    private String agentNumber;

    /**
     * 固定值876
     */
    @ExcelProperty(value = "公司代码")
    @ApiModelProperty(value = "公司代码")
    private String companyCode;

    /**
     * 取逾重行李结算报表财务订单号
     */
    @ExcelProperty(value = "票证类型")
    @ApiModelProperty(value = "票证类型")
    private String ticketType;
    /**
     * 票号后七位
     */
    @ExcelProperty(value = "票号")
    @ApiModelProperty(value = "票号")
    private String tktNo;

    /**
     * 票联号 固定值1
     */
    @ExcelProperty(value = "票联号")
    @ApiModelProperty(value = "票联号")
    private String tktCNo;

    @ExcelProperty(value = "出票日期")
    @ApiModelProperty(value = "出票日期")
    private String issueTime;

    @ExcelProperty(value = "出发地")
    @ApiModelProperty(value = "出发地")
    private String org;

    @ExcelProperty(value = "目的地")
    @ApiModelProperty(value = "目的地")
    private String dst;

    @ExcelProperty(value = "航班号")
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ExcelProperty(value = "航班日期")
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ExcelProperty(value = "行李重量")
    @ApiModelProperty(value = "超重行李重量（国内）")
    private String overWeightBkgWeight;

    /**
     * 值为空
     */
    @ExcelProperty(value = "声明价值附加费")
    @ApiModelProperty(value = "声明价值附加费")
    private String valuationCharge;

    /**
     * 订单总价
     */
    @ExcelProperty(value = "面额")
    @ApiModelProperty(value = "面额")
    @AmountConvers
    private String denomination;

    /**
     * 为空
     */
    @ExcelProperty(value = "标准手续费率")
    @ApiModelProperty(value = "标准手续费率")
    private String standardHandlingRate;

    /**
     * 订单总价
     */
    @ExcelProperty(value = "运费单价")
    @ApiModelProperty(value = "运费单价")
    @AmountConvers
    private String totalPrice;

//    @ExcelProperty(value = "银行订单号")
    @ExcelIgnore
    @ApiModelProperty(value = "银行订单号")
    private String bankOrderNo;

    @ExcelProperty(value = "客票公司代码")
    @ApiModelProperty(value = "客票公司代码")
    private String passengerTicketCompanyCode;

    @ExcelProperty(value = "客票票证类型")
    @ApiModelProperty(value = "客票票证类型")
    private String passengerTicketType;

    @ExcelProperty(value = "客票票号")
    @ApiModelProperty(value = "客票票哈")
    private String passengerTktNo;
    @ExcelIgnore
    @ApiModelProperty(value = "行政区域")
    private String areaCompany;

    public AoOverweightInputTemplateReportVo(){
        this.batchGroupNumber = null;
        this.subBatchNumber = null;
        this.agentNumber = null;
        this.companyCode = "876";
        this.tktCNo = "1";
        this.valuationCharge = null;
        this.standardHandlingRate = null;
    }
}
