package com.swcares.psi.stimulate.dto;

import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DepartmentStimulatePageDto extends BaseDto {

    @ApiModelProperty(value = "部门")
    private String stimulateDepartment;

    @ApiModelProperty(value = "科室")
    private String stimulateAdministrative;

    @ApiModelProperty(value = "航班日期开始")
    private String flightDateStart;

    @ApiModelProperty(value = "航班日期结束")
    private String flightDateEnd;

    @ApiModelProperty(value = "业务类型")
    private String orderType;

    @ApiModelProperty(value = "航线性质")
    private String segmentType;

}
