package com.swcares.psi.aoMergeOrder.service;

import com.swcares.psi.aoMergeOrder.entity.AoMergeCashTurnList;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.vo.CashTurnOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.CashTurnSaveVo;

import java.util.List;

/**
 * <p>
 * 合并支付现金缴纳清单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeCashTurnListService extends IService<AoMergeCashTurnList> {
    CashTurnSaveVo cashTurnResult(String turnNo);
    CashTurnSaveVo refreshCashTurnResult(String turnNo);

    List<CashTurnOrderTaskVo> getCashTurnOrderAll();
}
