package com.swcares;

import com.component.core.annotation.EnableColumnPermissions;
import com.swcares.psi.base.base.ScgsiRepositoryImpl;
import com.swcares.psi.common.security.annotation.EnablePsiFeignClients;
import com.swcares.psi.common.security.annotation.EnablePsiResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


@EnableSwagger2
@EnablePsiResourceServer
@EnablePsiFeignClients
@SpringCloudApplication
@EnableAutoConfiguration(exclude = {
        org.activiti.spring.boot.SecurityAutoConfiguration.class})
@EnableJpaRepositories(repositoryBaseClass= ScgsiRepositoryImpl.class)
@EnableColumnPermissions
public class LuggageApplication {
    public static void main(String[] args) {
        SpringApplication.run(LuggageApplication.class, args);
    }
}
