<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.stimulate.mapper.AoOrderImportMapper">
    <sql id="pageSql">
        select
            ID,
            date_format(FLIGHT_DATE, '%Y-%m-%d') AS flightDate,
            date_format(ORDER_DATE, '%Y-%m-%d') AS orderDate,
            FLIGHT_NO,
            ORG_NAME,
            DST_NAME,
            case  FLIGHT_TYPE
               when 'D' THEN '国内'
               when 'I' THEN '国际'
               when 'R' THEN '地区'
           END as flightType,
           TKT_NO,
           CASE
                WHEN ORDER_TYPE = '1' THEN '一人多座'
                WHEN ORDER_TYPE = '2' THEN '付费选座'
                WHEN ORDER_TYPE = '3' THEN '逾重行李'
                WHEN ORDER_TYPE = '4' THEN '贵宾室'
                when ORDER_TYPE = '5' then '柜台升舱'
                else '机上升舱'
            END AS orderType,
            ORDER_PRICE,
            CONCAT(CURRENCY_SPECIES_CODE,'(',CURRENCY_SPECIES,')')  as currencySpecies,
            ORDER_CREATE_USER,
            ORDER_CREATE_NO,
            ORDER_CREATE_USER_DEPARTMENT_NAME,
            ORDER_CREATE_ADMINISTRATIVE_NAME
         FROM    AO_ORDER_IMPORT
         <where>

             <if test="dto.orderCreateUser != null and dto.orderCreateUser !=''">
                 and ORDER_CREATE_USER like CONCAT('%',#{dto.orderCreateUser},'%')
             </if>
             <if test="dto.orderCreateNo != null and dto.orderCreateNo !=''">
                 and ORDER_CREATE_NO like CONCAT('%',#{dto.orderCreateNo},'%')
             </if>
             <if test="dto.orderCreateUserDepartment != null and dto.orderCreateUserDepartment !=''">
                 and ORDER_CREATE_USER_DEPARTMENT = #{dto.orderCreateUserDepartment}
             </if>

             <if test="dto.orderCreateAdministrative != null and dto.orderCreateAdministrative !=''">
                 and ORDER_CREATE_ADMINISTRATIVE = #{dto.orderCreateAdministrative}
             </if>

             <if test="dto.orderDateStart != null and dto.orderDateStart !=''">
                 and ORDER_DATE  <![CDATA[ >= ]]> date_format(#{dto.orderDateStart}, '%Y-%m-%d')
             </if>

             <if test="dto.orderDateEnd != null and dto.orderDateEnd !=''">
                 and ORDER_DATE  <![CDATA[ <= ]]> date_format(#{dto.orderDateEnd}, '%Y-%m-%d')
             </if>


             <if test="dto.flightDateStart != null and dto.flightDateStart !=''">
                 and FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d')
             </if>

             <if test="dto.flightDateEnd != null and dto.flightDateEnd !=''">
                 and FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d')
             </if>

             <if test="dto.flightNo != null and dto.flightNo !=''">
                 and FLIGHT_NO = #{dto.flightNo}
             </if>
             <if test="dto.orderType != null and dto.orderType !=''">
               AND find_in_set(ORDER_TYPE, #{dto.orderType})
             </if>

             <if test="dto.flightType != null and dto.flightType !=''">
                 AND find_in_set(FLIGHT_TYPE, #{dto.flightType})
             </if>

         </where>

         order by ORDER_TIME desc , FLIGHT_DATE desc
    </sql>

    <select id="pageList" resultType="com.swcares.psi.stimulate.vo.AoOrderImportPageVo">
        <include refid="pageSql"></include>
    </select>

    <select id="getList" resultType="com.swcares.psi.stimulate.vo.AoOrderImportPageVo">
        <include refid="pageSql"></include>
    </select>

</mapper>
