package com.swcares.psi.aoMergeOrder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.psi.common.utils.ExcelBaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AoMergeOverweightBkgAccountsReportVo implements ExcelBaseVo {

    @ExcelProperty(value = "订单日期")
    @ApiModelProperty(value = "订单日期")
    @ColumnWidth(16)
    private String orderDate;

    @ExcelProperty(value = "子订单号")
    @ApiModelProperty(value = "子订单号")
    @ColumnWidth(24)
    private String subOrderNo;

    @ExcelProperty(value = "银行订单号")
    @ApiModelProperty(value = "银行订单号")
    @ColumnWidth(20)
    private String bankOrderNo;

    @ExcelProperty(value = "财务订单号")
    @ApiModelProperty(value = "财务订单号")
    @ColumnWidth(20)
    private String financeOrderNo;

    @ExcelProperty(value = "航班日期")
    @ApiModelProperty(value = "航班日期")
    @ColumnWidth(16)
    private String flightDate;

    @ExcelProperty(value = "航班号")
    @ApiModelProperty(value = "航班号")
    @ColumnWidth(16)
    private String flightNo;

    @ExcelProperty(value = "出发地")
    @ApiModelProperty(value = "出发地")
    @ColumnWidth(16)
    private String org;

    @ExcelProperty(value = "目的地")
    @ApiModelProperty(value = "目的地")
    @ColumnWidth(16)
    private String dst;

    @ExcelProperty(value = "客票号")
    @ApiModelProperty(value = "客票号")
    @ColumnWidth(16)
    private String tktNo;

    @ExcelProperty(value = "航线性质")
    @ApiModelProperty(value = "航线性质")
    @ColumnWidth(16)
    private String flightType;

    @ExcelProperty(value = "行李超重重量")
    @ApiModelProperty(value = "行李超重重量")
    @ColumnWidth(20)
    private String specialOverWeight;

    @ExcelProperty(value = "行李超出件数")
    @ApiModelProperty(value = "行李超出件数")
    @ColumnWidth(20)
    private String overQuantity;

    @ExcelProperty(value = "运费单价")
    @ApiModelProperty(value = "运费单价")
    @ColumnWidth(16)
    private String practicalOrderPrice;

    @ExcelProperty(value = "收款方式")
    @ApiModelProperty(value = "收款方式")
    @ColumnWidth(16)
    private String payType;

    @ExcelProperty(value = "币种")
    @ApiModelProperty(value = "币种")
    @ColumnWidth(12)
    private String currencySpecies;

    @ExcelProperty(value = "总价")
    @ApiModelProperty(value = "总价")
    @ColumnWidth(8)
    private String payOrRefundPrice;

    @ExcelProperty(value = "子订单状态")
    @ApiModelProperty(value = "子订单状态")
    @ColumnWidth(20)
    private String orderStatus;

    @ExcelProperty(value = "支付状态")
    @ApiModelProperty(value = "支付状态")
    @ColumnWidth(16)
    private String payStatus;

    @ExcelProperty(value = "支付时间")
    @ApiModelProperty(value = "支付时间")
    @ColumnWidth(24)
    private String payDate;

    @ExcelProperty(value = "旅客姓名")
    @ApiModelProperty(value = "旅客姓名")
    @ColumnWidth(20)
    private String paxName;

    @ExcelProperty(value = "旅客类型")
    @ApiModelProperty(value = "旅客类型")
    @ColumnWidth(16)
    private String paxType;

    @ExcelProperty(value = "旅客类别")
    @ApiModelProperty(value = "旅客类别")
    @ColumnWidth(16)
    private String paxCategory;

    @ExcelProperty(value = "常客卡号")
    @ApiModelProperty(value = "常客卡号")
    @ColumnWidth(16)
    private String ffpNo;

    @ExcelProperty(value = "操作人号")
    @ApiModelProperty(value = "操作人号")
    @ColumnWidth(16)
    private String createNo;

    @ExcelProperty(value = "操作人")
    @ApiModelProperty(value = "操作人")
    @ColumnWidth(20)
    private String createUser;

    @ExcelProperty(value = "部门")
    @ApiModelProperty(value = "部门")
    @ColumnWidth(16)
    private String department;

    @ExcelProperty(value = "缴纳单号")
    @ApiModelProperty(value = "缴纳单号")
    @ColumnWidth(16)
    private String turnNumber;

    @ExcelProperty(value = "缴纳状态")
    @ApiModelProperty(value = "缴纳状态")
    private String turnStatus;

    @ExcelProperty(value = "行政区域")
    @ApiModelProperty(value = "行政区域")
    @ColumnWidth(16)
    private String areaCompany;
}
