package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergePayOrderInfo;
import com.swcares.psi.aoMergeOrder.mapper.AoMergePayOrderInfoMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergePayOrderInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 辅营合并总支付订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergePayOrderInfoServiceImpl extends ServiceImpl<AoMergePayOrderInfoMapper, AoMergePayOrderInfo> implements IAoMergePayOrderInfoService {


    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;


    @Override
    public String getShowQrCode(String orderNo) {
        AoMergeOrderInfo one = aoMergeOrderInfoService.lambdaQuery()
                .eq(AoMergeOrderInfo::getOrderNo, orderNo)
                .one();
        if (one == null) {
            return null;
        }
        AoMergePayOrderInfo payOrderInfo = this.lambdaQuery()
                .eq(AoMergePayOrderInfo::getId, one.getOrderPayId())
                .eq(AoMergePayOrderInfo::getStatus, AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT)
                .one();
        if (payOrderInfo == null) {
            return null;
        }
        return payOrderInfo.getPayPath();
    }
}
