<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.base.data.mapper.SmsSendRecordMapper">

    <sql id="commonQuerySmsRecord">
        select ssr.ID,
            ssr.MOBILE,
            ssr.RECIVER_NAME,
            ssr.MSG_CONTENT,
            ssr.SEND_TIME,
            case ssr.SEND_STATE when '0' then '成功' when '1' then '失败' else ssr.SEND_STATE end as sendState,
            case ssr.OPERATORS_SEND_STATE
                when '0' then '成功'
                when '1' then '失败'
            else ssr.OPERATORS_SEND_STATE end                                             as operatorsSendState,
            ssr.SMS_TYPE,
            ssr.FLIGHT_DATE,
            ssr.FLIGHT_NO,
            ssr.SEND_USER,
            ssr.SEND_RETURN_CODE,
            ssr.ERROR_INFO
        from sms_send_record ssr
        where 1 = 1
        <if test="smsReDto.flightDateBegin != null and smsReDto.flightDateBegin != '' and smsReDto.flightDateEnd != null and smsReDto.flightDateEnd != ''">
            AND ssr.FLIGHT_DATE between #{smsReDto.flightDateBegin} and #{smsReDto.flightDateEnd}
        </if>
        <if test="smsReDto.mobile != null and smsReDto.mobile != ''">
            AND ssr.MOBILE = #{smsReDto.mobile}
        </if>
        <if test="smsReDto.flightNo != null and smsReDto.flightNo != ''">
            AND ssr.FLIGHT_NO = #{smsReDto.flightNo}
        </if>
        <if test="smsReDto.sendTimeBegin != null and smsReDto.sendTimeBegin != '' and smsReDto.sendTimeEnd != null and smsReDto.sendTimeEnd != ''">
            AND ssr.SEND_TIME between concat(#{smsReDto.sendTimeBegin},' 00:00:00') and concat(#{smsReDto.sendTimeEnd},' 23:59:59')
        </if>
        <if test="smsReDto.smsType != null and smsReDto.smsType != ''">
            AND ssr.SMS_TYPE = #{smsReDto.smsType}
        </if>
        <if test="smsReDto.sendState != null and smsReDto.sendState != ''">
            AND ssr.SEND_STATE = #{smsReDto.sendState}
        </if>
        <if test="smsReDto.sendUser != null and smsReDto.sendUser != ''">
            AND ssr.SEND_USER = #{smsReDto.sendUser}
        </if>
        ORDER BY SEND_TIME DESC
    </sql>
    
    <select id="getSmsSendRecordList" parameterType="com.swcares.psi.base.data.api.dto.SmsSendRecordDto"
            resultType="com.swcares.psi.base.data.api.entity.SmsSendRecord">
        <include refid="commonQuerySmsRecord"/>
    </select>
    <select id="getSmsSendRecordPage" parameterType="com.swcares.psi.base.data.api.dto.SmsSendRecordDto"
            resultType="com.swcares.psi.base.data.api.entity.SmsSendRecord">
        <include refid="commonQuerySmsRecord"/>
    </select>

    <sql id="getNotifierSmsRecord">
        SELECT
            SSR.RECIVER_NAME as reciverName,
            SSR.MOBILE AS mobile,
            CASE SSR.SEND_STATE WHEN '0' THEN '成功' WHEN '1' THEN '失败' END AS sendStatusName,
            SSR.SEND_STATE AS sendStatus,
            CASE ST.TEMPLATE_BUSINESS_TYPE WHEN '0' THEN '航班调整' WHEN '1' THEN '服务补偿'
            WHEN '2' THEN '商务礼遇' WHEN '3' THEN '重点旅客' WHEN '4' THEN '其他' WHEN '5' THEN '疫情提示'
            WHEN '6' THEN '关注航班' WHEN '7' THEN '关注旅客' WHEN '8' THEN '贵宾短信' WHEN '9' THEN '大客户升舱'  WHEN '10' THEN '辅营通知'
            WHEN '11' THEN 'Wi-Fi服务通知'
            WHEN '2-1' THEN '满意度调查'
            END AS smsServiceTypeName,
            ST.TEMPLATE_BUSINESS_TYPE AS smsServiceType,
            SSR.FLIGHT_NO AS flightNo,
            DATE_FORMAT(ssr.FLIGHT_DATE,'%Y-%m-%d') AS flightDate,
            IFNULL(SSR.SEGMENT,CONCAT(SSR.ORG,'-',SSR.DST)) AS segment,
            SSR.TKT_NO as tktNo,
            SSR.PNR_REF as pnr,
            DATE_FORMAT(ssr.SEND_TIME,'%Y-%m-%d %H:%i:%s') AS sendTime,
            ssr.MSG_CONTENT AS smsContent,
            SSR.SMS_TYPE AS smsType,
            CASE SSR.SMS_TYPE WHEN 'C' THEN '取消' WHEN 'CR' THEN '取消恢复' WHEN 'D' THEN '延误' WHEN 'T' THEN '调时'
            WHEN 'DA' THEN '延误提前' WHEN 'DR' THEN '延误恢复' WHEN 'B' THEN '补班' WHEN 'PC' THEN '取消保护' WHEN 'PD'
            THEN '延误保护' END AS smsTypeName,
            SSR.SEND_TYPE AS sendType,
            ST.TEMPLATE_CATEGORY as templateCategory,
            CASE ST.TEMPLATE_CATEGORY WHEN '0' THEN '通知提醒' WHEN '1' THEN '增值服务' WHEN '2' THEN '调查问卷' END as templateCategoryName,
            CASE SSR.SEND_TYPE WHEN '0' THEN '手动' WHEN '1' THEN '自动' END AS sendTypeName
        FROM
            sms_send_record SSR
        LEFT JOIN SMS_TEMPLATE ST ON SSR.SMS_TEMPLATE_ID = ST.ID
        WHERE
        1 = 1
        <if test="paramDto.templateCategory != null and paramDto.templateCategory != ''">
            AND ST.TEMPLATE_CATEGORY = #{paramDto.templateCategory}
        </if>
        <if test="paramDto.reciverName != null and paramDto.reciverName != ''">
            AND  SSR.RECIVER_NAME LIKE CONCAT('%',#{paramDto.reciverName},'%')
        </if>
        <if test="paramDto.mobile != null and paramDto.mobile != ''">
            AND  SSR.MOBILE  LIKE CONCAT('%',#{paramDto.mobile},'%')
        </if>
        <if test="paramDto.sendStatus != null and paramDto.sendStatus != ''">
            AND  SSR.SEND_STATE = #{paramDto.sendStatus}
        </if>
        <if test="paramDto.smsServiceType != null and paramDto.smsServiceType != ''">
            AND  ST.TEMPLATE_BUSINESS_TYPE IN
            <foreach collection="paramDto.smsServiceTypeList" item="serviceType" separator="," open="(" close=")">
                #{serviceType}
            </foreach>
        </if>
        <if test="paramDto.flightNo != null and paramDto.flightNo != ''">
            AND  SSR.FLIGHT_NO = #{paramDto.flightNo}
        </if>
        <if test="paramDto.flightStartTime != null and paramDto.flightStartTime != ''">
            AND  SSR.FLIGHT_DATE <![CDATA[ >= ]]> CONCAT(#{paramDto.flightStartTime},' 00:00:00')
        </if>
        <if test="paramDto.flightEndTime != null and paramDto.flightEndTime != ''">
            AND  SSR.FLIGHT_DATE <![CDATA[ <= ]]> CONCAT(#{paramDto.flightEndTime},' 23:59:59')
        </if>
        <if test="paramDto.sendStartTime != null and paramDto.sendStartTime != ''">
            AND  ssr.SEND_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.sendStartTime},' 00:00:00')
        </if>
        <if test="paramDto.sendEndTime != null and paramDto.sendEndTime != ''">
            AND  SSR.SEND_TIME <![CDATA[ <=  ]]> CONCAT(#{paramDto.sendEndTime},' 23:59:59')
        </if>
        <if test="paramDto.pnr != null and paramDto.pnr != ''">
            AND  SSR.PNR_REF  LIKE CONCAT('%',#{paramDto.pnr},'%')
        </if>
        <if test="paramDto.tktNo != null and paramDto.tktNo != ''">
            AND  SSR.TKT_NO  LIKE CONCAT('%',#{paramDto.tktNo},'%')
        </if>
        <if test="paramDto.smsType != null and paramDto.smsType != ''">
            AND  SSR.SMS_TYPE IN
            <foreach collection="paramDto.smsTypeList" separator="," open="(" close=")" item="type">
                #{type}
            </foreach>
        </if>
        <if test="paramDto.sendType != null and paramDto.sendType != ''">
            AND SSR.SEND_TYPE = #{paramDto.sendType}
        </if>
        ORDER BY ssr.SEND_TIME DESC
    </sql>

    <select id="getNotifierSmsRecordPage"
            resultType="com.swcares.psi.base.data.api.vo.SmsNotifierSendRecordPageVo">
        <include refid="getNotifierSmsRecord"/>
    </select>

    <select id="getNotifierSmsRecordPageSum" resultType="com.swcares.psi.base.data.api.vo.SmsNotifierSendRecordVo">
        SELECT
            COUNT(*) AS totalCount
        FROM
        sms_send_record SSR
        LEFT JOIN SMS_TEMPLATE ST ON SSR.SMS_TEMPLATE_ID = ST.ID
        WHERE
        1 = 1
        <if test="paramDto.templateCategory != null and paramDto.templateCategory != ''">
            AND ST.TEMPLATE_CATEGORY = #{paramDto.templateCategory}
        </if>
        <if test="paramDto.reciverName != null and paramDto.reciverName != ''">
            AND  SSR.RECIVER_NAME LIKE CONCAT('%',#{paramDto.reciverName},'%')
        </if>
        <if test="paramDto.mobile != null and paramDto.mobile != ''">
            AND  SSR.MOBILE = #{paramDto.mobile}
        </if>
        <if test="paramDto.sendStatus != null and paramDto.sendStatus != ''">
            AND  SSR.SEND_STATE = #{paramDto.sendStatus}
        </if>
        <if test="paramDto.smsServiceType != null and paramDto.smsServiceType != ''">
            AND  ST.TEMPLATE_BUSINESS_TYPE IN
            <foreach collection="paramDto.smsServiceTypeList" item="serviceType" separator="," open="(" close=")">
                #{serviceType}
            </foreach>
        </if>
        <if test="paramDto.flightNo != null and paramDto.flightNo != ''">
            AND  SSR.FLIGHT_NO = #{paramDto.flightNo}
        </if>
        <if test="paramDto.flightStartTime != null and paramDto.flightStartTime != ''">
            AND  SSR.FLIGHT_DATE <![CDATA[ >= ]]> CONCAT(#{paramDto.flightStartTime},' 00:00:00')
        </if>
        <if test="paramDto.flightEndTime != null and paramDto.flightEndTime != ''">
            AND  SSR.FLIGHT_DATE <![CDATA[ <= ]]> CONCAT(#{paramDto.flightEndTime},' 23:59:59')
        </if>
        <if test="paramDto.sendStartTime != null and paramDto.sendStartTime != ''">
            AND  ssr.SEND_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.sendStartTime},' 00:00:00')
        </if>
        <if test="paramDto.sendEndTime != null and paramDto.sendEndTime != ''">
            AND  SSR.SEND_TIME <![CDATA[ <=  ]]> CONCAT(#{paramDto.sendEndTime},' 23:59:59')
        </if>
        <if test="paramDto.pnr != null and paramDto.pnr != ''">
            AND  SSR.PNR_REF  LIKE CONCAT('%',#{paramDto.pnr},'%')
        </if>
        <if test="paramDto.tktNo != null and paramDto.tktNo != ''">
            AND  SSR.TKT_NO LIKE CONCAT('%',#{paramDto.tktNo},'%')
        </if>
        <if test="paramDto.smsType != null and paramDto.smsType != ''">
            AND  SSR.SMS_TYPE IN
            <foreach collection="paramDto.smsTypeList" separator="," open="(" close=")" item="type">
                #{type}
            </foreach>
        </if>
        ORDER BY ssr.SEND_TIME DESC
    </select>

    <select id="getRecordCount" resultType="java.lang.String">
        select count(total) from
        (SELECT distinct (SUBSTRING_INDEX(SUBSTRING_INDEX(t.reciver_Name  , ',', help_topic_id + 1), ',', -1)) as total
        FROM mysql.help_topic,(SELECT SSR.reciver_Name FROM
        sms_send_record SSR
        LEFT JOIN SMS_TEMPLATE ST ON SSR.SMS_TEMPLATE_ID = ST.ID
        WHERE
        1 = 1
        AND ST.TEMPLATE_CATEGORY = '0'
        <if test="paramDto.reciverName != null and paramDto.reciverName != ''">
            AND  SSR.RECIVER_NAME LIKE CONCAT('%',#{paramDto.reciverName},'%')
        </if>
        <if test="paramDto.mobile != null and paramDto.mobile != ''">
            AND  SSR.MOBILE = #{paramDto.mobile}
        </if>
        <if test="paramDto.sendStatus != null and paramDto.sendStatus != ''">
            AND  SSR.SEND_STATE = #{paramDto.sendStatus}
        </if>
        <if test="paramDto.smsServiceType != null and paramDto.smsServiceType != ''">
            AND  ST.TEMPLATE_BUSINESS_TYPE IN
            <foreach collection="paramDto.smsServiceTypeList" item="serviceType" separator="," open="(" close=")">
                #{serviceType}
            </foreach>
        </if>
        <if test="paramDto.flightNo != null and paramDto.flightNo != ''">
            AND  SSR.FLIGHT_NO = #{paramDto.flightNo}
        </if>
        <if test="paramDto.flightStartTime != null and paramDto.flightStartTime != ''">
            AND  SSR.FLIGHT_DATE <![CDATA[ >= ]]> CONCAT(#{paramDto.flightStartTime},' 00:00:00')
        </if>
        <if test="paramDto.flightEndTime != null and paramDto.flightEndTime != ''">
            AND  SSR.FLIGHT_DATE <![CDATA[ <= ]]> CONCAT(#{paramDto.flightEndTime},' 23:59:59')
        </if>
        <if test="paramDto.sendStartTime != null and paramDto.sendStartTime != ''">
            AND  ssr.SEND_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.sendStartTime},' 00:00:00')
        </if>
        <if test="paramDto.sendEndTime != null and paramDto.sendEndTime != ''">
            AND  SSR.SEND_TIME <![CDATA[ <=  ]]> CONCAT(#{paramDto.sendEndTime},' 23:59:59')
        </if>
        <if test="paramDto.pnr != null and paramDto.pnr != ''">
            AND  SSR.PNR_REF  LIKE CONCAT('%',#{paramDto.pnr},'%')
        </if>
        <if test="paramDto.tktNo != null and paramDto.tktNo != ''">
            AND  SSR.TKT_NO LIKE CONCAT('%',#{paramDto.tktNo},'%')
        </if>
        <if test="paramDto.smsType != null and paramDto.smsType != ''">
            AND  SSR.SMS_TYPE IN
            <foreach collection="paramDto.smsTypeList" separator="," open="(" close=")" item="type">
                #{type}
            </foreach>
        </if>
        ORDER BY ssr.SEND_TIME DESC) t
        where help_topic_id &lt; LENGTH(t.reciver_Name) - LENGTH(REPLACE(t.reciver_Name, ',', ''))+1) a
        where a.total !=''
    </select>

    <select id="exportNotifierSmsRecord"
            resultType="com.swcares.psi.base.data.api.vo.SmsNotifierSendRecordPageVo">
        <include refid="getNotifierSmsRecord"/>
    </select>
</mapper>
