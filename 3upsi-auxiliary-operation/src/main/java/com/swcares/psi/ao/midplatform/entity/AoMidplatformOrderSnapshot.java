package com.swcares.psi.ao.midplatform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 辅营中台订单快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AoMidplatformOrderSnapshot对象", description="辅营中台订单快照表")
public class AoMidplatformOrderSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    private String id;

    @ApiModelProperty(value = "主订单ID")
    @TableField("MAIN_ORDER_ID")
    private String mainOrderId;

    @ApiModelProperty(value = "主订单号")
    @TableField("MAIN_ORDER_NO")
    private String mainOrderNo;

    @ApiModelProperty(value = "订单信息")
    @TableField("ORDER_INFO")
    private String orderInfo;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;


}
