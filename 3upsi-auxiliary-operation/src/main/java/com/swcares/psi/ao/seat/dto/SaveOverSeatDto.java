package com.swcares.psi.ao.seat.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.seat.dto.SaveOverSeatDto <br>
 * Description：(用一句话描述这个类或者接口表示什么)<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022-3-9 13:57<br>
 * @version v1.0 <br>
 */
@Data
public class SaveOverSeatDto {
    @ApiModelProperty("主键Id")
    private Long sgsId;

    @ApiModelProperty("记录编号")
    private String recordNo;

    @ApiModelProperty("人数")
    private String planNum;

    @ApiModelProperty("实控人数")
    private String actualNum;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("ID编号")
    private Long applyId;

    @ApiModelProperty("申报编号")
    private String applyNo;

    @ApiModelProperty("原因")
    private String remark;
}

