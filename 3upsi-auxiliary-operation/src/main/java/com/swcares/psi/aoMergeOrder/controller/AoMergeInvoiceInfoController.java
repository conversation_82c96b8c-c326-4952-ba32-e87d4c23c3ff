package com.swcares.psi.aoMergeOrder.controller;


import com.swcares.psi.ao.invoice.vo.AoInvoiceDetailVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSelfInvoiceCacheDto;
import com.swcares.psi.aoMergeOrder.dto.InvoiceInfoMergeSaveDto;
import com.swcares.psi.aoMergeOrder.dto.MergeInvoiceInfoDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeInvoiceInfoService;
import com.swcares.psi.aoMergeOrder.service.impl.AoCommonService;
import com.swcares.psi.aoMergeOrder.service.impl.TempTransitionService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceDetailVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceInfoVo;
import com.swcares.psi.aoMergeOrder.vo.InvoiceWarnOrderPaxInfoVo;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合并支付开发票记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@Api(tags = "合并支付开发票")
@RequestMapping("/api/ao/merge/invoice")
public class AoMergeInvoiceInfoController {

    @Autowired
    AoCommonService aoCommonService;
    @Autowired
    IAoMergeInvoiceInfoService aoMergeInvoiceInfoService;
    @Autowired
    private Redisson redisson;

    //todo-houchuan 临时过渡
    @Autowired
    TempTransitionService tempTransitionService;

    @ApiOperation(value = "合并发票列表")
    @GetMapping("/getInvoiceInfoPage")
    public RenderResult<PsiPage<AoMergeInvoiceInfoVo>> getInvoiceInfoPage(MergeInvoiceInfoDto dto){
        PsiPage<AoMergeInvoiceInfoVo> invoiceInfoPage = aoMergeInvoiceInfoService.getInvoiceInfoPage(dto);
       return RenderResult.success(invoiceInfoPage);
    }


    @ApiOperation(value = "发票导出")
    @GetMapping("export")
    public void export(MergeInvoiceInfoDto dto, HttpServletResponse response, HttpServletRequest request) throws Exception{
        response.setContentType("application/csv");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("合并发票导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeInvoiceInfoVo> list = aoMergeInvoiceInfoService.getInvoiceInfoList(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), list, AoMergeInvoiceInfoVo.class, "", null);

    }

    @ApiOperation(value = "通过发票Id获取发票详情信息")
    @GetMapping("getInvoiceDetailInfo")
    public RenderResult<AoMergeInvoiceDetailVo> getInvoiceInfo(String mergeInvoiceId) {
        return RenderResult.success(aoMergeInvoiceInfoService.invoiceDetail( mergeInvoiceId));
    }

    @ApiOperation(value = "发票开具提示")
    @PostMapping("/invoiceWarnSms")
    public RenderResult invoiceWarnSms(@RequestBody List<InvoiceWarnOrderPaxInfoVo> invoiceSmsSendDtos) {
        aoCommonService.invoiceSms(invoiceSmsSendDtos);
        return RenderResult.success();
    }
    @ApiOperation(value = "可开票的子订单信息")
    @PostMapping("/getOrderPaxPhone")
    public RenderResult<List<InvoiceWarnOrderPaxInfoVo>> getOrderPaxPhone(String mergeOrderNo) {
        List<InvoiceWarnOrderPaxInfoVo> invoiceOrderInfo = aoCommonService.getInvoiceOrderInfo(mergeOrderNo);
        return RenderResult.success(invoiceOrderInfo);
    }

    @ApiOperation(value = "发票开具")
    @PostMapping("/save")
    public RenderResult save(@RequestBody List<InvoiceInfoMergeSaveDto> invoiceDto) {
        RLock lock = redisson.getLock("saveInvoice_v2_lock");
        try {
            while (!lock.tryLock()) {
                try {
                    Thread.sleep(200L);
                } catch (Exception e) {

                }
            }

            //todo-houchuan 临时过渡
//            tempTransitionService.saveInvoice(invoiceDto);
            aoMergeInvoiceInfoService.saveInvoice(invoiceDto);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return RenderResult.success();
    }


    @ApiOperation(value = "旅客C端发票开具")
    @PostMapping("/saveByC")
    public RenderResult saveByC(@RequestBody List<InvoiceInfoMergeSaveDto> invoiceDto) {
        RLock lock = redisson.getLock("saveInvoice_v2_lock");
        try {
            while (!lock.tryLock()) {
                try {
                    Thread.sleep(200L);
                } catch (Exception e) {

                }
            }

            //todo-houchuan 临时过渡
//            tempTransitionService.saveInvoiceByC(invoiceDto);
            aoMergeInvoiceInfoService.saveInvoiceByC(invoiceDto);
        }catch (BusinessException e){
            if(e.getCode().equals(MessageCode.ORDER_INVOICE_YES.getCode())){
                throw new BusinessException(MessageCode.ORDER_INVOICE_YES.getCode());
            }else{
                throw e;
            }
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return RenderResult.success();
    }



    @ApiOperation(value = "生成自助开票签名")
    @PostMapping("/getInvoiceSign")
    public RenderResult<String> getInvoiceSign(String mergeOrderNo) {
        String invoiceSign = aoMergeInvoiceInfoService.getInvoiceSign(mergeOrderNo);
        return RenderResult.success(invoiceSign);
    }

    @ApiOperation(value = "获取自助开票参数")
    @GetMapping("/getInvoiceParam")
    public RenderResult<AoMergeSelfInvoiceCacheDto> getInvoiceParam(String signId) {
        return RenderResult.success(aoMergeInvoiceInfoService.getInvoiceParam(signId));
    }

}

