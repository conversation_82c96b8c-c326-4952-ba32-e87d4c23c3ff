package com.swcares.psi.aoMergeOrder.mapper;

import com.swcares.psi.ao.counterUpgrade.vo.UpgradeStatusUpDateVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountQueryDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderDiscountList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付订单折扣清单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeOrderDiscountListMapper extends BaseMapper<AoMergeOrderDiscountList> {
    PsiPage<AoMergeOrderDiscountListVo> getPage(PsiPage<AoMergeOrderDiscountListVo> page, @Param("dto") AoMergeOrderDiscountQueryDto dto);
    List<AoMergeOrderDiscountListVo> getList( @Param("dto") AoMergeOrderDiscountQueryDto dto);

}
