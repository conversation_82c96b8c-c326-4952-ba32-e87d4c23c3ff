<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.pkgManage.mapper.PkgManageMapper">

    <sql id="tableName">
        DP_PKG_MANAGE_INFO
    </sql>

    <update id="updatePkg">
        UPDATE
        <include refid="tableName"/>
        <set>
            <if test="pkgManageInfo.pkgName != null and pkgManageInfo.pkgName != ''">
                PKG_NAME = #{pkgManageInfo.pkgName},
            </if>
            <if test="pkgManageInfo.pkgSize != null and pkgManageInfo.pkgSize != ''">
                PKG_SIZE = #{pkgManageInfo.pkgSize},
            </if>
            <if test="pkgManageInfo.pkgPrice != null and pkgManageInfo.pkgPrice != ''">
                PKG_PRICE = #{pkgManageInfo.pkgPrice},
            </if>
            <if test="pkgManageInfo.pkgCount != null and pkgManageInfo.pkgCount != ''">
                PKG_COUNT = #{pkgManageInfo.pkgCount},
            </if>
            <if test="pkgManageInfo.updateUser != null and pkgManageInfo.updateUser != ''">
                UPDATE_USER = #{pkgManageInfo.updateUser},
            </if>
            <if test="pkgManageInfo.updateTime != null ">
                UPDATE_TIME = date_format(#{pkgManageInfo.updateTime}, '%Y-%m-%d %T'),
            </if>
            <if test="pkgManageInfo.del != null and pkgManageInfo.del != ''">
                IS_DELETE = #{pkgManageInfo.del},
            </if>
            <if test="pkgManageInfo.serviceCity != null and pkgManageInfo.serviceCity != ''">
                SERVICE_CITY = #{pkgManageInfo.serviceCity},
            </if>
            <if test="pkgManageInfo.pkgNo != null and pkgManageInfo.pkgNo != ''">
                PKG_NO = #{pkgManageInfo.pkgNo},
            </if>
        </set>
        WHERE
            IS_DELETE = 0
        AND
            ID = #{pkgManageInfo.id}
    </update>
    <update id="updateCount">
        UPDATE
        <include refid="tableName"/>
        <set>
            <if test="pkgManageInfo.pkgCount != null and pkgManageInfo.pkgCount != ''">
                PKG_COUNT = PKG_COUNT + #{pkgManageInfo.pkgCount},
            </if>
            <if test="pkgManageInfo.updateUser != null and pkgManageInfo.updateUser != ''">
                UPDATE_USER = #{pkgManageInfo.updateUser},
            </if>
            <if test="pkgManageInfo.updateTime != null ">
                UPDATE_TIME = date_format(#{pkgManageInfo.updateTime}, '%Y-%m-%d %T'),
            </if>
        </set>
        WHERE
            IS_DELETE = 0
        AND
            ID = #{pkgManageInfo.id}
    </update>

    <select id="listPkg" resultType="com.swcares.psi.pkgManage.vo.PkgManageSearchVo">
     SELECT
        DP_PKG_MANAGE_INFO.ID,
        DP_PKG_MANAGE_INFO.PKG_NO,
        DP_PKG_MANAGE_INFO.PKG_NAME,
        DP_PKG_MANAGE_INFO.PKG_SIZE,
        DP_PKG_MANAGE_INFO.PKG_PRICE,
        DP_PKG_MANAGE_INFO.SERVICE_CITY,
        DP_PKG_MANAGE_INFO.PKG_COUNT,
        IFNULL((SELECT SUM(PKG_COUNT) FROM DP_ORDER_INFO
        WHERE DP_ORDER_INFO.PKG_ID = DP_PKG_MANAGE_INFO.ID
            AND DP_ORDER_INFO.`STATUS` = '10'
        ),0)AS PAY_COUNT,
        DP_PKG_MANAGE_INFO.PKG_COUNT -
        IFNULL((SELECT SUM(PKG_COUNT) FROM DP_ORDER_INFO
        WHERE DP_ORDER_INFO.PKG_ID = DP_PKG_MANAGE_INFO.ID
        AND DP_ORDER_INFO.`STATUS` = '10'
        ),0)AS STOCK_COUNT,
        get_user_name(DP_PKG_MANAGE_INFO.CREATE_USER) AS createUser,
        DP_PKG_MANAGE_INFO.CREATE_TIME,
        get_user_name(DP_PKG_MANAGE_INFO.UPDATE_USER) AS updateUser,
        IFNULL(DP_PKG_MANAGE_INFO.UPDATE_TIME,DP_PKG_MANAGE_INFO.CREATE_TIME) AS updateTime
    FROM
        DP_PKG_MANAGE_INFO
    WHERE DP_PKG_MANAGE_INFO.IS_DELETE = 0
        <if test="pkgManageSearchDto.pkgNo != null and pkgManageSearchDto.pkgNo != ''">
            AND DP_PKG_MANAGE_INFO.PKG_NO = #{pkgManageSearchDto.pkgNo}
        </if>
        <if test="pkgManageSearchDto.pkgName != null and pkgManageSearchDto.pkgName != ''">
            AND DP_PKG_MANAGE_INFO.PKG_NAME LIKE CONCAT('%',#{pkgManageSearchDto.pkgName},'%')
        </if>
        <if test="pkgManageSearchDto.pkgSize != null and pkgManageSearchDto.pkgSize != ''">
            AND DP_PKG_MANAGE_INFO.PKG_SIZE = #{pkgManageSearchDto.pkgSize}
        </if>
        <if test="pkgManageSearchDto.pkgPriceLow != null and pkgManageSearchDto.pkgPriceLow != ''">
            AND DP_PKG_MANAGE_INFO.PKG_PRICE <![CDATA[ >= ]]> #{pkgManageSearchDto.pkgPriceLow} + 0
        </if>
        <if test="pkgManageSearchDto.pkgPriceHigh != null and pkgManageSearchDto.pkgPriceHigh != ''">
            AND DP_PKG_MANAGE_INFO.PKG_PRICE <![CDATA[ <= ]]> #{pkgManageSearchDto.pkgPriceHigh} + 0
        </if>
        <if test="pkgManageSearchDto.pkgStation != null and pkgManageSearchDto.pkgStation != ''">
            AND DP_PKG_MANAGE_INFO.SERVICE_CITY = #{pkgManageSearchDto.pkgStation}
        </if>
    GROUP BY
        DP_PKG_MANAGE_INFO.PKG_NO
    </select>
    <select id="getByPkgNo" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            <include refid="tableName"/>
        WHERE
            IS_DELETE = 0
        AND
            PKG_NO = #{pkgNo}
        <if test="id != null and id != ''">
           AND  ID <![CDATA[ <> ]]> #{id}
        </if>
    </select>
    <select id="getMonthPay" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(DOI.PKG_COUNT),0)
        FROM
            DP_ORDER_INFO DOI
            LEFT JOIN DP_PKG_MANAGE_INFO DMI ON DOI.PKG_ID = DMI.ID
        WHERE
            DOI.PKG_ID IS NOT NULL
            AND DOI.STATUS = '10'
            AND DMI.SERVICE_CITY = #{serviceCity}
            AND DOI.GRANT_TIME <![CDATA[ >= ]]> #{startTime}
            AND DOI.GRANT_TIME <![CDATA[ <= ]]> #{endTime}
            AND DMI.IS_DELETE = '0'
    </select>
    <select id="getPayCount" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(DOI.PKG_COUNT),0)
        FROM
            DP_ORDER_INFO DOI
        LEFT JOIN
            DP_PKG_MANAGE_INFO DMI ON DOI.PKG_ID = DMI.ID
        WHERE
            DOI.PKG_ID IS NOT NULL
        AND
            DOI.STATUS = '10'
        AND
            DMI.SERVICE_CITY = #{serviceCity}
        <if test="startTime != null and startTime != ''">
        AND
            DOI.GRANT_TIME <![CDATA[ >= ]]>  CONCAT(#{startTime}," 00:00:00")
        </if>
        <if test="endTime != null and endTime != ''">
        AND
            DOI.GRANT_TIME <![CDATA[ <= ]]>  CONCAT(#{endTime}," 23:59:59")
        </if>
        AND
            DMI.IS_DELETE = '0'
    </select>
    <select id="getTotalCount" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(PKG_COUNT),0)
        FROM
            DP_PKG_MANAGE_INFO
        WHERE
            SERVICE_CITY = #{serviceCity}
        AND
            IS_DELETE = '0'
    </select>
</mapper>
