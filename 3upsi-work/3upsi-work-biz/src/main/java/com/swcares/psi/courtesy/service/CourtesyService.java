package com.swcares.psi.courtesy.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.common.retrievephone.PaxerPhoneUtil;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.courtesy.entity.CourtesyScheduleRemarkEntity;
import com.swcares.psi.courtesy.dto.CourtesyItineraryDto;
import com.swcares.psi.courtesy.mapper.CourtesyMapper;
import com.swcares.psi.courtesy.vo.CourtesyItineraryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class CourtesyService extends ServiceImpl<CourtesyMapper, CourtesyScheduleRemarkEntity> {

    @Autowired
    CourtesyMapper courtesyMapper;

    @Autowired
    PaxerPhoneUtil paxerPhoneUtil;
    @EncryptMethod
    public IPage<CourtesyItineraryVo> getScheduleList(CourtesyItineraryDto courtesyItineraryDto) {
        PsiPage<CourtesyItineraryVo> psiPage = new PsiPage<>(courtesyItineraryDto);
        psiPage.setOptimizeCountSql(false);
        IPage<CourtesyItineraryVo> scheduleList = courtesyMapper.getScheduleList(courtesyItineraryDto, psiPage);
        Optional.ofNullable(scheduleList.getRecords()).ifPresent(records ->records.forEach(r->{
            if (StrUtil.isBlank(r.getConcatInfo())){
                Map<String, String> paxersPhones = paxerPhoneUtil.getPaxersPhones(Collections.singletonList(r.getPassengerId()));
                r.setConcatInfo(paxersPhones.get(r.getPassengerId()));
            }
        }));
        return scheduleList;
    }

    @EncryptMethod
    public List<CourtesyItineraryVo> export(CourtesyItineraryDto courtesyItineraryDto) {
        List<CourtesyItineraryVo> scheduleList = courtesyMapper.getScheduleList(courtesyItineraryDto);
        Optional.ofNullable(scheduleList).ifPresent(result -> result.forEach(r -> {
            if (StrUtil.isBlank(r.getConcatInfo())) {
                Map<String, String> paxersPhones = paxerPhoneUtil.getPaxersPhones(Collections.singletonList(r.getPassengerId()));
                r.setConcatInfo(paxersPhones.get(r.getPassengerId()));
            }
        }));
        return scheduleList;
    }
}
