package com.swcares.psi.aoMergeOrder.controller;


import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeDiscountListSaveDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeQueryDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCardTypeDiscountListService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 合并支付卡级别折扣清单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Api(tags = "合并支付卡级别折扣")
@RestController
@Slf4j
@RequestMapping("/api/ao/card/discount")
public class AoMergeCardTypeDiscountListController {

    @Autowired
    private IAoMergeCardTypeDiscountListService aoMergeCardTypeDiscountListService;

    @GetMapping("/config/page")
    @ApiOperation(value = "列表查询")
    public RenderResult<PsiPage<AoMergeCardTypegetListVo>> page(AoMergeCardTypeQueryDto dto ) {
        PsiPage<AoMergeCardTypegetListVo> page = aoMergeCardTypeDiscountListService.getPage(dto);
        return RenderResult.success(page);
    }


    @PostMapping("/config/save")
    @ApiOperation(value = "保存")
    public RenderResult save(@RequestBody AoMergeCardTypeDiscountListSaveDto dto ) {
        aoMergeCardTypeDiscountListService.saveOrUpdate(dto);
        return RenderResult.success();
    }

    @PostMapping("/config/delete")
    @ApiOperation(value = "删除")
    public RenderResult delete(String id ) {
        aoMergeCardTypeDiscountListService.deleteConfig(id);
        return RenderResult.success();
    }
}

