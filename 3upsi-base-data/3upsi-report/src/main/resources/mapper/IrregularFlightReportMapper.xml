<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.report.compensate.irregularflight.mapper.IrregularFlightReportMapper">

    <sql id="whereSql">
        and datr.PAY_STATUS = '1'
        <if test="dto.compensationType != null and dto.compensationType != ''">
            and podr.APPLY_TYPE=#{dto.compensationType}
        </if>
        <if test="dto.payType != null and dto.payType.size() > 0">
            and datr.PAY_TYPE in
            <foreach collection="dto.payType" item="ele" open="(" close=")" separator=",">
                #{ele}
            </foreach>
        </if>
        <if test="dto.applyCode != null and dto.applyCode != ''">
            and datr.APPLY_CODE=#{dto.applyCode}
        </if>
        <if test="dto.payStartTime != null and dto.payStartTime != '' and dto.payEndTime != null and dto.payEndTime != ''">
            and podr.PAY_DATE between DATE_FORMAT(#{dto.payStartTime},'%Y-%m-%d 00:00:00') and DATE_FORMAT(#{dto.payEndTime},'%Y-%m-%d 23:59:59')
        </if>
    </sql>
    
    <select id="payTypesSummaryMoney"
            parameterType="com.swcares.psi.report.compensate.irregularflight.dto.PayTypesSummaryMoneyDto"
            resultType="com.swcares.psi.report.compensate.irregularflight.vo.PayTypesSummaryMoneyVo">
        SELECT
        sum(CASE WHEN aa.payType = '微信' THEN aa.transAmount ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN aa.payType = '银联' THEN aa.transAmount ELSE 0 end ) AS unionpayMoney,
        sum(CASE WHEN aa.payType = '支付宝' THEN aa.transAmount ELSE 0 end ) AS alipayMoney,
        sum(CASE WHEN aa.payType = '优惠券' THEN aa.transAmount ELSE 0 end) AS classPayMoney,
        sum(CASE WHEN aa.payType = '现金' THEN aa.transAmount ELSE 0 end) AS cashMoney,
        sum(aa.transAmount  ) AS allMoney
        from(
        <include refid="payRecordSql"></include>
        ) aa


    </select>

    <sql id="payRecordSql">
        SELECT
            datr.id                                AS id,
            DATE_FORMAT(podr.PAY_DATE, '%Y-%m-%d') AS payDate,
            DATE_FORMAT(podr.PAY_DATE, '%T')       AS payTime,
            datr.APPLY_CODE                               AS applyCode,

        CASE
        WHEN datr.PAY_TYPE = '3' THEN(

        select dict.DATA_VALUE from dp_pax_info psx
        left join dp_order_info ord on  psx.ORDER_ID= ord.ORDER_ID
        left join sys_dict dict on dict.DATA_CODE=ord.ACTIVITY_ID and dict.DATA_TYPE_CODE='dp_coupons_activity'
        where psx.APPLY_CODE=datr.APPLY_CODE
        )
        ELSE datr.TRANS_AMOUNT END     AS transAmount,



            CASE
            WHEN datr.PAY_TYPE = '0' THEN '微信'
            WHEN datr.PAY_TYPE = '1' THEN '银联'
            WHEN datr.PAY_TYPE = '2' THEN '支付宝'
            WHEN datr.PAY_TYPE = '3' THEN '优惠券'
            WHEN datr.PAY_TYPE = '4' THEN '现金'
            ELSE '易宝' END                              AS payType,
            datr.APPLY_ACCOUNT                            AS applyAccount,
            datr.RETURN_ORDER_NO                          AS returnOrderNo,
            CASE
            WHEN datr.PAY_STATUS = '0' THEN '未支付'
            WHEN datr.PAY_STATUS = '1' THEN '已支付'
            ELSE '支付失败' END                          AS payStatus,
            case podr.GET_MONEY_WAY when '1' then podr.APPLY_USER else '-' end AS accountName,
            podr.OPEN_BANK_NAME                           AS depositBank,
            datr.MER_Id as merId
        FROM
            dp_ao_trans_record datr
        left join dp_apply_order podr on podr.APPLY_CODE = datr.APPLY_CODE
        <where>
            1=1
            <include refid="whereSql"></include>
        </where>
    </sql>

    <select id="payRecordInfo"
            parameterType="com.swcares.psi.report.compensate.irregularflight.dto.PayTypesSummaryMoneyDto"
            resultType="com.swcares.psi.report.compensate.irregularflight.vo.AoTransRecordVo"
    >
        <include refid="payRecordSql"></include>
    </select>
    <select id="payRecordInfoPage"
            parameterType="com.swcares.psi.report.compensate.irregularflight.dto.PayTypesSummaryMoneyDto"
            resultType="com.swcares.psi.report.compensate.irregularflight.vo.AoTransRecordVo"
    >
        <include refid="payRecordSql"></include>
    </select>

    <sql id="compensateLuggageReport">
        union
        select distinct
                        ''                                                                as paxMainKey,
                        dpPax.PAX_NAME                                                    as paxName,
                        dpPax.TKT_NO                                                      as tktNo,
                        '-'                                                               as orderId,
                        DATE_FORMAT(dfi.FLIGHT_DATE, '%Y-%m-%d')                         as flightDate,
                        dfi.FLIGHT_NO                                                as flightNo,
                        dfi.ORG                                                          as org,
                        dfi.DST                                                          as dst,
                        get_city_name(dfi.ORG)                                           as orgName,
                        get_city_name(dfi.DST)                                           as dstName,
                        get_city_name(dpi.SERVICE_CITY)                                   as serviceCity,
                        dfi.AC_TYPE                                                 as flightModel,
                        (case dfi.FLIGHT_TYPE when 'D' then '国内' when 'I' then '国际' when 'R' then '地区' end) as flightType,
                        '-'                                                                as delayTime,
                        dpi.remark                                                         as innerReason,
                        '箱包补偿'                                                             as luggageCompensationType,
                        dpi.ID                                                             as id,
                        dpi.ACCIDENT_ID                                                    as accidentId,
                        ''                                                                 as currentAmount,
                        '-'                                                                as isFlag,
                        ''                                                                 as payAmount,
                        ''                                                                 as difference,
                        '-'                                                                as receiveType,
                        '-'                                                                as financialStatus,
                        '-'                                                                as applyCode,
                        '-'                                                                as payStatus,
                        '-'                                                                as scene,
                        '-'                                                                as payType,
                        '-'                                                                as accountName,
                        '-'                                                                as bankName,
                        '-'                                                                as moneyAccount,
                        '-'                                                                as applyStatus,
                        '-'                                                                as payDate,
                        '-'                                                                as payTime,
                        '-'                                                                as returnOrderNo,
                        '-'                                                                as expiryDate,
                        '-'                                                                as auditInfo,
                        '-'                                                                as trans_amount
                    from dp_pkg_info dpi
                             left join dp_pax_info dpPax on dpi.PAX_ID = dpPax.PAX_ID
                             left join dp_flight_info dfi on dpi.FLIGHT_ID = dfi.FLIGHT_ID and dpi.ACCIDENT_ID =dfi.ACCIDENT_ID
                    where 1 = 1
                      and dpi.STATUS != '2' and dpi.ACCIDENT_TYPE = '5'
                    <if test="dto.beginDate != null and dto.beginDate != ''">
                        and dfi.FLIGHT_DATE <![CDATA[>=]]> #{dto.beginDate}
                    </if>
                    <if test="dto.endDate != null and dto.endDate!= '' ">
                        and dfi.FLIGHT_DATE <![CDATA[<=]]> #{dto.endDate}
                    </if>
                    <if test="dto.paxName != null and dto.paxName != ''">
                        and dpPax.PAX_NAME like concat('%',#{dto.paxName},'%')
                    </if>
                    <if test="dto.tktNo != null and dto.tktNo != ''">
                        and dpPax.TKT_NO =#{dto.tktNo}
                    </if>
                    <if test="dto.flightNo != null and dto.flightNo != ''">
                        and dfi.FLIGHT_NO =#{dto.flightNo}
                    </if>
                    <if test="dto.org != null and dto.org !='' ">
                        and dfi.ORG =#{dto.org}
                    </if>
                    <if test="dto.dst != null and dto.dst !='' ">
                        and dfi.DST =#{dto.dst}
                    </if>
                    <if test="dto.serviceCity != null and dto.serviceCity !='' ">
                        and dpi.SERVICE_CITY = (select AIRPORT_NAME from sys_airport_info where CODE = #{dto.serviceCity})
                    </if>
                    <if test="dto.flightModel != null and dto.flightModel.size() > 0">
                        and dfi.AC_TYPE in
                        <foreach collection="dto.flightModel" item="ele" open="(" close=")" separator=",">
                            #{ele}
                        </foreach>
                    </if>
                    <if test="dto.flightType != null and dto.flightType.size() > 0">
                        and dfi.FLIGHT_TYPE in
                        <foreach collection="dto.flightType" item="ele" open="(" close=")" separator=",">
                            #{ele}
                        </foreach>
                    </if>
                    <if test="dto.isFlag != null and dto.isFlag.size() > 0">
                        and dpPax.IS_FLAG in
                        <foreach collection="dto.isFlag" item="ele" open="(" close=")" separator=",">
                            #{ele}
                        </foreach>
                    </if>
    </sql>

    <sql id="costReport">
    select *from(
     SELECT
        pax.id as paxMainKey,
        pax.PAX_NAME AS paxName,
        pax.TKT_NO AS tktNo,
        odr.ORDER_ID AS orderId,
        odr.ORDER_EXPLAIN AS orderExplain,
        DATE_FORMAT(
        DFI.FLIGHT_DATE,
        '%Y-%m-%d'
        ) AS flightDate,
        DFI.FLIGHT_NO AS flightNo,
        pax.ORG_CITY_AIRP AS org,
        pax.DST_CITY_AIRP AS dst,
        get_city_name(pax.ORG_CITY_AIRP) AS orgName,
        get_city_name(pax.DST_CITY_AIRP) AS dstName,
        odr.SERVICE_CITY AS serviceCity,
        DFI.AC_TYPE AS flightModel,
        CASE
        WHEN DFI.FLIGHT_TYPE = 'D' THEN
        '国内'
        WHEN DFI.FLIGHT_TYPE = 'I' THEN
        '国际'
        WHEN DFI.FLIGHT_TYPE = 'R' THEN
        '地区'
        END AS flightType,
        <choose>
            <when test="dto.compensationType == '0'.toString()">

                ifnull( CONCAT(TIMESTAMPDIFF(HOUR, ifnull(refli.STD,0), ifnull(refli.ETD,0)),
                '小时',
                TIMESTAMPDIFF(MINUTE, ifnull(refli.STD,0), ifnull(refli.ETD,0)) % 60,
                '分') ,'0小时0分') AS  delayTime,

                refli.INNER_REASON  AS innerReason,
            </when>
            <when test="dto.compensationType == '1'.toString()">
                '-' as delayTime,
                odr.REMARK  as innerReason,
                (case dpi.ACCIDENT_TYPE
                when '1' then '破损补偿'
                when '2' then '少收补偿'
                when '4' then '内件缺失补偿' end)          as luggageCompensationType,
                ''                                    as id,
                dpi.ACCIDENT_ID                       as accidentId,
            </when>
            <when test="dto.compensationType == '2'.toString()">
                '-' as delayTime,
                (case ovi.TYPE
                when '0' then
<!--                concat('改签延误时差', round(-->
<!--                (UNIX_TIMESTAMP(str_to_date(concat(ovi.FLIGHT_DATE, ' ', ovi.PLAN_DATE), '%Y-%m-%d %H:%i:%s')) - -->
<!--                UNIX_TIMESTAMP(refli.STD)) / 3600, 0),-->
<!--                '小时，原票价',ovi.PRICE,'元，补偿金额', cast(odr.SUM_MONEY as signed), '元',-->
<!--                (case ovi.PAY_WAY when '3' then '（特殊）' else '' end))-->
                concat( ovi.FLIGHT_DATE,
                '####',
                ovi.PLAN_DATE,
                '####',
                F.FLIGHT_DATE,
                '####',
                ovi.STANDARD_PRICE,
                '####',
                ovi.SPECIAL_PRICE,
                '####',
                ovi.FIXED_PRICE,
                '####',
                ovi.PERCENT_PRICE,
                '####',
                ovi.type,
                '####',
                ovi.PRICE,
                '####',
                ovi.PAY_WAY,
                '####',
                F.STD)





                when '1' then concat('退票，补偿金额', cast(odr.SUM_MONEY as signed), '元',
                (case ovi.PAY_WAY when '3' then '（特殊）' else '' end)) end) as innerReason,
            </when>
        </choose>
        pax.CURRENT_AMOUNT AS currentAmount,
        CASE
        WHEN pax.IS_FLAG = '1' THEN
        '标记'
        ELSE
        '未标记'
        END AS isFlag,
        CASE  WHEN appodr.status = '1' THEN
            CASE WHEN (appodr.GET_MONEY_WAY <![CDATA[!=]]> '3' and  pax.PAX_ID=appodr.PAX_ID and pax.ORDER_ID=appodr.ORDER_ID) then appodr.TRANS_AMOUNT else 0 END
        ELSE
            0
        END AS payAmount,
        CASE
        WHEN (appodr.GET_MONEY_WAY = '3' )
                    then (
                            select dict.DATA_VALUE from dp_pax_info psx
                            left join dp_order_info ord on  psx.ORDER_ID= ord.ORDER_ID
                            left join sys_dict dict on dict.DATA_CODE=ord.ACTIVITY_ID and dict.DATA_TYPE_CODE='dp_coupons_activity'
                            where psx.APPLY_CODE=appodr.apply_code
                             )
        else 0 END AS classPayAmount,
        ifnull((select DATA_VALUE from sys_dict where DATA_CODE=odr.ACTIVITY_ID limit 0,1),0) AS classOrderPayAmount,
        (
        CASE WHEN appodr.status = '1' THEN
            CASE WHEN (appodr.GET_MONEY_WAY = '3' ) then 0
                else CASE WHEN (pax.PAX_ID=appodr.PAX_ID and pax.ORDER_ID=appodr.ORDER_ID) then appodr.TRANS_AMOUNT else 0 END
            end
        ELSE
            0
        END
        ) - pax.CURRENT_AMOUNT AS difference,
        CASE
            WHEN pax. RECEIVE_STATUS = '1' THEN
            '已领取'
            WHEN pax. RECEIVE_STATUS = '2' THEN
            '领取中'
            WHEN pax. RECEIVE_STATUS = '3' THEN
            '领取失败'
            ELSE
            '未领取'
        END AS receiveType,
        NULL AS financialStatus,
        appodr.APPLY_CODE AS applyCode,
        CASE
        WHEN appodr.PAY_STATUS = '0' THEN
            '未支付'
        WHEN appodr.PAY_STATUS = '1' THEN
             '已支付'
        WHEN appodr.PAY_STATUS = '2' THEN
            '支付失败'
        WHEN appodr.PAY_STATUS = '3' THEN
            '处理中'
        ELSE
        NULL
        END AS payStatus,
        CASE
        WHEN appodr.IS_SCENE = '1' THEN
        '现场'
        WHEN appodr.IS_SCENE = '0' THEN
        '事后'
        ELSE
        NULL
        END AS scene,
        CASE
        WHEN appodr.GET_MONEY_WAY = '0' THEN
        '微信'
        WHEN appodr.GET_MONEY_WAY = '1' THEN
        '银联'
        WHEN appodr.GET_MONEY_WAY = '2' THEN
        '支付宝'
        WHEN appodr.GET_MONEY_WAY = '3' THEN
        '优惠券'
        WHEN appodr.GET_MONEY_WAY = '4' THEN
        '现金'
        WHEN appodr.GET_MONEY_WAY = '5' THEN
        '易宝'
        ELSE
        NULL
        END AS payType,
        case appodr.GET_MONEY_WAY when '1' then appodr.APPLY_USER else '-' end AS accountName,
        appodr.OPEN_BANK_NAME AS bankName,
        appodr.GET_MONEY_ACCOUNT AS moneyAccount,
        case (case when (date_add(appodr.AUDIT_TIME, interval con.content MINUTE) >= now() and appodr.IS_SCENE='0' 
            and appodr.APPLY_WAY !='0' and appodr.GET_MONEY_WAY != '4' and appodr.quick_pay != '1') then '0' else appodr.APPLY_STATUS end)
        when '0' then '待审核' when '1' then '通过' when '2' then '未通过' end  AS applyStatus,
        DATE_FORMAT(appodr.PAY_DATE, '%Y-%m-%d') AS payDate,
        DATE_FORMAT(appodr.PAY_DATE, '%H:%i:%s') AS payTime,
        appodr.RETURN_ORDER_NO AS returnOrderNo,
        DATE_FORMAT(date_add(DFI.FLIGHT_DATE, interval 365 day ), '%Y-%m-%d') AS expiryDate,
        '-' AS auditInfo,
        case when (pax.ORDER_ID = appodr.ORDER_ID and pax.PAX_ID = appodr.PAX_ID and
            pax.APPLY_CODE = appodr.APPLY_CODE) then appodr.TRANS_AMOUNT
            else null end as trans_amount,
        case odr.ADJUST_TYPE when 'T' then '临时性调整' when 'P' then '计划性调整' else '' end as adjustType
        FROM
        dp_pax_info pax
        LEFT JOIN dp_order_info odr ON odr.ORDER_ID = pax.ORDER_ID
        LEFT JOIN flt_flight_real_info refli ON refli.id = odr.FLIGHT_ID
        LEFT JOIN dp_apply_order appodr ON appodr.APPLY_CODE=pax.APPLY_CODE
        LEFT JOIN (select distinct CONTENT as content from dp_config_info where type = '0' and status = '0') con
        on 1 = 1 AND odr.order_id = pax.order_id
        LEFT JOIN DP_FLIGHT_INFO DFI ON DFI.ORDER_ID=odr.ORDER_ID
        <choose>
            <when test="dto.compensationType == '2'.toString()">
                left join dp_over_info ovi on odr.ORDER_ID = ovi.ORDER_ID
                LEFT JOIN DP_FLIGHT_INFO F ON F.ORDER_ID=odr.ORDER_ID
            </when>
            <when test="dto.compensationType == '1'.toString()">
                left join dp_pkg_info dpi on dpi.ACCIDENT_ID = odr.ACCIDENT_ID and dpi.ACCIDENT_TYPE != '5'
            </when>
        </choose>

        <where>
            1=1
            and (odr.STATUS = '3' or odr.STATUS = '4' or odr.STATUS = '8')
            <if test="dto.compensationType != null and dto.compensationType != ''">
                and odr.PAY_TYPE = #{dto.compensationType}
            </if>
            <if test="dto.adjustType != null and dto.adjustType != ''">
                and odr.ADJUST_TYPE = #{dto.adjustType}
            </if>
            <if test="dto.beginDate != null and dto.beginDate != ''">
                and DFI.FLIGHT_DATE <![CDATA[>=]]> #{dto.beginDate}
            </if>
            <if test="dto.endDate != null and dto.endDate!= '' ">
                and DFI.FLIGHT_DATE <![CDATA[<=]]> #{dto.endDate}
            </if>
            <if test="dto.payBeginDate != null and dto.payBeginDate != ''">
                and appodr.PAY_DATE <![CDATA[>=]]> DATE_FORMAT(#{dto.payBeginDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="dto.payEndDate != null and dto.payEndDate != ''">
                and appodr.PAY_DATE <![CDATA[<=]]> DATE_FORMAT(#{dto.payEndDate},'%Y-%m-%d 23:59:59')
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and pax.PAX_NAME like concat('%',#{dto.paxName},'%')
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and pax.TKT_NO =#{dto.tktNo}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and DFI.FLIGHT_NO =#{dto.flightNo}
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and appodr.APPLY_CODE =#{dto.applyCode}
            </if>
            <if test="dto.org != null and dto.org !='' ">
                and DFI.ORG =#{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst !='' ">
                and DFI.DST =#{dto.dst}
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity !='' ">
                and odr.SERVICE_CITY = (select AIRPORT_NAME from sys_airport_info where CODE = #{dto.serviceCity})
            </if>

            <if test="dto.payTypes != null and dto.payTypes.size() > 0">
                and appodr.GET_MONEY_WAY in
                <foreach collection="dto.payTypes" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.flightModel != null and dto.flightModel.size() > 0">
                and DFI.AC_TYPE in
                <foreach collection="dto.flightModel" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.flightType != null and dto.flightType.size() > 0">
                and DFI.FLIGHT_TYPE in
                <foreach collection="dto.flightType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.innerReason != null and dto.innerReason.size() > 0">
                and refli.INNER_REASON in
                <foreach collection="dto.innerReason" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.isFlag != null and dto.isFlag.size() > 0">
                and pax.IS_FLAG in
                <foreach collection="dto.isFlag" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.receiveType != null and dto.receiveType.size() > 0">
                and pax.RECEIVE_STATUS in
                <foreach collection="dto.receiveType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.payStatus != null and dto.payStatus.size() > 0">
                and appodr.PAY_STATUS in
                <foreach collection="dto.payStatus" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.applyStatus != null and dto.applyStatus.size() > 0">
                and appodr.APPLY_STATUS in
                <foreach collection="dto.applyStatus" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="dto.isScene != null and dto.isScene.size() > 0">
                and appodr.IS_SCENE in
                <foreach collection="dto.isScene" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>

        </where>
        <if test="dto.compensationType == '1'.toString() and (dto.applyCode == null or dto.applyCode == '') 
            and (dto.receiveType == null) 
            and (dto.payStatus == null) 
            and (dto.applyStatus == null) 
            and (dto.payTypes == null)
            and (dto.payBeginDate == null or dto.payBeginDate == '')
            and (dto.payEndDate == null or dto.payEndDate == '')">
            <include refid="compensateLuggageReport"/>
        </if> ) as dataTemp
        <choose>
            <when test="dto.compensationType == '1'.toString() ">
                order by dataTemp.flightDate desc ,dataTemp.flightNo desc,dataTemp.accidentId desc,
                    dataTemp.orderId desc
            </when>
            <otherwise>
                order by dataTemp.flightDate desc ,dataTemp.flightNo desc ,dataTemp.applyCode desc,
                    dataTemp.trans_amount desc , convert(substr(dataTemp.paxName,1,1) using 'GBK') asc,dataTemp.orderId desc
            </otherwise>
        </choose>
    </sql>
    <select id="costReportInfo" parameterType="com.swcares.psi.report.compensate.irregularflight.dto.CostReportDto"
            resultType="com.swcares.psi.report.compensate.irregularflight.vo.CostReportVo">
        <include refid="costReport"></include>
    </select>
    <select id="costReportInfoPage" parameterType="com.swcares.psi.report.compensate.irregularflight.dto.CostReportDto"
            resultType="com.swcares.psi.report.compensate.irregularflight.vo.CostReportVo">
        <include refid="costReport"></include>
    </select>


    <update id="quickUpdateFlag" >
        UPDATE  DP_PAX_INFO SET IS_FLAG=#{flag} where ID IN
        <foreach collection="ids" item="ele" open="(" close=")" separator=",">
            #{ele}
        </foreach>

    </update>

</mapper>
