package com.swcares.psi.ao.flightUpgrades.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.ao.flightUpgrades.dto.PidQueryUpgradesDTO;
import com.swcares.psi.ao.flightUpgrades.entity.GradeOrder;
import com.swcares.psi.ao.flightUpgrades.vo.FltOrderResultVO;
import com.swcares.psi.ao.flightUpgrades.vo.PidQueryVO;
import com.swcares.psi.ao.flightUpgrades.vo.QueryResultVO;
import com.swcares.psi.base.data.api.entity.FltPassengerRealInfo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：$.$
 * Description：(用一句话描述这个类或者接口表示什么)
 * Copyright © 2022$ xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/1/20 9:45
 * @version v1.0
 */
@Mapper
public interface GradeOrderMapper extends BaseMapper<GradeOrder> {


    PsiPage<FltOrderResultVO<QueryResultVO>> getGradeOrderByParams(PsiPage<FltOrderResultVO<QueryResultVO>> page, @Param("flightNo") String flightNo, @Param("flightDateStar") String flightDateStar,
                                                                   @Param("flightDateEnd") String flightDateEnd,
                                                                   @Param("org") String org, @Param("dst") String dst, @Param("name") String passageName);

    int updateGradeOrderByParam(@Param("g") GradeOrder gradeOrder);


    List<FltPassengerRealInfo> queryFltPassengerReal(@Param("ticket") String ticket, @Param("flight")String flight, @Param("date") String date);


    List<QueryResultVO> paramsDownInfo( @Param("flightNo") String flightNo, @Param("flightDateStar") String flightDateStar,
                                        @Param("flightDateEnd") String flightDateEnd,
                                        @Param("org") String org, @Param("dst") String dst, @Param("name") String passageName);

    List<QueryResultVO> pidGetGradeOrderInfo(@Param("gradeOrder")GradeOrder gradeOrder);

   List<PidQueryVO> pidQueryInfo(PsiPage<PidQueryVO> page, @Param("dto") PidQueryUpgradesDTO resParam);
}
