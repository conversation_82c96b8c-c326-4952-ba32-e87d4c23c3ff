<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.mapper.AoViproomItemSellMapper">

    <resultMap id="aoViproomItemSellResultMap" type="com.swcares.psi.ao.model.AoViproomItemSell">
        <id column="ID" property="id"/>
        <result column="ITEM_ID" property="itemId"/>
        <result column="ORDER_ID" property="orderId"/>
        <result column="PAX_ID" property="paxId"/>
        <result column="PAX_NAME" property="paxName"/>
        <result column="PAX_TYPE" property="paxType"/>
        <result column="CARD_TYPE" property="cardType"/>
        <result column="ID_NO" property="idNo"/>
        <result column="FLIGHT_NO" property="flightNo"/>
        <result column="FLIGHT_DATE" property="flightDate"/>
        <result column="ORG" property="org"/>
        <result column="DST" property="dst"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="DST_NAME" property="dstName"/>
        <result column="STD" property="std"/>
        <result column="TKT_NO" property="tktNo"/>
        <result column="SEGMENT_TYPE" property="segmentType"/>
        <result column="SUB_CABIN" property="subCabin"/>
        <result column="GATE" property="gate"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>

    <sql id="SelectSql">
        SELECT (<include refid="com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper.getPaxHedCategories"></include>) as paxTypeDesc,
        pax.FQTV_NUMBER as ffpNumber,
        A.PAX_NAME,
	    A.ORDER_ID AS orderNo,
	    B.BANK_ORDER_NO,
        B.COMPLETE_DEPARTMENT_INFO,
	    DATE_FORMAT(A.CREATE_TIME, '%Y-%m-%d') AS orderDate,
	    A.TKT_NO,
	    DATE_FORMAT(A.FLIGHT_DATE, '%Y-%m-%d') AS flightDate,
	    A.FLIGHT_NO,
	    CONCAT( A.ORG, '-', A.DST ) AS flightRoute,
	    '1' as customerNo,
        CASE apr.PAY_TYPE
            when '3' then
                concat('-',B.ORDER_PRICE)
            else B.ORDER_PRICE
        end as price,
	    CASE B.CHARGE_TYPE
	    WHEN '0' THEN '微信'
	    WHEN '1' THEN '银行卡'
	    WHEN '2' THEN '支付宝'
	    WHEN '3' THEN '现金'
	    WHEN '4' THEN 'POS机'
        when '5' then 'worldPay'
	    END AS CHARGE_TYPE,
	    CASE apr.PAY_TYPE
            WHEN  '0' THEN
                '未支付'
            WHEN  '1' THEN
                '支付完成'
            WHEN  '2' THEN
                '支付失败'
            WHEN  '3' THEN
                '退款成功'
            WHEN  '4' THEN
                '退款失败'
        END AS applyStatus,
	    CASE apr.PAY_TYPE
            WHEN  '0' THEN
                '未支付'
            WHEN  '1' THEN
                '支付完成'
            WHEN  '2' THEN
                '支付失败'
            WHEN  '3' THEN
                '退款成功'
            WHEN  '4' THEN
                '退款失败'
        END AS ORDER_STATUS,
	    B.SEGMENT_TYPE,
	    C.NAME AS remark,
	    B.CELL_CHANNEL AS sellChannel,
	    B.USER_NO,
	    B.USER_NAME,
	    B.DEPARTMENT,
        B.DEPARTMENT_ID,
	    B.TURN_NUMBER AS payNumber,
        B.AREA_COMPANY,
	    date_format(apr.OPERATION_DATE,'%Y-%m-%d %H:%i:%s' )as OPERATION_DATE,

        B.CURRENCY_SPECIES,
        case B.CURRENCY_SPECIES
        when 'CNY' THEN CONCAT(B.CURRENCY_SPECIES,'(人民币)')
        ELSE
        (SELECT CONCAT(B.CURRENCY_SPECIES,'(', DATA_VALUE,')') FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=B.CURRENCY_SPECIES limit 1)
        end AS currencySpeciesName,
	    A.ORDER_ID AS cashNo
        FROM
	    AO_VIPROOM_ITEM_SELL A
	    INNER JOIN AO_VIPROOM_ITEM_CONF C ON A.ITEM_ID = C.ID
	    LEFT JOIN AO_ORDER_INFO B ON A.ORDER_ID = B.ORDER_NO
	    LEFT join ao_pay_record apr on B.ID=apr.ORDER_ID
        left join flt_passenger_real_info pax on pax.id=a.pax_id
        WHERE
	       apr.PAY_TYPE  in ('1','3','4')
    </sql>

    <sql id="WhereSql">
        <if test="dto.areaCompany != null and dto.areaCompany !='' ">
            AND B.AREA_COMPANY like concat('%',#{dto.areaCompany},'%')
        </if>
        <if test="dto.flightNo!=null and dto.flightNo!=''">
            AND A.FLIGHT_NO = #{dto.flightNo}
        </if>
        <if test="dto.beginDate!=null and dto.beginDate!=''">
            AND A.FLIGHT_DATE &gt;= #{dto.beginDate}
        </if>
        <if test="dto.endDate!=null and dto.endDate!=''">
            AND A.FLIGHT_DATE &lt;= DATE_FORMAT(#{dto.endDate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="dto.org!=null and dto.org!=''">
            AND A.ORG = #{dto.org}
        </if>
        <if test="dto.dst!=null and dto.dst!=''">
            AND A.DST = #{dto.dst}
        </if>

        <if test="dto.departmentName!=null and dto.departmentName!=''">
            AND B.DEPARTMENT like concat('%',#{dto.departmentName},'%')
        </if>


        <if test="dto.orderBeginDate != null and dto.orderBeginDate !=''">
            and B.ORDER_DATE &gt;= date_format(#{dto.orderBeginDate},'%Y-%m-%d 00:00:00')
        </if>
        <if test="dto.orderEndDate != null and dto.orderEndDate !='' ">
            and B.ORDER_DATE &lt;= date_format(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.payBeginDate != null and dto.payBeginDate !=''">
            and apr.OPERATION_DATE &gt;= date_format(#{dto.payBeginDate},'%Y-%m-%d 00:00:00')
        </if>
        <if test="dto.payEndDate != null and dto.payEndDate !='' ">
            and apr.OPERATION_DATE &lt;= date_format(#{dto.payEndDate},'%Y-%m-%d 23:59:59')
        </if>


        <if test="dto.orderNo!=null and dto.orderNo!=''">
            AND B.ORDER_NO = #{dto.orderNo}
        </if>
        <if test="dto.bankOrderNo!=null and dto.bankOrderNo!=''">
            AND B.BANK_ORDER_NO = #{dto.bankOrderNo}
        </if>
        <if test="dto.tktNo!=null and dto.tktNo!=''">
            AND A.TKT_NO = #{dto.tktNo}
        </if>
        <if test="dto.payNumber!=null and dto.payNumber!=''">
            AND B.TURN_NUMBER = #{dto.payNumber}
        </if>
        <if test="dto.segmentType!=null and dto.segmentType!=''">
            AND B.SEGMENT_TYPE = #{dto.segmentType}
        </if>
        <if test="dto.chargeType!=null and dto.chargeType!=''">
            AND FIND_IN_SET(B.CHARGE_TYPE,#{dto.chargeType}) >0
        </if>
        <if test="dto.orderStatus!=null and dto.orderStatus!=''">
            AND B.ORDER_STATUS = #{dto.orderStatus}
        </if>
        <if test="dto.payType != null and dto.payType !='' ">
            AND FIND_IN_SET(B.PAY_STATUS,#{dto.payType}) >0
        </if>
        <if test="dto.sellChannel!=null and dto.sellChannel!=''">
            AND B.CELL_CHANNEL = #{dto.sellChannel}
        </if>
    </sql>

    <sql id="payTypesSummaryMoneySql">
        select t.* from (
        SELECT
        sum(CASE WHEN ss.CHARGE_TYPE = '0' THEN ss.price ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN ss.CHARGE_TYPE = '1' THEN ss.price ELSE 0 end ) AS bankCardMoney,
        sum(CASE WHEN ss.CHARGE_TYPE = '2'  THEN ss.price ELSE 0 end ) AS alipayMoney,
        sum(CASE WHEN (ss.CHARGE_TYPE = '3' or ss.CHARGE_TYPE = '4') THEN ss.price ELSE 0 end) AS cashMoney,
        sum(CASE WHEN ss.CHARGE_TYPE = '5' THEN ss.price ELSE 0 end) AS worldPay,
        sum(ss.price  ) AS allMoney,
        case  ss.CURRENCY_SPECIES
        when 'CNY' then  concat(ss.CURRENCY_SPECIES,'(人民币)')
        else   concat(ss.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=ss.CURRENCY_SPECIES limit 1),')')
        end as currencySpecies
        from(
        SELECT
        s.orderNo,
        s.CHARGE_TYPE,
        s.CURRENCY_SPECIES,
        s.price AS price
        FROM(
        SELECT
        ss.orderNo,
        ss.CHARGE_TYPE,
        ss.CURRENCY_SPECIES,
        SUM(ss.price) AS price
        FROM (
        select
        B.CURRENCY_SPECIES,
        A.ORDER_ID AS orderNo,
        A.TKT_NO,
        CASE apr.PAY_TYPE
        when '3' then
        concat('-',B.ORDER_PRICE)
        else B.ORDER_PRICE
        end as price,
        B.CHARGE_TYPE AS CHARGE_TYPE
        FROM
        AO_VIPROOM_ITEM_SELL A
        INNER JOIN AO_VIPROOM_ITEM_CONF C ON A.ITEM_ID = C.ID
        LEFT JOIN AO_ORDER_INFO B ON A.ORDER_ID = B.ORDER_NO
        LEFT join ao_pay_record apr on B.ID=apr.ORDER_ID
        WHERE
        apr.PAY_TYPE  in ('1','3','4')
            <include refid="WhereSql"></include>
        ) ss
        GROUP BY ss.orderNo, ss.TKT_NO,ss.CURRENCY_SPECIES
        ) s
        GROUP BY s.orderNo
        ) ss
        group by ss.CURRENCY_SPECIES
        ) t where t.allMoney > 0
    </sql>

    <select id="page" parameterType="com.swcares.psi.ao.model.dto.AoViproomItemSellReportDto" resultType="com.swcares.psi.ao.model.vo.AoViproomItemSellReportVO">
        <include refid="SelectSql"></include>
        <include refid="WhereSql"></include>
    </select>

    <select id="list" parameterType="com.swcares.psi.ao.model.dto.AoViproomItemSellReportDto" resultType="com.swcares.psi.ao.model.vo.AoViproomItemSellReportVO">
        <include refid="SelectSql"></include>
        <include refid="WhereSql"></include>
    </select>

    <select id="getOrderInfo" parameterType="String" resultType="com.swcares.psi.ao.model.vo.AoViproomOrderDetailVO">
        SELECT
        A.ID AS productId,
        A.ID_NO AS paxNo,
	    A.PAX_NAME AS paxName,
	    B.ORDER_PRICE AS price,
	    C.NAME AS item
	    FROM AO_VIPROOM_ITEM_SELL A
	    INNER JOIN AO_VIPROOM_ITEM_CONF C ON A.ITEM_ID = C.ID
	    LEFT JOIN AO_ORDER_INFO B ON A.ORDER_ID = B.ORDER_NO
	    WHERE A.ORDER_ID=#{orderId}
    </select>
    <select id="summary" parameterType="com.swcares.psi.ao.model.dto.AoViproomItemSellReportDto" resultType="com.swcares.psi.ao.model.vo.AoViproomItemSellReportSummaryVO">
        SELECT
        SUM( price ) AS amount,
        count( TKT_NO ) AS total
        from(
        SELECT
        s.TKT_NO,
        s.price AS price
        FROM(
        SELECT
        ss.orderNo,
        ss.TKT_NO,
        SUM(ss.price) AS price
        FROM (
        <include refid="SelectSql"></include>
        <include refid="WhereSql"></include>
        ) ss
        GROUP BY ss.orderNo, ss.TKT_NO
        ) s
        GROUP BY s.orderNo
        ) ss
    </select>
    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo">
        <include refid="payTypesSummaryMoneySql"></include>
    </select>


</mapper>