package com.swcares.psi.base.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.base.data.api.entity.DpServiceInfo;
import com.swcares.psi.base.data.api.vo.DpServiceInfoVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
public interface IDpServiceInfoService extends IService<DpServiceInfo> {
    List<DpServiceInfoVo> getServiceInfoByOrderId(String orderId);
}
