package com.swcares.psi.overbook.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.swcares.psi.activity.audit.enums.AuditProcessType;
import com.swcares.psi.activity.audit.enums.AuditTypeEnum;
import com.swcares.psi.activity.audit.service.OrderAuditService;
import com.swcares.psi.activity.audit.vo.CompensationProgressVo;
import com.swcares.psi.base.data.api.dto.CompensateFltPassengerDto;
import com.swcares.psi.base.data.api.dto.FltFlightRealInfoDTO;
import com.swcares.psi.base.data.api.entity.DpServiceInfo;
import com.swcares.psi.base.data.api.vo.DpServiceInfoVo;
import com.swcares.psi.base.data.api.vo.FltFlightRealInfoVo;
import com.swcares.psi.base.data.api.vo.PaxInfoParseVo;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.FltPassengerRealInfoService;
import com.swcares.psi.base.data.service.IDpServiceInfoService;
import com.swcares.psi.base.fileuploadanddownload.UploadAndDownload;
import com.swcares.psi.base.util.Asserts;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.combine.util.UID;
import com.swcares.psi.common.compensate.dao.CompensatesInfoDao;
import com.swcares.psi.common.compensate.dao.FlightsInfoDao;
import com.swcares.psi.common.compensate.dao.OrdersInfoDao;
import com.swcares.psi.common.compensate.dao.PassengerInfoDao;
import com.swcares.psi.common.compensate.dao.impl.CompensateInfoDaoImpl;
import com.swcares.psi.common.compensate.dao.impl.FlightCompensateDaoImpl;
import com.swcares.psi.common.compensate.dao.impl.OrderInfoDaoImpl;
import com.swcares.psi.common.compensate.entity.CompensatesInfo;
import com.swcares.psi.common.compensate.entity.FlightsInfo;
import com.swcares.psi.common.compensate.entity.OrderInfo;
import com.swcares.psi.common.compensate.entity.PassengerInfo;
import com.swcares.psi.common.compensate.vo.*;
import com.swcares.psi.common.core.rpc.RpcRequest;
import com.swcares.psi.common.core.rpc.RpcUtil;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.RegExpUtil;
import com.swcares.psi.common.utils.query.QueryResults;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.irregular.flight.dao.OrderInfoDao;
import com.swcares.psi.irregular.flight.dao.OrderSceneConfigDao;
import com.swcares.psi.irregular.flight.entity.OrderSceneConfig;
import com.swcares.psi.overbook.dao.OverBookConfigDao;
import com.swcares.psi.overbook.dao.OverInfoDao;
import com.swcares.psi.overbook.dao.impl.OverBookConfigDaoImpl;
import com.swcares.psi.overbook.dao.impl.OverInfoDaoImpl;
import com.swcares.psi.overbook.dto.*;
import com.swcares.psi.overbook.entity.OverBookConfigInfo;
import com.swcares.psi.overbook.entity.OverInfo;
import com.swcares.psi.overbook.service.ExpectOverBookService;
import com.swcares.psi.overbook.service.OverBookService;
import com.swcares.psi.overbook.vo.*;
import com.swcares.psi.overbook.vo.FlightInfoVo;
import com.swcares.psi.overbook.vo.PaxInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.script.ScriptException;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static com.swcares.psi.combine.constant.CommonConstants.AUTO_AUDIT_TUNO;

/**
 * ClassName：com.swcares.psi.audit.service.impl <br>
 * Description：H5-旅客超售 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 13:14 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class OverBookServiceImpl implements OverBookService {

    @Resource
    private FlightsInfoDao flightInfoDao;
    @Resource
    private OrderInfoDao orderInfoDao;
    @Resource
    private OrdersInfoDao ordersInfoDao;
    @Resource
    private OrderInfoDaoImpl orderInfoDaoImpl;
    @Resource
    private PassengerInfoDao paxInfoDao;

    @Resource
    private CompensatesInfoDao compensatesInfoDao;
    @Resource
    private CompensateInfoDaoImpl compensateInfoDaoImpl;

    @Resource
    private OverInfoDao overInfoDao;
    @Resource
    private OverInfoDaoImpl overInfoDaoImpl;
    @Resource
    private OverBookConfigDao overBookConfigDao;
    @Resource
    private OverBookConfigDaoImpl overBookConfigDaoImpl;
    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;
    @Resource
    private UploadAndDownload uploadAndDownload;

    @Resource
    private OrderAuditService orderAuditService;
    @Resource
    private FltPassengerRealInfoService fltPassengerRealInfoService;
    @Resource
    private FltFlightRealInfoService fltFlightRealInfoService;
    @Resource
    private ExpectOverBookService expectOverBookService;
    @Autowired
    private OrderSceneConfigDao orderSceneConfigDao;
    @Autowired
    private UID uid;
    @Autowired
    IDpServiceInfoService serviceInfoService;
    @Resource
    private RpcUtil rpcUtil;

    DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");


    // 超售改签
    private static final String TYPE_REBOOK = "0";
    // 超售退票
    private static final String TYPE_REFUND = "1";
    // 比例赔付
    private static final String PAY_TYPE_RATIO = "0";
    // 退票固定赔付
    private static final String PAY_TYPE_FIXED = "1";
    // 补偿单
    private static final String PAY_TYPE_IRREGULAR = "0";
    // 超售
    private static final String PAY_TYPE_OVER = "2";
    // 保存提交
    private static final String SAVE_SUBMIT = "1";
    // 保存草稿
    private static final String SAVE_DRAFT = "0";
    //状态3-发放
    private static final String ORDER_STATUS_GRANT = "3";
    //补偿类型 不正常航班补偿0
    private static final String STATUS_IRREGULAR = "0";
    // 补偿方式 百分比
    private static final String COMPENSATE_WAY_PERCENT = "1";
    // 补偿方式 固定金额
    private static final String COMPENSATE_WAY_FIXED = "2";
    // 补偿方式 特殊金额
    private static final String COMPENSATE_WAY_SPECIAL = "3";
    // 领取方式 线上
    private static final String PAYMENT_TYPE_ONLINE = "1";
    // 领取方式 优惠券
    private static final String PAYMENT_TYPE_COUPON = "2";
    // 超售补偿一级审核角色名
    private static final String FIRST_LEVEL_AUDIT_ROLE_NAME = "超售补偿一级审核";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setOrderExplain(String orderId, String explain) {
        if (StringUtils.isEmpty(orderId) || StringUtils.isEmpty(explain)) {
            throw new BusinessException(MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
        }
        orderInfoDaoImpl.setOrderExplain(orderId, explain);
        PsiUser psiUser = (PsiUser) AuthenticationUtil.getAuthentication();
        log.info("{}-{}添加订单说明,订单ID{}-说明内容:{}", psiUser.getId(), psiUser.getRealName(), orderId, explain);
    }


    @Override
    public QueryResults overBookWebList(OverBookListQueryDto overBookListQueryDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        //三字码转中文
        if (StringUtils.isNotBlank(overBookListQueryDto.getServiceCity())) {
            CityCodeVo cityCodeVo = flightCompensateDao.getCityCodeInfoByCityCode3(overBookListQueryDto.getServiceCity());
            Asserts.notNull(cityCodeVo, MessageCode.OVER_SERVICE_CITY_IS_NULL.getCode(), new String[]{overBookListQueryDto.getServiceCity()});
            overBookListQueryDto.setServiceCity(cityCodeVo.getCityChName());
        }

        log.info("web-超售列表【前端请求参数】" + overBookListQueryDto.toString() + ";获取当前登录人：" + userId);
        return overInfoDaoImpl.findOverBookWebList(overBookListQueryDto, userId);
    }

    @Override
    public List<OverBookListVo> excelOverBookList(OverBookListQueryDto overBookListQueryDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        //三字码转中文
        if (StringUtils.isNotBlank(overBookListQueryDto.getServiceCity())) {
            CityCodeVo cityCodeVo = flightCompensateDao.getCityCodeInfoByCityCode3(overBookListQueryDto.getServiceCity());
            Asserts.notNull(cityCodeVo, MessageCode.OVER_SERVICE_CITY_IS_NULL.getCode(), new String[]{overBookListQueryDto.getServiceCity()});
            overBookListQueryDto.setServiceCity(cityCodeVo.getCityChName());
        }
        return overInfoDaoImpl.excelOverBookList(overBookListQueryDto, userId);
    }

    @Override
    public OverBookDetailsVo overBookWebDetails(String overId) {
        // 验空
        Asserts.isNotEmpty(overId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"超售id"});
        return overInfoDaoImpl.findWebOverDetailsById(overId);
    }

    /**
     * Title: saveOverBookConfig
     * param: [timeDifferenceInfo, refundInfo]
     *
     * @return void
     * Description: 保存超售补偿规则
     * author: makai
     * date: 2020-09-16
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOverBookConfig(List<TimeDifferenceInfoVo> timeDifferenceInfo,
                                   RefundInfoVo refundInfo) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        List<OverBookConfigInfo> configList = new ArrayList<>();
        Date createTime = new Date();
        for (TimeDifferenceInfoVo tObj : timeDifferenceInfo) {
            OverBookConfigInfo overBookConfigInfo = new OverBookConfigInfo();
            BeanUtils.copyProperties(tObj, overBookConfigInfo);
            getParamIsUpd(overBookConfigInfo, createTime, userId);
            overBookConfigInfo.setType(TYPE_REBOOK);
            overBookConfigInfo.setPayType(PAY_TYPE_RATIO);
            configList.add(overBookConfigInfo);
        }
        if (null != refundInfo) {
            Asserts.isNotEmpty(refundInfo.getPayMoney(), MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"PayMoney 不允许为空！"});
            OverBookConfigInfo refundConfig = new OverBookConfigInfo();
            refundConfig.setType(TYPE_REFUND);
            refundConfig.setPayMoney(refundInfo.getPayMoney());
            refundConfig.setPayType(PAY_TYPE_FIXED);
            getParamIsUpd(refundConfig, createTime, userId);
            configList.add(refundConfig);
        }
        overBookConfigDao.deleteAll();
        overBookConfigDao.saveAll(configList);
    }

    private void getParamIsUpd(OverBookConfigInfo overBookConfigInfo, Date createTime,
                               String createUser) {
        overBookConfigInfo.setCreateTime(createTime);
        overBookConfigInfo.setCreateUser(createUser);
        overBookConfigInfo.setStatus("0");

    }

    @Override
    public QueryResults findOverBookList(OverBookQueryDto overBookQueryDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        log.info("H5-超售列表查询【请求参数】" + overBookQueryDto.toString() + " 【userId】" + userId);
        return overInfoDaoImpl.findOverBookList(overBookQueryDto, userId);
    }

    @Override
    public OverBookVerftnVo overBookVerification(OverBookVerificationDto overBookVerificationDto) {
        String keySearch = overBookVerificationDto.getKeySearch();
        String flightNo = overBookVerificationDto.getFlightNo();
        String flightDate = overBookVerificationDto.getFlightDate();
        String serviceCity = overBookVerificationDto.getServiceCity();
        Asserts.isNotEmpty(keySearch, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"模糊查询字段"});
        Asserts.isNotEmpty(flightNo, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"航班号"});
        Asserts.isNotEmpty(flightDate, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"航班日期"});
        Asserts.isNotEmpty(serviceCity, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"服务航站三字码"});
        // 请求参数身份证加密处理
        if (RegExpUtil.isIdCard(keySearch)) {
            keySearch = AesEncryptUtil.encrypt(keySearch);
        }
        CompensateFltPassengerDto compensateFltPassengerDto = new CompensateFltPassengerDto();
        compensateFltPassengerDto.setFlightNumber(flightNo);
        compensateFltPassengerDto.setFlightStartDate(flightDate);
        compensateFltPassengerDto.setFlightEndDate(flightDate);
        compensateFltPassengerDto.setKeySearch(keySearch);
        // 必须要已出票的旅客
        compensateFltPassengerDto.setIsPrintTktNo("Y");
        List<PaxInfoParseVo> paxInfoList = fltPassengerRealInfoService.getPassengerList(compensateFltPassengerDto);
        if (paxInfoList.size() > 0) {
            // 获取有票号的旅客数据
            PaxInfoParseVo tracePaxInfoVo = new PaxInfoParseVo();
            for (PaxInfoParseVo paxInfoParseVo : paxInfoList) {
                if (StringUtils.isNotEmpty(paxInfoParseVo.getEtNum())) {
                    tracePaxInfoVo = paxInfoParseVo;
                    break;
                }
            }
            //服务航站与旅客起始站不匹配
            if (!serviceCity.equals(tracePaxInfoVo.getOrig())) {
                throw new BusinessException(MessageCode.OVER_VERIFICATION_ORG_ERROR.getCode());
            }
            // 判断当前航班预超信息是否配置
            ExpectOverBookVo expectOverBook = findExpectOverBookInfo(tracePaxInfoVo);
            if (ObjectUtils.isEmpty(expectOverBook) || ObjectUtils.isEmpty(expectOverBook.getPlancount())) {
                //预计超售信息没有查询到，不允许建单
                throw new BusinessException(MessageCode.OVER_EXPECT_INFO_NOT_FOUND.getCode());
            }
            FltFlightRealInfoDTO fltFlightRealInfoDTO = new FltFlightRealInfoDTO();
            fltFlightRealInfoDTO.setBeginFlightDate(flightDate);
            fltFlightRealInfoDTO.setEndFlightDate(flightDate);
            fltFlightRealInfoDTO.setFlightNumber(overBookVerificationDto.getFlightNo());
            fltFlightRealInfoDTO.setTransit("Y");
            fltFlightRealInfoDTO.setOrg(tracePaxInfoVo.getOrig());
            List<FltFlightRealInfoVo> flightList = fltFlightRealInfoService.getFlightInfo(fltFlightRealInfoDTO);
            if (flightList.size() > 0) {
                FltFlightRealInfoVo flightInfo = flightList.get(0);
                return getReturnVerftnVo(flightInfo, paxInfoList);
            }
        }
        return new OverBookVerftnVo();
    }

    /**
     * Title：getReturnVerftnVo <br>
     * Description： 封装验证返回参数对象<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 11:56 <br>
     *
     * @param
     * @return
     */
    private OverBookVerftnVo getReturnVerftnVo(FltFlightRealInfoVo flight, List<PaxInfoParseVo> paxInfo) {
        OverBookVerftnVo verftnVo = new OverBookVerftnVo();
        verftnVo.setFlightId(flight.getId());
        verftnVo.setFlightNo(flight.getFlightNumber());
        verftnVo.setFlightDate(flight.getFlightDate());
        verftnVo.setAcType(flight.getFltCode());
        verftnVo.setSta(flight.getSegmentSTA());
        verftnVo.setStd(flight.getSegmentSTD());
        verftnVo.setPaxInfoList(paxInfo);
        return verftnVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOverBookInfo(OverBookInfoDto overBookInfoDto) throws Exception {

        log.info("H5-【超售-保存接口】，前端传入参数：{}", overBookInfoDto.toString());
        // 旅客信息查询
        PaxInfoParseVo paxInfo = fltPassengerRealInfoService.getPassengerById(overBookInfoDto.getPaxId());
        Asserts.isNotEmpty(paxInfo, MessageCode.OVER_PAX_ID_QUERY_IS_NULL.getCode());
        // 验证补偿金额 如果是特殊补偿类型则不用验证
        try {
            if (SAVE_SUBMIT.equals(overBookInfoDto.getIsCommit()) && !COMPENSATE_WAY_SPECIAL.equals(overBookInfoDto.getPayWay())) {
                Asserts.isNotEmpty(overBookInfoDto.getPayMoney(),
                        MessageCode.OVER_PAY_MONEY_IS_NULL.getCode());
                if (TYPE_REBOOK.equals(overBookInfoDto.getType())) Asserts.isNotEmpty(overBookInfoDto.getPlaneDate(),
                        MessageCode.OVER_PLANEDATE_IS_NULL.getCode());
                if (Integer.parseInt(overBookInfoDto.getPayMoney()) < 1) {
                    throw new BusinessException(MessageCode.OVER_PAY_MONEY_IS_NULL.getCode());
                }
                inspectPayMoney(overBookInfoDto);
            }
        } catch (ScriptException e) {
            throw new ScriptException("H5保存超售数据-验证补偿金额计算异常！");
        } catch (ParseException e2) {
            throw new ParseException("H5保存超售数据-验证补偿金额-航班计划起飞时间转换异常", e2.getErrorOffset());
        }
        // 航段采用旅客实际航段
        overBookInfoDto.setSegment(paxInfo.getSegment());
        // 查原航班详细信息
        FltFlightRealInfoVo fltFlightRealInfoVo = new FltFlightRealInfoVo();
        FltFlightRealInfoDTO fltFlightRealInfoDTO = new FltFlightRealInfoDTO();
        fltFlightRealInfoDTO.setBeginFlightDate(paxInfo.getFlightDate());
        fltFlightRealInfoDTO.setEndFlightDate(paxInfo.getFlightDate());
        fltFlightRealInfoDTO.setFlightNumber(paxInfo.getFlightNum());
        fltFlightRealInfoDTO.setOrg(paxInfo.getOrig());
        fltFlightRealInfoDTO.setTransit("Y");
        List<FltFlightRealInfoVo> flightRealInfoVoList = fltFlightRealInfoService.getFlightInfo(fltFlightRealInfoDTO);
        if (flightRealInfoVoList.size() > 0) {
            fltFlightRealInfoVo = flightRealInfoVoList.get(0);
        }
        overBookInfoDto.setFlightDate(fltFlightRealInfoVo.getFlightDate());
        // servicesCity 还原成旅客出发地中文名
        overBookInfoDto.setServiceCity(paxInfo.getOrigName());

        // 草稿保存或提交 删除旧数据，保存最新数据
        String orderId = overBookInfoDto.getOrderId();
        String overId = overBookInfoDto.getOverId();
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();

        if (ObjectUtils.isNotEmpty(overBookInfoDto.getOverId())) {
            delDraftInfo(overBookInfoDto.getOrderId());
        } else {
            orderId = uid.generateUIDStartWithTimestamp() + PAY_TYPE_OVER;
            overId = uid.generateUIDStartWithTimestamp() + PAY_TYPE_OVER;
        }
        Date createTime = new Date();// payType 不正常航班补偿0、异常行李1、超售旅客2
        overBookInfoDto.setOrderId(orderId);
        overBookInfoDto.setOverId(overId);

        log.info("H5保存超售数据-[保存业务数据表]，orderId[{}],overId[{}],实体信息[{}]", orderId, overId, overBookInfoDto.toString());
        // 保存超售表信息
        saveOverInfo(createTime, createUser, overBookInfoDto, false);
        // 保存赔付标准
        saveCompensateInfo(createTime, createUser, overBookInfoDto);
        // 保存旅客信息
        savePaxInfo(createTime, createUser, orderId, overBookInfoDto, paxInfo);
        // 保存赔付单信息
        OrderInfo orderInfo = saveOrderInfo(createTime, createUser, fltFlightRealInfoVo, overBookInfoDto);
        // 保存航延航班信息 原旅客航班信息
        saveCompensateFlightInfo(createTime, createUser, fltFlightRealInfoVo, overBookInfoDto);

        //先删除旧数据
        serviceInfoService.lambdaUpdate().eq(DpServiceInfo::getOrderNo, orderInfo.getOrderId()).remove();

        if (overBookInfoDto.getServiceDataCode() != null && overBookInfoDto.getServiceDataCode().size() > 0) {
            List<DpServiceInfo> serviceInfos = new ArrayList<>();
            for (String ele : overBookInfoDto.getServiceDataCode()) {
                DpServiceInfo en = new DpServiceInfo();
                en.setCreateTime(LocalDateTime.now());
                en.setCreateUser(createUser);
                en.setOrderNo(orderInfo.getOrderId());
                en.setServiceFrom("1");
                en.setServiceDataCode(ele);
                en.setServiceRemark(overBookInfoDto.getServiceRemark());
                en.setRemark("创建超售订单时创建");
                serviceInfos.add(en);
            }
            serviceInfoService.saveBatch(serviceInfos, serviceInfos.size());
        } else if (StringUtils.isNotEmpty(overBookInfoDto.getServiceRemark())) {
            DpServiceInfo en = new DpServiceInfo();
            en.setCreateTime(LocalDateTime.now());
            en.setOrderNo(orderInfo.getOrderId());
            en.setCreateUser(createUser);
            en.setServiceRemark(overBookInfoDto.getServiceRemark());
            en.setRemark("创建超售订单时创建");
            en.setServiceFrom("1");
            serviceInfoService.save(en);
        }

        String auditType = overBookInfoDto.getAuditType();
        if (overBookInfoDto.getIsCommit().equals(SAVE_SUBMIT)) {
            Asserts.isNotEmpty(auditType, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"auditType"});
            // 超售审核入口
            if (AuditTypeEnum.MANUAL_AUDIT.getKey().equals(auditType)) {
                orderAuditService.launchAuditProcess(createUser, AuditProcessType.LAUNCH_PROCESS_KEY_OVER_BOOKING.getNode(), orderId,
                        overBookInfoDto.getAuditIds(), overBookInfoDto.getPayMoney(), AuditTypeEnum.MANUAL_AUDIT.getKey(), null, null);
            } else if (AuditTypeEnum.AUTO_AUDIT.getKey().equals(auditType)) {
                // 自动审核流程
                orderAuditService.launchAuditProcess(createUser, AuditProcessType.LAUNCH_PROCESS_KEY_OVER_BOOKING.getNode(), orderId,
                        null, overBookInfoDto.getPayMoney(), AuditTypeEnum.AUTO_AUDIT.getKey(), null, null);
                if("1".equals(overBookInfoDto.getGrant())){
                    flightCompensateDao.updateOrderStatusByOrderIdAndStatus(orderId, OrderStatusEnum.EFFECT.getKey());
                    OrderSceneConfig orderSceneConfig = new OrderSceneConfig();
                    orderSceneConfig.setCreateTime(new Date());
                    orderSceneConfig.setStartTime(new Date());
                    orderSceneConfig.setEffectiveTime(OrderSceneConfig.EFFECTIVE_TIME);
                    orderSceneConfig.setType(OrderSceneConfig.TYPE_CREATE);
                    orderSceneConfig.setServiceUser(createUser);
                    orderSceneConfig.setOrderId(orderId);
                    orderSceneConfig.setIsHistory("0");
                    orderSceneConfigDao.save(orderSceneConfig);
                }
            }
        }
        return orderId;
    }


    /**
     * Title： findOverBookConfig<br>
     * Description：WEB - 查询超售配置回显<br>
     * author：傅欣荣 <br>
     * date：2020/3/31 17:21 <br>
     *
     * @param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> findOverBookConfig() {
        return getConfigInfoByType();
    }

    /**
     * Title：getConfigInfo <br>
     * Description： H5 - 查询超售配置信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:31 <br>
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> getConfigInfo() {
        return getConfigInfoByType();
    }

    @Override
    public QueryResults findOverDraftList(OverDraftListQueryDto overDraftListQueryDto) {
        return overInfoDaoImpl.findOverDraftList(overDraftListQueryDto);
    }

    @Override
    public OverBookDraftVo findOverDraftDetails(String overId) {
        Asserts.isNotEmpty(overId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"超售id"});
        OverBookDraftVo overDraftDetails = overInfoDaoImpl.findOverDraftDetails(overId);
        List<DpServiceInfoVo> list = serviceInfoService.getServiceInfoByOrderId(overDraftDetails.getOrderId());
        List<String> service = new ArrayList<>();
        List<String> serviceValue = new ArrayList<>();
        for (DpServiceInfoVo ele : list) {
            if (StringUtils.isNotEmpty(ele.getServiceDataCode())) {
                service.add(ele.getServiceDataCode());
                serviceValue.add(ele.getServiceDataValue());
            }
            overDraftDetails.setServiceRemark(ele.getServiceRemark());
        }
        overDraftDetails.setServiceDataCode(service);
        overDraftDetails.setServiceDataValue(serviceValue);
        return overDraftDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delOverDraftInfo(String[] ids) {
        log.info("-----H5-【旅客超售草稿箱-删除】，请求参数：超售id{}", ids.toString());
        for (String id : ids) {
            OverInfo overInfo = overInfoDao.findTById(id);
            delDraftInfo(overInfo.getOrderId());
        }
    }

    /**
     * Title：delDraftInfo <br>
     * Description：根据服务单删除草稿信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/31 14:55 <br>
     *
     * @param
     * @return
     */
    private void delDraftInfo(String orderId) {
        overInfoDao.deleteByOrderId(orderId);
        compensatesInfoDao.deleteByOrderId(orderId);
        flightInfoDao.deleteByOrderId(orderId);
        OrderInfo orderInfo = orderInfoDao.findByOrderId(orderId);
        if (ObjectUtils.isNotEmpty(orderInfo)) {
            orderInfoDao.delete(orderInfo);
        }
        paxInfoDao.deleteByOrderId(orderId);
    }

    @Override
    public Map<String, Object> findOverDetails(String orderId) throws Exception {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"补偿单id"});
        Map<String, Object> dataMap = new HashMap<>();
        // 补偿单信息 类型 补偿金额 客票价格 审核状态
        OverAndOrderInfo overAndOrderInfo = overInfoDaoImpl.findOverInfoAndOrder(orderId);
        dataMap.put("overBookInfo", overAndOrderInfo);
        //补偿进度信息
        CompensationProgressVo cpVo = orderAuditService.queryCpsProgressQuery(orderId);
        dataMap.put("cpsProgressInfo", cpVo);

        // 乘机人信息
        PaxInfoVo paxInfoVo = overInfoDaoImpl.findPaxInfo(orderId);
        dataMap.put("overPaxInfo", paxInfoVo);
        //服务信息
        List<DpServiceInfoVo> serviceInfoByOrderId = serviceInfoService.getServiceInfoByOrderId(orderId);
        List<String> service = new ArrayList<>();
        List<String> serviceValue = new ArrayList<>();
        for (DpServiceInfoVo ele : serviceInfoByOrderId) {
            if (StringUtils.isNotEmpty(ele.getServiceDataCode())) {
                service.add(ele.getServiceDataCode());
                serviceValue.add(ele.getServiceDataValue());
            }
            dataMap.put("serviceRemark", ele.getServiceRemark());
        }
        dataMap.put("serviceDataCode", service);
        dataMap.put("serviceDataValue", serviceValue);
        // 根据航班id 查航班信息
        FlightInfoVo flightInfoVo = overInfoDaoImpl.getFlightInfoVo(orderId);
        dataMap.put("flightInfo", flightInfoVo);
        if (overAndOrderInfo.getType().equals(TYPE_REBOOK)) {
            // 原航班信息 根据旅客id 查 航班信息
            OverInfo overInfo = overInfoDao.findTById(overAndOrderInfo.getOverId());
            // 改签航班信息 根据id查对象 ，航班信息 查航班详情
            FlightInfoVo flightInfoVo2 = new FlightInfoVo();
            BeanUtils.copyProperties(overInfo, flightInfoVo2);

            String timeDiff;
            // 拼接时间 ，计算时差
            String startTime = flightInfoVo.getFlightDate() + " "
                    + (StringUtils.isEmpty(flightInfoVo.getPlanDate()) ? "00:00" : flightInfoVo.getPlanDate());
            String endTime = flightInfoVo2.getFlightDate() + " "
                    + (StringUtils.isEmpty(flightInfoVo2.getPlanDate()) ? "00:00" : flightInfoVo2.getPlanDate());
            try {
                timeDiff = getHoursMin(startTime, endTime);
            } catch (ParseException e) {
                throw new ParseException(MessageCode.OVER_TIME_DIFFERENCE_ERROR.getCode(),
                        e.getErrorOffset());
            }
            flightInfoVo.setTimeDiff(timeDiff);
            dataMap.put("overBookFlight", flightInfoVo2);
        }
        return dataMap;
    }


    @Override
    public OverOrderDetailsVo findOrderDetails(String orderId) {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"补偿单id"});

        // 补偿单号 、 补偿类型 、 航段 、申请人 、 申请时间 、 终审人 审核时间
        OverOrderDetailsVo overOrderDetailsVo = overInfoDaoImpl.getOrderAuditInfo(orderId);
        // 补偿标准查询
        List<CompensateInfoVo> cList = compensateInfoDaoImpl.getCompensateInfoByOrderId(orderId);
        overOrderDetailsVo.setCompensateInfo(cList);
        return overOrderDetailsVo;
    }

    @Override
    public Map<String, Object> getRebookFlightInfo(String flightNo, String flightDate,
                                                   String paxId) {

        Asserts.isNotEmpty(flightNo, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"航班号"});
        Asserts.isNotEmpty(flightDate, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"航班时间"});
        Asserts.isNotEmpty(paxId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"旅客id"});

        // 旅客id查询旅客航班信息
        PaxInfoParseVo paxInfo = fltPassengerRealInfoService.getPassengerById(paxId);
        Map<String, Object> data = new HashMap<>();
        // 航班信息查改签航班
        FltFlightRealInfoDTO fltFlightRealInfoDTO = new FltFlightRealInfoDTO();
        fltFlightRealInfoDTO.setBeginFlightDate(flightDate);
        fltFlightRealInfoDTO.setEndFlightDate(flightDate);
        fltFlightRealInfoDTO.setFlightNumber(flightNo);
        fltFlightRealInfoDTO.setOrg(paxInfo.getOrig());
        fltFlightRealInfoDTO.setDst(paxInfo.getDest());
        FltFlightRealInfoVo flight = new FltFlightRealInfoVo();
        List<FltFlightRealInfoVo> flightInfo = fltFlightRealInfoService.getFlightInfo(fltFlightRealInfoDTO);
        if (flightInfo.size() > 0) {
            flight = flightInfo.get(0);
        }
        if (StringUtils.isNotBlank(flight.getFlightNumber())
                && flight.getFlightDate() != null) {
            data.put("flightNo", flight.getFlightNumber());
            data.put("flightDate", flight.getFlightDate());
            data.put("std", flight.getStd());
        }
        return data;
    }

    /**
     * 验证前端传入补偿金额，返回补偿金额
     */
    public String inspectPayMoney(OverBookInfoDto overBookInfoDto)
            throws ScriptException, ParseException {
        String payMoney = "-1";
        if (TYPE_REBOOK.equals(overBookInfoDto.getType()) && ObjectUtils.isNotEmpty(overBookInfoDto.getPrice())) {
            payMoney = getRebookMoney(overBookInfoDto);
        }

        if (TYPE_REFUND.equals(overBookInfoDto.getType())) {
            payMoney = getRefundMoney(overBookInfoDto);
        }
        // 后台计算 比较 前端传入补偿金额
        if (!(Double.parseDouble(overBookInfoDto.getPayMoney()) == Double.parseDouble(payMoney))) {
            log.info("补偿金额计算有误，请核对！前端计算{}，后台计算{}", overBookInfoDto.getPayMoney(), payMoney);
            throw new BusinessException(MessageCode.OVER_PAY_MONEY_ATYPISM.getCode());
        }
        return payMoney;
    }

    /**
     * Title: JudgeAuditType
     * param: [judgeAuditTypeDto]
     *
     * @return com.swcares.psi.overbook.vo.OverAuditTypeVo
     * Description: 判断当前超售补偿单审核类型，自动审核、手动审核
     * author: makai
     * date: 2020-09-28
     */
    public OverAuditTypeVo JudgeAuditType(JudgeAuditTypeDto judgeAuditTypeDto) {
        String paxId = judgeAuditTypeDto.getPaxId();
        String payWay = judgeAuditTypeDto.getPayWay();
        String flightId = judgeAuditTypeDto.getFlightId();
        String reEdit = judgeAuditTypeDto.getReEdit();
        Asserts.isNotEmpty(paxId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"paxId"});
        Asserts.isNotEmpty(payWay, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"payWay"});
        Asserts.isNotEmpty(flightId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"flightId"});
        OverAuditTypeVo overAuditTypeVo = new OverAuditTypeVo();
        overAuditTypeVo.setFirstLevelAuditRoleName(FIRST_LEVEL_AUDIT_ROLE_NAME);
        // 超售审核入口 特殊金额走手动审核流程 
        if (COMPENSATE_WAY_SPECIAL.equals(payWay)) {
            overAuditTypeVo.setAuditType(AuditTypeEnum.MANUAL_AUDIT.getKey());
        } else if (COMPENSATE_WAY_PERCENT.equals(payWay) || COMPENSATE_WAY_FIXED.equals(payWay)) {
            overAuditTypeVo.setAuditType(AuditTypeEnum.AUTO_AUDIT.getKey());
        }

        PaxInfoParseVo paxInfoParseVo = fltPassengerRealInfoService.getPassengerById(paxId);

        // 查询当前航班已经生效的的超售赔偿单
        List<OrderCorrelationInfoVo> orderCorrelationInfoVos = orderInfoDaoImpl.findOrderInfoByInfo("2", paxId,
                flightId, paxInfoParseVo.getFlightNum(), paxInfoParseVo.getFlightDate(), paxInfoParseVo.getOrig());
        OrderCorrelationInfoVo orderCorrelationPaxInfoVo = new OrderCorrelationInfoVo();
        OrderCorrelationInfoVo orderCorrelationFlightInfoVo = new OrderCorrelationInfoVo();
        for (OrderCorrelationInfoVo orderCorrelationInfoVo : orderCorrelationInfoVos) {
            if ("0".equals(orderCorrelationInfoVo.getType())) {
                orderCorrelationPaxInfoVo = orderCorrelationInfoVo;
            } else if ("1".equals(orderCorrelationInfoVo.getType())) {
                orderCorrelationFlightInfoVo = orderCorrelationInfoVo;
            }
        }
        // 判断航线预超信息是否超出 以旅客实际 OD 为准
        int flightCompensateCount = orderCorrelationFlightInfoVo.getCount();
        // 查询该航班预计超售信息
        ExpectOverBookVo expectOverBook = findExpectOverBookInfo(paxInfoParseVo);
        if (ObjectUtils.isEmpty(expectOverBook) || ObjectUtils.isEmpty(expectOverBook.getPlancount())) {
            //预计超售信息没有查询到，不允许建单
            throw new BusinessException(MessageCode.OVER_EXPECT_INFO_NOT_FOUND.getCode());
        }
        //判断当前航班超售补偿单数量 是否大于 该航班预计超售数量 
        if (ObjectUtils.isNotEmpty(expectOverBook.getPlancount()) && flightCompensateCount >= expectOverBook.getPlancount().intValue()) {
            overAuditTypeVo.setAuditType(AuditTypeEnum.MANUAL_AUDIT.getKey());
        }
        //  判断旅客是否已存在超售补偿 数量大于1 或者 数量等于1且不是重新编辑草稿
        int paxCompensateCount = orderCorrelationPaxInfoVo.getCount();
        if (paxCompensateCount > 1 || (paxCompensateCount == 1 && "0".equals(reEdit))) {
            overAuditTypeVo.setAuditType(AuditTypeEnum.MANUAL_AUDIT.getKey());
        }
        return overAuditTypeVo;
    }

    // ----------------分割 以下是私有的---------------------------------------------------

    /**
     * 保存超售信息表
     */
    private void saveOverInfo(Date createTime, String createUser, OverBookInfoDto overBookInfoDto,
                              Boolean isUpd) throws Exception {
        OverInfo overInfo = new OverInfo();
        overInfo.setId(overBookInfoDto.getOverId());
        overInfo.setOrderId(overBookInfoDto.getOrderId());
        overInfo.setType(overBookInfoDto.getType());
        overInfo.setFlightNo(overBookInfoDto.getOverBookFlightNo());
        overInfo.setFlightDate(overBookInfoDto.getOverBookFlightDate());
        overInfo.setPlanDate(overBookInfoDto.getPlaneDate());
        if (StringUtils.isEmpty(overBookInfoDto.getPrice())) {
            overBookInfoDto.setPrice("0");
        }
        overInfo.setPrice(overBookInfoDto.getPrice());
        overInfo.setPriceSpread(overBookInfoDto.getPriceSpread());
        overInfo.setHourDuration(overBookInfoDto.getHourDuration());
        overInfo.setPayWay(overBookInfoDto.getPayWay());
        overInfo.setSpecialPrice(overBookInfoDto.getSpecialPrice());
        overInfo.setPercentPrice(overBookInfoDto.getPercentPrice());
        overInfo.setFixedPrice(overBookInfoDto.getFixedPrice());
        overInfo.setAuditId(overBookInfoDto.getAuditIds());
        overInfo.setStandardPrice(overBookInfoDto.getStandardPrice());
        String imgUrlStr = "";
        if (StringUtils.isNotBlank(overBookInfoDto.getImgUrl())) {
            String[] imgUrls = overBookInfoDto.getImgUrl().split(",");
            String tempStr = "";
            for (String imgUrl : imgUrls) {
                tempStr = imgUrl;
                if (imgUrl.contains("/temp/")) {
                    tempStr = uploadAndDownload.saveImg(imgUrl);
                }
                imgUrlStr += tempStr;
                imgUrlStr += ",";
            }
            overInfo.setAttachment(imgUrlStr.substring(0, imgUrlStr.length() - 1));
        }
        if (!isUpd) {
            overInfo.setCreateUser(createUser);
            overInfo.setCreateTime(createTime);
        }
        overInfoDao.save(overInfo);

    }

    /**
     * 保存赔付标准, 以补偿金额为准
     */
    private void saveCompensateInfo(Date createTime, String createUser,
                                    OverBookInfoDto overBookInfoDto) {
        CompensatesInfo compensatesInfo = new CompensatesInfo();
        compensatesInfo.setCreateId(createUser);
        compensatesInfo.setCreateTime(createTime);
        if (StringUtils.isNotEmpty(overBookInfoDto.getPayMoney())) {
            compensatesInfo.setCpsNum(Integer.parseInt(overBookInfoDto.getPayMoney()));
        } else {
            compensatesInfo.setCpsNum(0);
        }
        compensatesInfo.setClassType("3");
        compensatesInfo.setOrderId(overBookInfoDto.getOrderId());
        compensatesInfo.setAccidentId(overBookInfoDto.getOverId());
        compensatesInfoDao.save(compensatesInfo);
    }

    /**
     * 服务单保存
     */
    private OrderInfo saveOrderInfo(Date createTime, String createUser, FltFlightRealInfoVo fltFlightRealInfoVo,
                                    OverBookInfoDto overBookInfoDto) {

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setPayType(Integer.valueOf(PAY_TYPE_OVER));// 补偿类型 不正常航班补偿0、异常行李1、超售旅客2、
        orderInfo.setOrderId(overBookInfoDto.getOrderId());
        orderInfo.setAccidentId(overBookInfoDto.getOverId());
        orderInfo.setFlightId(fltFlightRealInfoVo.getId());
        orderInfo.setFlightNo(fltFlightRealInfoVo.getFlightNumber());
        orderInfo.setFlightDate(overBookInfoDto.getFlightDate());
        orderInfo.setCreateTime(createTime);
        orderInfo.setCreateId(createUser);
        orderInfo.setStatus(SAVE_DRAFT);// 默认发起 草稿
        orderInfo.setServiceCity(overBookInfoDto.getServiceCity()); // 旅客出发地
        //默认领取方式 优惠券
        orderInfo.setPaymentClass(PAYMENT_TYPE_ONLINE);
        String payMoney = "0";
        if (StringUtils.isNotBlank(overBookInfoDto.getPayMoney())) {
            payMoney = overBookInfoDto.getPayMoney();
        }
        orderInfo.setSumMoney(Integer.parseInt(payMoney));
        orderInfo.setChoiceSegment(overBookInfoDto.getSegment());
        orderInfoDao.save(orderInfo);
        return orderInfo;

    }

    /**
     * 保存航班信息
     */
    private void saveCompensateFlightInfo(Date createTime, String createUser,
                                          FltFlightRealInfoVo fltFlightRealInfoVo, OverBookInfoDto overBookInfoDto) {

        FlightsInfo flightInfo = new FlightsInfo();
        flightInfo.setOrderId(overBookInfoDto.getOrderId());
        flightInfo.setFlightNo(fltFlightRealInfoVo.getFlightNumber());
        flightInfo.setFlightDate(overBookInfoDto.getFlightDate());
        flightInfo.setSegment(fltFlightRealInfoVo.getOrgName() + "-" + fltFlightRealInfoVo.getDstName());
        flightInfo.setOrg(fltFlightRealInfoVo.getOrg());
        flightInfo.setDst(fltFlightRealInfoVo.getDst());
        flightInfo.setCreateTime(createTime);
        flightInfo.setCreateId(createUser);
        flightInfo.setFlightId(fltFlightRealInfoVo.getId());
        flightInfo.setStd(fltFlightRealInfoVo.getSegmentSTD());
        flightInfo.setSta(fltFlightRealInfoVo.getSegmentSTA());
        flightInfo.setAcType(fltFlightRealInfoVo.getFltCode());
        flightInfo.setFlightType(fltFlightRealInfoVo.getFlightType());
        flightInfoDao.save(flightInfo);
    }

    /**
     * 保存旅客信息
     */
    private void savePaxInfo(Date createTime, String createUser, String orderId,
                             OverBookInfoDto overBookInfoDto, PaxInfoParseVo tPaxInfo) {

        PassengerInfo paxInfo = new PassengerInfo();
        paxInfo.setOrderId(orderId);
        paxInfo.setPaxId(overBookInfoDto.getPaxId());
        paxInfo.setTelephone(AesEncryptUtil.encrypt(overBookInfoDto.getTelephone()));

        paxInfo.setPaxName(tPaxInfo.getPsgName());
        paxInfo.setIdNo(AesEncryptUtil.encrypt(tPaxInfo.getIdNum()));
        paxInfo.setIdType(tPaxInfo.getIdType());
        paxInfo.setSex(tPaxInfo.getGender());
        paxInfo.setSegment(tPaxInfo.getSegment().replace(" ", ""));
        paxInfo.setPaxStatus(tPaxInfo.getStatus());
        paxInfo.setTktNo(tPaxInfo.getEtNum());
        paxInfo.setBabyName(tPaxInfo.getInfantName());
        paxInfo.setPkgNo(tPaxInfo.getBagTag());
        paxInfo.setPkgWeight(tPaxInfo.getBagWht());
        paxInfo.setIsChild(tPaxInfo.getIsChild());
        paxInfo.setOrgCityAirp(tPaxInfo.getOrig());
        paxInfo.setDstCityAirp(tPaxInfo.getDest());
        paxInfo.setPnr(tPaxInfo.getPnrRef());
        paxInfo.setPassengerType(tPaxInfo.getPassengerType());
        paxInfo.setWithBaby(tPaxInfo.getWithBaby());
        paxInfo.setReceiveStatus(0);
        String payMoney = "0";
        if (StringUtils.isNotBlank(overBookInfoDto.getPayMoney())) {
            payMoney = overBookInfoDto.getPayMoney();

        }
        paxInfo.setCurrentAmount(Integer.parseInt(payMoney));
        paxInfo.setIsFlag(0);
        paxInfo.setSwitchOff(0);
        paxInfo.setCreateId(createUser);
        paxInfo.setCreateTime(createTime);
        paxInfo.setMainClass(tPaxInfo.getMainClass());
        paxInfo.setSubClass(tPaxInfo.getSubClass());
        paxInfoDao.save(paxInfo);
    }


    private String getRefundMoney(OverBookInfoDto overBookInfoDto) throws ScriptException {
        RefundInfoVo refundInfo = overBookConfigDaoImpl.getRefundInfo();
        if (StringUtils.isEmpty(refundInfo.getPayMoney())) {
            log.info("超售退票查无对应规则！请联系管理员配置！");
            throw new BusinessException(MessageCode.OVER_REFUND_RULE_IS_NULL.getCode());
        }
        return refundInfo.getPayMoney();
    }

    /**
     * 改签补偿金额计算
     */
    private String getRebookMoney(OverBookInfoDto overBookInfoDto)
            throws ScriptException, ParseException {
        String result = "";
        String money = "-1";
        List<TimeDifferenceInfoVo> configList = new ArrayList<>();
        List<TimeDifferenceInfoVo> bookList =
                overBookConfigDaoImpl.getTimeDifferenceInfo(TYPE_REBOOK);

        // 拼接时间
        String startTime =
                overBookInfoDto.getFlightDate() + " " + overBookInfoDto.getOldPlaneDate();
        String endTime =
                overBookInfoDto.getOverBookFlightDate() + " " + overBookInfoDto.getPlaneDate();
        // 计算时差
        String time = getHours(startTime, endTime);
        // 规则成立并返回规则比例值
        for (TimeDifferenceInfoVo tdi : bookList) {
            if (StringUtils.isBlank(tdi.getLeftConditionValue())
                    || StringUtils.isBlank(time) || StringUtils.isBlank(tdi.getDamagePart())) {
                continue;
            }
            // 四舍五入计算
            long timeDiff = Math.round(Math.floor(Double.parseDouble(time)));
            long leftConditionValue = Long.parseLong(tdi.getLeftConditionValue());
            long conditionValue = 0;
            if (StringUtils.isNotEmpty(tdi.getConditionValue())) {
                conditionValue = Long.parseLong(tdi.getConditionValue());
            }
            // 2021-03-23 变更为leftConditionValue<=时差<conditionValue
            if (leftConditionValue <= timeDiff) {
                if ((conditionValue == 0) ||
                        ((conditionValue > 0) && timeDiff < conditionValue)) {
                    configList.add(tdi);
                }
            }
        }
        if (configList.size() < 1) {
            log.info("改签航班时差{}，查无对应规则！请联系管理员配置！", time);
            throw new BusinessException(MessageCode.OVER_TIME_DIFFERENCE_RULE_IS_NULL.getCode());
        }
        // 改签 百分比补偿
        if (COMPENSATE_WAY_PERCENT.equals(overBookInfoDto.getPayWay())) {
            // 取符合条件的规则
            result = configList.get(0).getDamagePart();
            // 根据比例计算补偿金
            money = String.valueOf(NumberUtil.mul(overBookInfoDto.getPrice(), result, "0.01"));
            //改签 固定金额补偿
        } else if (COMPENSATE_WAY_FIXED.equals(overBookInfoDto.getPayWay())) {
            money = configList.get(0).getPayMoney();
        }
        return money;
    }

    /**
     * 得到两个时间的时间差
     */
    private String getHours(String startTime, String endTime) throws ParseException {
        Date time = df.parse(startTime);
        Date time2 = df.parse(endTime);
        long timeout = time2.getTime() - time.getTime();
        double quot = 0.00;
        quot = ((double) timeout) / (1000 * 60 * 60);
        DecimalFormat formater = new DecimalFormat();
        formater.setMaximumFractionDigits(2);
        formater.setGroupingSize(0);
        formater.setRoundingMode(RoundingMode.FLOOR);
        return formater.format(quot);
    }

    /**
     * 得到两个时间的时间差 天 小时 分
     */
    private String getHoursMin(String startTime, String endTime) throws ParseException {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        Date time = df.parse(startTime);
        Date time2 = df.parse(endTime);
        long diff = time2.getTime() - time.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        StringBuffer strTime = new StringBuffer();
        if (day != 0) {
            strTime.append(day).append("天 ");
        }
        return strTime.append(hour).append("小时 ").append(min).append("分钟").toString();
    }

    // H5 WEB 查询超售配置信息
    private Map<String, Object> getConfigInfoByType() {

        Map<String, Object> dataMap = new HashMap<>();
        // 查询改签比例
        List<TimeDifferenceInfoVo> timeDifferenceInfoList = overBookConfigDaoImpl.getTimeDifferenceInfo(TYPE_REBOOK);
        RefundInfoVo refundInfo = overBookConfigDaoImpl.getRefundInfo();
        //设置默认值
        if (ObjectUtils.isEmpty(timeDifferenceInfoList) || null == refundInfo) {
            TimeDifferenceInfoVo timeDifferenceInfoVo = new TimeDifferenceInfoVo();
            RefundInfoVo refundInfoVo = new RefundInfoVo();
            Date createTime = new Date();
            overBookConfigDao.deleteAll();
            OverBookConfigInfo dateChangeRule = new OverBookConfigInfo();
            dateChangeRule.setPayType("0");
            dateChangeRule.setType("0");
            dateChangeRule.setPayMoney("300");
            dateChangeRule.setDamagePart("20");
            dateChangeRule.setStatus("0");
            dateChangeRule.setConditionValue("2");
            dateChangeRule.setLeftConditionValue("0");
            dateChangeRule.setCreateUser(AUTO_AUDIT_TUNO);
            dateChangeRule.setCreateTime(createTime);
            overBookConfigDao.save(dateChangeRule);
            OverBookConfigInfo refundRule = new OverBookConfigInfo();
            refundRule.setType("1");
            refundRule.setPayType("1");
            refundRule.setPayMoney("300");
            refundRule.setStatus("0");
            refundRule.setCreateUser(AUTO_AUDIT_TUNO);
            refundRule.setCreateTime(createTime);
            overBookConfigDao.save(refundRule);
            BeanUtils.copyProperties(dateChangeRule, timeDifferenceInfoVo);
            BeanUtils.copyProperties(refundRule, refundInfoVo);
            timeDifferenceInfoList.add(timeDifferenceInfoVo);
            refundInfo = refundInfoVo;
        }
        dataMap.put("timeDifferenceInfo", timeDifferenceInfoList);
        // 退票比例 最低赔付
        dataMap.put("refundInfo", refundInfo);
        return dataMap;
    }

    private ExpectOverBookVo findExpectOverBookInfo(PaxInfoParseVo paxInfoParseVo) {
        ExpectOverBookDto expectOverBookDto = new ExpectOverBookDto();
        expectOverBookDto.setFlightNum(paxInfoParseVo.getFlightNum());
        expectOverBookDto.setAirportS(paxInfoParseVo.getOrig());
        expectOverBookDto.setFlightDateStart(paxInfoParseVo.getFlightDate());
        expectOverBookDto.setFlightDateEnd(paxInfoParseVo.getFlightDate());
        List<ExpectOverBookVo> expectOverBookInfos = expectOverBookService.getExpectOverBookInfo(expectOverBookDto);
        if (ObjectUtils.isEmpty(expectOverBookInfos)) {
            return null;
        } else {
            return expectOverBookInfos.get(0);
        }
    }
}