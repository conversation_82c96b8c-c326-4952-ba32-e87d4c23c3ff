<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.stimulate.mapper.StimulateCommonMapper">


    <sql id="orderAll">
                 ( SELECT
                     IFNULL(a.ORDER_PRICE, 0)/100 as price,
                     CASE a.CURRENCY_SPECIES
                         WHEN 'CNY' THEN '人民币'
                         else
                         (SELECT  aac.DATA_VALUE FROM SYS_DICT aac WHERE aac.DATA_TYPE_CODE='currency_type' AND aac.DATA_STATUS='0' and aac.DATA_CODE=a.CURRENCY_SPECIES limit 1)
                     end as currencySpecies,
                     a.CURRENCY_SPECIES as currencySpeciesCode
                    FROM ao_order_info a
                    where (
                            (a.ORDER_STATUS = '2' and a.PAY_STATUS = '1')
                            or
                            (a.ORDER_STATUS = '4' and a.PAY_STATUS = '4')
                        )
                      and a.USER_NO = #{userNo}
                      and a.ORDER_DATE &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                      and a.ORDER_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                     <if test="orderTypeCode != null  and orderTypeCode != ''">
                         AND find_in_set(a.ORDER_TYPE_CODE, #{orderTypeCode})
                     </if>
                  )union all(
                    select
                     IFNULL(b.PRACTICAL_ORDER_PRICE, 0) as price,
                     ampoi.CURRENCY_SPECIES_NAME as currencySpecies,
                     ampoi.CURRENCY_SPECIES as currencySpeciesCode
                    from ao_merge_suborder_info b
                    left join ao_merge_order_info amoi on amoi.id=b.AO_MERGE_ORDER_INFO_ID
                    left join ao_merge_pay_order_info ampoi  on ampoi.id=amoi.ORDER_PAY_ID
                    where  b.SUBORDER_ORDER_STATUS ='1'
                    and amoi.CREATE_NO=#{userNo}
                    and ampoi.CREATE_DATE &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and ampoi.CREATE_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    <if test="orderTypeCode != null  and orderTypeCode != ''">
                        AND find_in_set(b.ORDER_TYPE, #{orderTypeCode})
                    </if>
                  )union all(
                    select
                     IFNULL(apoi.PRICE, 0)/100 as price,
                     apoi.CURRENCY_SPECIES as currencySpecies,
                     apoi.CURRENCY_SPECIES_CODE as currencySpeciesCode
                    from AO_ORDER_INFO_NEW c
                    left join AO_PAY_ORDER_INFO apoi  on apoi.AO_ORDER_ID=c.id
                    where  c.ORDER_STATUS ='2'
                    and c.USER_NO=#{userNo}
                    and c.CREATE_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and c.CREATE_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    <if test="orderTypeCode != null  and orderTypeCode != ''">
                        AND find_in_set(c.ORDER_TYPE_CODE, #{orderTypeCode})
                    </if>
                  )union all(
                    select
                     IFNULL(d.ORDER_PRICE, 0) as price,
                     d.CURRENCY_SPECIES as currencySpecies,
                     d.CURRENCY_SPECIES_CODE as currencySpeciesCode
                    from AO_ORDER_IMPORT d
                    where  d.ORDER_STATUS='1'
                    and d.ORDER_CREATE_NO=#{userNo}
                    and d.ORDER_DATE &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d')
                    and d.ORDER_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d')
                    <if test="orderTypeCode != null  and orderTypeCode != ''">
                        AND find_in_set(d.ORDER_TYPE, #{orderTypeCode})
                    </if>
                  )
    </sql>


    <select id="sellChampion" resultType="com.swcares.psi.stimulate.vo.AoSellDetailStatisticsVo$SellChampionvo">
    select
      a.*
    from (
                select
                sum(asod.STIMULATE_PRICE) as stimulatePrice,
                asod.STIMULATE_USER,
                asod.STIMULATE_USER_NO,
                asod.STIMULATE_DEPARTMENT,
                asod.STIMULATE_DEPARTMENT_NAME,
                asod.STIMULATE_ADMINISTRATIVE,
                asod.STIMULATE_ADMINISTRATIVE_NAME
                from AO_STIMULATE_ORDER_DETAILS asod
                inner join AO_STIMULATE_ORDER_INFO asoi  on asod.STIMULATE_ORDER_ID=asoi.id

                where asod.STIMULATE_TYPE='1'  and asod.HISTORY='0' and asoi.IS_CANCEL='N'

                and asoi.ORDER_TIME  <![CDATA[ >= ]]> date_format(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                and asoi.ORDER_TIME  <![CDATA[ <= ]]> date_format(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                <if test="orderTypeCode != null  and orderTypeCode != ''">
                    AND find_in_set(asoi.ORDER_TYPE, #{orderTypeCode})
                </if>
                and exists(
                        ( SELECT
                                    1
                                    FROM ao_order_info a
                                    where   a.id=asoi.ORDER_ID and (
                                            (a.ORDER_STATUS = '2' and a.PAY_STATUS = '1')
                                            or
                                            (a.ORDER_STATUS = '4' and a.PAY_STATUS = '4')
                                        )

                                  )union all(
                                    select
                                     1
                                    from ao_merge_suborder_info b
                                    where  b.id=asoi.ORDER_ID and b.SUBORDER_ORDER_STATUS ='1'
                                  )union all(
                                    select
                                   1
                                    from AO_ORDER_INFO_NEW c
                                    where  c.id=asoi.ORDER_ID and c.ORDER_STATUS ='2'
                                  )union all(
                                    select
                                    1
                                    from AO_ORDER_IMPORT d
                                    where  d.id=asoi.ORDER_ID and d.ORDER_STATUS='1'
                                  )

                        )
              group by asod.STIMULATE_USER,  asod.STIMULATE_USER_NO,  asod.STIMULATE_DEPARTMENT, asod.STIMULATE_DEPARTMENT_NAME,
               asod.STIMULATE_ADMINISTRATIVE,asod.STIMULATE_ADMINISTRATIVE_NAME
        ) a
    order by a.stimulatePrice desc limit 1


    </select>



    <select id="sellCurrencySpeciesPrice" resultType="com.swcares.psi.stimulate.vo.AoSellDetailStatisticsVo$CurrencySpeciesPrice">

        select
        IFNULL(SUM(x.price), 0) as price,
        CONCAT(x.currencySpeciesCode,'(', x.currencySpecies,')')  as currencySpecies
          from (
            <include refid="orderAll"></include>
          ) x  group by x.currencySpeciesCode

    </select>

    <select id="stimulateOrder" resultType="com.swcares.psi.stimulate.vo.AoSellDetailStatisticsVo$StimulateOrder">
        select
        count(distinct asoi.ID) as stimulateOrderAmount,
        sum(asod.STIMULATE_PRICE) as stimulatePrice
        from AO_STIMULATE_ORDER_DETAILS asod

        left join AO_STIMULATE_ORDER_INFO asoi  on asod.STIMULATE_ORDER_ID=asoi.id

        where asod.HISTORY='0' and asoi.STATUS='1' AND asoi.IS_CANCEL='N' and asoi.ORDER_STATUS='1'
        AND ASOD.STIMULATE_USER_NO=#{userNo}
        and asoi.ORDER_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
        and asoi.ORDER_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
        <if test="orderTypeCode != null  and orderTypeCode != ''">
            AND find_in_set(asoi.ORDER_TYPE, #{orderTypeCode})
        </if>
        and exists(


        ( SELECT
                    1
                    FROM ao_order_info a
                    where   a.id=asoi.ORDER_ID and (
                            (a.ORDER_STATUS = '2' and a.PAY_STATUS = '1')
                            or
                            (a.ORDER_STATUS = '4' and a.PAY_STATUS = '4')
                        )

                  )union all(
                    select
                     1
                    from ao_merge_suborder_info b
                    where  b.id=asoi.ORDER_ID and b.SUBORDER_ORDER_STATUS ='1'
                    and  not exists(
                        select
                          1
                        from ao_merge_refund_order_info br
                        where br.AO_MERGE_SUBORDER_INFO_ID=b.id and br.AO_MERGE_ORDER_INFO_ID=b.AO_MERGE_ORDER_INFO_ID
                        and br.status='1' and br.REFUND_STATUS in ('0','1','3')

                    )
                  )union all(
                    select
                   1
                    from AO_ORDER_INFO_NEW c
                    where  c.id=asoi.ORDER_ID and c.ORDER_STATUS = '2'
                  )union all(
                    select
                    1
                    from AO_ORDER_IMPORT d
                    where  d.id=asoi.ORDER_ID and d.ORDER_STATUS='1'
                  )

        )





    </select>

  <select id="orderAmount" resultType="java.lang.Integer">
      select
      COUNT(ss.currencySpeciesCode)
      from (
      <include refid="orderAll"></include>
      ) ss
  </select>



<select id="stimulateOrOrder" resultType="com.swcares.psi.stimulate.vo.AoSellDetailStatisticsVo$DetailsOrderVo">
    select * from (
    <include refid="orderData"></include>
    )temp
    where 1=1

    <if test="dto.orderTypeCode != null and dto.orderTypeCode != ''">
        AND find_in_set(temp.orderTypeCode, #{dto.orderTypeCode})
    </if>

    <if test="dto.label != null and dto.label != ''">

        and (
        1=2
            <if test="dto.refund != null and dto.refund != ''">
                or temp.orderStatus='2'
            </if>

            <if test="dto.review != null and dto.review != ''">
                or (temp.reviewUser is null  and temp.orderTypeCode='6')

            </if>

            <if test="dto.exchangeRate != null and dto.exchangeRate != ''">
                or (temp.exchangeRateId is null  and temp.currencySpecies &lt;&gt;'CNY')

            </if>

            <if test="dto.productCost != null and dto.productCost != ''">
                or (temp.productCostId is null  and temp.orderTypeCode ='4')

            </if>
             <if test="dto.cancelStimulate != null and dto.cancelStimulate != ''">
                or (temp.cancelCause is not null  )

            </if>


        )


    </if>
   GROUP BY temp.orderId,temp.paxName  order by temp.orderDate desc


</select>

<sql id="orderData">
     (
                    SELECT
                     aso.REMARKS as remarks,
                     a.id as orderId,
                     a.ORDER_DATE as orderDate,
                     aso.VERSIONS as versions,
                     a.PAX_NAME as paxName,
                     CASE
                        when a.ORDER_TYPE_CODE = '01' then '5'
                        when a.ORDER_TYPE_CODE = '02' then '3'
                        else a.ORDER_TYPE_CODE
                     END as orderTypeCode,
                     CASE
                        WHEN a.ORDER_TYPE_CODE = '1' THEN '一人多座'
                        WHEN a.ORDER_TYPE_CODE = '2' THEN '付费选座'
                        WHEN a.ORDER_TYPE_CODE = '3' THEN '逾重行李'
                        WHEN a.ORDER_TYPE_CODE = '4' THEN '贵宾室'
                        when a.ORDER_TYPE_CODE = '5' then '柜台升舱'
                        when a.ORDER_TYPE_CODE = '01' then '柜台升舱'
                        when a.ORDER_TYPE_CODE = '02' then '逾重行李'
                    END AS orderType,
                    a.ORDER_PRICE/100 as orderPrice,
                    CASE
                        WHEN a.ORDER_STATUS = '2' THEN '1'
                        WHEN a.ORDER_STATUS = '3' THEN '1'
                        WHEN a.ORDER_STATUS = '4' THEN '1'
                        WHEN a.ORDER_STATUS = '5' THEN '2'
                        WHEN a.ORDER_STATUS = '7' THEN '1'
                    END AS orderStatus,
                    a.CURRENCY_SPECIES AS currencySpecies,
                    asod.STIMULATE_PRICE AS stimulatePrice,
                    aso.EXCHANG_RATE_ID as exchangeRateId,
                    aso.PRODUCT_COST_ID as productCostId,
                    0 as reviewEnd,
                    null as reviewUser,
                    aso.IS_CANCEL as stimulateCancel,
                    aso.CANCEL_CAUSE as cancelCause
                    FROM ao_order_info a
                    left join   AO_STIMULATE_ORDER_INFO aso  on a.id=aso.ORDER_ID and aso.VERSIONS='1' and aso.STATUS='1'
                    left join AO_STIMULATE_ORDER_DETAILS  asod on asod.STIMULATE_ORDER_ID=aso.id  and asod.STIMULATE_TYPE='1'  and asod.HISTORY='0'
                    where (
                             a.USER_NO = #{dto.userNo}
                            or
                            asod.STIMULATE_USER_NO = #{dto.userNo}
                        )

                      and a.ORDER_DATE &gt;= DATE_FORMAT(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
                      and a.ORDER_DATE &lt;= DATE_FORMAT(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
                      and (
                            a.ORDER_STATUS ='2' or a.ORDER_STATUS ='4' or (a.ORDER_STATUS ='5' and aso.id is not  null)
                            )

                  )union all(
                     SELECT
                        aso.REMARKS as remarks,
                        b.id as orderId,
                        amoi.CREATE_DATE as orderDate,
                        aso.VERSIONS as versions,
                        b.PAX_NAME as paxName,
                        b.ORDER_TYPE as orderTypeCode,
                        b.ORDER_TYPE_NAME AS orderType,
                        b.PRACTICAL_ORDER_PRICE as orderPrice,
                        CASE
                            WHEN b.SUBORDER_ORDER_STATUS = '1' THEN '1'
                            WHEN b.SUBORDER_ORDER_STATUS = '2' THEN '1'
                            WHEN b.SUBORDER_ORDER_STATUS = '3' THEN '1'
                            WHEN b.SUBORDER_ORDER_STATUS = '4' THEN '2'
                            WHEN b.SUBORDER_ORDER_STATUS = '7' THEN '1'
                        END AS orderStatus,
                        ampoi.CURRENCY_SPECIES AS currencySpecies,
                        asod.STIMULATE_PRICE AS stimulatePrice,
                        aso.EXCHANG_RATE_ID as exchangeRateId,
                        aso.PRODUCT_COST_ID as productCostId,
                        0 as reviewEnd,
                        null as reviewUser,
                        aso.IS_CANCEL as stimulateCancel,
                        aso.CANCEL_CAUSE as cancelCause
                    from ao_merge_suborder_info b
                    left join ao_merge_order_info amoi on amoi.id=b.AO_MERGE_ORDER_INFO_ID
                    left join ao_merge_pay_order_info ampoi  on ampoi.id=amoi.ORDER_PAY_ID
                    left join AO_STIMULATE_ORDER_INFO aso  on b.id=aso.ORDER_ID and aso.VERSIONS='3' and aso.STATUS='1'
                    left join AO_STIMULATE_ORDER_DETAILS  asod on asod.STIMULATE_ORDER_ID=aso.id  and asod.STIMULATE_TYPE='1'  and asod.HISTORY='0'
                    where
                     (
                            b.SUBORDER_ORDER_STATUS ='1' or  (b.SUBORDER_ORDER_STATUS ='4' and aso.id is not  null)
                            )
                    and (
                            amoi.CREATE_NO = #{dto.userNo}
                            or
                            asod.STIMULATE_USER_NO = #{dto.userNo}
                        )

                    and ampoi.CREATE_DATE &gt;= DATE_FORMAT(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
                    and ampoi.CREATE_DATE &lt;= DATE_FORMAT(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
                  )union all(
                    select
                        aso.REMARKS as remarks,
                        c.id as orderId,
                        c.CREATE_TIME as orderDate,
                        aso.VERSIONS as versions,
                        aocu.PAX_NAME as paxName,
                        c.ORDER_TYPE_CODE as orderTypeCode,
                        CASE
                            WHEN c.ORDER_TYPE_CODE = '1' THEN '一人多座'
                            WHEN c.ORDER_TYPE_CODE = '2' THEN '付费选座'
                            WHEN c.ORDER_TYPE_CODE = '3' THEN '逾重行李'
                            WHEN c.ORDER_TYPE_CODE = '4' THEN '贵宾室'
                            when c.ORDER_TYPE_CODE = '5' then '柜台升舱'
                            when c.ORDER_TYPE_CODE = '6' then '机上升舱'
                        END AS orderType,
                        aocu.PRICE/100 as orderPrice,
                        CASE
                            WHEN c.ORDER_STATUS = '2' THEN '1'
                            WHEN c.ORDER_STATUS = '4' THEN '2'
                        END AS orderStatus,
                        apoi.CURRENCY_SPECIES_CODE AS currencySpecies,
                        sum(asod.STIMULATE_PRICE) AS stimulatePrice,
                        aso.EXCHANG_RATE_ID as exchangeRateId,
                        aso.PRODUCT_COST_ID as productCostId,
                        if( (select count(x.id) from AO_OLP_COST_UPGRADES x  where aocu.AO_ORDER_ID=x.AO_ORDER_ID  and x.REVIEW_USER  is null ) = 0,1,0) as reviewEnd,
                        aocu.REVIEW_USER as reviewUser,
                        aso.IS_CANCEL as stimulateCancel,
                        aso.CANCEL_CAUSE as cancelCause
                    from AO_ORDER_INFO_NEW c
                    left join AO_STIMULATE_ORDER_INFO aso  on c.id=aso.ORDER_ID and aso.VERSIONS='2' and aso.STATUS='1'
                    left join AO_OLP_COST_UPGRADES aocu on aocu.AO_ORDER_ID=c.id
                    left join AO_PAY_ORDER_INFO apoi  on apoi.AO_ORDER_ID=c.id
                    left join AO_STIMULATE_ORDER_DETAILS  asod on asod.STIMULATE_ORDER_ID=aso.id   and aocu.id=asod.AO_OLP_COST_UPGRADES_id and asod.STIMULATE_TYPE='1'  and asod.HISTORY='0'
                    where  (
                             c.ORDER_STATUS in ('2','4')
                         or
                             (
                                c.ORDER_STATUS='5'
                                and EXISTS (
                                    select 1 from  AO_STIMULATE_ORDER_INFO aso2 where  c.id=aso.ORDER_ID and aso2.VERSIONS='2' and aso2.STATUS='1' and aso2.IS_CANCEL='Y'
                                )
                            )
                        )
                    and (
                             (asod.STIMULATE_USER_NO  is null  and  c.USER_NO = #{dto.userNo})
                            or
                            asod.STIMULATE_USER_NO = #{dto.userNo}
                        )

                    and c.CREATE_TIME &gt;= DATE_FORMAT(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
                    and c.CREATE_TIME &lt;= DATE_FORMAT(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
                    group by c.id,aocu.PAX_NAME
                  )union all(
                    select
                        aso.REMARKS as remarks,
                        d.id as orderId,
                        d.ORDER_DATE as orderDate,
                        aso.VERSIONS as versions,
                        d.PAX_NAME as paxName,
                        d.ORDER_TYPE as orderTypeCode,
                        CASE
                            WHEN d.ORDER_TYPE = '1' THEN '一人多座'
                            WHEN d.ORDER_TYPE = '2' THEN '付费选座'
                            WHEN d.ORDER_TYPE = '3' THEN '逾重行李'
                            WHEN d.ORDER_TYPE = '4' THEN '贵宾室'
                            when d.ORDER_TYPE = '5' then '柜台升舱'
                        END AS orderType,
                        d.ORDER_PRICE as orderPrice,
                        d.ORDER_STATUS as  orderStatus,
                        d.CURRENCY_SPECIES_CODE AS currencySpecies,
                        asod.STIMULATE_PRICE AS stimulatePrice,
                        aso.EXCHANG_RATE_ID as exchangeRateId,
                        aso.PRODUCT_COST_ID as productCostId,
                        0 as reviewEnd,
                        null as reviewUser,
                        aso.IS_CANCEL as stimulateCancel,
                        aso.CANCEL_CAUSE as cancelCause
                    from AO_ORDER_IMPORT d
                    left join AO_STIMULATE_ORDER_INFO aso  on d.id=aso.ORDER_ID and aso.VERSIONS='4' and aso.STATUS='1'
                    left join AO_STIMULATE_ORDER_DETAILS  asod on asod.STIMULATE_ORDER_ID=aso.id  and asod.STIMULATE_TYPE='1'  and asod.HISTORY='0'
                    where  d.ORDER_STATUS in ('2','1')
                    and (
                            d.ORDER_CREATE_NO = #{dto.userNo}
                            or
                            asod.STIMULATE_USER_NO = #{dto.userNo}
                        )
                    and d.ORDER_DATE &gt;= DATE_FORMAT(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
                    and d.ORDER_DATE &lt;= DATE_FORMAT(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
                  )
</sql>



<select id="getDelaySitmulateOrderInfo" resultType="com.swcares.psi.stimulate.vo.DelaySitmulateOrderInfoVo">

                   ( SELECT
                     '1' as versions,
                     a.id as orderId,
                       CASE
                        when a.ORDER_TYPE_CODE = '01' then '5'
                        when a.ORDER_TYPE_CODE = '02' then '3'
                        else a.ORDER_TYPE_CODE
                     END as orderTypeCode ,
                     null as subOrderId
                    FROM ao_order_info a
                    where a.ORDER_DATE&gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and a.ORDER_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    and a.ORDER_STATUS in ('2','4')
                    and a.id not in (
                    select distinct(aso.ORDER_ID) from AO_STIMULATE_ORDER_INFO aso  where aso.VERSIONS='1' and aso.STATUS='1'
                    AND aso.ORDER_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and aso.ORDER_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    )
                  )union(
                    select
                     '3' as versions,
                     b.AO_MERGE_ORDER_INFO_ID as orderId,
                     b.ORDER_TYPE as orderTypeCode,
                     b.id as subOrderId
                    from ao_merge_suborder_info b
                    left join ao_merge_order_info amoi on amoi.id=b.AO_MERGE_ORDER_INFO_ID
                    where amoi.CREATE_DATE&gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and amoi.CREATE_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    and b.SUBORDER_ORDER_STATUS ='1'
                    and b.id not in (
                    select distinct(aso.ORDER_ID) from AO_STIMULATE_ORDER_INFO aso  where aso.VERSIONS='3' and aso.STATUS='1'
                    AND aso.ORDER_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and aso.ORDER_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    )
                  )union(
                    select
                     '2' as versions,
                     c.id as orderId,
                     c.ORDER_TYPE_CODE as orderTypeCode,
                     null as subOrderId
                    from AO_ORDER_INFO_NEW c
                    where  c.ORDER_STATUS='2'
                    and c.CREATE_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and c.CREATE_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    and c.id not in (
                    select distinct(aso.ORDER_ID) from AO_STIMULATE_ORDER_INFO aso  where aso.VERSIONS='2' and aso.STATUS='1'
                    AND aso.ORDER_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and aso.ORDER_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    )
                  )union(
                    select
                      '4' as versions,
                     d.id as orderId,
                     d.ORDER_TYPE as orderTypeCode,
                     null as subOrderId
                    from AO_ORDER_IMPORT d
                    where  d.ORDER_STATUS='1'
                    and d.ORDER_DATE &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d')
                    and d.ORDER_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d')
                    and d.id not in (
                    select distinct(aso.ORDER_ID) from AO_STIMULATE_ORDER_INFO aso  where aso.VERSIONS='4' and aso.STATUS='1'
                    AND aso.ORDER_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                    and aso.ORDER_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                    )
                  )
</select>




<select id="getUpdateSitmulateOrderInfo" resultType="com.swcares.psi.stimulate.entity.AoStimulateOrderInfo">
    select
        *
    from AO_STIMULATE_ORDER_INFO
    where
    ORDER_TIME &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
     and ORDER_TIME &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
     and STATUS='1'
    <if test="currencySpeciesCode != null and currencySpeciesCode != ''">
      and  CURRENCY_SPECIES_CODE=#{currencySpeciesCode}
    </if>
    <if test="orderType != null and orderType != ''">
        and  ORDER_TYPE=#{orderType}
    </if>


</select>


<select id="vipCostOrderTotalPriceByCurrencySpecies"  resultType="com.swcares.psi.stimulate.utils.StimulateOrderUtils$VipCostOrderTotalPriceByCurrencySpeciesVo">

            select

             xx.currencySpecies,
             sum(xx.price) as price
             from (
                       ( SELECT
                            a.CURRENCY_SPECIES as currencySpecies,
                            a.ORDER_PRICE/100  as price
                        FROM ao_order_info a
                        where a.ORDER_TYPE_CODE='4'
                        and a.ORDER_DATE&gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                        and a.ORDER_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                        and a.ORDER_STATUS in ('2','4')
                        and a.SERVICE_PORT=#{servicePort}
                      )union(
                        select
                          ampoi.CURRENCY_SPECIES as currencySpecies,
                          b.PRACTICAL_ORDER_PRICE  as price
                        from ao_merge_suborder_info b
                        left join ao_merge_order_info amoi on amoi.id=b.AO_MERGE_ORDER_INFO_ID
                        left join ao_merge_pay_order_info ampoi  on ampoi.id=amoi.ORDER_PAY_ID
                        where b.ORDER_TYPE ='4'
                        and  amoi.CREATE_DATE&gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d 00:00:00')
                        and amoi.CREATE_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d 23:59:59')
                        and b.SUBORDER_ORDER_STATUS ='1'
                        and amoi.SERVICE_PORT=#{servicePort}
                      )union(
                        select
                        d.CURRENCY_SPECIES_CODE as currencySpecies,
                        d.ORDER_PRICE as price
                        from AO_ORDER_IMPORT d
                        where  d.ORDER_STATUS='1'  and ORDER_TYPE='4'
                        and d.ORDER_DATE &gt;= DATE_FORMAT(#{orderDateStart}, '%Y-%m-%d')
                        and d.ORDER_DATE &lt;= DATE_FORMAT(#{orderDateEnd}, '%Y-%m-%d')
                        and d.SERVICE_PORT=#{servicePort}

                      )
                  ) xx
                  group by  xx.currencySpecies


</select>



</mapper>
