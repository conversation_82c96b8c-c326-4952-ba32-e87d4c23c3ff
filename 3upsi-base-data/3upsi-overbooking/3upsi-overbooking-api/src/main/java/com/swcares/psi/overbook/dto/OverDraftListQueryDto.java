package com.swcares.psi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.psi.audit.dto <br>
 * Description：H5-超售草稿箱列表查询参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月26日 20:44 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5-超售草稿箱列表查询")
public class OverDraftListQueryDto {

    /** 服务航站 **/
    @ApiModelProperty(value = "服务航站")
   private String serviceCity ;

    /** 当前页数，默认为第一页 **/
    @ApiModelProperty(value = "current")
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    @ApiModelProperty(value = "pageSize")
    private int pageSize = 10;
}
