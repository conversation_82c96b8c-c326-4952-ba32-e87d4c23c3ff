package com.swcares.psi.ao.register.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 邀请入会-用户行为监控记录
 * </p>
 *
 * @since 2023-09-19
 */
@Data
@TableName("ao_user_behavior_record")
@EqualsAndHashCode(callSuper = false)
public class UserBehaviorRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "用户信息")
    @TableField("USER_INFO")
    private String userInfo;

    @ApiModelProperty(value = "操作类型（0按钮或1界面）")
    @TableField("OPERATION_TYPE")
    private String operationType;

    @ApiModelProperty(value = "类型数值（xx按钮或xx界面）")
    @TableField("TYPE_VALUE")
    private String typeValue;

    @ApiModelProperty(value = "记录该界面或按钮的PV（访问或点击）次数")
    @TableField("PV_COUNT")
    private Integer pvCount;

    @ApiModelProperty(value = "操作时间")
    @TableField("OPERATION_TIME")
    private LocalDateTime operationTime;
}
