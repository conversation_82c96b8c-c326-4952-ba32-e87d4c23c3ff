package com.swcares.psi.stimulate.controller;


import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.stimulate.dto.AoStimulateQueryDto;
import com.swcares.psi.stimulate.dto.AoStimulateSaveDto;
import com.swcares.psi.stimulate.service.IAoStimulateConfigInfoService;
import com.swcares.psi.stimulate.vo.AoStimulateConfigInfoPageListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 辅营产品激励配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Api(tags = "辅营产品激励配置")
@RestController
@Slf4j
@RequestMapping("/api/ao/stimulate/config")
public class AoStimulateConfigInfoController {


    @Autowired
    private IAoStimulateConfigInfoService aoStimulateConfigInfoService;

    @ApiOperation(value = "保存激励配置")
    @PostMapping("/saveConfig")
    public RenderResult saveConfig(@RequestBody AoStimulateSaveDto dto) {
        aoStimulateConfigInfoService.saveConfig(dto);
        return RenderResult.success();
    }


    @ApiOperation(value = "删除激励配置")
    @PostMapping("/deleteConfig")
    public RenderResult deleteConfig(String id) {
        aoStimulateConfigInfoService.deleteConfig(id);
        return RenderResult.success();
    }

    @GetMapping("/configPage")
    @ApiOperation(value = "列表查询")
    public RenderResult<PsiPage<AoStimulateConfigInfoPageListVo>> page(AoStimulateQueryDto dto) {
        PsiPage<AoStimulateConfigInfoPageListVo> page = aoStimulateConfigInfoService.pageList(dto);
        return RenderResult.success(page);
    }

}

