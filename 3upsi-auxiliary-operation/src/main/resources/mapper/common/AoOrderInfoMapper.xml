<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.common.mapper.AoOrderInfoMapper">
    <sql id="orderInfoSql">
        select aoi.ORDER_TYPE,
               aoi.ORDER_TYPE_CODE,
               case
                   when ffri.FLIGHT_TYPE = 'D' then '国内'
                   when ffri.FLIGHT_TYPE = 'I' then '国际'
                   when ffri.FLIGHT_TYPE = 'R' then '地区'
                   end                                                 as FLIGHT_TYPE,
               date_format(pass.DEPARTURE_TIME, '%Y-%m-%d %H:%i:%s')   as std,
               date_format(aoi.FLIGHT_DATE, '%Y-%m-%d')                as FLIGHT_DATE,
               aoi.FLIGHT_NO,
               aoi.ORDER_NO,
               aoi.ORDER_NO_CTERMINAL,
               date_format(aoi.ORDER_DATE, '%Y-%m-%d')                 as orderDate,
               aoi.BANK_ORDER_NO,
               aoi.FINANCE_ORDER_NO,
               aoi.ORG,
               get_city_name(aoi.ORG)                                  as ORG_NAME,
               aoi.DST,
               get_city_name(aoi.DST)                                  as DST_NAME,
               aoi.TKT_NO,
               aoi.SEGMENT_TYPE,
               case
                   when aoi.CHARGE_TYPE = '0' then '微信'
                   when aoi.CHARGE_TYPE = '1' then '易宝'
                   when aoi.CHARGE_TYPE = '2' then '支付宝'
                   when aoi.CHARGE_TYPE = '3' then '现金'
                   when aoi.CHARGE_TYPE = '5' then 'worldPay'
                   else '现金'
                   end                                                 as  CHARGE_TYPE_NAME,
               aoi.CHARGE_TYPE,
               aoi.CURRENCY_SPECIES,
               case aoi.CURRENCY_SPECIES
               when 'CNY' THEN '人民币'
               ELSE
               (SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=aoi.CURRENCY_SPECIES limit 1)
               end AS currencySpeciesName,
               aoi.ORDER_PRICE,
               case
                   when aoi.ORDER_STATUS = '0' then '未支付'
                   when aoi.ORDER_STATUS = '1' then '支付失败'
                   when aoi.ORDER_STATUS = '2' then '支付完成'
                   when aoi.ORDER_STATUS = '3' then '退款中'
                   when aoi.ORDER_STATUS = '4' then '退款失败'
                   when aoi.ORDER_STATUS = '5' then '退款成功'
                   when aoi.ORDER_STATUS = '6' then '已取消'
                   when aoi.ORDER_STATUS = '8' then '支付中'
                   else '发起退款'
                   end                                                 as ORDER_STATUS,
               aoi.ORDER_STATUS                                        as ORDER_STATUS_CODE,
               case
                   when aoi.PAY_STATUS = '0' then '未支付'
                   when aoi.PAY_STATUS = '1' then '支付完成'
                   when aoi.PAY_STATUS = '2' then '支付失败'
                   when aoi.PAY_STATUS = '3' then '退款完成'
                   when aoi.PAY_STATUS = '4' then '退款失败'
                   end                                                 as PAY_STATUS,
               aoi.PAY_STATUS                                          as PAY_STATUS_CODE,
               date_format(aoi.PAY_TIME, '%Y-%m-%d %H:%i:%s')          as PAY_TIME,
               date_format(aoi.REFUND_START_TIME, '%Y-%m-%d %H:%i:%s') as REFUND_START_TIME,
               date_format(aoi.REFUND_END_TIME, '%Y-%m-%d %H:%i:%s')   as REFUND_END_TIME,
               aoi.PAX_NAME,
               aoi.CELL_CHANNEL,
               aoi.REFUND_PRICE,
               aoi.REFUND_CAUSE,
               aoi.REFUND_EXPLAIN,
               if(
                   (aoi.ORDER_TYPE_CODE='01' or aoi.ORDER_TYPE_CODE='02' or aoi.ORDER_TYPE_CODE='03' ),turn.CALLBACK_PAY_NO,aoi.TURN_NUMBER
                ) as TURN_NUMBER,
               date_format( aoi.TURN_TIME, '%Y-%m-%d %H:%i:%s')   as TURN_TIME,
               aoi.USER_NO,
               aoi.USER_NAME,
               aoi.DEPARTMENT,
               aoi.CASH_RECEIVE_USER,
               aoi.CASH_RECEIVE_USER_ID,
               aoi.CASH_RECEIVE_DEPARTMENT,
               aoi.CASH_REFUND_USER,
               aoi.CASH_REFUND_USER_ID,
               aoi.CASH_REFUND_DEPARTMENT,
               aoi.PAX_NO,
               aoi.INVOICE,
                if(
                (SELECT DATA_CODE FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0' AND (DATA_CODE = aoi.org OR
                DATA_CODE = aoi.dst) limit 1 ) is null,'N','Y'
                ) as isGat,
               ACU.UPGRADE_STATUS AS upgradeStatus,
               ACU.UPGRADE_UPDATE_STATUS AS upgradeUpdateStatus,
               ACU.ERROR_INFO AS errorInfo,
               aoi.PAX_ID,
               case
                   when turn.TURN_STATUS is null then '-1'
                   else turn.TURN_STATUS
               end                                                 as turnStatus
        from ao_order_info aoi
                 left join flt_flight_real_info ffri on aoi.FLIGHT_ID = ffri.ID
                 LEFT JOIN flt_passenger_real_info pass on pass.id = aoi.PAX_ID
                 LEFT JOIN ao_counter_upgrade ACU ON  aoi.ORDER_NO = ACU.ORDER_NO
                 LEFT JOIN ao_temp_cash_return turn ON  turn.ORDER_NO = aoi.ORDER_NO
    </sql>
    <sql id="orderWhereSql">
        <where>
            <if test=" dto.queryParam != null and dto.queryParam != '' ">
                (
                aoi.ORDER_TYPE like CONCAT('%',#{dto.queryParam},'%')
                OR aoi.ORDER_NO like CONCAT('%',#{dto.queryParam},'%')
                OR aoi.PAX_NAME LIKE CONCAT('%',#{dto.queryParam},'%')
                OR aoi.FLIGHT_NO LIKE CONCAT('%',#{dto.queryParam},'%')
                <if test=" param.queryParam != null and param.queryParam != '' ">
                    OR aoi.PAX_NO = #{param.queryParam}
                </if>
                )
            </if>
           <if test="dto.currencySpecies != null and dto.currencySpecies != '' ">
               AND aoi.CURRENCY_SPECIES=#{dto.currencySpecies}
           </if>
            <if test=" dto.orderStartDate != null and dto.orderStartDate != '' ">
                AND aoi.ORDER_DATE <![CDATA[ >= ]]> DATE_FORMAT(#{dto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>

            <if test=" dto.orderEndDate != null and dto.orderEndDate != '' ">
                AND aoi.ORDER_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test=" dto.flightDate != null and dto.flightDate != '' ">
                AND aoi.FLIGHT_DATE = DATE_FORMAT(#{dto.flightDate},'%Y-%m-%d')
            </if>
            <if test=" dto.segmentType != null and dto.segmentType != '' ">
                AND aoi.SEGMENT_TYPE = #{dto.segmentType}
            </if>

            <if test=" dto.flightNo != null and dto.flightNo != '' ">
                AND aoi.FLIGHT_NO=#{dto.flightNo}
            </if>
            <if test=" dto.orderNo != null and dto.orderNo != '' ">
                AND aoi.ORDER_NO=#{dto.orderNo}
            </if>
            <if test=" dto.org != null and dto.org != '' ">
                AND aoi.ORG=#{dto.org}
            </if>
            <if test=" dto.dst != null and dto.dst != '' ">
                AND aoi.DST=#{dto.dst}
            </if>
            <if test=" dto.tktNo != null and dto.tktNo != '' ">
                AND aoi.TKT_NO=#{dto.tktNo}
            </if>

            <if test=" dto.paxNo != null and dto.paxNo != '' ">
                AND aoi.PAX_NO = #{dto.PAX_NO}
            </if>


            <if test=" dto.cashReceiveUserId != null and dto.cashReceiveUserId != '' ">
                AND aoi.CASH_RECEIVE_USER_ID=#{dto.cashReceiveUserId}
            </if>

            <if test=" dto.turnStatus != null and dto.turnStatus != '' ">
                <choose>
                    <when test="dto.turnStatus == '-1'">
                        AND (aoi.TURN_STATUS is null or aoi.TURN_STATUS = '1')
                    </when>
                    <otherwise>
                        AND aoi.TURN_STATUS=#{dto.turnStatus}
                    </otherwise>
                </choose>
            </if>

            <if test="param.orderType != null ">
                and aoi.ORDER_TYPE_CODE in
                <foreach collection="param.orderType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>

            <if test="param.orderStatus != null ">
                and aoi.ORDER_STATUS in
                <foreach collection="param.orderStatus" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test="param.chargeType != null">
                AND aoi.CHARGE_TYPE in
                <foreach collection="param.chargeType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>

            <if test=" dto.userNo != null and dto.userNo != '' ">
                AND ( aoi.USER_NO=#{dto.userNo}
                <!-- # 指定用户只能查看的数据-->
                <if test=" dto.userId != null and dto.userId != ''">
                    OR aoi.USER_NO in (
                    select DISTINCT(emy.TUNO) from data_permissions dp
                    LEFT JOIN user_role ur on dp.ROLE_ID=ur.ROLE_ID
                    LEFT JOIN user_role ur2 on ur2.ROLE_ID=dp.PERMISSIONS_ROLE_ID
                    LEFT JOIN employee emy on ur2.EMPLOYEE_ID=emy.id
                    where ur.EMPLOYEE_ID=#{dto.userId}
                    )
                </if>
                )
            </if>

        </where>
        order by aoi.ORDER_DATE desc
    </sql>
    <sql id="orderByPassengerWhereSql">
        <where>
            <if test=" queryParam != null and queryParam != '' ">
                (
                aoi.ORDER_TYPE like CONCAT('%',#{queryParam},'%')
                OR aoi.ORDER_NO like CONCAT('%',#{queryParam},'%')
                )
            </if>


            <if test=" paxNo != null and paxNo != '' ">
                AND aoi.PAX_NO = #{paxNo}
            </if>
            <if test=" orderStatus != null ">
                and aoi.ORDER_STATUS in
                <foreach collection="orderStatus" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
            <if test=" orderType != null ">
                and aoi.ORDER_TYPE_CODE in
                <foreach collection="orderType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>

            <if test=" chargeType != null">
                AND aoi.CHARGE_TYPE in
                <foreach collection="chargeType" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>

            <if test=" idNos != null">
                AND aoi.PAX_NO in
                <foreach collection="idNos" item="idNo" open="(" close=")" separator=",">
                    #{idNo}
                </foreach>
            </if>

        </where>
        order by aoi.ORDER_DATE desc
    </sql>
    <select id="searchOrderInfo" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        <include refid="orderInfoSql"></include>
        <include refid="orderWhereSql"></include>
    </select>
    <select id="searchOrderInfoPage" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        <include refid="orderInfoSql"></include>
        <include refid="orderWhereSql"></include>
    </select>


    <select id="orderInfoList" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        <include refid="orderInfoSql"></include>
        <include refid="whereSql"></include>
    </select>
    <select id="orderInfoListPage" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        <include refid="orderInfoSql"></include>
        <include refid="whereSql"></include>
    </select>


    <sql id="whereSql">
        <where>
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                AND aoi.FLIGHT_DATE <![CDATA[ >= ]]> DATE_FORMAT(#{dto.flightStartDate},'%Y-%m-%d')

            </if>
            <if test="dto.flightEndDate != null and dto.flightEndDate != ''">
                AND aoi.FLIGHT_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{dto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="dto.orderStartDate != null and dto.orderStartDate != ''">
                AND aoi.ORDER_DATE <![CDATA[ >= ]]> DATE_FORMAT(#{dto.orderStartDate},'%Y-%m-%d 00:00:00')

            </if>
            <if test="dto.orderEndDate != null and dto.orderEndDate != ''">
                AND aoi.ORDER_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{dto.orderEndDate},'%Y-%m-%d  23:59:59')

            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                AND aoi.ORDER_NO = #{dto.orderNo}

            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                AND aoi.FLIGHT_NO = #{dto.flightNo}

            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                AND aoi.PAX_NAME like CONCAT('%',#{dto.paxName},'%')

            </if>
            <if test="dto.paxNo != null and dto.paxNo != ''">
                AND aoi.PAX_NO = #{dto.paxNo}
            </if>
            <if test="dto.userName != null and dto.userName != ''">
                AND aoi.USER_NAME like CONCAT('%',#{dto.userName},'%')
            </if>
            <if test="dto.userNo != null and dto.userNo != ''">
                AND aoi.USER_NO like CONCAT('%',#{dto.userNo},'%')
            </if>
            <if test="dto.orderType != null and dto.orderType != ''">
                AND aoi.ORDER_TYPE_CODE = #{dto.orderType}
            </if>
            <if test="dto.invoice != null and dto.invoice != ''">
                AND aoi.INVOICE = #{dto.invoice}
            </if>

            <if test="dto.org != null and dto.org != ''">
                AND aoi.ORG = #{dto.org}
            </if>

            <if test="dto.dst != null and dto.dst != ''">
                AND aoi.DST = #{dto.dst}
            </if>
        </where>
            order by aoi.ORDER_DATE desc
    </sql>


    <select id="searchOrderInfoByPassengerPage" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        <include refid="orderInfoSql"></include>
        <include refid="orderByPassengerWhereSql"></include>
    </select>
    <select id="searchOrderInfoByPassenger" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        <include refid="orderInfoSql"></include>
        <include refid="orderByPassengerWhereSql"></include>
    </select>

    <select id="sellNumberByDate" resultType="com.swcares.psi.ao.common.vo.ComparisonGrowthInteriorVo">
        SELECT IFNULL(SUM(ORDER_PRICE), 0)/100 as price,CURRENCY_SPECIES as priceType
        FROM ao_order_info
        where (
                (ORDER_STATUS = '2' and PAY_STATUS = '1')
                or
                (ORDER_STATUS = '4' and PAY_STATUS = '4')
            )
          and USER_NO = #{userNo}
          and ORDER_DATE &gt;= DATE_FORMAT(#{begin}, '%Y-%m-%d 00:00:00')
          and ORDER_DATE &lt;= DATE_FORMAT(#{end}, '%Y-%m-%d 23:59:59')
          group by CURRENCY_SPECIES
    </select>

    <select id="sellOrderNumberByDate" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(1), 0)
        FROM ao_order_info
        where (
                (ORDER_STATUS = '2' and PAY_STATUS = '1')
                or
                (ORDER_STATUS = '4' and PAY_STATUS = '4')
            )
          and USER_NO = #{userNo}
          and ORDER_DATE &gt;= DATE_FORMAT(#{begin}, '%Y-%m-%d 00:00:00')
          and ORDER_DATE &lt;= DATE_FORMAT(#{end}, '%Y-%m-%d 23:59:59')
    </select>

    <select id="sellOrderByUser" resultType="com.swcares.psi.ao.common.vo.AuxiliaryOperationOrderVo">
        SELECT
        ORDER_TYPE,
        ORDER_TYPE_CODE,
        CURRENCY_SPECIES,
        case CURRENCY_SPECIES
        when 'CNY' THEN '人民币'
        ELSE
        (SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=CURRENCY_SPECIES limit 1)
        end AS currencySpeciesName,
        PAX_NAME,
        ORDER_PRICE
        FROM ao_order_info
        <where>
            (
            (ORDER_STATUS='2' and PAY_STATUS='1')
            or
            ( ORDER_STATUS='4' and PAY_STATUS='4')
            )
            <if test=" userNo != null and userNo != ''">
                and USER_NO=#{userNo}
            </if>
            <if test=" begin != null and begin != ''">
                and ORDER_DATE &gt;= DATE_FORMAT(#{begin},'%Y-%m-%d 00:00:00')
            </if>
            <if test=" end != null and end != ''">
                and ORDER_DATE &lt;= DATE_FORMAT(#{end},'%Y-%m-%d 23:59:59')
            </if>
            <if test=" orderTypeCode != null and  orderTypeCode.size() > 0">
                and ORDER_TYPE_CODE in
                <foreach collection="orderTypeCode" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
        </where>
    </select>

    <select id="sellSumPriceByUser"  resultType="com.swcares.psi.ao.common.vo.ComparisonGrowthInteriorVo">
        SELECT IFNULL(SUM(ORDER_PRICE), 0)/100 as price,CURRENCY_SPECIES as priceType
        FROM ao_order_info
        <where>
            (
            (ORDER_STATUS='2' and PAY_STATUS='1')
            or
            ( ORDER_STATUS='4' and PAY_STATUS='4')
            )
            <if test=" userNo != null and userNo != ''">
                and USER_NO=#{userNo}
            </if>
            <if test=" begin != null and begin != ''">
                and ORDER_DATE &gt;= DATE_FORMAT(#{begin},'%Y-%m-%d 00:00:00')
            </if>
            <if test=" end != null and end != ''">
                and ORDER_DATE &lt;= DATE_FORMAT(#{end},'%Y-%m-%d 23:59:59')
            </if>
            <if test=" orderTypeCode != null and  orderTypeCode.size() > 0">
                and ORDER_TYPE_CODE in
                <foreach collection="orderTypeCode" item="ele" open="(" close=")" separator=",">
                    #{ele}
                </foreach>
            </if>
        </where>
        group by CURRENCY_SPECIES
    </select>
    <select id="getUpgradeOrderList" resultType="com.swcares.psi.ao.common.vo.UpgradeOrderVo">
        SELECT
            AOI.ID AS id,
            AOI.ORDER_NO AS orderNo,
            ACU.UPGRADE_STATUS AS upgradeStatus,
            ACU.UPGRADE_ORDER AS upgradeOrder,
            AOI.FLIGHT_NO AS flightNo,
            AOI.ORG AS org
        FROM
            ao_order_info AS AOI
            LEFT JOIN ao_counter_upgrade AS ACU ON AOI.ORDER_NO = ACU.ORDER_NO
        WHERE
            1 = 1
            AND ((AOI.ORDER_STATUS = '2' AND AOI.PAY_STATUS = '1')
            OR (AOI.PAY_STATUS = '3'))
            AND UPGRADE_STATUS = '1'
    </select>

</mapper>