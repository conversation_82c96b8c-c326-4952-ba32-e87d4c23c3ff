
package com.swcares.psi.common.security.annotation;

import com.swcares.psi.common.security.component.PsiResourceServerAutoConfiguration;
import com.swcares.psi.common.security.component.PsiSecurityBeanDefinitionRegistrar;
import org.springframework.context.annotation.Import;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2020-05-27
 * <p>
 * 资源服务注解
 */
@Documented
@Inherited
@EnableResourceServer
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@EnableGlobalMethodSecurity(prePostEnabled = true)
@Import({PsiResourceServerAutoConfiguration.class, PsiSecurityBeanDefinitionRegistrar.class})
public @interface EnablePsiResourceServer {

}
