package com.swcares.psi.ao.counterUpgrade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradeConfigPriceDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradesPriceConfigDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradesPriceConfigSaveDto;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgradesPriceConfig;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeConfigPriceH5Vo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeConfigPriceVo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradesPriceConfigPageVo;
import com.swcares.psi.ao.pad.dto.OlpCostUpgradesPriceConfigDto;
import com.swcares.psi.ao.pad.dto.OlpCostUpgradesPriceConfigSaveDto;
import com.swcares.psi.ao.pad.improHander.ImporEntity;
import com.swcares.psi.ao.pad.vo.OlpCostUpgradesPriceConfigPageVo;
import com.swcares.psi.base.data.api.vo.OlpCostUpgradePriceVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AoCounterUpgradesPriceConfigService extends IService<AoCounterUpgradesPriceConfig> {

    /**
     * 配置保存修改
     * @param dto
     */
    void saveOrUpdateEntity(AoCounterUpgradesPriceConfigSaveDto dto);

    /**
     * 价格列表查询
     * @param dto
     * @return
     */
    PsiPage<AoCounterUpgradesPriceConfigPageVo> getPage(AoCounterUpgradesPriceConfigDto dto);
    List<AoCounterUpgradesPriceConfigPageVo> getList(AoCounterUpgradesPriceConfigDto dto);

    /**
     * 价格导入
     * @param file
     * @return
     */
    List<ImporEntity> importExcel(MultipartFile file);

    /**
     * 价格配置删除
     * @param ids
     * @return
     */
    Boolean deleteCounterUpgrade(List<String> ids);
    /**
     * 获取当前升舱价格
     * @param aoCounterUpgradePriceConfigDto
     * @return 获取当前两个地址之间的升舱价格
     */
    AoCounterUpgradeConfigPriceH5Vo getPrice(AoCounterUpgradeConfigPriceDto aoCounterUpgradePriceConfigDto);
}
