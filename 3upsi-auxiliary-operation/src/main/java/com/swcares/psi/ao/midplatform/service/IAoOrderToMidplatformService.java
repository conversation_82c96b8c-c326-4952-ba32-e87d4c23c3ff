package com.swcares.psi.ao.midplatform.service;

import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.BussinessAesCryptUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public interface IAoOrderToMidplatformService {

    DateTimeFormatter aompDateStr = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    DateTimeFormatter aompDateTimeStr = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    default String aompToDateStr(TemporalAccessor localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return aompDateStr.format(localDateTime);
    }

    default String aompToDatetimeStr(TemporalAccessor localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        if (localDateTime instanceof LocalDate) {
            localDateTime = LocalDateTime.of((LocalDate)localDateTime, LocalTime.MIDNIGHT);
        }
        return aompDateTimeStr.format(localDateTime);
    }

    default String aes(String decryptKey, String encryptKey, String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        return aesEncrypt(aesDecrypt(str, decryptKey), encryptKey);
    }

    /**
     * 旅服系统数据对称解密
     *
     * @param str
     * @param aesKey
     * @return
     */
    default String aesDecrypt(String str, String aesKey) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        if (str.contains(",")) {
            return AesEncryptUtil.aesDecryptMultiJoin(aesKey, str, ",");
        }
        return AesEncryptUtil.decryption(str);
    }

    /**
     * 推送中台敏感信息对称加密
     *
     * @param str
     * @param aesKey
     * @return
     */
    default String aesEncrypt(String str, String aesKey) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        return Arrays.stream(str.split(",")).map(s -> {
            try {
                return BussinessAesCryptUtil.encrypt(s, aesKey);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.joining(","));
    }

    void toMidplatformVo(String orderNo);
    void toUpgradeOnboardMidplatformVo(String orderNo);

    void syncOrderToMidplatform(List<String> orderNos, boolean isOlp);

}
