package com.swcares.psi.test.activity

import com.swcares.SystemRootApplication
import com.swcares.psi.base.data.api.dto.P3012FileInfoDto
import com.swcares.psi.base.data.service.impl.P3012FileInfoServiceImpl
import com.swcares.psi.combine.user.impl.PsiUser
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

import java.nio.file.Files

@SpringBootTest(classes = SystemRootApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class P3012FileInfoServiceTest extends Specification{

    @Autowired
    P3012FileInfoServiceImpl p3012FileInfoService;

    def setSecurityContext() {
        def authentication = Mock(PsiUser)
        authentication.getUsername() >> "mockUser"
        authentication.getRealName() >> "mock测试账号"
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        mock.getUserAuthentication() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    def "测试资料文件上传功能"(){
        given:"设置参数"
        setSecurityContext()

        and:"添加DTO参数"
        P3012FileInfoDto dto = mockDto()

        when:"测试上传流程"
        p3012FileInfoService.save(dto)

        then:"输出结果"
        noExceptionThrown()

    }


    @Unroll
    def "input 主键ID：#id,有效期开始时间: #startDate,有效期结束时间 #endDate,是否学习 #studied"(){
        given:"设置参数"
        setSecurityContext()

        and:"添加DTO参数"
        P3012FileInfoDto dto = mockDto()
        dto.setId(id)
        dto.setEffectiveStartDate(startDate)
        dto.setEffectiveEndDate(endDate)
        dto.setStudied(studied)

        when:"测试上传流程"
       p3012FileInfoService.save(dto)

        then:"输出结果"
        noExceptionThrown()

        where:"分支场景测试"
        id                   | startDate    | endDate      | studied
        "228233247832670208" | "2023-11-20" | "2023-11-20" | "0"
        "228233247832670208" | ""           | ""           | "0"
        "221422980478009349" | null         | null         | "1"
        "221423057653202950" | ""           | ""           | "1"
    }

    // 模拟上传和更新的数据
    private static P3012FileInfoDto mockDto() {
        def dto = new P3012FileInfoDto()
        def file = new File("/Users/<USER>/Documents/川航首乘旅客打标撤销接口说明1.docx")
        def fileBytes = Files.readAllBytes(file.toPath())
        def multipartFile = new MockMultipartFile("file", file.getName(), "application/vnd.openxmlformats-officedocument.wordprocessingml.document", fileBytes)
        dto.setFile(multipartFile)
        dto.setEmployee("source_employee_other")
        dto.setDataType("manual")
        dto.setIsImportant("1")
        dto.setNote("测试一下接口数据")
        dto.setEffectiveStartDate("2023-11-11")
        dto.setEffectiveEndDate("2023-11-11")
        dto
    }

}