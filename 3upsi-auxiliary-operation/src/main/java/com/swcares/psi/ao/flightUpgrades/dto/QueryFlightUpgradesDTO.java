package com.swcares.psi.ao.flightUpgrades.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：$.$
 * Description：(根据航班要素查询订单的DTO类)
 * Copyright © 2022$ xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2022/2/11 9:40
 * @version v1.0
 */

@Data
public class QueryFlightUpgradesDTO implements Serializable {
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班开始日期")
    private String flightDateStar;
    @ApiModelProperty(value = "航班结束日期")
    private String flightDateEnd;
    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;
    @ApiModelProperty(value = "出发地三字码")
    private String orgCityAirp;
    @ApiModelProperty(value = "目的地三字码")
    private String dstCityAirp;
}
