<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.stimulate.mapper.AoStimulateProductCostConfigMapper">

    <select id="dateLatestConfig" resultType="java.time.LocalDate">
        select
        max(`month`)
        from AO_STIMULATE_PRODUCT_COST_CONFIG
        where STATUS='2' and HISTORY='0'
    </select>


    <select id="pageList" resultType="com.swcares.psi.stimulate.vo.AoStimulateProductCostPageVo">
        select
        a.ORDER_TYPE,
        a.PORT,
       (select AIRPORT_NAME from sys_airport_info where CODE = a.PORT and IS_USE = '0' limit 1) as portName,
        CASE
        WHEN a.ORDER_TYPE = '1' THEN '一人多座'
        WHEN a.ORDER_TYPE = '2' THEN '付费选座'
        WHEN a.ORDER_TYPE = '3' THEN '逾重行李'
        WHEN a.ORDER_TYPE = '4' THEN '贵宾室'
        when a.ORDER_TYPE = '5' then '柜台升舱'
        else '机上升舱'
        END AS orderTypeName,
        date_format(max(CREATE_TIME), '%Y-%m-%d %H:%i:%S')  as updateTime
        from AO_STIMULATE_PRODUCT_COST_CONFIG a
        where a.STATUS='2' and a.HISTORY='0'
        <if test="port != null and port != ''">
            AND find_in_set(a.PORT, #{port})
        </if>
        group by a.ORDER_TYPE,a.PORT
    </select>


    <select id="getMonthConfig" resultType="com.swcares.psi.stimulate.vo.AoStimulateProductCostPageVo$MonthConfig">
        select
        a.COST as cost,
        date_format(a.MONTH, '%Y-%m-%d')  as monthDate
        from AO_STIMULATE_PRODUCT_COST_CONFIG a
        where a.STATUS='2' and a.HISTORY='0'  and a.MONTH between  date_format(#{startDate}, '%Y-%m-%d') and   date_format(#{endDate}, '%Y-%m-%d')
        <if test="port != null and port != ''">
            AND find_in_set(a.PORT, #{port})
        </if>
        <if test="orderType != null and orderType != ''">
            AND a.ORDER_TYPE= #{orderType}
        </if>
        order by a.MONTH desc
    </select>
</mapper>
