package com.swcares.psi.admin.mapper;

import com.swcares.psi.base.base.BaseDAO;
import com.swcares.psi.common.utils.query.QueryResults;
import com.swcares.psi.admin.api.vo.ResourcesView;
import com.swcares.psi.admin.api.vo.ModuleTreeVO;
import com.swcares.psi.admin.api.entity.RoleResources;
import com.swcares.psi.admin.api.entity.UserRole;
import com.swcares.psi.admin.api.entity.Resources;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourcesDao <br>
 * Package：com.swcares.psi.dao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月02日 16:19 <br>
 * @version v1.0 <br>
 */
@Repository
public class ResourcesDao {

    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getResourcesByRoleID（） <br>
     * Description：根据角色ID去查询该用户的所有资源 <br>
     * author：于琦海 <br>
     * date：2020/4/2 16:20 <br>
     * @param roleId String
     * @return List<Resources>
     */
    public List<ModuleTreeVO> getResourcesByRoleID(String roleId) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT T.ID as 'key',T.NAME as title,T.PID,T.PORT FROM RESOURCES_SCGSI T "
                                + "LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = T.ID WHERE RR.ROLE_ID =:roleId order by T.WEIGHTS");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("roleId", roleId);
        return (List<ModuleTreeVO>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                ModuleTreeVO.class);
    }

    /**
     * Title：getResources() <br>
     * Description：根据模块名称和模块类型查询资源列表（分页） <br>
     * author：于琦海 <br>
     * date：2020/4/8 10:36 <br>
     * @param parameters Map<String, Object>
     * @param sql String
     * @param current Integer
     * @param pageSize Integer
     * @return QueryResults
     */
    public QueryResults getResources(String sql, Map<String, Object> parameters, Integer current,
                                     Integer pageSize) {
        return baseDAO.findBySQLPage_comm(sql, current, pageSize, parameters, ResourcesView.class);
    }

    /**
     * Title：batchDisabled（） <br>
     * Description：批量启用停用 <br>
     * author：于琦海 <br>
     * date：2020/4/8 14:24 <br>
     * @param values Object[]
     * @param status Boolean
     */
    @Transactional
    public void batchDisabled(Object[] values, Boolean status) {
        List<Object> params = new LinkedList<>();
        StringBuffer sql = new StringBuffer("UPDATE RESOURCES_SCGSI SET STATUS = ?");
        params.add(status);
        sql.append(" where ID in (");
        for (Object obj : values) {
            if (obj != null) {
                sql.append(" ").append("?,");
                params.add(obj);
            }
        }
        sql.delete(sql.length() - 1, sql.length());
        sql.append(" ").append(")");
        baseDAO.batchUpdate(sql.toString(), params);
    }

    /**
     * Title：getModuleRoot() <br>
     * Description：获取模块树根节点 <br>
     * author：于琦海 <br>
     * date：2020/4/8 17:25 <br>
     * @return  List<ModuleTreeVO>
     */
    public List<ModuleTreeVO> getModuleRoot() {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT id as 'key',name as title,pid,port FROM RESOURCES_SCGSI WHERE STATUS = '1' order by WEIGHTS");
        return (List<ModuleTreeVO>) baseDAO
                .findBySQL_comm(sql.toString(), null, ModuleTreeVO.class);
    }

    /**
     * Title：getResourcesName（） <br>
     * Description：获取资源名称 <br>
     * author：于琦海 <br>
     * date：2020/4/15 18:18 <br>
     * @param id String
     * @return String
     */
    public String getResourcesName(String id) {
        StringBuilder sql = new StringBuilder("select name from RESOURCES_SCGSI where ID =:id");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("id", id);
        List<Map<String, Object>> results =
                (List<Map<String, Object>>) baseDAO
                        .findBySQL_comm(sql.toString(), parameters, null);
        if (results.size() > 0) {
            Map<String, Object> stringObjectMap = results.get(0);
            return (String) stringObjectMap.get("NAME");
        }
        return null;
    }

    /**
     * Title：findRolesResourceByUid <br>
     * Description：通过用户ID查询出所有的角色类型为管理员角色下的所有资源 <br>
     * author：于琦海 <br>
     * date：2020/4/20 14:34 <br>
     * @param id String
     * @return List<ModuleTreeVO>
     */
    public List<ModuleTreeVO> findRolesResourceByUid(String competence, String id) {
        StringBuilder sql =
                new StringBuilder("SELECT distinct RS.ID as 'KEY',RS.NAME as TITLE,RS.PID,RS.PORT FROM "
                        + "RESOURCES_SCGSI RS"
                        + " LEFT JOIN ROLE_RESOURCES RR ON RS.ID = RR.RESOURCES_ID "
                        + "WHERE RR.ROLE_ID IN ("
                        + "SELECT R.ID FROM ROLE R LEFT JOIN USER_ROLE UR ON R.ID = UR.ROLE_ID "
                        + "WHERE UR.EMPLOYEE_ID=:id AND R.COMPETENCE='0')"
                        + "AND RS.`STATUS` != '0'");
        if ("1".equals(competence)) {
            sql.append(" and RS.FUNCTIONAL ='0' ");
        }
        sql.append(" order by RS.WEIGHTS");
        Map<String, Object> parameters = new HashMap<>(1);
        parameters.put("id", id);
        return (List<ModuleTreeVO>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                ModuleTreeVO.class);
    }

    /**
     * Title：removeRolesBind <br>
     * Description：去除角色的资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:25 <br>
     * @param sql String
     * @param parameters String
     */
    public void removeRolesBind(String sql, String parameters) {
        if (StringUtils.isNotBlank(parameters)){
            baseDAO.batchUpdate(sql, parameters);
        }
    }

    /**
     * Title：bindResources <br>
     * Description：资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:34 <br>
     * @param resources List<RoleResources>
     */
    @Transactional
    public void bindResources(List<RoleResources> resources) {
        StringBuilder sql = new StringBuilder("INSERT INTO ROLE_RESOURCES(role_id, resources_id) VALUES ");
        List<Object> params = new LinkedList<>();
        for (RoleResources roleResources : resources) {
            params.add(roleResources.getRoleID());
            params.add(roleResources.getResourcesID());
            sql.append("(?,?),");
        }
        sql.replace(sql.length()-1,sql.length(),";");
        System.out.println(sql);
        baseDAO.batchUpdate(sql.toString(), params);
    }

    /**
     * Title：bindUsers <br>
     * Description：用户绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 16:02 <br>
     * @param resources List<UserRole>
     */
    @Transactional
    public void bindUsers(List<UserRole> resources) {
        StringBuilder sql = new StringBuilder("REPLACE INTO USER_ROLE(employee_id, role_id) VALUES ");
        List<Object> params = new LinkedList<>();
        for (UserRole userRole : resources) {
            params.add(userRole.getEmployeeId());
            params.add(userRole.getRoleId());
            sql.append("(?,?),");
        }
        sql.replace(sql.length()-1,sql.length(),";");
        baseDAO.batchUpdate(sql.toString(), params);
    }

    /**
     * Title：getRootResource <br>
     * Description：获取当前节点资源的父亲节点 <br>
     * author：于琦海 <br>
     * date：2020/5/8 15:41 <br>
     * @param resourceStr String
     * @return List<Resources>
     */
    public List<Resources> getRootResource(String resourceStr,String id) {
       /* StringBuilder sql = new StringBuilder("select distinct(ID), name, types, status, resource_url, contain_url, icon, port, resource_code, pid, remarks, components_id, functional " +
                "from RESOURCES_SCGSI start with PID=:resourceStr and ID=:id connect by prior PID=ID");
        */
        StringBuilder sql= new StringBuilder("select distinct(ID), name, types, status, resource_url, contain_url, icon, port, resource_code, pid, remarks, components_id, functional "  +
                "from RESOURCES_SCGSI,(SELECT getParentResources ( :resourceStr ) resources) t " +
                "WHERE FIND_IN_SET( ID, resources ); ");
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("resourceStr",resourceStr);
        //parameters.put("id",id);
        return (List<Resources>) baseDAO.findBySQL_comm(sql.toString(),parameters,Resources.class);
    }

    /**
     * Title：removeBindUsers <br>
     * Description：用户和角色解除绑定 <br>
     * author：王磊 <br>
     * date：2020年5月7日 下午3:39:58 <br>
     * @param userRoles <br>
     */
    @Transactional
    public void removeBindUsers(List<UserRole> userRoles) {
        for (UserRole userRole : userRoles) {
            List<Object> params = new LinkedList<>();
            StringBuilder sql =
                    new StringBuilder("DELETE FROM USER_ROLE WHERE EMPLOYEE_ID = ? AND ROLE_ID = ?;");
            params.add(userRole.getEmployeeId());
            params.add(userRole.getRoleId());
            baseDAO.batchUpdate(sql.toString(), params);
        }
    }


    /**
     * Title：getUserRoleByRoleId <br>
     * Description：通过角色ID获取角色用户关联表数据 <br>
     * author：王磊 <br>
     * date：2020年5月11日 下午3:07:02 <br>
     * @param roleId
     * @return <br>
     */
    @SuppressWarnings("unchecked")
    public List<UserRole> getUserRoleByRoleId(String roleId) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT * FROM USER_ROLE  "
                                + "WHERE ROLE_ID = :roleId");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("roleId", roleId);
        return (List<UserRole>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                UserRole.class);
    }

}
