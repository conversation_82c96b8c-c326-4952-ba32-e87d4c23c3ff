package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.vo.ElectronicVoucherVo;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.overweightbkg.vo.AoOverweightBkgBusinessVo;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightInputTemplateReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOverweightBkg;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOverweightBkgChargeDetails;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeOverweightBkgMapper;
import com.swcares.psi.aoMergeOrder.service.AoMergeOverweightBkgChargeDetailsService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOverweightBkgService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightInputTemplateReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.vo.SysY100PriceVo;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.base.data.service.SysY100PriceService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 合并逾重行李产品详情订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeOverweightBkgServiceImpl extends ServiceImpl<AoMergeOverweightBkgMapper, AoMergeOverweightBkg> implements IAoMergeOverweightBkgService {

    @Autowired
    private IAoMergeSuborderInfoService suborderInfoService;
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;
    @Autowired
    private IAoMergeOrderInfoService aoMergeOrderInfoService;
    @Autowired
    private SysAirportInfoService sysAirportInfoService;
    @Autowired
    private SysY100PriceService sysY100PriceService;
    @Autowired
    private AoMergeOverweightBkgChargeDetailsService aoMergeOverweightBkgChargeDetailsService;
    @Override
    public List<AoMergeOverweightBkgAccountsReportVo> getAccountsList(AoMergeOverweightBkgAccountsReportDto dto) {
        return baseMapper.getAccountsList(dto);
    }

    @Override
    public PsiPage<AoMergeOverweightBkgAccountsReportVo> getAccountsListPage(AoMergeOverweightBkgAccountsReportDto dto) {
        PsiPage<AoMergeOverweightBkgAccountsReportVo> page = new PsiPage<>(dto);
        return baseMapper.getAccountsListPage(page,dto);
    }

    @Override
    public List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeOverweightBkgAccountsReportDto dto) {
        return baseMapper.payTypesSummaryMoney(dto);
    }

    @Override
    public PsiPage<AoMergeOverweightInputTemplateReportVo> getAoMergeOverweightInputTemplatePage(AoMergeOverweightInputTemplateReportDto dto) {
        PsiPage<AoMergeOverweightInputTemplateReportVo> page = new PsiPage<>(dto);
        PsiPage<AoMergeOverweightInputTemplateReportVo> inputTemplatePage = baseMapper.getAoMergeOverweightInputTemplatePage(page, dto);
        for (AoMergeOverweightInputTemplateReportVo ele : inputTemplatePage.getRecords()){
            String totalPrice = new BigDecimal(ele.getTotalPrice()).setScale(2, RoundingMode.DOWN).toString();
            ele.setTotalPrice(totalPrice);
        }
        return inputTemplatePage;
    }

    @Override
    public List<AoMergeOverweightInputTemplateReportVo> getAoMergeOverweightInputTemplateList(AoMergeOverweightInputTemplateReportDto dto) {
        List<AoMergeOverweightInputTemplateReportVo> aoMergeOverweightInputTemplateList = baseMapper.getAoMergeOverweightInputTemplateList(dto);
        for (AoMergeOverweightInputTemplateReportVo ele : aoMergeOverweightInputTemplateList){
            String totalPrice = new BigDecimal(ele.getTotalPrice()).setScale(2, RoundingMode.DOWN).toString();
            ele.setTotalPrice(totalPrice);
        }
        return aoMergeOverweightInputTemplateList;
    }

    @Override
    public ElectronicVoucherVo getElectronicVoucher(String suborderNo) {
        AoMergeSuborderInfo order = suborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getSuborderOrderNo, suborderNo)
                .one();
        if (order == null || !AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(order.getOrderType())) {
            return null;
        }
        FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                .eq(FltFlightRealInfo::getFlightNumber, order.getFlightNo())
                .eq(FltFlightRealInfo::getFlightDate, order.getFlightDate())
                .eq(FltFlightRealInfo::getOrg, order.getOrg())
                .eq(FltFlightRealInfo::getFlightType, "D")
                .one();
        if (flightRealInfo == null) {
            return null;
        }
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(order.getAoMergeOrderInfoId());
        String flightNo = order.getFlightNo();
        LocalDateTime orderDate = mergeOrderInfo.getCreateDate();
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String dateFormat = dtf2.format(orderDate);
        String dateFormat2 = dtf2.format(LocalDate.now());
        String userName = mergeOrderInfo.getCreateUser();
        ElectronicVoucherVo electronicVoucherVo = new ElectronicVoucherVo();
        electronicVoucherVo.setOrderDate(dateFormat);
        electronicVoucherVo.setDate(dateFormat2);
        electronicVoucherVo.setPaxName(order.getPaxName());
        electronicVoucherVo.setTktNo(order.getTktNo());
        electronicVoucherVo.setCarrier(StringUtils.isEmpty(flightNo) ? "" : flightNo.substring(0, 2));
        electronicVoucherVo.setFlightNo(StringUtils.isEmpty(flightNo) ? "" : flightNo.substring(2));
        electronicVoucherVo.setIssued(userName);
        electronicVoucherVo.setDeclaredValue("0.00");//声明价值附加费  默认为0
        electronicVoucherVo.setCharge(order.getPracticalOrderPrice().toString());

        List<SysAirportInfoEntity> list = sysAirportInfoService.lambdaQuery()
                .in(SysAirportInfoEntity::getCode, new String[]{order.getOrg(), order.getDst()})
                .list();
        if (list.size() == 2) {
            if (list.get(0).getCode().equals(order.getOrg())) {
                electronicVoucherVo.setOrgName(list.get(0).getAirportName());
                electronicVoucherVo.setDstName(list.get(1).getAirportName());
            } else {
                electronicVoucherVo.setOrgName(list.get(1).getAirportName());
                electronicVoucherVo.setDstName(list.get(0).getAirportName());
            }
        }
        //国内逾重
        AoMergeOverweightBkg one = this.lambdaQuery()
                .eq(AoMergeOverweightBkg::getAoMergeSuborderInfoId, order.getId())
                .one();
        AoMergeOverweightBkgChargeDetails details = aoMergeOverweightBkgChargeDetailsService.lambdaQuery()
                .eq(AoMergeOverweightBkgChargeDetails::getAoMergeOverweightBkgId, one.getId())
                .eq(AoMergeOverweightBkgChargeDetails::getChargeItem, "6")
                .one();
        electronicVoucherVo.setWeight(details.getNumber().toString());

        SysY100PriceVo priceInfo = sysY100PriceService.getPriceInfo(order.getOrg(), order.getDst());
        if(priceInfo==null || priceInfo.getPrice()==null){
            priceInfo = sysY100PriceService.getPriceInfo(flightRealInfo.getOrg(), flightRealInfo.getDst());
        }
        if (priceInfo != null && priceInfo.getPrice() !=null) {
            BigDecimal price = new BigDecimal(priceInfo.getPrice()).multiply(new BigDecimal("0.015"));
            String yPrice = price.setScale(0, RoundingMode.HALF_UP).setScale(2).toString();
            electronicVoucherVo.setRate(yPrice);
        }

        electronicVoucherVo.setTotal(electronicVoucherVo.getCharge());
        return electronicVoucherVo;
    }
}
