package com.swcares.psi.aoMergeOrder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeDiscountListSaveDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeQueryDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCardTypeDiscountList;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeCardTypeDiscountListMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCardTypeDiscountListService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderTypeListVo;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.PsiPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 合并支付卡级别折扣清单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Service
public class AoMergeCardTypeDiscountListServiceImpl extends ServiceImpl<AoMergeCardTypeDiscountListMapper, AoMergeCardTypeDiscountList> implements IAoMergeCardTypeDiscountListService {


    @Override
    public void saveOrUpdate(AoMergeCardTypeDiscountListSaveDto dto) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        String username = authentication.getRealName();
        LocalDateTime now = LocalDateTime.now();
        log.info("{}-{}操作合并订单折扣配置{}", currentUser, username, JSON.toJSONString(dto));
        if (StringUtils.isEmpty(dto.getId())) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        AoMergeCardTypeDiscountList byId1 = this.getById(dto.getId());
        if (byId1 == null) {
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        BeanUtils.copyProperties(dto, byId1);
        byId1.setUpdateDate(now);
        byId1.setUpdateUser(username + "-" + currentUser);
        this.saveOrUpdate(byId1);
    }

    @Override
    public void deleteConfig(String id) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        String username = authentication.getRealName();
        LocalDateTime now = LocalDateTime.now();
        log.info("{}-{}删除合并订单折扣配置{}", currentUser, username, id);
        this.lambdaUpdate()
                .set(AoMergeCardTypeDiscountList::getStatus, AoMergeCardTypeDiscountList.STATUS_DELETE)
                .eq(AoMergeCardTypeDiscountList::getId, id)
                .set(AoMergeCardTypeDiscountList::getUpdateDate, now)
                .set(AoMergeCardTypeDiscountList::getUpdateUser, username + "-" + currentUser)
                .update();
    }

    @Override
    public List<AoMergeOrderTypeListVo> getPaxTypeList() {
        List<AoMergeCardTypeDiscountList> list = this.lambdaQuery().list();
        List<AoMergeOrderTypeListVo> result = new ArrayList<>();
        list.stream().forEach(e -> {
            AoMergeOrderTypeListVo vo = new AoMergeOrderTypeListVo();
            vo.setId(e.getId());
            vo.setCardType(e.getCardType());
        });

        return result;
    }


    @Override
    public PsiPage<AoMergeCardTypegetListVo> getPage(AoMergeCardTypeQueryDto dto) {
        PsiPage<AoMergeCardTypegetListVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.getPage(page, dto);
    }

    @Override
    public List<AoMergeCardTypegetListVo> getList(AoMergeCardTypeQueryDto dto) {
        return baseMapper.getList(dto);
    }

}
