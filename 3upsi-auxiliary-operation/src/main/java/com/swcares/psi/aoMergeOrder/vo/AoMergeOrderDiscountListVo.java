package com.swcares.psi.aoMergeOrder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 打包订单组合折扣配置列表VO
 */
@Data
public class AoMergeOrderDiscountListVo {

    @ExcelIgnore
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "打包产品组合编码（1逾重行李2柜台升舱3付费选座4一人多座5贵宾室购买）")
    @ExcelIgnore
    private String itemsCode;

    @ApiModelProperty(value = "打包产品名称")
    @ExcelProperty(value = "打包产品名称")
    @ColumnWidth(40)
    private String itemsName;

    @ExcelProperty(value = "折扣率")
    @ApiModelProperty(value = "折扣率")
    private String discount;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "状态")
    @ApiModelProperty(value = "状态（0停用,1启用）")
    private String status;

    @ColumnWidth(24)
    @ExcelProperty(value = "更新时间")
    @ApiModelProperty(value = "修改时间")
    private String updateDate;

    @ColumnWidth(24)
    @ExcelProperty(value = "更新人")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

}
