package com.swcares.psi.modules.follow.passenger.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.base.data.api.common.SmsBusinessTypeEnum;
import com.swcares.psi.base.data.api.entity.SmsTemplate;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.service.SmsTemplateService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.rce.RceSendMessageFailedException;
import com.swcares.psi.common.utils.rce.RceUtil;
import com.swcares.psi.message.api.enums.MessageTypeEnum;
import com.swcares.psi.message.api.form.MessageSendForm;
import com.swcares.psi.message.service.MessageService;
import com.swcares.psi.message.util.MessageBuilder;
import com.swcares.psi.modules.follow.passenger.PassengerFollowTypeEnum;
import com.swcares.psi.modules.follow.passenger.entity.SysPaxFollow;
import com.swcares.psi.modules.follow.passenger.mapper.PassengerFollowMapper;
import com.swcares.psi.modules.follow.passenger.service.PassengerFollowService;
import com.swcares.psi.passengereventsubscribe.dto.FocusonMessageDto;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ClassName：com.swcares.psi.follow.passenger.service.impl.PassengerFollowServiceImpl <br>;
 * Description：旅客关注服务实现 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/8/27 3:38 <br>;
 * @version v1.0 <br>;
 */
@Slf4j
@Service
public class PassengerFollowServiceImpl extends ServiceImpl<PassengerFollowMapper, SysPaxFollow> implements PassengerFollowService {

    @Resource
    private PassengerFollowMapper passengerFollowMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private SmsTemplateService smsTemplateService;

    @Resource
    private SysAirportInfoService sysAirportInfoService;

    @Autowired
    private RceUtil rceUtil;

    @Override
    public void sendMessages(FocusonMessageDto focusonMessageDto, PassengerFollowTypeEnum passengerFollowTypeEnum) {

        LambdaQueryWrapper<SysPaxFollow> lambdaQueryChainWrapper = Wrappers.lambdaQuery();
        lambdaQueryChainWrapper.eq(SysPaxFollow::getFollowIdNo, focusonMessageDto.getIdNumber())
                .like(SysPaxFollow::getFollowStatus, passengerFollowTypeEnum.getCode())
                .like(SysPaxFollow::getFollowType, "0")
                .eq(SysPaxFollow::getStatus, "1");

        List<SysPaxFollow> sysPaxFollows = this.list(lambdaQueryChainWrapper);

        if (CollectionUtils.isEmpty(sysPaxFollows)) {
            log.info("旅客关注事件站内信通知触发后置拦截，非关注旅客。事件类型:{},事件:{}", passengerFollowTypeEnum.getType(), focusonMessageDto.brief());
            return;
        }

        sysPaxFollows.forEach(o -> {
            try {
                MessageSendForm messageSendForm = getMessageSendForm();

                SmsTemplate template = smsTemplateService.getOne(Wrappers.<SmsTemplate>lambdaQuery()
                        .eq(SmsTemplate::getTemplateCode, SmsBusinessTypeEnum.PASSENGER_FOLLOW_MODE_CODE.getCode())
                        .eq(SmsTemplate::getState, SmsTemplate.STATE_AVAILABLE));

                String messageMode = template.getTemplateContent();

                String msgContent = getMessage(messageMode, focusonMessageDto, passengerFollowTypeEnum, sysPaxFollows.get(0));

                //获取发送内容
                messageSendForm.setMsgContent(msgContent);
                messageSendForm.setFlightNo(focusonMessageDto.getFlightNumber());
                messageSendForm.setFlightDate(focusonMessageDto.getFlightDate());
                String[] userIds = { o.getCreateUser()};
                messageSendForm.setMsgReplyUser(userIds);
                messageService.sendMsgToUser(messageSendForm);
                log.info("旅客关注站内信发送成功，发送的对象-》{}", messageSendForm);
            }catch (Exception e) {
                log.error("旅客关注站内信发送失败，入参-》{}, 关注规则-》{}", focusonMessageDto, o);
            }
        });
    }

    @Override
    public void sendSmsMessages(FocusonMessageDto focusonMessageDto, PassengerFollowTypeEnum passengerFollowTypeEnum) {

        LambdaQueryWrapper<SysPaxFollow> sysPaxFollowLambdaQueryChainWrapper = Wrappers.lambdaQuery();
        sysPaxFollowLambdaQueryChainWrapper.eq(SysPaxFollow::getFollowIdNo, focusonMessageDto.getIdNumber())
                .like(SysPaxFollow::getFollowStatus, passengerFollowTypeEnum.getCode())
                .like(SysPaxFollow::getFollowType, "1")
                .eq(SysPaxFollow::getStatus, "1");

        List<SysPaxFollow> sysPaxFollows = passengerFollowMapper.selectList(sysPaxFollowLambdaQueryChainWrapper);

        if (CollectionUtils.isEmpty(sysPaxFollows)) {
            log.info("旅客关注事件短信通知触发后置拦截，非关注旅客。事件类型:{},事件:{}", passengerFollowTypeEnum.getType(), focusonMessageDto.brief());
            return;
        }

        sysPaxFollows.forEach(o -> o.setFollowPaxPhone(AesEncryptUtil.decryption(o.getFollowPaxPhone())));

        smsSend(focusonMessageDto, passengerFollowTypeEnum, sysPaxFollows);
    }

    private MessageSendForm getMessageSendForm() {
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgTitle(MessageTypeEnum.PASSENGER_FOLLOW.getTypeName());
        sendForm.setMsgType(MessageTypeEnum.PASSENGER_FOLLOW.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.PASSENGER_FOLLOW.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.PASSENGER_FOLLOW.getChildType() + "");
        sendForm.setMsgChildTypeName(MessageTypeEnum.PASSENGER_FOLLOW.getChildTypeName());
        sendForm.setPcUrl(MessageTypeEnum.PASSENGER_FOLLOW.getPcUrl());
        sendForm.setMobileUrl(MessageTypeEnum.PASSENGER_FOLLOW.getMobileUrl());
        sendForm.setIsAudit(MessageTypeEnum.PASSENGER_FOLLOW.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }

    private String getMessage(String messageMode, FocusonMessageDto focusonMessageDto, PassengerFollowTypeEnum passengerFollowTypeEnum, SysPaxFollow sysPaxFollow) {

        messageMode = messageMode.replace("[passengerName]", sysPaxFollow.getFollowPaxName());
        messageMode = messageMode.replace("[flightNumber]", focusonMessageDto.getFlightNumber());
        messageMode = messageMode.replace("[flightDate]", focusonMessageDto.getFlightDate());
        messageMode = messageMode.replace("[org]", focusonMessageDto.getOrg());

        List<SysAirportInfoEntity> orgName = sysAirportInfoService.list(new LambdaQueryWrapper<SysAirportInfoEntity>().eq(SysAirportInfoEntity::getCode, focusonMessageDto.getOrg()));

        messageMode = messageMode.replace("[orgName]", CollectionUtils.isEmpty(orgName) ? "" : orgName.get(0).getAirportName());
        messageMode = messageMode.replace("[dst]", focusonMessageDto.getDst());

        List<SysAirportInfoEntity> dstName = sysAirportInfoService.list(new LambdaQueryWrapper<SysAirportInfoEntity>().eq(SysAirportInfoEntity::getCode, focusonMessageDto.getDst()));

        messageMode = messageMode.replace("[dstName]", CollectionUtils.isEmpty(dstName) ? "" : dstName.get(0).getAirportName());
        messageMode = messageMode.replace("[event]", passengerFollowTypeEnum.getMessage());

        return messageMode;
    }

    private void smsSend(FocusonMessageDto focusonMessageDto, PassengerFollowTypeEnum passengerFollowTypeEnum, List<SysPaxFollow> sysPaxFollows) {

        SmsTemplate template = smsTemplateService.getOne(Wrappers.<SmsTemplate>lambdaQuery()
                .eq(SmsTemplate::getTemplateCode, SmsBusinessTypeEnum.PASSENGER_FOLLOW_MODE_CODE.getCode())
                .eq(SmsTemplate::getState, SmsTemplate.STATE_AVAILABLE));

        String messageMode = template.getTemplateContent();

        String msgContent = getMessage(messageMode, focusonMessageDto, passengerFollowTypeEnum, sysPaxFollows.get(0));

        sysPaxFollows.forEach(ele -> {
            boolean flag = false;
            String sendUser = ele.getUpdateUser() != null ? ele.getUpdateUser() : ele.getCreateUser();
            try {
                rceUtil.sendMessage(sendUser, msgContent);
                flag = true;
                log.info("旅客关注办公助手消息发送成功，发送的对象-》{}, 短信内容-> {}", ele, msgContent);
            } catch (RceSendMessageFailedException e) {
                log.error("旅客关注办公助手消息发送失败，发送的对象-》{}, 短信内容-> {}", ele, msgContent, e);
            } catch (Exception e) {
                log.error("旅客关注办公助手消息发送出错，发送的对象-》{}, 短信内容-> {}", ele, msgContent, e);
            }

            if (flag) {
                MessageSendForm messageSendForm = MessageBuilder.getMessageSendForm(MessageTypeEnum.PASSENGER_FOLLOW_RCE);
                //获取发送内容
                messageSendForm.setMsgContent(msgContent);
                messageSendForm.setMsgReplyUser(new String[]{sendUser});
                try {
                    messageService.sendMsgToUser(messageSendForm);
                } catch (Exception e) {
                    log.error("旅客关注办公助手通知后站内信通知记录出错，发送的对象-》{}, 短信内容-> {}", ele, msgContent, e);
                }
            }
        });
    }
}
