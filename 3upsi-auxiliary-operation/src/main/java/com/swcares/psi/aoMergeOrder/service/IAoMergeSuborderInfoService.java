package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSalesRecordDetailDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeComparisonGrowthVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSellDetailsVo;

/**
 * <p>
 * 合并支付订单子订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeSuborderInfoService extends IService<AoMergeSuborderInfo> {
    AoMergeComparisonGrowthVo getComparisonGrowth();
    AoMergeSellDetailsVo salesRecordByUser(AoMergeSalesRecordDetailDto dto);
}
