import com.swcares.psi.AoApplication;
import com.swcares.psi.ao.register.dto.VIPRegisterDto;
import com.swcares.psi.ao.register.interfaces.crm.PapersRegister;
import com.swcares.psi.ao.register.interfaces.sensors.ShortUrl;
import com.swcares.psi.ao.register.interfaces.sensors.impl.ShortUrlImpl;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;

/**
 * Description: []
 * Created on 2020-11-17 9:47
 *
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootTest(classes = AoApplication.class)
public class Tests {

    @Resource
    private PapersRegister papersRegister;

    @Resource
    private ShortUrl aShortUrl;

    @Test
    public void testPersVerification() throws IOException {
        VIPRegisterDto vipVerificationDto = new VIPRegisterDto();
        vipVerificationDto.setBirthDate("dsads");
        papersRegister.papersRegister(vipVerificationDto);

    }

    @Test
    public void testPersVerifications() throws IOException {

        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("id","sadsa");
        objectObjectHashMap.put("type", "like");
        System.out.println(new ShortUrlImpl().getShortUrl("https://newpssn.sichuanair.com:10072/memberInformation?id=sadsad&type=link", objectObjectHashMap));




//        HashMap<String, String> stringStringHashMap = new HashMap<>();
//        stringStringHashMap.put("id","sdads");
//        stringStringHashMap.put("type", "sdadsadsa");
//        String dataTemp = JSONObject.toJSONString(stringStringHashMap);
//        HashMap<String, String> baseData = new HashMap<>();
//        baseData.put("target_url", "ssdsds");
//        baseData.put("properties", dataTemp);
//        List<Map<String, String>> maps = new ArrayList<>();
//        maps.add(baseData);
//        String s = JSONObject.toJSONString(maps);
//        System.out.println(s);

//        String post = HttpUtil.post("https://s.sichuanair.com:8107/api/short_url/create?project=production&token=e7a9acb0905781515bc3bf33981125aac4b9c12d4a8e901451d5bfa8280d65cd", "" +
//                "[{\n" +
//                " \"target_url\":\"https://newpssn.sichuanair.com:10072?id=sadsad&type=link\",\n" +
//                " \"properties\":\"{\\\"$id\\\":\\\"sadsad\\\",\\\"$type\\\":\\\"link\\\"}\"\n" +
//                "}]");
//        System.out.println(post);
//        System.out.println(JSONObject.parseArray(post, DivineStrategyShortDto.class));

    }
}
