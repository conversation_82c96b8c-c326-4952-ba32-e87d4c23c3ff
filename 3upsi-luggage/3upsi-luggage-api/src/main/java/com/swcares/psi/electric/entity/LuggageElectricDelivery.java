package com.swcares.psi.electric.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on  2022/4/7
 * Title:       [行李电子交付]
 * Description: [行李电子交付]
 *
 * @author: like
 * @version: 1.0
 */
@Data
@TableName("DP_LUGGAGE_ELECTRIC_DELIVERY")
public class LuggageElectricDelivery {
    /**
     * 删除标识 0未删除 1删除
     */
    public static final String IS_CANCEL_YES = "1";
    public static final String IS_CANCEL_NO = "0";

    /**
     * 扫描结果 0失败 1成功
     */
    public static final String IS_SCAN_SUCCESS = "1";
    public static final String IS_SCAN_FAIL = "0";

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "旅客票号")
    @TableField(value = "TKT_NO")
    private String tktNo;

    @ApiModelProperty(value = "行李号")
    @TableField(value = "PKG_NO")
    private String pkgNo;

    @ApiModelProperty(value = "航班号")
    @TableField(value = "FLIGHT_NO")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @TableField(value = "FLIGHT_DATE")
    private String flightDate;

    @ApiModelProperty(value = "始发地")
    @TableField(value = "ORG")
    private String org;

    @ApiModelProperty(value = "到达地")
    @TableField(value = "DST")
    private String dst;

    @ApiModelProperty(value = "扫描行李牌 0失败 1 成功")
    @TableField(value = "IS_SCAN")
    private String isScan;

    @ApiModelProperty(value = "扫描日期")
    @TableField(value = "CREATE_TIME")
    private String createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    @ApiModelProperty(value = "是否删除")
    @TableField(value = "IS_CANCEL")
    private String isCancel;
}
