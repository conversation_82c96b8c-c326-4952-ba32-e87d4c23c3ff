package com.swcares.psi.base.data.api.vo;


import lombok.Data;

@Data
public class LocalFlightDynamicInfoVo {


    /**
     * 通知上客时间
     */
    private String onBoardTime;

    /**
     * 预计上客
     */
    private String estimateSkTime;

    /**
     * 取消标志
     */
    private String flgCs;

    /**
     * 调整类型
     */
    private String adjustType;

    /**
     * 延误标志
     */
    private String flgDelay;

    /**
     * 预计起飞时间
     */
    private String etd;

    /**
     * 计划起飞时间
     */
    private String std;

    /**
     * 登机口
     */
    private String gate;

    /**
     * 实际出发时间
     */
    private String atd;

    private String flightDate;
    private String flightNo;
    private String org;
    private String dst;
}
