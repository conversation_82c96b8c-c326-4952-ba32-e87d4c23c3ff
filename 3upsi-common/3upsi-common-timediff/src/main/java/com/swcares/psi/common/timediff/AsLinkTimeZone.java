/**
 * @Title: AsLinkTimeZone.java<br>
 * @Package com.swcares.aslink.util<br>
 * @Description: TODO(用一句话描述该文件做什么)<br>
 * <AUTHOR>
 * @date 2017年7月24日
 * @version V1.0
 */
package com.swcares.psi.common.timediff;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * Created on 2017年7月24日<br>
 * Title: [3UPSI]_[模块名]<br>
 * Description: [国际航班的时差处理]<br>
 * Copyright: Copyright (c) 2017<br>
 * Company: 民航西南凯亚<br>
 * Department: 研发部<br>
 * 
 * <AUTHOR>   
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AsLinkTimeZone implements InitializingBean {

    public static Map<String, Integer> TIME_ZONE = new HashMap<>();
    public static Map<String, Map<String, Object>> TIME_ZONE_WITH_SUMMER = new HashMap<>();

    private JdbcTemplate jdbcTemplate;
    private final DataSource dataSource;
    /**
     * 初始化时区数据
     */
    public static void initTimeZone() {
        // 温哥华
        if (isSummerInYvr()) {
            AsLinkTimeZone.TIME_ZONE.put("YVR", 900);
        } else {
            AsLinkTimeZone.TIME_ZONE.put("YVR", 960);
        }
        // 墨尔本
        if (isSummerInMel()) {
            AsLinkTimeZone.TIME_ZONE.put("MEL", -180);
        } else {
            AsLinkTimeZone.TIME_ZONE.put("MEL", -120);
        }
        // 布拉格
        if (isSummerInPrg()) {
            AsLinkTimeZone.TIME_ZONE.put("PRG", 420);
        } else {
            AsLinkTimeZone.TIME_ZONE.put("PRG", 360);
        }
        // 洛杉矶
        if (isSummerInlax()) {
            AsLinkTimeZone.TIME_ZONE.put("LAX", 900);
        } else {
            AsLinkTimeZone.TIME_ZONE.put("LAX", 960);
        }
        // 其他都是固定的
        AsLinkTimeZone.TIME_ZONE.put("SPN", -120);
        AsLinkTimeZone.TIME_ZONE.put("ICN", -60);
        AsLinkTimeZone.TIME_ZONE.put("SGN", 60);
        AsLinkTimeZone.TIME_ZONE.put("HAN", 60);
        AsLinkTimeZone.TIME_ZONE.put("HKT", 60);
        AsLinkTimeZone.TIME_ZONE.put("CGK", 60);
        AsLinkTimeZone.TIME_ZONE.put("MEL", 180);
        AsLinkTimeZone.TIME_ZONE.put("KTM", 135);
        AsLinkTimeZone.TIME_ZONE.put("SYD", -120);
        AsLinkTimeZone.TIME_ZONE.put("SVO", 300);
        AsLinkTimeZone.TIME_ZONE.put("DXB", 240);
        AsLinkTimeZone.TIME_ZONE.put("DAD", 60);
    }

    /**
     * 计算洛杉矶是不是在夏天
     * 
     * @return
     */
    private static boolean isSummerInlax() {
        // 布拉格：洛杉矶，开始时间（4月第1个星期日） 结束时间（11月第1个星期日）
        boolean flag = false;
        Calendar current = Calendar.getInstance();
        // 创建3月最后1个星期日
        Calendar apr = Calendar.getInstance();
        apr.set(Calendar.YEAR, current.get(Calendar.YEAR));
        apr.set(Calendar.MONTH, 3);
        apr.set(Calendar.DAY_OF_WEEK, 1);
        apr.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        // 创建10月最后1个星期日
        Calendar oct = Calendar.getInstance();
        oct.set(Calendar.YEAR, current.get(Calendar.YEAR));
        oct.set(Calendar.MONTH, 10);
        oct.set(Calendar.DAY_OF_WEEK, 1);
        oct.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        if (current.getTimeInMillis() > apr.getTimeInMillis() && current.getTimeInMillis() < oct.getTimeInMillis()) {
            flag = true;
        }
        return flag;
    }

    /**
     * 计算布拉格是不是在夏天
     * 
     * @return
     */
    private static boolean isSummerInPrg() {
        // 布拉格：夏时令，开始时间（3月最后1个星期日2点） 结束时间（10月最后1个星期日3点）
        boolean flag = false;
        Calendar current = Calendar.getInstance();
        // 创建3月最后1个星期日
        Calendar mar = Calendar.getInstance();
        mar.set(Calendar.YEAR, current.get(Calendar.YEAR));
        mar.set(Calendar.MONTH, 3);
        mar.set(Calendar.DAY_OF_WEEK, 1);
        mar.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        mar.set(Calendar.HOUR_OF_DAY, 2);
        mar.set(Calendar.MINUTE, 0);
        mar.set(Calendar.DATE, mar.get(Calendar.DATE) - 7);
        // 创建10月最后1个星期日
        Calendar oct = Calendar.getInstance();
        oct.set(Calendar.YEAR, current.get(Calendar.YEAR));
        oct.set(Calendar.MONTH, 10);
        oct.set(Calendar.DAY_OF_WEEK, 1);
        oct.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        oct.set(Calendar.HOUR_OF_DAY, 3);
        oct.set(Calendar.MINUTE, 0);
        oct.set(Calendar.DATE, oct.get(Calendar.DATE) - 7);
        if (current.getTimeInMillis() > mar.getTimeInMillis() && current.getTimeInMillis() < oct.getTimeInMillis()) {
            flag = true;
        }
        return flag;
    }

    /**
     * 计算墨尔本是不是在夏天
     * 
     * @return
     */
    private static boolean isSummerInMel() {
        // 墨尔本：夏秋时令，开始时间（10月第一个星期日） 结束时间（4月第一个星期日）
        boolean flag = true;
        Calendar current = Calendar.getInstance();
        // 创建4月第一个星期日
        Calendar apr = Calendar.getInstance();
        apr.set(Calendar.YEAR, current.get(Calendar.YEAR));
        apr.set(Calendar.MONTH, 3);
        apr.set(Calendar.DAY_OF_WEEK, 1);
        apr.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        // 创建10月第一个星期日
        Calendar oct = Calendar.getInstance();
        oct.set(Calendar.YEAR, current.get(Calendar.YEAR));
        oct.set(Calendar.MONTH, 9);
        oct.set(Calendar.DAY_OF_WEEK, 1);
        oct.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        if (current.getTimeInMillis() > apr.getTimeInMillis() && current.getTimeInMillis() < oct.getTimeInMillis())
            flag = false;
        return flag;
    }

    /**
     * 计算温哥华是不是在夏天
     * 
     * @return
     */
    private static boolean isSummerInYvr() {
        // 温哥华：夏秋时令，开始时间（三月的第二个周日凌晨2） 结束时间（十一月的第一个周日凌晨2点）
        boolean flag = false;
        Calendar current = Calendar.getInstance();
        // 创建一个当年三月第二个周日
        Calendar mar = Calendar.getInstance();
        mar.set(Calendar.YEAR, current.get(Calendar.YEAR));
        mar.set(Calendar.MONTH, 2);
        mar.set(Calendar.DAY_OF_WEEK, 1);
        mar.set(Calendar.DAY_OF_WEEK_IN_MONTH, 2);
        mar.set(Calendar.HOUR_OF_DAY, 2);
        mar.set(Calendar.MINUTE, 0);
        // 创建一个十一月的第一个周日
        Calendar nov = Calendar.getInstance();
        nov.set(Calendar.YEAR, current.get(Calendar.YEAR));
        nov.set(Calendar.MONTH, 12);
        nov.set(Calendar.DAY_OF_WEEK, 1);
        nov.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);
        nov.set(Calendar.HOUR_OF_DAY, 2);
        nov.set(Calendar.MINUTE, 0);
        if (current.getTimeInMillis() > mar.getTimeInMillis() && current.getTimeInMillis() < nov.getTimeInMillis())
            flag = true;
        return flag;
    }

    /**
     * Description:[根据指定的年、月、第w周、第d天、小时获取具体日期]<br>
     * <AUTHOR>
     * @update 2018年1月4日
     * @param y 年
     * @param m 月
     * @param w 第w周
     * @param d 第d天
     * @param h 小时
     * @return 日期
     */
    private static Calendar getDateTimeByMonthWeek(int y, int m, int w, int d, int h) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, y);
        cal.set(Calendar.MONTH, m - 1); // y年的m-1月
        cal.set(Calendar.DAY_OF_WEEK, d); // w周的d天
        cal.set(Calendar.DAY_OF_WEEK_IN_MONTH, w); // 月的第w周
        cal.set(Calendar.HOUR_OF_DAY, h); // d天的h点
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal;
    }

    /**
     * Description:[判断航班日期std是否在配置夏令时的start和end时间区间范围内，flag表示夏令时区间存在跨年，因此通过配置参数比较后取反，例如墨尔本是当年10月—第二年4月为夏令时]<br>
     * <AUTHOR>
     * @update 2018年1月5日
     * @param start 夏令时开始时间
     * @param end 夏令结束时间
     * @param std 航班日期
     * @param flag true-表示夏令时不跨年，反之跨年
     * @return
     */
    private static boolean isSummerTime(Calendar start, Calendar end, Calendar std, boolean flag) {
        long lstd = std.getTimeInMillis();
        long lst = start.getTimeInMillis();
        long let = end.getTimeInMillis();
        boolean res = lstd >= lst && lstd <= let;
        return flag ? res : !res;
    }

    /**
     * Description:[通过加载得到的系统国际时差数据，获取当前机场对应的时差值，包括夏令时的处理;夏令时转换时间节点已调整为北京时间处理。]<br>
     * <AUTHOR>
     * @update 2018年1月4日
     * @param diffs 系统维护的国际时差数据集，里边包含夏令时开始和结束配置,机场三字码为key,夏令时配置的key带有下划线前缀，例如：_LAX
     * @param airCode 当前机场三字码
     * @param std 当前航班的计划起飞时间
     * @return
     */
    public static Integer getAirportTimeDifferen(String airCode, Date std) {
        return getAirportTimeDifferen(TIME_ZONE_WITH_SUMMER, airCode, std, true);
    }

    /**
     * Description:[通过加载得到的系统国际时差数据，获取当前机场对应的时差值，包括夏令时的处理]<br>
     * <AUTHOR>
     * @update 2018年1月4日
     * @param diffs 系统维护的国际时差数据集，里边包含夏令时开始和结束配置,机场三字码为key,夏令时配置的key带有下划线前缀，例如：_LAX
     * @param airCode 当前机场三字码
     * @param std 当前航班的计划起飞时间
     * @param flag true表示将夏令时转换时间节点调整为北京时间来处理
     * @return
     */
    public static Integer getAirportTimeDifferen(Map<String, Map<String, Object>> diffs, String airCode, Date std, boolean flag) {
        if (null == diffs || StringUtils.isBlank(airCode) || null == std) {
            return null;
        }
        Map<String, Object> dif = diffs.get(airCode.trim());
        // 夏令时时差配置
        Map<String, Object> sdif = diffs.get("_" + airCode.trim());
        Integer diff = null;
        BigDecimal timeLag = null;
        BigDecimal stl = null;
        BigDecimal tl = null;
        if (null == sdif && null != dif) {
            timeLag = getBigDecimal(dif.get("TIME_LAG"));
            log.info("{} 获取时差为：{}",airCode, timeLag.intValue());
        } else if (null != sdif && null != dif) {
            String start = (String) sdif.get("SUMMER_TIME_START"), end = (String) sdif.get("SUMMER_TIME_END");
            if (StringUtils.isNotBlank(start) && StringUtils.isNotBlank(end)) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(std);
                int y = cal.get(Calendar.YEAR), s = 0, t = 0;
                stl = getBigDecimal(sdif.get("TIME_LAG"));
                tl = getBigDecimal(dif.get("TIME_LAG"));
                if (flag) {
                    s = (stl.intValue() < 0) ? (stl.intValue() * -1) / 60 : stl.intValue() / 60;
                    t = (tl.intValue() < 0) ? (tl.intValue() * -1) / 60 : tl.intValue() / 60;
                }
                Calendar scal = configConvertCalendar(start, y, t);
                Calendar ecal = configConvertCalendar(end, y, s);
                if (isSummerTime(scal, ecal, DateUtils.toCalendar(std), judgeBeyondYear(start, end))) {
                    timeLag = stl;
                    log.info("{} 获取夏令时：{}", airCode, timeLag.intValue());
                } else {
                    timeLag = tl;
                    log.info("{} 获取冬令时：{}", airCode, timeLag.intValue());
                }
            }
        }
        if (null != timeLag) {
            diff = timeLag.intValue();
        }
        return diff;
    }

    /**
     * Description:[通过加载得到的系统国际时差数据，获取当前机场对应的时差值，包括夏令时的处理,并将得到的时差值乘以-1来取反]<br>
     * 处理逻辑：[业务复杂的方法罗列出处理逻辑，可选]<br>
     * 适用场景：[描述方法使用的业务场景，可选]<br>
     * <AUTHOR>
     * @update 2018年1月4日
     * @param diffs 系统维护的国际时差数据集，里边包含夏令时开始和结束配置
     * @param airCode 当前机场三字码
     * @param std 当前航班的计划起飞时间
     * @return
     */
    public static Integer getAirportTimeDifferenInvert(Map<String, Map<String, Object>> diffs, String airCode, Date std) {
        Integer val = getAirportTimeDifferen(diffs, airCode, std, false);
        if (null == val)
            return val;
        return val * -1;
    }

    /**
     * Description:[将时差表中的夏令时配置转换为对应的日期]<br>
     * <AUTHOR>
     * @update 2018年1月4日
     * @param config
     * @param y
     * @return
     */
    private static Calendar configConvertCalendar(String config, int y, int k) {
        String[] stmp = config.split(",");
        int m = Integer.valueOf(stmp[0]).intValue(), w = Integer.valueOf(stmp[1]).intValue(), d = Integer.valueOf(stmp[2]).intValue(),
                h = Integer.valueOf(stmp[3]).intValue();
        if (k > 0)
            h = h + k;
        return getDateTimeByMonthWeek(y, m, w, d, h);
    }

    /**
     * Description:[判断夏令时开始结束时间是否跨年，比如：墨尔本 当年10月至第二年4月为夏令时]<br>
     * <AUTHOR>
     * @update 2018年1月5日
     * @param start
     * @param end
     * @param y
     * @return
     */
    private static boolean judgeBeyondYear(String start, String end) {
        String[] stmp = start.split(","), etmp = end.split(",");
        int sm = Integer.valueOf(stmp[0]).intValue(), em = Integer.valueOf(etmp[0]).intValue();
        return sm < em;
    }

    /**
     * Description:[加载旅服系统维护的国际时差，包含夏令时开始结束配置]<br>
     * <AUTHOR>
     * @update 2018年1月4日
     * @return
     * @throws SQLException
     */
    public Map<String, Map<String, Object>> loadTimeDifferen() throws SQLException {
        Map<String, Map<String, Object>> diffs = null;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT T.AIRPORT_3CODE,T.TIME_LAG,T.SUMMER_TIME_START,T.SUMMER_TIME_END FROM SYS_INTER_TIME_DIFFEREN T WHERE T.STATU='1'");
        List<Map<String, Object>> rs = jdbcTemplate.queryForList(sql.toString());
        diffs = new HashMap<>();
        String code = null;
        String start = null;
        String end = null;
        for (Map<String, Object> map : rs) {
            code = (String) map.get("AIRPORT_3CODE");
            start = (String) map.get("SUMMER_TIME_START");
            end = (String) map.get("SUMMER_TIME_END");
            if (StringUtils.isNotBlank(start) && StringUtils.isNotBlank(end)) {
                diffs.put("_" + code.trim(), map);
            } else {
                diffs.put(code.trim(), map);
            }
        }
        return diffs;
    }

    /**
     * Description:[通过访问FOC提供的国际航班航站获取时区时差]<br>
     * <AUTHOR>
     * @update 2017年8月15日
     * @return
     */
    public Map<String, Integer> findFlightDateTimeZone() {
        Map<String, Integer> dtZone = null;
        try {
            StringBuffer sql = new StringBuffer();
            sql.append("SELECT TC.*,T.AIRPORT_3CODE FROM AIRPORT_LOCAL_TIME_CONVERSION TC ");
            sql.append("	LEFT JOIN FOC50_T7001 T ON TC.AIRPORT_4CODE = T.AIRPORT_4CODE ");
            List<Map<String, Object>> rs = jdbcTemplate.queryForList(sql.toString());
            if (null != rs && !rs.isEmpty()) {
                dtZone = new HashMap<String, Integer>();
                BigDecimal timeLag;
                String code;
                for (Map<String, Object> map : rs) {
                    code = (String) map.get("AIRPORT_3CODE");
                    timeLag = (BigDecimal) map.get("TIME_LAG");
                    if (null != timeLag) {
                        dtZone.put(code, timeLag.intValue());
                    }
                }
            }
            return dtZone;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtZone;
    }

    /**
     * Description:[通过访问FOC提供的国际航班航站获取时区时差,并将得到的时差值乘以-1来取反]<br>
     * <AUTHOR>
     * @update 2017年8月15日
     * @return
     */
    public Map<String, Integer> findFlightDateTimeZoneInvert() {
        Map<String, Integer> zone = findFlightDateTimeZone();
        Map<String, Integer> invert = null;
        if (null != zone && !zone.isEmpty()) {
            invert = new HashMap<String, Integer>();
            String key = null;
            for (Iterator<String> it = zone.keySet().iterator(); it.hasNext();) {
                key = it.next();
                invert.put(key, (zone.get(key) * -1));
            }
        }
        return invert;
    }


    public static BigDecimal getBigDecimal(Object value) {
        BigDecimal ret = null;
        if (value != null) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal) value;
            } else if (value instanceof String) {
                ret = new BigDecimal((String) value);
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                ret = new BigDecimal(((Number) value).doubleValue());
            } else {
                throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
            }
        }
        return ret;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        jdbcTemplate = new JdbcTemplate(dataSource);
        TIME_ZONE_WITH_SUMMER = loadTimeDifferen();
    }
}
