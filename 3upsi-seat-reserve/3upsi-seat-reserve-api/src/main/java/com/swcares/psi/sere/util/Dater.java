package com.swcares.psi.sere.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.Vector;
import org.apache.commons.lang.StringUtils;

/**
 * 日期工具
 * 
 * <AUTHOR>
 */
public class Dater {

    /** 月份 */
    private static final Map<String, String> MONTH = new HashMap<>();
    /** 星期 */
    private static final Map<String, String> WEEK = new HashMap<>();
    /** 中文数字 */
    private static final Map<String, String> NUMBER = new HashMap<>();

    public static final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    public static final DateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static final DateFormat DATETIME_NOSECOND_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    public static final DateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm:ss");
    public static final DateFormat TIME_NOSECOND_FORMAT = new SimpleDateFormat("HH:mm");
    public static final DateFormat TIMESTAMP_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    static {
        MONTH.put("01", "JAN");
        MONTH.put("02", "FEB");
        MONTH.put("03", "MAR");
        MONTH.put("04", "APR");
        MONTH.put("05", "MAY");
        MONTH.put("06", "JUN");
        MONTH.put("07", "JUL");
        MONTH.put("08", "AUG");
        MONTH.put("09", "SEP");
        MONTH.put("10", "OCT");
        MONTH.put("11", "NOV");
        MONTH.put("12", "DEC");
        MONTH.put("JAN", "01");
        MONTH.put("FEB", "02");
        MONTH.put("MAR", "03");
        MONTH.put("APR", "04");
        MONTH.put("MAY", "05");
        MONTH.put("JUN", "06");
        MONTH.put("JUL", "07");
        MONTH.put("AUG", "08");
        MONTH.put("SEP", "09");
        MONTH.put("OCT", "10");
        MONTH.put("NOV", "11");
        MONTH.put("DEC", "12");
        WEEK.put(Calendar.SUNDAY + "", "日");
        WEEK.put(Calendar.MONDAY + "", "一");
        WEEK.put(Calendar.TUESDAY + "", "二");
        WEEK.put(Calendar.WEDNESDAY + "", "三");
        WEEK.put(Calendar.THURSDAY + "", "四");
        WEEK.put(Calendar.FRIDAY + "", "五");
        WEEK.put(Calendar.SATURDAY + "", "六");
        NUMBER.put("0", "零");
        NUMBER.put("1", "一");
        NUMBER.put("2", "二");
        NUMBER.put("3", "三");
        NUMBER.put("4", "四");
        NUMBER.put("5", "五");
        NUMBER.put("6", "六");
        NUMBER.put("7", "七");
        NUMBER.put("8", "八");
        NUMBER.put("9", "九");
    }

    private Dater() {}

    /**
     * 获取当前时间
     * 
     * @return 当前时间
     */
    public static Date now() {
        return new Date(System.currentTimeMillis());
    }
    
    public static Date nowYmd() {
        return ymdToDate(ymd(now()));
    }
    
    public static LocalDateTime nowLdt() {
    	return LocalDateTime.now();
    }

    /**
     * 将yyyy-MM-dd或yyyy/MM/dd格式的日期字符串转换为日期类型
     * 
     * @param ymd yyyy-MM-dd或yyyy/MM/dd格式的字符串
     * @return 日期
     * @throws ParseException
     *             日期转换异常
     */
    public static Date ymd2Date(String ymd) throws ParseException {
        String nymd = ymd.trim();
        if (!nymd.contains("-") && !nymd.contains("/")) {
            throw new ParseException("日期格式无法转换：" + ymd, 0);
        }
        String year = "";
        if (nymd.contains("-")) {
            year = nymd.substring(0, nymd.indexOf("-"));
        } else if (nymd.contains("/")) {
            year = nymd.substring(0, nymd.indexOf("/"));
        }
        if (year.length() == 2) {
            int ny = Integer.parseInt("20" + year);
            int ly = Integer.parseInt("19" + year);
            int ty = Dater.year(Dater.now());
            if (Math.abs(ny - ty) < Math.abs(ly - ty)) {
                nymd = "20" + nymd;
            } else {
                nymd = "19" + nymd;
            }
        }
        String[] f = {"yyyy-MM-dd", "yyyy/MM/dd"};
        for (String o : f) {
            try {
                return parse(nymd, o);
            } catch (ParseException e) {
            }
        }
        throw new ParseException("日期格式无法转换：" + ymd, 0);
    }

    /**
     * 将yyyy-MM-dd HH:mm:ss格式的日期字符串转换为日期类型
     * 
     * @param ymdhms yyyy-MM-dd HH:mm:ss格式的字符串
     * @return 日期
     * @throws ParseException
     *             日期转换异常
     */
    public static Date ymdhms2Date(String ymdhms) throws ParseException {
        return parse(ymdhms, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * Description:处理（2018-8-12 13:24:34.0）不规则日期 <br>
     * <AUTHOR>
     * @update 2018年3月30日
     * @param str
     * @return
     */
    public static Date dateFormatDate(Date date) {
        try {
            return ymdhms2Date(ymdhms(date));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 将yyyy-MM-dd HH:mm:ss.SSS格式的日期字符串转换为日期类型
     * 
     * @param ymdhmss yyyy-MM-dd HH:mm:ss.SSS格式的字符串
     * @return 日期
     * @throws ParseException
     *             日期转换异常
     */
    public static Date ymdhmss2Date(String ymdhmss) throws ParseException {
        return parse(ymdhmss, "yyyy-MM-dd HH:mm:ss.SSS");
    }

    /**
     * 将yyyy-MM-dd HH:mm:ss.SSS格式的日期类型转换为日期字符串
     * 
     * @param ymdhmss
     *            yyyy-MM-dd HH:mm:ss.SSS格式的日期类型
     * @return 日期
     * @throws ParseException
     *             日期转换异常
     */
    public static String formatYmdhmss(Date ymdhmss) throws ParseException {
        return TIMESTAMP_FORMAT.format(ymdhmss);
    }

    /**
     * Description:[转换格式为yyyy-MM-dd的日期对象输出]<br>
     * 
     * <AUTHOR>
     * @update 2017年6月28日
     * @param ymd
     * @return
     * @throws ParseException
     */
    public static Date ymdToDate(String ymd) {
        try {
            return parse(ymd, "yyyy-MM-dd");
        } catch (ParseException e) {
        }
        return null;
    }

    /**
     * 将指定格式字符串转换为日期
     * 
     * @param s 字符串
     * @param pattern 格式
     * @return 日期
     * @throws ParseException
     */
    public static Date parse(String s, String pattern) throws ParseException {
        return new SimpleDateFormat(pattern).parse(s);
    }

    /**
     * 将日期转换成yyyy-MM-dd格式的字符串
     * 
     * @param date 日期
     * @return yyyy-MM-dd格式的字符串
     */
    public static String ymd(Date date) {
        return format(date, "yyyy-MM-dd");
    }
    
    public static String ymd2(Date date) {
        return format(date, "yyyyMMdd");
    }
    
    public static String md(Date date) {
        return format(date, "MM-dd");
    }

    /**
     * 将日期转换成HH:mm:ss格式的字符串
     * 
     * @param date 日期
     * @return HH:mm:ss格式的字符串
     */
    public static String hms(Date date) {
        return format(date, "HH:mm:ss");
    }

    /**
     * 将日期转换成yyyy-MM-dd HH:mm:ss格式的字符串
     * 
     * @param date 日期
     * @return yyyy-MM-dd HH:mm:ss格式的字符串
     */
    public static String ymdhms(Date date) {
        return format(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * Title：ymd <br>
     * Description：获取当前日期，yyyyMMdd格式的字符串输出 <br>
     * author：罗江林 <br>
     * date：2020年4月22日 下午3:17:19 <br>
     * @return <br>
     */
    public static String ymd() {
        return format(now(), "yyyyMMdd");
    }

    /**
     * 将日期转换成指定格式的字符串
     * 
     * @param date
     *            日期
     * @return 指定格式的字符串
     */
    public static String format(Date date, String pattern) {
        if (null == date) {
            return "";
        }
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * Description:[获取指定时间的指定毫秒数以前的时间]<br>
     * 
     * @author: luojl
     * @update: 2017年6月12日
     * @param d 指定时间
     * @param mi 毫秒数
     * @return
     * @throws ParseException
     */
    public static Date previous(Date d, long mi) throws ParseException {
        return ymdhms2Date(ymdhms(new Date(d.getTime() - mi)));
    }

    /**
     * Description:[获取当前时间的指定毫秒数以前的时间]<br>
     * 
     * @author: luojl
     * @update: 2017年6月12日
     * @param mi 毫秒数
     * @return
     */
    public static String previous(long mi) {
        return ymdhms(new Date(now().getTime() - mi));
    }

    /**
     * 指定类型往前推
     * 
     * @param type 类型：年、月、日等
     * @param date 参考日期
     * @param length 长度
     * @return 往前推的日期
     */
    public static Date previous(int type, Date date, int length) {
        return next(type, date, 0 - length);
    }

    /**
     * 指定类型往后推
     * 
     * @param type 类型：年、月、日等
     * @param date 参考日期
     * @param length 长度
     * @return 往后推的日期
     */
    public static Date next(int type, Date date, int length) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(type, length);
        return c.getTime();
    }

    /**
     * 获得前几天的日期
     * 
     * @param date 参考日期
     * @return 前几天的日期
     */
    public static Date previous(Date date, int days) {
        return previous(Calendar.DATE, date, days);
    }

    /**
     * 获得后几天的日期
     * 
     * @param date 参考日期
     * @return 后几天的日期
     */
    public static Date next(Date date, int days) {
        return next(Calendar.DATE, date, days);
    }

    /**
     * 月份往后推的日期
     * 
     * @param date
     *            参考日期
     * @param months
     *            推后几个月
     * @return 月份往后推的日期
     */
    public static Date nextMonth(Date date, int months) {
        return next(Calendar.MONTH, date, months);
    }

    /**
     * 月份往前推的日期
     * 
     * @param date 参考日期
     * @param months 前推几个月
     * @return 月份往前推的日期
     */
    public static Date previousMonth(Date date, int months) {
        return previous(Calendar.MONTH, date, months);
    }

    /**
     * 年份往后推的日期
     * 
     * @param date 参考日期
     * @param months 推后几个月
     * @return 月份往后推的日期
     */
    public static Date nextYear(Date date, int years) {
        return next(Calendar.YEAR, date, years);
    }

    /**
     * 年份往前推的日期
     * 
     * @param date 参考日期
     * @param months 前推几个月
     * @return 月份往前推的日期
     */
    public static Date previousYear(Date date, int years) {
        return previous(Calendar.YEAR, date, years);
    }

    /**
     * 某日期的某类型的数值
     * 
     * @param type 类型：年、月、日等
     * @param date 日期
     * @return 数值
     */
    public static int get(int type, Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(type);
    }

    /**
     * 获取年份
     * 
     * @param date 日期
     * @return 年份
     */
    public static int year(Date date) {
        return get(Calendar.YEAR, date);
    }

    /**
     * 获取月份
     * 
     * @param date 日期
     * @return 月份，如01、10
     */
    public static String month(Date date) {
        int m = get(Calendar.MONTH, date) + 1;
        return (m < 10 ? "0" : "") + m;
    }

    /**
     * 获取天
     * 
     * @param date
     *            日期
     * @return 天，如01、23
     */
    public static String day(Date date) {
        int d = get(Calendar.DATE, date);
        return (d < 10 ? "0" : "") + d;
    }

    /**
     * 日期转换为DDMMM格式
     * 
     * @param ymd YYYY-MM-DD格式的字符串
     * @return 1MAY10 01MAY10 1MAY2010 01MAY2010
     */
    public static String dm(String ymd) {
        if(StringUtils.isEmpty(ymd)) {
            return ymd;
        }
        String[] n = ymd.split("-");
        return n[2] + MONTH.get(n[1]) + n[0].substring(2);
    }

    /**
     * 日期字符串转换为YYYY-MM-DD格式
     * 
     * @param dayMonthYear
     *            支持的参数格式: 1MAY 01MAY 1MAY10 01MAY10 1MAY2010 01MAY2010，不区分大小写，以上转换后为2010-05-01
     * @return YYYY-MM-DD格式的字符串
     */
    public static String ymd(String dayMonthYear) {
        return ymd(dayMonthYear, "20");
    }

    /**
     * 日期字符串转换为YYYY-MM-DD格式
     * 
     * @param dayMonthYear
     *            支持的参数格式: 1MAY 01MAY 1MAY10 01MAY10 1MAY2010 01MAY2010
     * @param yearPrefix
     *            年份前缀，如yearPrefix=19,1MAY09=1909-05-01
     * @return YYYY-MM-DD格式的字符串
     */
    public static String ymd(String dayMonthYear, String yearPrefix) {
        String s = dayMonthYear.toUpperCase();
        switch (s.length()) {
            case 4:
                s = "0" + s;
                s = s + Dater.year(Dater.now());
                break;
            case 5:
                s = s + Dater.year(Dater.now());
                break;
            case 6:
                s = "0" + s;
                s = s.substring(0, 5) + yearPrefix + s.substring(5);
                break;
            case 7:
                s = s.substring(0, 5) + yearPrefix + s.substring(5);
                break;
            case 8:
                s = "0" + s;
                break;
        }
        s = s.substring(5) + "-" + MONTH.get(s.substring(2, 5)) + "-" + s.substring(0, 2);
        return s;
    }

    /**
     * 获取某日期的星期中文表示
     * 
     * @param date
     *            日期
     * @return 日、一、二、三、四、五、六
     */
    public static String weekCH(Date date) {
        return WEEK.get(get(Calendar.DAY_OF_WEEK, date) + "");
    }

    /**
     * 获取某日期的月份中文表示
     * 
     * @param date
     *            日期
     * @return 一、二、三、四、五、六
     */
    public static String monthCH(Date date) {
        return new String[] {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"}[get(Calendar.MONTH, date)];
    }

    /**
     * 获取某日期的年份中文表示
     * 
     * @param date
     *            日期
     * @return 年份，如二零一零
     */
    public static String yearCH(Date date) {
        String[] y = (year(date) + "").split("");
        StringBuilder s = new StringBuilder();
        for (String o : y) {
            s.append(null == NUMBER.get(o) ? "" : NUMBER.get(o));
        }
        return s.toString();
    }

    /**
     * Description:[在当前日期上加上或减去(负数)指定整数（y）年]<br>
     * 
     * <AUTHOR>
     * @update 2017年6月28日
     * @param y
     * @return
     */
    public static String yearAddSub(int y) {
        Calendar specialDate = Calendar.getInstance();
        specialDate.add(Calendar.YEAR, y);
        return ymd(specialDate.getTime());
    }

    public static Date yearSubAdd(int y) throws ParseException {
        return ymdToDate(yearAddSub(y));
    }

    /**
     * Description:[在当前日期上加上或减去(负数)指定整数（d）天]<br>
     * 
     * <AUTHOR>
     * @update 2017年6月29日
     * @param d
     * @return
     * @throws ParseException
     */
    public static Date daySubAdd(int d) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, d);
        return ymdhms2Date(ymdhms(calendar.getTime()));
    }

    /**
     * Description:[两日期比较大小，d1小于d2时返回-1，d1大于d2时返回1，d1等于d2时返回0]<br>
     * 
     * <AUTHOR>
     * @update 2017年6月28日
     * @param d1
     * @param d2
     * @return
     */
    public static int compareTo(Date d1, Date d2) {
        return d1.compareTo(d2);
    }

    /**
     * Description:[日期比较，d1小于d2返回true]<br>
     * 
     * <AUTHOR>
     * @update 2017年6月28日
     * @param d1
     * @param d2
     * @return
     */
    public static boolean before(Date d1, Date d2) {
        return d1.before(d2);
    }

    /**
     * Description:[日期比较，d1大于d2返回true]<br>
     * 
     * <AUTHOR>
     * @update 2017年6月28日
     * @param d1
     * @param d2
     * @return
     */
    public static boolean after(Date d1, Date d2) {
        return d1.after(d2);
    }
    
    /**
     * Title：minuteDiffer <br>
     * Description：计算结束时间与开始时间的分钟差 <br>
     * author：罗江林 <br>
     * date：2021年1月4日 下午5:16:23 <br>
     * @param form 开始时间
     * @param end 结束时间
     * @return <br>
     */
    public static int minuteDiffer(Date form, Date end) {
        return (int) ((end.getTime() - form.getTime()) / (1000 * 60));  
    }

    /**
     * 对日期(时间)中的日进行加减计算. <br>
     * 例子: <br>
     * 如果Date类型的d为 2005年8月20日,那么 <br>
     * calculateByDate(d,-10)的值为2005年8月10日 <br>
     * 而calculateByDate(d,+10)的值为2005年8月30日 <br>
     * 
     * @param d 日期(时间).
     * @param amount 加减计算的幅度.+n=加n天;-n=减n天.
     * @return 计算后的日期(时间).
     */
    public static Date calculateByDate(Date d, int amount) {
        return calculate(d, GregorianCalendar.DATE, amount);
    }

    public static Date calculateByMinute(Date d, int amount) {
        return calculate(d, GregorianCalendar.MINUTE, amount);
    }

    public static Date calculateByYear(Date d, int amount) {
        return calculate(d, GregorianCalendar.YEAR, amount);
    }

    /**
     * 对日期(时间)中由field参数指定的日期成员进行加减计算. <br>
     * 例子: <br>
     * 如果Date类型的d为 2005年8月20日,那么 <br>
     * calculate(d,GregorianCalendar.YEAR,-10)的值为1995年8月20日 <br>
     * 而calculate(d,GregorianCalendar.YEAR,+10)的值为2015年8月20日 <br>
     * 
     * @param d 日期(时间).
     * @param field
     *            日期成员. <br>
     *            日期成员主要有: <br>
     *            年:GregorianCalendar.YEAR <br>
     *            月:GregorianCalendar.MONTH <br>
     *            日:GregorianCalendar.DATE <br>
     *            时:GregorianCalendar.HOUR <br>
     *            分:GregorianCalendar.MINUTE <br>
     *            秒:GregorianCalendar.SECOND <br>
     *            毫秒:GregorianCalendar.MILLISECOND <br>
     * @param amount 加减计算的幅度.+n=加n个由参数field指定的日期成员值;-n=减n个由参数field代表的日期成员值.
     * @return 计算后的日期(时间).
     */
    private static Date calculate(Date d, int field, int amount) {
        if (d == null)
            return null;
        GregorianCalendar g = new GregorianCalendar();
        g.setGregorianChange(d);
        g.add(field, amount);
        return g.getTime();
    }

    /**
     * 获取所有的时区编号. <br>
     * 排序规则:按照ASCII字符的正序进行排序. <br>
     * 排序时候忽略字符大小写.
     * 
     * @return 所有的时区编号(时区编号已经按照字符[忽略大小写]排序).
     */
    public static String[] fecthAllTimeZoneIds() {
        Vector<String> v = new Vector<>();
        String[] ids = TimeZone.getAvailableIDs();
        for (int i = 0; i < ids.length; i++) {
            v.add(ids[i]);
        }
        java.util.Collections.sort(v, String.CASE_INSENSITIVE_ORDER);
        v.copyInto(ids);
        v = null;
        return ids;
    }

    /**
     * 将日期时间字符串根据转换为指定时区的日期时间.
     * 
     * @param srcDateTime 待转化的日期时间.
     * @param dstTimeZoneId 目标的时区编号.
     * 
     * @return 转化后的日期时间.
     * @see #string2Timezone(String, String)
     */
    public static String string2TimezoneDefault(String srcDateTime, String dstTimeZoneId) {
        return string2Timezone(srcDateTime, dstTimeZoneId);
    }

    /**
     * 将日期时间字符串根据转换为指定时区的日期时间.
     * 
     * @param srcDateTime 待转化的日期时间.
     * @param dstTimeZoneId 目标的时区编号.
     * 
     * @return 转化后的日期时间.
     */
    public static String string2Timezone(String srcDateTime, String dstTimeZoneId) {
        if (StringUtils.isBlank(srcDateTime) || StringUtils.isBlank(dstTimeZoneId))
            return null;

        try {
            int diffTime = getDiffTimeZoneRawOffset(dstTimeZoneId);
            Date d = DATETIME_FORMAT.parse(srcDateTime);
            long nowTime = d.getTime();
            long newNowTime = nowTime - diffTime;
            d = new Date(newNowTime);
            return DATETIME_FORMAT.format(d);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String timeZoneConverGMT8(String dstTimeZoneId, String localDate) {
        try {
            if (StringUtils.isBlank(localDate) || StringUtils.isBlank(dstTimeZoneId))
                return null;
            int diffTime = getRawOffsetTimeZoneDiff(dstTimeZoneId);
            Date d = DATETIME_FORMAT.parse(localDate);
            long nowTime = d.getTime();
            long newNowTime = nowTime - diffTime;
            d = new Date(newNowTime);
            return DATETIME_FORMAT.format(d);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 日期(时间)转化为字符串.
     * 
     * @param formater 日期或时间的格式.
     * @param aDate java.util.Date类的实例.
     * @return 日期转化后的字符串.
     */
    public static String date2String(String formater, Date aDate) {
        if (formater == null || "".equals(formater)) {
            return null;
        }
        if (aDate == null) {
            return null;
        }
        return (new SimpleDateFormat(formater)).format(aDate);
    }

    /**
     * 当前日期(时间)转化为字符串.
     * 
     * @param formater 日期或时间的格式.
     * @return 日期转化后的字符串.
     */
    public static String date2String(String formater) {
        return date2String(formater, new Date());
    }

    /**
     * 获取系统当前默认时区与指定时区的时间差.(单位:毫秒)
     * 
     * @param timeZoneId 时区Id
     * @return 系统当前默认时区与指定时区的时间差.(单位:毫秒)
     */
    private static int getDiffTimeZoneRawOffset(String timeZoneId) {
        return TimeZone.getDefault().getRawOffset() - TimeZone.getTimeZone(timeZoneId).getRawOffset();
    }

    /**
     * Description:[指定时区与系统当前默认时区的时间差.(单位:毫秒)]<br>
     * 
     * <AUTHOR>
     * @update 2017年7月24日
     * @param timeZoneId
     * @return 指定时区与当前默认时区的时间差
     */
    private static int getRawOffsetTimeZoneDiff(String timeZoneId) {
        return TimeZone.getTimeZone(timeZoneId).getRawOffset() - TimeZone.getDefault().getRawOffset();
    }
    
    /**
     * Title：isEffectiveDate <br>
     * Description：判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致 <br>
     * author：罗江林 <br>
     * date：2021年1月21日 上午9:04:03 <br>
     * @param nowTime 当前时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return <br>
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /*public static void main(String[] args) throws ParseException {
    	String[] ids = fecthAllTimeZoneIds();
    	String nowDateTime = date2String("yyyy-MM-dd HH:mm:ss");
    	System.out.println("The time Asia/Shanhai is " + nowDateTime);// 程序本地运行所在时区为[Asia/Shanhai]
    	// 显示世界每个时区当前的实际时间
    	
    	 * for(int i=0;i <ids.length;i++){ System.out.println(" * " + ids[i] + " = " + string2TimezoneDefault(nowDateTime,ids[i])); }
    	 
    	// 显示程序运行所在地的时区
    	System.out.println("TimeZone.getDefault().getID()=" + TimeZone.getDefault().getID());
    	System.out.println("当前时间："+new Date(TimeZone.getDefault().getRawOffset()));
    	System.out.println("莫斯科时间与北京时间的时差为："+getRawOffsetTimeZoneDiff("Europe/Moscow"));
    	System.out.println("莫斯科时间转北京时间： " + timeZoneConverGMT8("Europe/Moscow", "2017-07-20 20:20:00"));
    	System.out.println("莫斯科当前时间： " + string2Timezone(nowDateTime, "Pacific/Honolulu"));
    }*/

}
