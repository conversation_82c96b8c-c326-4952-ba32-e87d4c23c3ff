package com.swcares.psi.aoMergeOrder.dataTransfer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.entity.AoTempCashReturnEntity;
import com.swcares.psi.ao.common.service.AoOrderInfoService;
import com.swcares.psi.ao.common.service.AoTempCashReturnService;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeOrderService;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeService;
import com.swcares.psi.ao.flightUpgrades.mapper.UserMapper;
import com.swcares.psi.ao.invoice.entity.AoInvoiceInfoDetailEntity;
import com.swcares.psi.ao.invoice.entity.AoInvoiceInfoEntity;
import com.swcares.psi.ao.invoice.service.AoInvoiceDetailInfoService;
import com.swcares.psi.ao.invoice.service.IAoInvoiceInfoService;
import com.swcares.psi.ao.manySeats.entity.AoManySeatsEntity;
import com.swcares.psi.ao.manySeats.service.ManySeatsService;
import com.swcares.psi.ao.model.AoViproomItemSell;
import com.swcares.psi.ao.overweightbkg.entity.AoOverweightBkg;
import com.swcares.psi.ao.overweightbkg.service.AoOverweightBkgService;
import com.swcares.psi.ao.sceneSeats.entity.AoSceneSeatsEntity;
import com.swcares.psi.ao.sceneSeats.service.SceneSeatsService;
import com.swcares.psi.ao.service.AoViproomItemConfService;
import com.swcares.psi.ao.service.AoViproomItemSellService;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.entity.*;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.base.data.api.dto.FltPaxInfoPageDto;
import com.swcares.psi.base.data.api.entity.*;
import com.swcares.psi.base.data.api.vo.FltPaxInfoPageVo;
import com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.ISysDictService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.base.file.FileUtils;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class DataTransferService {

    @Autowired
    FltFlightRealInfoService fltFlightRealInfoService;
    @Autowired
    UserMapper userMapper;
    @Autowired
    SysAirportInfoService sysAirportInfoService;
    @Autowired
    FltPassengerRealInfoMapper fltPassengerRealInfoMapper;
    @Resource
    ISysDictService sysDictService;


    @Autowired
    AoOrderInfoService aoOrderInfoService;
    @Autowired
    AoTempCashReturnService aoTempCashReturnService;
    @Autowired
    ManySeatsService manySeatsService;
    @Autowired
    AoCounterUpgradeOrderService aoCounterUpgradeOrderService;
    @Autowired
    AoViproomItemSellService aoViproomItemSellService;
    @Autowired
    SceneSeatsService sceneSeatsService;
    @Autowired
    AoOverweightBkgService aoOverweightBkgService;
    @Autowired
    IAoInvoiceInfoService aoInvoiceInfoService;
    @Autowired
    AoInvoiceDetailInfoService detailInfoService;




    @Autowired
    AoMergeCashReturnPayOrderInfoService aoMergeCashReturnPayOrderInfoService;
    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;
    @Autowired
    IAoMergePayOrderInfoService aoMergePayOrderInfoService;
    @Autowired
    IAoMergeManySeatsService aoMergeManySeatsService;
    @Autowired
    IAoMergeSceneSeatsService aoMergeSceneSeatsService;
    @Autowired
    IAoMergeOverweightBkgService aoMergeOverweightBkgService;
    @Autowired
    IAoMergeViproomItemSellService aoMergeViproomItemSellService;
    @Autowired
    IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    AoMergeOverweightBkgChargeDetailsService aoMergeOverweightBkgChargeDetailsService;
    @Autowired
    AoMergeSubOrderJoinCashReturnPayService aoMergeSubOrderJoinCashReturnPayService;
    @Autowired
    IAoMergeInvoiceInfoService aoMergeInvoiceInfoService;
    @Autowired
    AoMergeSubOrderJoinInvoiceInfoService aoMergeSubOrderJoinInvoiceInfoService;

    @Transactional(rollbackFor = Exception.class)
    public void operation(AoOrderInfoEntity orderInfoEntity, List<SysDict> currency_type) {
//        @ApiModelProperty(value = "单订单状态(0未支付，1支付失败，2支付完成，3退款中，4退款失败，5退款成功,6取消,7旅客发起退款)")
//        @ApiModelProperty(value = "合并订单状态(0未支付,1支付完成,2部分退款,3全部退款,4过期,5支付取消,6支付中) ")
        String mergeOrderNo = "MERGE_" + orderInfoEntity.getOrderNo();
        Integer count = aoMergeOrderInfoService.lambdaQuery().eq(AoMergeOrderInfo::getOrderNo, mergeOrderNo).count();
        if (count > 0) {
            throw new RuntimeException("重复迁移数据" + orderInfoEntity.getOrderNo());
        }

        orderInfoEntity.setCreateTime(orderInfoEntity.getOrderDate());


        AoMergeOrderInfo aoMergeOrderInfo = new AoMergeOrderInfo();

        aoMergeOrderInfo.setTurnTime(orderInfoEntity.getTurnTime());
        aoMergeOrderInfo.setTurnNumber(orderInfoEntity.getTurnNumber());
        aoMergeOrderInfo.setOrderNo(mergeOrderNo);
        aoMergeOrderInfo.setCashReceiveUser(orderInfoEntity.getCashReceiveUserId());
        aoMergeOrderInfo.setCashReceiveUserName(orderInfoEntity.getCashReceiveUser());
        aoMergeOrderInfo.setCashReceiveUserDepartment(orderInfoEntity.getCashReceiveDepartment());
        aoMergeOrderInfo.setCreateUser(orderInfoEntity.getUserName());
        aoMergeOrderInfo.setCreateNo(orderInfoEntity.getUserNo());
        aoMergeOrderInfo.setCreateUserDepartment(orderInfoEntity.getDepartment());
        aoMergeOrderInfo.setCreateDate(orderInfoEntity.getCreateTime());
        aoMergeOrderInfo.setAreaCompany(orderInfoEntity.getAreaCompany());
        Employee employee = userMapper.getUserByName(orderInfoEntity.getUserNo());
        aoMergeOrderInfo.setServicePort(employee.getCity3code());
        SysAirportInfoEntity one = sysAirportInfoService.getOne(Wrappers.<SysAirportInfoEntity>lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, employee.getCity3code()));
        aoMergeOrderInfo.setServicePortType(one.getType());
        //"合并订单状态(0未支付,1支付完成,2部分退款,3全部退款,4过期,5支付取消,6支付中)
        //单订单状态(0未支付，1支付失败，2支付完成，3退款中，4退款失败，5退款成功,6取消,7旅客发起退款)
        //"0", "2", "5", "6"
        switch (orderInfoEntity.getOrderStatus()){
            case "0":aoMergeOrderInfo.setMergeOrderStatus("0"); break;
            case "2":aoMergeOrderInfo.setMergeOrderStatus("1"); break;
            case "5":aoMergeOrderInfo.setMergeOrderStatus("3"); break;
            case "6":aoMergeOrderInfo.setMergeOrderStatus("5"); break;
        }
        aoMergeOrderInfoService.save(aoMergeOrderInfo);

        AoMergePayOrderInfo payOrderInfo = new AoMergePayOrderInfo();
        payOrderInfo.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
        payOrderInfo.setPayType(orderInfoEntity.getChargeType());
        payOrderInfo.setPrice(new BigInteger(orderInfoEntity.getOrderPrice().toString()));
        payOrderInfo.setCurrencySpecies(orderInfoEntity.getCurrencySpecies());
        Optional<SysDict> first = currency_type.stream().filter(e -> e.getDataCode().equals(orderInfoEntity.getCurrencySpecies())).findFirst();
        if (first.isPresent()) {
            payOrderInfo.setCurrencySpeciesName(first.get().getDataValue());
        }else if ("CNY".equals(orderInfoEntity.getCurrencySpecies().toUpperCase())){
            payOrderInfo.setCurrencySpeciesName("人民币");
        }
        payOrderInfo.setPayPath(orderInfoEntity.getQrcodePath());
        if (AoOrderConstant.ORDER_PAY_STATUS_UNPAY.equals(orderInfoEntity.getPayStatus()) || AoOrderConstant.ORDER_PAY_STATUS_FAIL.equals(orderInfoEntity.getPayStatus())) {
            payOrderInfo.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_UNPAY);
        } else {
            payOrderInfo.setPayStatus(AoMergeCons.PAY_ORDER_STATUS_SUCCESS);
        }

        if (AoOrderConstant.ORDER_STATUS_CANCEL.equals(orderInfoEntity.getPayStatus())) {
            payOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_CANCEL);
        } else if (AoOrderConstant.ORDER_STATUS_UNPAY.equals(orderInfoEntity.getPayStatus())) {
            LocalDateTime now = LocalDateTime.now().minusSeconds(600L);
            if (orderInfoEntity.getCreateTime().isBefore(now)) {
                payOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EXPIRED);

            } else {
                payOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT);
            }
        } else {
            payOrderInfo.setStatus(AoMergePayOrderInfo.PAY_ORDER_STATUS_EFFECT);
        }
        payOrderInfo.setPayOrderNo(orderInfoEntity.getOrderNo());
        if (orderInfoEntity.getChargeType().equals(AoOrderConstant.ORDER_CHARGE_TYPE_POS)) {
            payOrderInfo.setBankOrderNo(orderInfoEntity.getBankOrderNo());
        } else if (orderInfoEntity.getChargeType().equals(AoOrderConstant.ORDER_CHARGE_TYPE_CASH)) {
            payOrderInfo.setBankOrderNo(null);
        } else {
            payOrderInfo.setBankOrderNo(orderInfoEntity.getOrderNo());
        }
        payOrderInfo.setPayTime(orderInfoEntity.getPayTime());
        payOrderInfo.setCreateDate(orderInfoEntity.getCreateTime());
        aoMergePayOrderInfoService.save(payOrderInfo);

        aoMergeOrderInfo.setOrderPayId(payOrderInfo.getId());
        aoMergeOrderInfoService.saveOrUpdate(aoMergeOrderInfo);

        AoMergeCashReturnPayOrderInfo aoMergeCashReturnPayOrderInfo =null;

        //现金支付
        if (orderInfoEntity.getChargeType().equals("3") && StringUtils.isNotEmpty(orderInfoEntity.getTurnNumber())) {
            AoTempCashReturnEntity returnEntities = aoTempCashReturnService.lambdaQuery()
                    .eq(AoTempCashReturnEntity::getOrderNo, orderInfoEntity.getOrderNo())
                    .eq(AoTempCashReturnEntity::getTurnStatus, "2")
                    .one();
            if (returnEntities != null ) {
                aoMergeCashReturnPayOrderInfo = aoMergeCashReturnPayOrderInfoService.lambdaQuery()
                        .eq(AoMergeCashReturnPayOrderInfo::getPayOrderNo,returnEntities.getCallbackPayNo())
                        .one();

                if (aoMergeCashReturnPayOrderInfo == null) {
                    aoMergeCashReturnPayOrderInfo=new AoMergeCashReturnPayOrderInfo();
                    aoMergeCashReturnPayOrderInfo.setPrice(returnEntities.getAmount());
                    aoMergeCashReturnPayOrderInfo.setPayType(returnEntities.getChargeType());
                    aoMergeCashReturnPayOrderInfo.setPayPath(returnEntities.getQrcodePath());
                    aoMergeCashReturnPayOrderInfo.setPayStatus(AoMergeCashReturnPayOrderInfo.PAY_ORDER_PAY_STATUS_SUCCESS);
                    aoMergeCashReturnPayOrderInfo.setStatus(AoMergeCashReturnPayOrderInfo.PAY_ORDER_STATUS_EFFECT);
                    aoMergeCashReturnPayOrderInfo.setPayOrderNo(returnEntities.getCallbackPayNo());
                    aoMergeCashReturnPayOrderInfo.setCreateDate(returnEntities.getCreateTime());
                    aoMergeCashReturnPayOrderInfo.setPayTime(returnEntities.getCreateTime());
                    aoMergeCashReturnPayOrderInfoService.save(aoMergeCashReturnPayOrderInfo);
                }
            } else {
                throw new RuntimeException("有缴纳信息,没有有效缴纳单" + orderInfoEntity.getOrderNo());
            }

        }

//
//        @Autowired
//        IAoInvoiceInfoService aoInvoiceInfoService;
//        @Autowired
//        AoInvoiceDetailInfoService detailInfoService;

//        @Autowired
//        IAoMergeInvoiceInfoService aoMergeInvoiceInfoService;
//        @Autowired
//        AoMergeSubOrderJoinInvoiceInfoService aoMergeSubOrderJoinInvoiceInfoService;
        if (orderInfoEntity.getOrderTypeCode().equals(AoOrderConstant.ORDER_TYPE_CODE_MANY_SEATS)) {
            AoManySeatsEntity manySeatsEntity = manySeatsService.lambdaQuery().eq(AoManySeatsEntity::getOrderNo, orderInfoEntity.getOrderNo()).one();
            if (manySeatsEntity == null) {
                throw new RuntimeException("订单没有一人多坐详情信息" + orderInfoEntity.getOrderNo());
            }
            FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                    .eq(FltFlightRealInfo::getFlightDate, orderInfoEntity.getFlightDate())
                    .eq(FltFlightRealInfo::getDst, orderInfoEntity.getDst())
                    .eq(FltFlightRealInfo::getOrg, orderInfoEntity.getOrg())
                    .eq(FltFlightRealInfo::getFlightNumber, orderInfoEntity.getFlightNo())
                    .one();

            ArrayList<String> paxId = new ArrayList<>();
            paxId.add(orderInfoEntity.getPaxId());
            List<FltPaxInfoPageVo> paxInfoList = fltPassengerRealInfoMapper.getPaxInfoList(new FltPaxInfoPageDto(), paxId);
            FltPaxInfoPageVo paxInfoVo = paxInfoList.get(0);

            AoMergeSuborderInfo mergeSuborderInfo = saveAoMergeSuborderInfo(orderInfoEntity, aoMergeOrderInfo, flightRealInfo, paxInfoVo);
            AoMergeManySeats aoMergeManySeats = new AoMergeManySeats();
            BeanUtils.copyProperties(mergeSuborderInfo, aoMergeManySeats);
            aoMergeManySeats.setPaxPhone(paxInfoVo.getPhoneNo());
            aoMergeManySeats.setPaxPnr(paxInfoVo.getPnr());
            aoMergeManySeats.setSeatsNumber(manySeatsEntity.getSeatsNumber());
            aoMergeManySeats.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
            aoMergeManySeats.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
            aoMergeManySeats.setPaxId(mergeSuborderInfo.getPayPaxId());
            aoMergeManySeats.setId(null);
            aoMergeManySeatsService.save(aoMergeManySeats);
            if(aoMergeCashReturnPayOrderInfo!=null){
                AoMergeSubOrderJoinCashReturnPay aoMergeSubOrderJoinCashReturnPay= new AoMergeSubOrderJoinCashReturnPay();
                aoMergeSubOrderJoinCashReturnPay.setCashReturnPayId(aoMergeCashReturnPayOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setSuborderOrderId(mergeSuborderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setMergeOrderId(aoMergeOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPayService.save(aoMergeSubOrderJoinCashReturnPay);
            }
            if("1".equals(orderInfoEntity.getInvoice())){
                AoInvoiceInfoDetailEntity infoDetailEntity = detailInfoService.lambdaQuery()
                        .eq(AoInvoiceInfoDetailEntity::getProductId, manySeatsEntity.getId())
                        .eq(AoInvoiceInfoDetailEntity::getOrderNo, orderInfoEntity.getOrderNo())
                        .one();
                if(infoDetailEntity!=null){
                    AoInvoiceInfoEntity invoiceInfoEntity = aoInvoiceInfoService.lambdaQuery()
                            .eq(AoInvoiceInfoEntity::getInvoiceNo, infoDetailEntity.getInvoiceNo())
                            .eq(AoInvoiceInfoEntity::getInvoiceStatus, "1")
                            .one();
                    if(invoiceInfoEntity==null){
                        throw new RuntimeException("发票信息不存在" + orderInfoEntity.getOrderNo()+">>"+infoDetailEntity.getProductId()+">>"+infoDetailEntity.getInvoiceNo());
                    }
                    AoMergeInvoiceInfo aoMergeInvoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                            .eq(AoMergeInvoiceInfo::getSeqNo, invoiceInfoEntity.getInvoiceNo())
                            .one();
                    if(aoMergeInvoiceInfo==null){
                        aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
                        BeanUtils.copyProperties(invoiceInfoEntity, aoMergeInvoiceInfo);
                        aoMergeInvoiceInfo.setSeqNo(invoiceInfoEntity.getInvoiceNo());
                        aoMergeInvoiceInfo.setInvoiceNo(StringUtils.isEmpty(invoiceInfoEntity.getSendInvoiceNo()) ? invoiceInfoEntity.getInvoiceNo() : invoiceInfoEntity.getSendInvoiceNo());
                        aoMergeInvoiceInfo.setTitleType(invoiceInfoEntity.getTitleType().equals("0")?"1":"2");
                         if("1".equals(invoiceInfoEntity.getInvoiceStatus())){
                             aoMergeInvoiceInfo.setInvoiceStatus("2");
                         }else   if("2".equals(invoiceInfoEntity.getInvoiceStatus())){
                             aoMergeInvoiceInfo.setInvoiceStatus("3");
                         }
                        aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
                    }
                    AoMergeSubOrderJoinInvoiceInfo aoMergeSubOrderJoinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                    aoMergeSubOrderJoinInvoiceInfo.setSeqNo(aoMergeInvoiceInfo.getSeqNo());
                    aoMergeSubOrderJoinInvoiceInfo.setSuborderOrderId(mergeSuborderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfo.setMergeOrderId(aoMergeOrderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfoService.save(aoMergeSubOrderJoinInvoiceInfo);
                }else{
                     throw new RuntimeException("发票信息记录不存在" + orderInfoEntity.getOrderNo());
                }
            }


        } else if (orderInfoEntity.getOrderTypeCode().equals(AoOrderConstant.ORDER_TYPE_CODE_SCENE_SEATS)) {

            List<AoSceneSeatsEntity> aoSceneSeatsEntities = sceneSeatsService.lambdaQuery().eq(AoSceneSeatsEntity::getOrderNo, orderInfoEntity.getOrderNo()).list();
            if (aoSceneSeatsEntities == null || aoSceneSeatsEntities.size() < 1) {
                throw new RuntimeException("订单没有现场座位销售详情信息" + orderInfoEntity.getOrderNo());
            }
            for (AoSceneSeatsEntity sse : aoSceneSeatsEntities) {

                ArrayList<String> paxId = new ArrayList<>();
                paxId.add(sse.getPaxId());
                List<FltPaxInfoPageVo> paxInfoList = fltPassengerRealInfoMapper.getPaxInfoList(new FltPaxInfoPageDto(), paxId);
                FltPaxInfoPageVo paxInfoVo = paxInfoList.get(0);
                FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                        .eq(FltFlightRealInfo::getFlightDate, orderInfoEntity.getFlightDate())
                        .eq(FltFlightRealInfo::getDst, orderInfoEntity.getDst())
                        .eq(FltFlightRealInfo::getOrg, orderInfoEntity.getOrg())
                        .eq(FltFlightRealInfo::getFlightNumber, orderInfoEntity.getFlightNo())
                        .one();

                AoMergeSuborderInfo mergeSuborderInfo = new AoMergeSuborderInfo();
                mergeSuborderInfo.setFlightType(flightRealInfo.getFlightType());
                mergeSuborderInfo.setOrderType(AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS);
                mergeSuborderInfo.setOrderTypeName(AoMergeCons.ORDER_TYPE_ANME_MANY_SEATS);
                String s = DateUtils.parseDateToStr(flightRealInfo.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
                mergeSuborderInfo.setStd(DateUtils.parseStringToLocalDateTime(s, DateUtils.YYYY_MM_DD_HH_MM_SS));

                StringBuilder segment = new StringBuilder();
                SysAirportInfoEntity org = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, flightRealInfo.getOrg())
                        .eq(SysAirportInfoEntity::getIsUse, "0")
                        .one();
                segment.append(flightRealInfo.getOrg());
                if (org == null) {
                    segment.append("null");
                } else {
                    segment.append(org.getAirportName());
                }
                segment.append("-");
                segment.append(flightRealInfo.getDst());
                SysAirportInfoEntity dst = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, flightRealInfo.getDst())
                        .eq(SysAirportInfoEntity::getIsUse, "0")
                        .one();
                if (dst == null) {
                    segment.append("null");
                } else {
                    segment.append(dst.getAirportName());
                }
                mergeSuborderInfo.setSegment(segment.toString());
                mergeSuborderInfo.setOrderPrice(new BigDecimal(AoUtils.pointsToYuan(sse.getSeatsPrice())));
                mergeSuborderInfo.setPracticalOrderPrice(mergeSuborderInfo.getOrderPrice());
                mergeSuborderInfo.setDiscount("1");
                mergeSuborderInfo.setIdentityDiscountDetails("身份折扣-10折");
                mergeSuborderInfo.setPaxNo(AesEncryptUtil.encrypt(paxInfoVo.getIdNo()));
                mergeSuborderInfo.setTktNo(paxInfoVo.getTktNo());
                mergeSuborderInfo.setFfpNo(paxInfoVo.getFfpNo());
                mergeSuborderInfo.setPaxName(paxInfoVo.getPaxName());
                mergeSuborderInfo.setPayPaxId(paxInfoVo.getId());
                mergeSuborderInfo.setPnr(paxInfoVo.getPnr());
                mergeSuborderInfo.setFlightDate(DateUtils.parseStringToLocalDate(paxInfoVo.getFlightDate(),DateUtils.YYYY_MM_DD));
                mergeSuborderInfo.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
                mergeSuborderInfo.setFlightNo(paxInfoVo.getFlightNo());
                mergeSuborderInfo.setOrg(orderInfoEntity.getOrg());
                mergeSuborderInfo.setDst(orderInfoEntity.getDst());
                mergeSuborderInfo.setSuborderOrderNo(orderInfoEntity.getOrderNo());
                if(orderInfoEntity.getPayStatus().equals("1")){
                    mergeSuborderInfo.setPermitRefund(AoMergeCons.SUBORDER_PERMIT_REFUND_YES);
                }else{
                    mergeSuborderInfo.setPermitRefund(AoMergeCons.SUBORDER_PERMIT_REFUND_NO);
                }
                setSuborderOrderStatus(mergeSuborderInfo, orderInfoEntity.getOrderStatus());
                setSuborderOrderPayStatus(mergeSuborderInfo, orderInfoEntity.getOrderStatus());
                aoMergeSuborderInfoService.saveOrUpdate(mergeSuborderInfo);
                AoMergeSceneSeats aoMergeSceneSeats = new AoMergeSceneSeats();

                BeanUtils.copyProperties(flightRealInfo,aoMergeSceneSeats);
                BeanUtils.copyProperties(sse,aoMergeSceneSeats);
                aoMergeSceneSeats.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                aoMergeSceneSeats.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
                aoMergeSceneSeats.setFlightDate(mergeSuborderInfo.getFlightDate());
                aoMergeSceneSeats.setFlightNo(mergeSuborderInfo.getFlightNo());
                aoMergeSceneSeats.setFfpNo(mergeSuborderInfo.getFfpNo());
                aoMergeSceneSeats.setPaxNo(mergeSuborderInfo.getPaxNo());
                aoMergeSceneSeats.setTktNo(mergeSuborderInfo.getTktNo());
                aoMergeSceneSeats.setPaxPnr(mergeSuborderInfo.getPnr());
                aoMergeSceneSeatsService.save(aoMergeSceneSeats);
                if(aoMergeCashReturnPayOrderInfo!=null){
                     AoMergeSubOrderJoinCashReturnPay aoMergeSubOrderJoinCashReturnPay= new AoMergeSubOrderJoinCashReturnPay();
                     aoMergeSubOrderJoinCashReturnPay.setCashReturnPayId(aoMergeCashReturnPayOrderInfo.getId());
                     aoMergeSubOrderJoinCashReturnPay.setSuborderOrderId(mergeSuborderInfo.getId());
                     aoMergeSubOrderJoinCashReturnPay.setMergeOrderId(aoMergeOrderInfo.getId());
                     aoMergeSubOrderJoinCashReturnPayService.save(aoMergeSubOrderJoinCashReturnPay);
                }


                if("1".equals(orderInfoEntity.getInvoice())){
                    AoInvoiceInfoDetailEntity infoDetailEntity = detailInfoService.lambdaQuery()
                            .eq(AoInvoiceInfoDetailEntity::getProductId, sse.getId())
                            .eq(AoInvoiceInfoDetailEntity::getOrderNo, orderInfoEntity.getOrderNo())
                            .one();
                    if(infoDetailEntity!=null){
                        AoInvoiceInfoEntity invoiceInfoEntity = aoInvoiceInfoService.lambdaQuery()
                                .eq(AoInvoiceInfoEntity::getInvoiceNo, infoDetailEntity.getInvoiceNo())
                                .eq(AoInvoiceInfoEntity::getInvoiceStatus, "1")
                                .one();
                        if(invoiceInfoEntity==null){
                            throw new RuntimeException("发票信息不存在" + orderInfoEntity.getOrderNo()+">>"+infoDetailEntity.getProductId()+">>"+infoDetailEntity.getInvoiceNo());
                        }
                        AoMergeInvoiceInfo aoMergeInvoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                                .eq(AoMergeInvoiceInfo::getSeqNo, invoiceInfoEntity.getInvoiceNo())
                                .one();
                        if(aoMergeInvoiceInfo==null){
                            aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
                            BeanUtils.copyProperties(invoiceInfoEntity, aoMergeInvoiceInfo);
                            aoMergeInvoiceInfo.setSeqNo(invoiceInfoEntity.getInvoiceNo());
                            aoMergeInvoiceInfo.setInvoiceNo(StringUtils.isEmpty(invoiceInfoEntity.getSendInvoiceNo()) ? invoiceInfoEntity.getInvoiceNo() : invoiceInfoEntity.getSendInvoiceNo());
                            aoMergeInvoiceInfo.setTitleType(invoiceInfoEntity.getTitleType().equals("0")?"1":"2");
                            if("1".equals(invoiceInfoEntity.getInvoiceStatus())){
                                aoMergeInvoiceInfo.setInvoiceStatus("2");
                            }else   if("2".equals(invoiceInfoEntity.getInvoiceStatus())){
                                aoMergeInvoiceInfo.setInvoiceStatus("3");
                            }
                            aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
                        }
                        AoMergeSubOrderJoinInvoiceInfo aoMergeSubOrderJoinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                        aoMergeSubOrderJoinInvoiceInfo.setSeqNo(aoMergeInvoiceInfo.getSeqNo());
                        aoMergeSubOrderJoinInvoiceInfo.setSuborderOrderId(mergeSuborderInfo.getId());
                        aoMergeSubOrderJoinInvoiceInfo.setMergeOrderId(aoMergeOrderInfo.getId());
                        aoMergeSubOrderJoinInvoiceInfoService.save(aoMergeSubOrderJoinInvoiceInfo);
                    }else{
                        throw new RuntimeException("发票信息记录不存在" + orderInfoEntity.getOrderNo());
                    }
                }

            }

        } else if (orderInfoEntity.getOrderTypeCode().equals(AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG)) {

            AoOverweightBkg aoOverweightBkg = aoOverweightBkgService.lambdaQuery().eq(AoOverweightBkg::getOrderNo, orderInfoEntity.getOrderNo()).one();
            if (aoOverweightBkg == null ) {
                throw new RuntimeException("订单没有逾重行李详情信息" + orderInfoEntity.getOrderNo());
            }
            FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                    .eq(FltFlightRealInfo::getFlightDate, orderInfoEntity.getFlightDate())
                    .eq(FltFlightRealInfo::getDst, orderInfoEntity.getDst())
                    .eq(FltFlightRealInfo::getOrg, orderInfoEntity.getOrg())
                    .eq(FltFlightRealInfo::getFlightNumber, orderInfoEntity.getFlightNo())
                    .one();

            ArrayList<String> paxId = new ArrayList<>();
            paxId.add(orderInfoEntity.getPaxId());
            List<FltPaxInfoPageVo> paxInfoList = fltPassengerRealInfoMapper.getPaxInfoList(new FltPaxInfoPageDto(), paxId);
            FltPaxInfoPageVo paxInfoVo = paxInfoList.get(0);

            AoMergeSuborderInfo mergeSuborderInfo = saveAoMergeSuborderInfo(orderInfoEntity, aoMergeOrderInfo, flightRealInfo, paxInfoVo);

            AoMergeOverweightBkg aoMergeOverweightBkg = new AoMergeOverweightBkg();
            aoMergeOverweightBkg.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
            aoMergeOverweightBkg.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
            BeanUtils.copyProperties(orderInfoEntity,aoMergeOverweightBkg);

            aoMergeOverweightBkg.setFlightType(flightRealInfo.getFlightType());
            aoMergeOverweightBkg.setFfpNo(paxInfoVo.getFfpNo());
            aoMergeOverweightBkg.setPaxPnr(paxInfoVo.getPnr());
            aoMergeOverweightBkg.setId(null);
            aoMergeOverweightBkgService.save(aoMergeOverweightBkg);
            if(aoMergeCashReturnPayOrderInfo!=null){
                AoMergeSubOrderJoinCashReturnPay aoMergeSubOrderJoinCashReturnPay= new AoMergeSubOrderJoinCashReturnPay();
                aoMergeSubOrderJoinCashReturnPay.setCashReturnPayId(aoMergeCashReturnPayOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setSuborderOrderId(mergeSuborderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setMergeOrderId(aoMergeOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPayService.save(aoMergeSubOrderJoinCashReturnPay);
            }

            if("1".equals(orderInfoEntity.getInvoice())){
                AoInvoiceInfoDetailEntity infoDetailEntity = detailInfoService.lambdaQuery()
                        .eq(AoInvoiceInfoDetailEntity::getProductId, aoOverweightBkg.getId())
                        .eq(AoInvoiceInfoDetailEntity::getOrderNo, orderInfoEntity.getOrderNo())
                        .one();
                if(infoDetailEntity!=null){
                    AoInvoiceInfoEntity invoiceInfoEntity = aoInvoiceInfoService.lambdaQuery()
                            .eq(AoInvoiceInfoEntity::getInvoiceNo, infoDetailEntity.getInvoiceNo())
                            .eq(AoInvoiceInfoEntity::getInvoiceStatus, "1")
                            .one();
                    if(invoiceInfoEntity==null){
                        throw new RuntimeException("发票信息不存在" + orderInfoEntity.getOrderNo()+">>"+infoDetailEntity.getProductId()+">>"+infoDetailEntity.getInvoiceNo());
                    }
                    AoMergeInvoiceInfo aoMergeInvoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                            .eq(AoMergeInvoiceInfo::getSeqNo, invoiceInfoEntity.getInvoiceNo())
                            .one();
                    if(aoMergeInvoiceInfo==null){
                        aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
                        BeanUtils.copyProperties(invoiceInfoEntity, aoMergeInvoiceInfo);
                        aoMergeInvoiceInfo.setSeqNo(invoiceInfoEntity.getInvoiceNo());
                        aoMergeInvoiceInfo.setInvoiceNo(StringUtils.isEmpty(invoiceInfoEntity.getSendInvoiceNo()) ? invoiceInfoEntity.getInvoiceNo() : invoiceInfoEntity.getSendInvoiceNo());
                        aoMergeInvoiceInfo.setTitleType(invoiceInfoEntity.getTitleType().equals("0")?"1":"2");
                        if("1".equals(invoiceInfoEntity.getInvoiceStatus())){
                            aoMergeInvoiceInfo.setInvoiceStatus("2");
                        }else   if("2".equals(invoiceInfoEntity.getInvoiceStatus())){
                            aoMergeInvoiceInfo.setInvoiceStatus("3");
                        }
                        aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
                    }
                    AoMergeSubOrderJoinInvoiceInfo aoMergeSubOrderJoinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                    aoMergeSubOrderJoinInvoiceInfo.setSeqNo(aoMergeInvoiceInfo.getSeqNo());
                    aoMergeSubOrderJoinInvoiceInfo.setSuborderOrderId(mergeSuborderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfo.setMergeOrderId(aoMergeOrderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfoService.save(aoMergeSubOrderJoinInvoiceInfo);
                }else{
                    throw new RuntimeException("发票信息记录不存在" + orderInfoEntity.getOrderNo());
                }
            }

        } else if (orderInfoEntity.getOrderTypeCode().equals(AoOrderConstant.ORDER_TYPE_CODE_VIP)) {
            List<AoViproomItemSell> aoViproomItemSells = aoViproomItemSellService.lambdaQuery().eq(AoViproomItemSell::getOrderId, orderInfoEntity.getOrderNo()).list();
            if (aoViproomItemSells == null || aoViproomItemSells.size() < 1) {
                throw new RuntimeException("订单没有贵宾厅详情信息" + orderInfoEntity.getOrderNo());
            }
            FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                    .eq(FltFlightRealInfo::getFlightDate, orderInfoEntity.getFlightDate())
                    .eq(FltFlightRealInfo::getDst, orderInfoEntity.getDst())
                    .eq(FltFlightRealInfo::getOrg, orderInfoEntity.getOrg())
                    .eq(FltFlightRealInfo::getFlightNumber, orderInfoEntity.getFlightNo())
                    .one();

            ArrayList<String> paxId = new ArrayList<>();
            paxId.add(orderInfoEntity.getPaxId());
            List<FltPaxInfoPageVo> paxInfoList = fltPassengerRealInfoMapper.getPaxInfoList(new FltPaxInfoPageDto(), paxId);
            FltPaxInfoPageVo paxInfoVo = paxInfoList.get(0);

            AoMergeSuborderInfo mergeSuborderInfo = saveAoMergeSuborderInfo(orderInfoEntity, aoMergeOrderInfo, flightRealInfo, paxInfoVo);
            for (AoViproomItemSell vip : aoViproomItemSells) {
                ArrayList<String> vipPaxId = new ArrayList<>();
                vipPaxId.add(vip.getPaxId());
                List<FltPaxInfoPageVo> vipPaxList = fltPassengerRealInfoMapper.getPaxInfoList(new FltPaxInfoPageDto(), vipPaxId);
                FltPaxInfoPageVo vipPax = vipPaxList.get(0);
                AoMergeViproomItemSell aoMergeViproomItemSell = new AoMergeViproomItemSell();
                BeanUtils.copyProperties(vip, aoMergeViproomItemSell);
                aoMergeViproomItemSell.setFfpNo(vipPax.getFfpNo());
                aoMergeViproomItemSell.setPaxNo(vip.getIdNo());
                aoMergeViproomItemSell.setPaxPnr(vipPax.getPnr());
                aoMergeViproomItemSell.setPaxCategory(vipPax.getCategory());
                FltFlightRealInfo vipFlightRealInfo = fltFlightRealInfoService.lambdaQuery()
                        .eq(FltFlightRealInfo::getFlightDate, vip.getFlightDate())
                        .eq(FltFlightRealInfo::getDst, vip.getDst())
                        .eq(FltFlightRealInfo::getOrg, vip.getOrg())
                        .eq(FltFlightRealInfo::getFlightNumber, vip.getFlightNo())
                        .one();
                aoMergeViproomItemSell.setFlightType(vipFlightRealInfo.getFlightType());
                aoMergeViproomItemSell.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                aoMergeViproomItemSell.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
                aoMergeViproomItemSellService.save(aoMergeViproomItemSell);
            }
            if("1".equals(orderInfoEntity.getInvoice())){
                AoInvoiceInfoDetailEntity infoDetailEntity = detailInfoService.lambdaQuery()
                        .eq(AoInvoiceInfoDetailEntity::getProductId, aoViproomItemSells.get(0).getId())
                        .eq(AoInvoiceInfoDetailEntity::getOrderNo, orderInfoEntity.getOrderNo())
                        .one();
                if(infoDetailEntity!=null){
                    AoInvoiceInfoEntity invoiceInfoEntity = aoInvoiceInfoService.lambdaQuery()
                            .eq(AoInvoiceInfoEntity::getInvoiceNo, infoDetailEntity.getInvoiceNo())
                            .eq(AoInvoiceInfoEntity::getInvoiceStatus, "1")
                            .one();
                    if(invoiceInfoEntity==null){
                        throw new RuntimeException("发票信息不存在" + orderInfoEntity.getOrderNo()+">>"+infoDetailEntity.getProductId()+">>"+infoDetailEntity.getInvoiceNo());
                    }
                    AoMergeInvoiceInfo aoMergeInvoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                            .eq(AoMergeInvoiceInfo::getSeqNo, invoiceInfoEntity.getInvoiceNo())
                            .one();
                    if(aoMergeInvoiceInfo==null){
                        aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
                        BeanUtils.copyProperties(invoiceInfoEntity, aoMergeInvoiceInfo);
                        aoMergeInvoiceInfo.setSeqNo(invoiceInfoEntity.getInvoiceNo());
                        aoMergeInvoiceInfo.setInvoiceNo(StringUtils.isEmpty(invoiceInfoEntity.getSendInvoiceNo()) ? invoiceInfoEntity.getInvoiceNo() : invoiceInfoEntity.getSendInvoiceNo());
                        aoMergeInvoiceInfo.setTitleType(invoiceInfoEntity.getTitleType().equals("0")?"1":"2");
                        if("1".equals(invoiceInfoEntity.getInvoiceStatus())){
                            aoMergeInvoiceInfo.setInvoiceStatus("2");
                        }else   if("2".equals(invoiceInfoEntity.getInvoiceStatus())){
                            aoMergeInvoiceInfo.setInvoiceStatus("3");
                        }
                        aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
                    }
                    AoMergeSubOrderJoinInvoiceInfo aoMergeSubOrderJoinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                    aoMergeSubOrderJoinInvoiceInfo.setSeqNo(aoMergeInvoiceInfo.getSeqNo());
                    aoMergeSubOrderJoinInvoiceInfo.setSuborderOrderId(mergeSuborderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfo.setMergeOrderId(aoMergeOrderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfoService.save(aoMergeSubOrderJoinInvoiceInfo);
                }else{
                    throw new RuntimeException("发票信息记录不存在" + orderInfoEntity.getOrderNo());
                }
            }
            if(aoMergeCashReturnPayOrderInfo!=null){
                AoMergeSubOrderJoinCashReturnPay aoMergeSubOrderJoinCashReturnPay= new AoMergeSubOrderJoinCashReturnPay();
                aoMergeSubOrderJoinCashReturnPay.setCashReturnPayId(aoMergeCashReturnPayOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setSuborderOrderId(mergeSuborderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setMergeOrderId(aoMergeOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPayService.save(aoMergeSubOrderJoinCashReturnPay);
            }
        } else if (orderInfoEntity.getOrderTypeCode().equals(AoOrderConstant.ORDER_TYPE_CODE_UPGRADE)) {
            AoCounterUpgrade aoCounterUpgrade = aoCounterUpgradeOrderService.lambdaQuery().eq(AoCounterUpgrade::getOrderNo, orderInfoEntity.getOrderNo()).one();
            if (aoCounterUpgrade == null) {
                throw new RuntimeException("订单没有柜台升舱详情信息" + orderInfoEntity.getOrderNo());
            }
            FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                    .eq(FltFlightRealInfo::getFlightDate, orderInfoEntity.getFlightDate())
                    .eq(FltFlightRealInfo::getDst, orderInfoEntity.getDst())
                    .eq(FltFlightRealInfo::getOrg, orderInfoEntity.getOrg())
                    .eq(FltFlightRealInfo::getFlightNumber, orderInfoEntity.getFlightNo())
                    .one();

            ArrayList<String> paxId = new ArrayList<>();
            paxId.add(orderInfoEntity.getPaxId());
            List<FltPaxInfoPageVo> paxInfoList = fltPassengerRealInfoMapper.getPaxInfoList(new FltPaxInfoPageDto(), paxId);
            FltPaxInfoPageVo paxInfoVo = paxInfoList.get(0);
            AoMergeSuborderInfo mergeSuborderInfo = saveAoMergeSuborderInfo(orderInfoEntity, aoMergeOrderInfo, flightRealInfo, paxInfoVo);

            AoMergeCounterUpgrade aoMergeCounterUpgrade = new AoMergeCounterUpgrade();
            BeanUtils.copyProperties(mergeSuborderInfo, aoMergeCounterUpgrade);
            BeanUtils.copyProperties(aoCounterUpgrade, aoMergeCounterUpgrade);
            Employee applyUser = userMapper.getUserByName(aoCounterUpgrade.getApplyUserNo());
            aoMergeCounterUpgrade.setApplyUser(applyUser.getTuCname());
            Employee approveUser = userMapper.getUserByName(aoCounterUpgrade.getApproveUserNo());
            aoMergeCounterUpgrade.setApproveUser(approveUser.getTuCname());
            aoMergeCounterUpgrade.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
            aoMergeCounterUpgrade.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
            aoMergeCounterUpgrade.setId(null);
            aoMergeCounterUpgradeService.save(aoMergeCounterUpgrade);

            if(aoMergeCashReturnPayOrderInfo!=null){
                AoMergeSubOrderJoinCashReturnPay aoMergeSubOrderJoinCashReturnPay= new AoMergeSubOrderJoinCashReturnPay();
                aoMergeSubOrderJoinCashReturnPay.setCashReturnPayId(aoMergeCashReturnPayOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setSuborderOrderId(mergeSuborderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setMergeOrderId(aoMergeOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPayService.save(aoMergeSubOrderJoinCashReturnPay);
            }
            if("1".equals(orderInfoEntity.getInvoice())){
                AoInvoiceInfoDetailEntity infoDetailEntity = detailInfoService.lambdaQuery()
                        .eq(AoInvoiceInfoDetailEntity::getProductId, aoCounterUpgrade.getId())
                        .eq(AoInvoiceInfoDetailEntity::getOrderNo, orderInfoEntity.getOrderNo())
                        .one();
                if(infoDetailEntity!=null){
                    AoInvoiceInfoEntity invoiceInfoEntity = aoInvoiceInfoService.lambdaQuery()
                            .eq(AoInvoiceInfoEntity::getInvoiceNo, infoDetailEntity.getInvoiceNo())
                            .eq(AoInvoiceInfoEntity::getInvoiceStatus, "1")
                            .one();
                    if(invoiceInfoEntity==null){
                        throw new RuntimeException("发票信息不存在" + orderInfoEntity.getOrderNo()+">>"+infoDetailEntity.getProductId()+">>"+infoDetailEntity.getInvoiceNo());
                    }
                    AoMergeInvoiceInfo aoMergeInvoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                            .eq(AoMergeInvoiceInfo::getSeqNo, invoiceInfoEntity.getInvoiceNo())
                            .one();
                    if(aoMergeInvoiceInfo==null){
                        aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
                        BeanUtils.copyProperties(invoiceInfoEntity, aoMergeInvoiceInfo);
                        aoMergeInvoiceInfo.setSeqNo(invoiceInfoEntity.getInvoiceNo());
                        aoMergeInvoiceInfo.setInvoiceNo(StringUtils.isEmpty(invoiceInfoEntity.getSendInvoiceNo()) ? invoiceInfoEntity.getInvoiceNo() : invoiceInfoEntity.getSendInvoiceNo());
                        aoMergeInvoiceInfo.setTitleType(invoiceInfoEntity.getTitleType().equals("0")?"1":"2");
                        if("1".equals(invoiceInfoEntity.getInvoiceStatus())){
                            aoMergeInvoiceInfo.setInvoiceStatus("2");
                        }else   if("2".equals(invoiceInfoEntity.getInvoiceStatus())){
                            aoMergeInvoiceInfo.setInvoiceStatus("3");
                        }
                        aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
                    }
                    AoMergeSubOrderJoinInvoiceInfo aoMergeSubOrderJoinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                    aoMergeSubOrderJoinInvoiceInfo.setSeqNo(aoMergeInvoiceInfo.getSeqNo());
                    aoMergeSubOrderJoinInvoiceInfo.setSuborderOrderId(mergeSuborderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfo.setMergeOrderId(aoMergeOrderInfo.getId());
                    aoMergeSubOrderJoinInvoiceInfoService.save(aoMergeSubOrderJoinInvoiceInfo);
                }else{
                    throw new RuntimeException("发票信息记录不存在" + orderInfoEntity.getOrderNo());
                }
            }
            if(aoMergeCashReturnPayOrderInfo!=null){
                AoMergeSubOrderJoinCashReturnPay aoMergeSubOrderJoinCashReturnPay= new AoMergeSubOrderJoinCashReturnPay();
                aoMergeSubOrderJoinCashReturnPay.setCashReturnPayId(aoMergeCashReturnPayOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setSuborderOrderId(mergeSuborderInfo.getId());
                aoMergeSubOrderJoinCashReturnPay.setMergeOrderId(aoMergeOrderInfo.getId());
                aoMergeSubOrderJoinCashReturnPayService.save(aoMergeSubOrderJoinCashReturnPay);
            }
        } else {
            throw new RuntimeException("未知订单类型" + orderInfoEntity.getOrderNo());
        }

    }

    private AoMergeSuborderInfo saveAoMergeSuborderInfo(AoOrderInfoEntity orderInfoEntity, AoMergeOrderInfo aoMergeOrderInfo, FltFlightRealInfo flightRealInfo, FltPaxInfoPageVo paxInfoVo) {
        AoMergeSuborderInfo mergeSuborderInfo = new AoMergeSuborderInfo();
        mergeSuborderInfo.setFlightType(flightRealInfo.getFlightType());
        mergeSuborderInfo.setFlightDate(orderInfoEntity.getFlightDate());
        mergeSuborderInfo.setOrderType(AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS);
        mergeSuborderInfo.setOrderTypeName(AoMergeCons.ORDER_TYPE_ANME_MANY_SEATS);
        String s = DateUtils.parseDateToStr(flightRealInfo.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        mergeSuborderInfo.setStd(DateUtils.parseStringToLocalDateTime(s, DateUtils.YYYY_MM_DD_HH_MM_SS));
        mergeSuborderInfo.setFlightNo(orderInfoEntity.getFlightNo());
        mergeSuborderInfo.setOrg(orderInfoEntity.getOrg());
        mergeSuborderInfo.setDst(orderInfoEntity.getDst());
        StringBuilder segment = new StringBuilder();
        SysAirportInfoEntity org = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, flightRealInfo.getOrg())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        segment.append(flightRealInfo.getOrg());
        if (org == null) {
            segment.append("null");
        } else {
            segment.append(org.getAirportName());
        }
        segment.append("-");
        segment.append(flightRealInfo.getDst());
        SysAirportInfoEntity dst = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, flightRealInfo.getDst())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        if (dst == null) {
            segment.append("null");
        } else {
            segment.append(dst.getAirportName());
        }
        mergeSuborderInfo.setSegment(segment.toString());
        mergeSuborderInfo.setOrderPrice(new BigDecimal(orderInfoEntity.centToYuan()));
        mergeSuborderInfo.setPracticalOrderPrice(mergeSuborderInfo.getOrderPrice());
        mergeSuborderInfo.setDiscount("1");
        mergeSuborderInfo.setIdentityDiscountDetails("身份折扣-10折");
        mergeSuborderInfo.setPaxNo(orderInfoEntity.getPaxNo());
        mergeSuborderInfo.setTktNo(paxInfoVo.getTktNo());
        mergeSuborderInfo.setFfpNo(paxInfoVo.getFfpNo());
        mergeSuborderInfo.setPaxName(paxInfoVo.getPaxName());
        mergeSuborderInfo.setPayPaxId(paxInfoVo.getId());
        mergeSuborderInfo.setPnr(paxInfoVo.getPnr());
        mergeSuborderInfo.setAoMergeOrderInfoId(aoMergeOrderInfo.getId());
        mergeSuborderInfo.setSuborderOrderNo(orderInfoEntity.getOrderNo());
        if(orderInfoEntity.getPayStatus().equals("1")){
            mergeSuborderInfo.setPermitRefund(AoMergeCons.SUBORDER_PERMIT_REFUND_YES);
        }else{
            mergeSuborderInfo.setPermitRefund(AoMergeCons.SUBORDER_PERMIT_REFUND_NO);
        }
        setSuborderOrderStatus(mergeSuborderInfo, orderInfoEntity.getOrderStatus());
        setSuborderOrderPayStatus(mergeSuborderInfo, orderInfoEntity.getOrderStatus());
        aoMergeSuborderInfoService.saveOrUpdate(mergeSuborderInfo);
        return mergeSuborderInfo;
    }

//    @ApiModelProperty(value = "单订单状态(0未支付，1支付失败，2支付完成，3退款中，4退款失败，5退款成功,6取消,7旅客发起退款)")

    private void setSuborderOrderStatus(AoMergeSuborderInfo sub, String orderStatus) {
        if (orderStatus.equals("0")) {
            sub.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_UNPAY);
        }
        if (orderStatus.equals("2")) {
            sub.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS);
        }
        if (orderStatus.equals("5")) {
            sub.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_REFUND);
        }
        if (orderStatus.equals("6")) {
            sub.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL);
        }
    }

    private void setSuborderOrderPayStatus(AoMergeSuborderInfo sub, String orderStatus) {
        if (orderStatus.equals("0")) {
            sub.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_UNPAY);
        }
        if (orderStatus.equals("2")) {
            sub.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_SUCCESS);
        }
        if (orderStatus.equals("5")) {
            sub.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_REFUND);
        }
        if (orderStatus.equals("6")) {
            sub.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_UNPAY);
        }
    }
}
