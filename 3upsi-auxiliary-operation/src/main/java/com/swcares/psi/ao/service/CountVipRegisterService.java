package com.swcares.psi.ao.service;

import com.swcares.psi.ao.register.vo.CountVIPNumberVo;
import com.swcares.psi.ao.register.vo.CountVipRegisterInfoParemsDto;
import com.swcares.psi.ao.register.vo.CountVipRegisterInfoVO;
import com.swcares.psi.ao.register.vo.VIPRegisterInfoVo;
import com.swcares.psi.common.utils.query.PsiPage;
import java.util.List;

/**
 * ClassName：com.swcares.psi.ao.service.CountVipRegister <br>;
 * Description：统计会员注册信息服务 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/1/24 14:43 <br>;
 * @version v1.0 <br>;
 */
public interface CountVipRegisterService {
    /**
     * Title：getVipRegisterInfos <br>;
     * Description：根据邀请人信息查询所有旅客信息 <br>;
     * @param:  <br>;
     * @return: RenderResult<PsiPage<CountVipRegisterInfoVO>> <br>;
     * <AUTHOR> <br>;
     * date 2022/1/24 15:05 <br>;
     * @throws  <br>;
     */
    PsiPage<CountVipRegisterInfoVO> getVipRegisterInfosByUser(CountVipRegisterInfoParemsDto paremsVo);

    /**
     * Title：countVipRegisterInfo <br>;
     * Description：邀请会员统计 <br>;
     * @param: CountVipRegisterInfoParemsVo <br>;
     * @return: RenderResult<PsiPage<CountVipRegisterInfoVO>> <br>;
     * <AUTHOR> <br>;
     * date 2022/1/24 16:01 <br>;
     * @throws  <br>;
     */
    PsiPage<CountVIPNumberVo> countVipRegisterInfo(CountVipRegisterInfoParemsDto paremsVo);

    /**   
     * Title：getVipRegisterInfos <br>;
     * Description： <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/1/24 16:34 <br>;
     * @throws  <br>;
     */
    PsiPage<VIPRegisterInfoVo> getVipRegisterInfos(CountVipRegisterInfoParemsDto paremsVo);

    /**
     *  邀请入会明细列表
     *
     * @param paremsVo
     * @return
     */
    List<VIPRegisterInfoVo> getVipRegisterInfosList(CountVipRegisterInfoParemsDto paremsVo);

    /**   
     * Title：countMemVipRegisterInfo <br>;
     * Description：当月个人所邀请的会员统计 <br>;
     * @param: CountVipRegisterInfoParemsVo <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/1/25 9:40 <br>;
     * @throws  <br>;
     */
    PsiPage<CountVIPNumberVo> countMemVipRegisterInfo(CountVipRegisterInfoParemsDto paremsVo);

    /**
     * Title：getVipRegisterTotal <br>;
     * Description：获取统计总数 <br>;
     * @param: CountVipRegisterInfoParemsDto <br>;
     * @return: CountVIPNumberVo <br>;
     * <AUTHOR> <br>;
     * date 2022/2/8 15:50 <br>;
     * @throws  <br>;
     */
    CountVIPNumberVo getVipRegisterTotal(CountVipRegisterInfoParemsDto paremsVo);
}
