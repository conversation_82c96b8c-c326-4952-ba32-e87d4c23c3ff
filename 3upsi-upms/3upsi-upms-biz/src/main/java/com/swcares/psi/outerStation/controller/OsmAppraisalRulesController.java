package com.swcares.psi.outerStation.controller;


import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.outerStation.dto.OsmAppraisalRulesDto;
import com.swcares.psi.outerStation.dto.OsmAppraisalRulesPageDto;
import com.swcares.psi.outerStation.service.OsmAppraisalRulesService;
import com.swcares.psi.outerStation.vo.OsmAppraisalRulesPageVo;
import com.swcares.psi.outerStation.vo.OsmDropConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
@RestController
@RequestMapping("/api/os/appraisalRules")
@Api(value = "/api/os/appraisalRules", tags = "外站不正常考评规则功能接口")
public class OsmAppraisalRulesController {

    @Resource
    private OsmAppraisalRulesService appraisalRulesService;

    @PostMapping(value = "/appraisalRulesPage")
    @ApiOperation(value = "不正常考评规则分页列表")
    public RenderResult<PsiPage<OsmAppraisalRulesPageVo>> appraisalRulesPage(@RequestBody OsmAppraisalRulesPageDto dto) {
        return RenderResult.success(appraisalRulesService.appraisalRulesPage(dto));
    }

    @PostMapping(value = "/addAppraisalRules")
    @ApiOperation(value = "不正常考评规则新增")
    public RenderResult<PsiPage<OsmAppraisalRulesPageVo>> addAppraisalRules(@RequestBody OsmAppraisalRulesDto dto) {
        appraisalRulesService.addAppraisalRules(dto);
        return RenderResult.success();
    }

    @PostMapping(value = "/updateAppraisalRules")
    @ApiOperation(value = "不正常考评规则修改")
    public RenderResult<PsiPage<OsmAppraisalRulesPageVo>> updateAppraisalRules(@RequestBody OsmAppraisalRulesDto dto) {
        appraisalRulesService.updateAppraisalRules(dto);
        return RenderResult.success();
    }

    @GetMapping(value = "/OsmDropConfig")
    @ApiOperation(value = "获取不正常考评下拉框配置数据")
    public RenderResult<Map<String, List<OsmDropConfigVo>>> OsmDropConfig() {
        return RenderResult.success(appraisalRulesService.OsmDropConfig());
    }



}

