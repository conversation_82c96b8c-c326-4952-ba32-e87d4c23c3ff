package com.swcares.psi.stimulate.dto;

import com.swcares.psi.stimulate.entity.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AoStimulateOrderInnerParam extends AoStimulateOrderInfo {
    private String oldAoStimulateOrderInfoId;
    private AoStimulateConfigInfo stimulateConfig;
    private AoStimulateProductCostConfig costConfig;
    private AoStimulateExchangeRateConfig serc;
    private List<AoStimulateOrderDetails> detailsList = new ArrayList<>();

}
