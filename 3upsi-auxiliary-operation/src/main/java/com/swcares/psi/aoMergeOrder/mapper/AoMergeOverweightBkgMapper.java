package com.swcares.psi.aoMergeOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightInputTemplateReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOverweightBkg;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightInputTemplateReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并逾重行李产品详情订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeOverweightBkgMapper extends BaseMapper<AoMergeOverweightBkg> {

    List<AoMergeOverweightBkgAccountsReportVo> getAccountsList(@Param("dto") AoMergeOverweightBkgAccountsReportDto dto);
    PsiPage<AoMergeOverweightBkgAccountsReportVo> getAccountsListPage(PsiPage<AoMergeOverweightBkgAccountsReportVo> page, @Param("dto") AoMergeOverweightBkgAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(@Param("dto") AoMergeOverweightBkgAccountsReportDto dto);
    PsiPage<AoMergeOverweightInputTemplateReportVo> getAoMergeOverweightInputTemplatePage(PsiPage<AoMergeOverweightInputTemplateReportVo> page,@Param("dto")  AoMergeOverweightInputTemplateReportDto dto);
    List<AoMergeOverweightInputTemplateReportVo> getAoMergeOverweightInputTemplateList(@Param("dto")  AoMergeOverweightInputTemplateReportDto dto);


}
