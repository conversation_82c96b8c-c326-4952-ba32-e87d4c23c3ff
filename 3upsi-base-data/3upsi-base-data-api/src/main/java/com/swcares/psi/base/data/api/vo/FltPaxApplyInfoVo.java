package com.swcares.psi.base.data.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on  2022/1/11
 * Title:       [旅客监控-旅客详情-赔付历史-申领记录]
 * Description: [旅客监控-旅客详情-赔付历史-申领记录]
 *
 * @author: like
 * @version: 1.0
 */
@Data
public class FltPaxApplyInfoVo {

    @ApiModelProperty(value = "申领人")
    private String applyPax;

    @ApiModelProperty(value = "申领单号")
    private String applyNo;

    @ApiModelProperty(value = "申领状态")
    private String applyStatus;

    @ApiModelProperty(value = "申领状态名称")
    private String applyStatusName;

    @ApiModelProperty(value = "本补偿单申领金额")
    private String applyMoney;

    @ApiModelProperty(value = "总申领金额")
    private String applySumMoney;

    @ApiModelProperty(value = "领取方式")
    private String receiveType;

    @ApiModelProperty(value = "支付方式")
    private String payType;

    @ApiModelProperty(value = "申领时间")
    private String applyTime;

    @ApiModelProperty(value = "到账时间")
    private String paymentTime;

    @ApiModelProperty(value = "申领明细")
    private String applyDetail;
}
