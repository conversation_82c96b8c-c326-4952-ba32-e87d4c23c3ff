package com.swcares.psi.stimulate.dto;

import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DetailsOrderDto extends BaseDto {

    @ApiModelProperty("订单日期开始")
    private String  orderDateStart;
    @ApiModelProperty("订单日期结束")
    private String  orderDateEnd;
    @ApiModelProperty("前端不传")
    private String  userNo;
    @ApiModelProperty("订单类型 1一人多座,2现场座位销售(选座),3逾重行李,4贵宾室,5柜台升舱,6机上升舱 逗号分割")
    private String  orderTypeCode;
    @ApiModelProperty("前端不传")
    private String  label;
    @ApiModelProperty("已退款 需要过滤传 1")
    private String  refund;
    @ApiModelProperty("未复核 需要过滤传 1")
    private String  review;
    @ApiModelProperty("待汇率 需要过滤传 1")
    private String  exchangeRate;
    @ApiModelProperty("待成本 需要过滤传 1")
    private String  productCost;
    @ApiModelProperty("取消激励 需要过滤传 1")
    private String  cancelStimulate;
}
