package com.swcares.psi.aoMergeOrder.service;

import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSceneSeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSceneSeats;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSceneSeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

/**
 * <p>
 * 合并支付现场座位销售产品详情订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeSceneSeatsService extends IService<AoMergeSceneSeats> {

    List<AoMergeSceneSeatsAccountsReportVo> getAccountsList(AoMergeSceneSeatsAccountsReportDto dto);
    PsiPage<AoMergeSceneSeatsAccountsReportVo> getAccountsListPage(AoMergeSceneSeatsAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeSceneSeatsAccountsReportDto dto);

}
