package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeH5RefundOrderListDto;
import com.swcares.psi.aoMergeOrder.entity.*;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeRefundOrderInfoMapper;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.aoMergeOrder.vo.AoMergeH5RefundOrderDetailsVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeH5RefundOrderListVo;
import com.swcares.psi.aoMergeOrder.vo.RefundOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.RefundSendTaskVo;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.vo.AoDataPermissionsVo;
import com.swcares.psi.base.data.service.AoDataPermissionsService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * <p>
 * 合并支付退款订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeRefundOrderInfoServiceImpl extends ServiceImpl<AoMergeRefundOrderInfoMapper, AoMergeRefundOrderInfo> implements IAoMergeRefundOrderInfoService {

    @Autowired
    private AoDataPermissionsService aoDataPermissionsService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;

    @Autowired
    IAoMergeManySeatsService aoMergeManySeatsService;
    @Autowired
    IAoMergeSceneSeatsService aoMergeSceneSeatsService;
    @Autowired
    IAoMergeOverweightBkgService aoMergeOverweightBkgService;
    @Autowired
    IAoMergeViproomItemSellService aoMergeViproomItemSellService;
    @Autowired
    IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    AoMergeOverweightBkgChargeDetailsService aoMergeOverweightBkgChargeDetailsService;
    @Autowired
    SysAirportInfoService airportInfoService;
    @Autowired
    IAoMergePayOrderInfoService aoMergePayOrderInfoService;
    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;

    @Override
    public List<RefundOrderTaskVo> getRefundOrderAll() {
        return baseMapper.getRefundOrderAll();
    }

    @Override
    public List<RefundSendTaskVo> getRefundSendOrder() {
        return baseMapper.getRefundSendOrder();
    }

    @Override
    public PsiPage<AoMergeH5RefundOrderListVo> h5RefundOrderList(AoMergeH5RefundOrderListDto dto) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userId = authentication.getId();
        PsiPage<AoMergeH5RefundOrderListVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.h5RefundOrderList(page,dto,userId,userNo);
    }


    @Override
    public AoMergeH5RefundOrderDetailsVo h5RefundOrderDetails(String refundId) {
        AoMergeH5RefundOrderDetailsVo detailsVo = new AoMergeH5RefundOrderDetailsVo();
        AoMergeRefundOrderInfo refundOrderInfo = this.getById(refundId);
        AoMergeSuborderInfo  suborderInfo = aoMergeSuborderInfoService.getById(refundOrderInfo.getAoMergeSuborderInfoId());
        detailsVo.setPaxName(suborderInfo.getPaxName());
        detailsVo.setPaxNo(AesEncryptUtil.decryption(suborderInfo.getPaxNo()));
        detailsVo.setFlightNo(suborderInfo.getFlightNo());
        detailsVo.setFlightDate(DateUtils.parseLocalDateTimeToString(suborderInfo.getStd(),DateUtils.YYYY_MM_DD_HH_MM_SS));

        SysAirportInfoEntity org = airportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, suborderInfo.getOrg())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        SysAirportInfoEntity dst = airportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, suborderInfo.getDst())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        detailsVo.setSegment(org.getCode() + "(" + org.getAirportName() + ")-" + dst.getCode() + "(" + dst.getAirportName() + ")");
        detailsVo.setPaxCategory(suborderInfo.getPaxCategory());
        detailsVo.setIdentityDiscountDetails(suborderInfo.getIdentityDiscountDetails());
        detailsVo.setSubOrderId(suborderInfo.getId());
        detailsVo.setSubOrderNo(suborderInfo.getSuborderOrderNo());
        detailsVo.setPayStatus(suborderInfo.getSuborderOrderPayStatus());
        AoMergeOrderInfo mergeOrderInfo = aoMergeOrderInfoService.getById(suborderInfo.getAoMergeOrderInfoId());
        AoMergePayOrderInfo payOrderInfo = aoMergePayOrderInfoService.getById(mergeOrderInfo.getOrderPayId());
        detailsVo.setPayType(payOrderInfo.getPayType());
        detailsVo.setPayTime(DateUtils.parseLocalDateTimeToString(payOrderInfo.getPayTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        detailsVo.setCreateUser(mergeOrderInfo.getCreateUser()+"-"+mergeOrderInfo.getCreateNo());
        detailsVo.setCreateDate(DateUtils.parseLocalDateTimeToString(mergeOrderInfo.getCreateDate(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        detailsVo.setCreateUserDepartment(mergeOrderInfo.getCreateUserDepartment());
        detailsVo.setMergeOrderNo(mergeOrderInfo.getOrderNo());
        SysAirportInfoEntity servicePort = airportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, mergeOrderInfo.getServicePort())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        detailsVo.setServicePort(servicePort.getAirportName()+servicePort.getCode());
        detailsVo.setRefundSause(refundOrderInfo.getRefundSause());
        detailsVo.setRefundExplain(refundOrderInfo.getRefundExplain());
        detailsVo.setRefundStatus(refundOrderInfo.getRefundStatus());
        detailsVo.setRefundStartTime(DateUtils.parseLocalDateTimeToString(refundOrderInfo.getCreateDate(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        detailsVo.setRefundPrice(refundOrderInfo.getRefundPrice().divide(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_UP).toString());
        if (ObjectUtils.isNotEmpty(refundOrderInfo.getRefundEndTime())) {
            detailsVo.setRefundEndTime(DateUtils.parseLocalDateTimeToString(refundOrderInfo.getRefundEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        detailsVo.setRefundApplyUser(refundOrderInfo.getCreateUser()+ "-" + refundOrderInfo.getCreateNo());
        if(StringUtils.isNotEmpty(refundOrderInfo.getApproveUserName())){
            detailsVo.setRefundApproveUser(refundOrderInfo.getApproveUserName() + "-" + refundOrderInfo.getApproveUserNo());
        }
        detailsVo.setApproveUserDepartment(refundOrderInfo.getApproveUserDepartment());
        detailsVo.setRefundFailCause(refundOrderInfo.getRefundFailCause());
        if (suborderInfo.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS)) {
            AoMergeManySeats one = aoMergeManySeatsService.lambdaQuery()
                    .eq(AoMergeManySeats::getAoMergeSuborderInfoId, suborderInfo.getId())
                    .one();
            AoMergeH5RefundOrderDetailsVo.ManySeatsDetailsVo manySeatsDetailsVo = new AoMergeH5RefundOrderDetailsVo.ManySeatsDetailsVo();
            manySeatsDetailsVo.setSeatsNumber(one.getSeatsNumber());
            detailsVo.setProductBaseVo(manySeatsDetailsVo);
        } else if ((suborderInfo.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS))) {
            AoMergeSceneSeats one = aoMergeSceneSeatsService.lambdaQuery()
                    .eq(AoMergeSceneSeats::getAoMergeSuborderInfoId, suborderInfo.getId())
                    .one();
            AoMergeH5RefundOrderDetailsVo.SceneSeatsDetailsVo seatsDetailsVo = new AoMergeH5RefundOrderDetailsVo.SceneSeatsDetailsVo();
            seatsDetailsVo.setSeatsType(one.getSeatsType());
            detailsVo.setProductBaseVo(seatsDetailsVo);
        } else if ((suborderInfo.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG))) {
            AoMergeOverweightBkg one = aoMergeOverweightBkgService.lambdaQuery()
                    .eq(AoMergeOverweightBkg::getAoMergeSuborderInfoId, suborderInfo.getId())
                    .one();
            List<AoMergeOverweightBkgChargeDetails> list1 = aoMergeOverweightBkgChargeDetailsService.lambdaQuery()
                    .eq(AoMergeOverweightBkgChargeDetails::getAoMergeOverweightBkgId, one.getId())
                    .list();
            AoMergeH5RefundOrderDetailsVo.OverweightBkgDetailsVo overweightBkgDetailsVo = new AoMergeH5RefundOrderDetailsVo.OverweightBkgDetailsVo();
            for (AoMergeOverweightBkgChargeDetails ele : list1) {
                AoMergeH5RefundOrderDetailsVo.OverweightBkgDetailsVo.AoMergeOverweightBkgChargeDetailVo aoMergeOverweightBkgChargeDetailVo = new AoMergeH5RefundOrderDetailsVo.OverweightBkgDetailsVo.AoMergeOverweightBkgChargeDetailVo();
                aoMergeOverweightBkgChargeDetailVo.setChargeItem(ele.getChargeItem());
                aoMergeOverweightBkgChargeDetailVo.setNumber(ele.getNumber().toString());
                aoMergeOverweightBkgChargeDetailVo.setPrice(ele.getPrice().toString());
                aoMergeOverweightBkgChargeDetailVo.setChargeStandardExplain(ele.getChargeStandardExplain());
                overweightBkgDetailsVo.getChargeDetails().add(aoMergeOverweightBkgChargeDetailVo);
            }
            detailsVo.setProductBaseVo(overweightBkgDetailsVo);

        } else if ((suborderInfo.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_VIP))) {
            AoMergeViproomItemSell one = aoMergeViproomItemSellService.lambdaQuery()
                    .eq(AoMergeViproomItemSell::getAoMergeSuborderInfoId, suborderInfo.getId())
                    .one();
            AoMergeH5RefundOrderDetailsVo.ViproomItemSellDetailsVo viproomItemSellDetailsVo = new AoMergeH5RefundOrderDetailsVo.ViproomItemSellDetailsVo();
            viproomItemSellDetailsVo.setItemName(one.getItemName());
            detailsVo.setProductBaseVo(viproomItemSellDetailsVo);
        } else if ((suborderInfo.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_UPGRADE))) {
            AoMergeCounterUpgrade one = aoMergeCounterUpgradeService.lambdaQuery()
                    .eq(AoMergeCounterUpgrade::getAoMergeOrderInfoId, suborderInfo.getAoMergeOrderInfoId())
                    .one();
            AoMergeH5RefundOrderDetailsVo.CounterUpgradeDetailsVo counterUpgradeDetailsVo = new AoMergeH5RefundOrderDetailsVo.CounterUpgradeDetailsVo();
            counterUpgradeDetailsVo.setErrorInfo(one.getErrorInfo());
            counterUpgradeDetailsVo.setNewSeat(one.getNewSeat());
            counterUpgradeDetailsVo.setUpgradeStatus(one.getUpgradeStatus());
            detailsVo.setProductBaseVo(counterUpgradeDetailsVo);
        }
        detailsVo.getProductBaseVo().setCurrencySpecies(payOrderInfo.getCurrencySpecies());
        detailsVo.getProductBaseVo().setCurrencySpeciesName(payOrderInfo.getCurrencySpeciesName());
        detailsVo.getProductBaseVo().setFlightType(suborderInfo.getFlightType());
        detailsVo.getProductBaseVo().setOrderType(suborderInfo.getOrderType());
        detailsVo.getProductBaseVo().setPayPrice(suborderInfo.getPracticalOrderPrice().toString());
        return detailsVo;
    }
}
