package com.swcares.psi.freeshuttle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @program: 3upsi_v3
 * @ClassName PvRuleDto
 * @description:
 * @author: 杜树涛
 * @create: 2021-11-15 11:09
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="PV_RULE_DTO对象", description="免费接送车旅客规则表DTO")
public class PvRuleDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id,在更新时传递该值")
    private String id;

    @ApiModelProperty(value = "航班规则")
    @Size(max = 24, message = "规则类型长度在0-24之间")
    private String ruleType;

    @ApiModelProperty(value = "航班类型（I国际，D国内）")
    @Size(max = 1, message = "航班类型长度在0-1之间")
    private String flightType;

    @ApiModelProperty(value = "起始航站")
    @Size(max = 3, message = "起始航站在0-3之间")
    private String org;

    @ApiModelProperty(value = "到达航站")
    @Size(max = 3, message = "到达航站在0-3之间")
    private String dst;

    @ApiModelProperty(value = "适用机场")
    @Size(max = 50, message = "机场长度在0-50之间")
    @Pattern(regexp = "[A-Z]{3}(、[A-Z]{3})*", message = "机场输入格式错误")
    private String airport;

    @ApiModelProperty(value = "允许乘坐的舱位（、分割）")
    @NotNull(message = "允许乘坐的舱位不能为空")
    @Pattern(regexp = "[A-Z](、[A-Z])*", message = "舱位参数格式错误")
    @Size(max = 50, message = "乘坐的舱位长度在0-50之间")
    private String cabinSeat;

    @ApiModelProperty(value = "是否替代舱位（Y，N）")
    @Size(max = 1, message = "替代舱位长度在0-1之间")
    private String isSubstitute;

    @ApiModelProperty(value = "折扣")
    @Size(min = 0, max = 4, message = "折扣在0-4之间")
    private String discount;

    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注长度在0-200之间")
    private String remarks;

    @ApiModelProperty(value = "转态（0停用，1使用，2删除）",required = true)
    @Size(max = 1, message = "转态长度在0-1之间")
    private String state;

}


