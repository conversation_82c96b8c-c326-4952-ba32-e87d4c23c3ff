<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeCashTurnListMapper">

    <select id="getCashTurnOrderAll" resultType="com.swcares.psi.aoMergeOrder.vo.CashTurnOrderTaskVo">
        select
        turnOrder.CALLBACK_PAY_NO as turnOrderNo,
        turnOrder.CHARGE_TYPE as chargeType,
        amsojcrp.MERGE_ORDER_ID as mergeOrderId,
        mergeOrder.ORDER_NO as mergeOrderNo
        from ao_merge_cash_turn_list turnOrder
        left join ao_merge_sub_order_join_cash_return_pay amsojcrp on turnOrder.id=amsojcrp.CASH_RETURN_PAY_ID
        left join ao_merge_order_info mergeOrder on mergeOrder.id=amsojcrp.MERGE_ORDER_ID

        where turnOrder.TURN_STATUS='1'
        and turnOrder.CREATE_TIME <![CDATA[ >=  ]]> DATE_FORMAT(#{createTime},'%Y-%m-%d %H:%i:%S')

    </select>



</mapper>
