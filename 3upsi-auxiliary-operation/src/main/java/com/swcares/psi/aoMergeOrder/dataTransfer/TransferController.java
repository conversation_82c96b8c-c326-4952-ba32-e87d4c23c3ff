package com.swcares.psi.aoMergeOrder.dataTransfer;

import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.service.AoOrderInfoService;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeOrderService;
import com.swcares.psi.ao.flightUpgrades.mapper.UserMapper;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.api.entity.SysDict;
import com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.ISysDictService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.base.file.FileUtils;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Api(tags = "数据迁移")
@RestController
@Slf4j
@RequestMapping("/api/ao/dataTransfer")
public class TransferController {

    @Autowired
    DataTransferService dataTransferService;

    @Autowired
    FltFlightRealInfoService fltFlightRealInfoService;
    @Autowired
    UserMapper userMapper;
    @Autowired
    SysAirportInfoService sysAirportInfoService;
    @Autowired
    FltPassengerRealInfoMapper fltPassengerRealInfoMapper;
    @Resource
    ISysDictService sysDictService;

    @GetMapping("/operation")
    public RenderResult dataTransfer(String dateStart, String dateEnd, String orderTypes) {
        String osName = System.getProperty("os.name").toLowerCase();
        String filePath = "";
        String timeToString = DateUtils.parseLocalDateTimeToString(LocalDateTime.now(), "yyyyMMddHHmmss");
        if (osName.contains("windows")) {
            FileUtils.createDirectory("C:\\Users\\<USER>\\Desktop\\auxiliaryDataTransfer");
            filePath = "C:\\Users\\<USER>\\Desktop\\auxiliaryDataTransfer\\" + dateStart + "_" + dateEnd + "_" + timeToString + ".txt";
        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
            FileUtils.createDirectory("/opt/applog/auxiliaryDataTransfer");
            filePath = "/opt/applog/auxiliaryDataTransfer/" + dateStart + "_" + dateEnd + "_" + timeToString + ".txt";
        } else {
            return RenderResult.fail("未知操作系统" + osName);
        }
        LocalDateTime start = DateUtils.parseStringToLocalDateTime(dateStart + " 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
        LocalDateTime end = DateUtils.parseStringToLocalDateTime(dateEnd + " 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS);
        String[] split = orderTypes.split(",");
        List<AoOrderInfoEntity> list = aoOrderInfoService.lambdaQuery()
                .le(AoOrderInfoEntity::getOrderDate, end)
                .ge(AoOrderInfoEntity::getOrderDate, start)
                .in(AoOrderInfoEntity::getOrderTypeCode, split)
                .in(AoOrderInfoEntity::getOrderStatus, new String[]{"0", "2", "5", "6"})
                .list();

        List<SysDict> currency_type = sysDictService.lambdaQuery()
                .eq(SysDict::getDataTypeCode, "currency_type")
                .eq(SysDict::getDataStatus, "0")
                .list();
        try {
            File myFile = new File(filePath);
            myFile.createNewFile();
        } catch (IOException e) {
            e.printStackTrace();
        }
        for (AoOrderInfoEntity ele : list) {
            String errorLog = "";
            try {
                dataTransferService.operation(ele, currency_type);
            } catch (Exception e) {
                log.info("数据迁移更新失败--{}", e.getMessage(), e);
                errorLog = ele.getOrderNo() + "----" + e.getMessage();
            }


            try {
                if (StringUtils.isNotEmpty(errorLog)) {
                    errorLog = errorLog + "\\n";
                    BufferedOutputStream buff = new BufferedOutputStream(new FileOutputStream(filePath));
                    buff.write(errorLog.getBytes());
                    buff.flush();
                    buff.close();
                }
            } catch (IOException e) {
                log.info("数据迁移日志写入失败" + ele.getOrderNo() + "\\n" + errorLog, e);
            }
        }
        return RenderResult.success();
    }


    @Autowired
    AoOrderInfoService aoOrderInfoService;
    @Autowired
    AoCounterUpgradeOrderService aoCounterUpgradeOrderService;

    @GetMapping("/xx")
    public RenderResult xx() throws Exception {
        BigInteger integer = new BigInteger("228926108674850270");
        List<AoOrderInfoEntity> list = aoOrderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderTypeCode, "5").list();
        for (AoOrderInfoEntity ele : list) {
            AoCounterUpgrade aoCounterUpgrade = new AoCounterUpgrade();
            integer = integer.add(new BigInteger("1"));
            aoCounterUpgrade.setId(integer.toString());
            aoCounterUpgrade.setOrderNo(ele.getOrderNo());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formatDateTime = ele.getCreateTime().format(formatter);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(formatDateTime);
            aoCounterUpgrade.setCreateDate(date);
            aoCounterUpgrade.setCreateUser(ele.getUserNo());
            aoCounterUpgradeOrderService.save(aoCounterUpgrade);
        }
        return RenderResult.success();
    }

    @GetMapping("/cc")
    public RenderResult cc() throws Exception {
        List<AoCounterUpgrade> list = aoCounterUpgradeOrderService.lambdaQuery()
                .isNull(AoCounterUpgrade::getOriginalCabin)
                .list();
        String fileName = "C:\\Users\\<USER>\\Desktop\\log\\";
        for (AoCounterUpgrade ele : list) {
            String log = DateUtils.parseDateToStr(ele.getCreateDate(), DateUtils.YYYY_MM_DD);
            String orderNo = ele.getOrderNo();
            // 读取文件内容到Stream流中，按行读取
            Stream<String> lines = Files.lines(Paths.get(fileName+log+".txt"));
            List<String> objects = lines.collect(Collectors.toList());
            //升舱成功
            boolean upgradeSuccess=false;


            for (String lineTxt : objects) {
                if (lineTxt.contains(ele.getOrderNo() + ",升舱成功")) {
                    upgradeSuccess = true;
                    System.out.println(lineTxt);
                     ele.setUpgradeStatus("2");
                    String[] t1 = lineTxt.split("订单号");
                    String emdNo = t1[1].split(";")[0];
                    ele.setUpgradeOrder(emdNo);
                    break;

                }
            }
            if(upgradeSuccess){
                for (String lineTxt : objects) {
                    if (lineTxt.contains("OrderNum:"+ele.getUpgradeOrder())) {
                        String  EmdNo = lineTxt.split("EmdNo:")[1].split(";")[0];
                        if(EmdNo.equals("null")){
                            continue;
                        }
                        ele.setEmdNo(EmdNo);
                        String  OriginalCabin = lineTxt.split("OriginalCabin:")[1].split(";")[0];
                        ele.setOriginalCabin(OriginalCabin);
                        String  SeniorCabin = lineTxt.split("SeniorCabin:")[1].split(";")[0];
                        ele.setNewCabin(SeniorCabin);
                        String  SeniorSest = lineTxt.split("SeniorSest:")[1].split(";")[0];
                        ele.setNewSeat(SeniorSest);
                        String  OriginalSeat = lineTxt.split("OriginalSeat:")[1].split(";")[0];
                        break;

                    }
                }
                aoCounterUpgradeOrderService.saveOrUpdate(ele);
            }




        }
        return RenderResult.success();
    }


    @GetMapping("/ee")
    public RenderResult ee() throws Exception {
        List<AoCounterUpgrade> list = aoCounterUpgradeOrderService.lambdaQuery()
                .isNull(AoCounterUpgrade::getOriginalCabin)
                .list();
        String fileName = "C:\\Users\\<USER>\\Desktop\\log\\";
        for (AoCounterUpgrade ele : list) {
            String log = DateUtils.parseDateToStr(ele.getCreateDate(), DateUtils.YYYY_MM_DD);
            String orderNo = ele.getOrderNo();
            // 读取文件内容到Stream流中，按行读取
            Stream<String> lines = Files.lines(Paths.get(fileName+log+".txt"));
            List<String> objects = lines.collect(Collectors.toList());

            for (String lineTxt : objects) {
                if (lineTxt.contains( "旅服订单号为:"+ele.getOrderNo())) {
                    System.out.println(lineTxt);
                    String[] t1 = lineTxt.split("PE订单号为：");
                    String emdNo = t1[1].split(",")[0];
                    ele.setUpgradeOrder(emdNo);
                    ele.setUpgradeStatus("3");

                    break;

                }
            }
                for (String lineTxt : objects) {
                    if (lineTxt.contains("OrderNum:"+ele.getUpgradeOrder())) {
                        String  ErrorMsg = lineTxt.split("ErrorMsg:")[1].split(";")[0];

                        if("null".equals(ErrorMsg)){
                            continue;
                        }
                        ele.setErrorInfo(ErrorMsg);
                        String  EmdNo = lineTxt.split("EmdNo:")[1].split(";")[0];
                        if(!EmdNo.equals("null")){
                        ele.setEmdNo(EmdNo);
                        }
                        String  OriginalCabin = lineTxt.split("OriginalCabin:")[1].split(";")[0];
                        ele.setOriginalCabin(OriginalCabin);
                        String  SeniorCabin = lineTxt.split("SeniorCabin:")[1].split(";")[0];
                        ele.setNewCabin(SeniorCabin);
                        String  SeniorSest = lineTxt.split("SeniorSest:")[1].split(";")[0];
                        ele.setNewSeat(SeniorSest);
                        ele.setFlightType("error");
                        aoCounterUpgradeOrderService.saveOrUpdate(ele);
                        break;
                    }
                }


        }
        return RenderResult.success();
    }




    @GetMapping("/olp")
    public RenderResult olp() throws Exception {
        List<AoCounterUpgrade> list = aoCounterUpgradeOrderService.lambdaQuery()
                .isNull(AoCounterUpgrade::getOriginalCabin)
                .list();
        String fileName = "C:\\Users\\<USER>\\Desktop\\log\\";
        for (AoCounterUpgrade ele : list) {
            String log = DateUtils.parseDateToStr(ele.getCreateDate(), DateUtils.YYYY_MM_DD);
            String orderNo = ele.getOrderNo();
            // 读取文件内容到Stream流中，按行读取
            Stream<String> lines = Files.lines(Paths.get(fileName+log+".txt"));
            List<String> objects = lines.collect(Collectors.toList());
            for (String lineTxt : objects) {
                    if (lineTxt.contains("OrderNum:"+ele.getUpgradeOrder())) {
                        String  ErrorMsg = lineTxt.split("ErrorMsg:")[1].split(";")[0];

                        if(!"null".equals(ErrorMsg)){
                        ele.setErrorInfo(ErrorMsg);
                        }
                        String  EmdNo = lineTxt.split("EmdNo:")[1].split(";")[0];
                        if(!EmdNo.equals("null")){
                            ele.setEmdNo(EmdNo);
                        }
                        String  OriginalCabin = lineTxt.split("OriginalCabin:")[1].split(";")[0];
                        ele.setOriginalCabin(OriginalCabin);
                        String  SeniorCabin = lineTxt.split("SeniorCabin:")[1].split(";")[0];
                        ele.setNewCabin(SeniorCabin);
                        String  SeniorSest = lineTxt.split("SeniorSest:")[1].split(";")[0];
                        ele.setNewSeat(SeniorSest);
                        ele.setFlightType("UN");
                        aoCounterUpgradeOrderService.saveOrUpdate(ele);
                        break;
                }
            }





        }
        return RenderResult.success();
    }

    @GetMapping("/auth")
    public RenderResult auth() throws Exception {
        List<AoCounterUpgrade> list = aoCounterUpgradeOrderService.lambdaQuery()
                .list();
        String fileName = "C:\\Users\\<USER>\\Desktop\\log\\";
            Stream<String> lines = Files.lines(Paths.get(fileName+"auth.txt"));
            List<String> objects = lines.collect(Collectors.toList());
        String orderList = objects.get(0);
        for (AoCounterUpgrade ele : list) {
            Date nextDay = new Date(ele.getCreateDate().getTime() + 24*60*60*1000);
            String log = DateUtils.parseDateToStr(nextDay, DateUtils.YYYY_MM_DD);
            // 读取文件内容到Stream流中，按行读取
            if(orderList.contains(ele.getOrderNo())){
                ele.setApproveUserNo("系统");
                ele.setApplyUpdateStatusReason("线上升舱失败，已线下升舱");
                ele.setApproveDate(DateUtils.parseStringToLocalDateTime(log+" 03:00:00",DateUtils.YYYY_MM_DD_HH_MM_SS));
                ele.setUpgradeStatus("2");
                ele.setUpgradeUpdateStatus("2");
            }

            aoCounterUpgradeOrderService.saveOrUpdate(ele);



        }
        return RenderResult.success();
    }



    public static void main(String[] args) throws Exception {
        String fileName = "C:\\Users\\<USER>\\Desktop\\log\\all";

        File file = new File(fileName);

        File[] subFiles = file.listFiles();
        String[] dateList = {"2024-01-19", "2024-01-20", "2024-01-21", "2024-01-22", "2024-01-23"};
        for (String d : dateList) {

            for (File e : subFiles) {
                if (e.getName().contains(d)) {

                    // 读取文件内容到Stream流中，按行读取
                    Stream<String> lines = Files.lines(Paths.get(fileName + "\\" + e.getName()));
                    List<String> collect = lines.collect(Collectors.toList());
                    System.out.println("");

                }
            }


        }

    }


}
