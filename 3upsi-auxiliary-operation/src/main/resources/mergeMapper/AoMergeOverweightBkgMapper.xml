<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeOverweightBkgMapper">

    <sql id="selectSql">
        select
        date_format(amoi.CREATE_DATE, '%Y-%m-%d') orderDate,
        aosi.SUBORDER_ORDER_NO as subOrderNo,
        ampoi.BANK_ORDER_NO as bankOrderNo,
        aosi.FINANCE_ORDER_NO,
        date_format(aosi.FLIGHT_DATE, '%Y-%m-%d') flightDate,
        aosi.FLIGHT_NO as flightNo,
        CONCAT(get_city_name(aosi.ORG), '-',aosi.ORG) as org,
        CONCAT(get_city_name(aosi.DST), '-',aosi.DST) as dst,
        amob.TKT_NO as tktNo,
        case amob.FLIGHT_TYPE
        when 'I' then '国际'
        when 'D' then '国内'
        else '地区'
        end as flightType,
        if(
        (select ifnull(sum(`NUMBER`),0) from ao_merge_overweight_bkg_charge_details bcd   where amob.ID=bcd.AO_MERGE_OVERWEIGHT_BKG_ID and bcd.CHARGE_ITEM='5')=0,
        (select ifnull(sum(`NUMBER`),0) from ao_merge_overweight_bkg_charge_details bcd   where amob.ID=bcd.AO_MERGE_OVERWEIGHT_BKG_ID and bcd.CHARGE_ITEM='6'),
        (select ifnull(sum(`NUMBER`),0) from ao_merge_overweight_bkg_charge_details bcd   where amob.ID=bcd.AO_MERGE_OVERWEIGHT_BKG_ID and bcd.CHARGE_ITEM='5')

        ) as specialOverWeight,

       (select ifnull(sum(`NUMBER`),0) from ao_merge_overweight_bkg_charge_details bcd   where amob.ID=bcd.AO_MERGE_OVERWEIGHT_BKG_ID and bcd.CHARGE_ITEM in ('1','2','3')) AS overQuantity,
        aosi.PRACTICAL_ORDER_PRICE,
        case ampoi.PAY_TYPE
        when '0' then '微信'
        when '1' then '易宝'
        when '2' then '支付宝'
        when '3' then '现金'
        when '4' then 'pos机'
        else 'worldpay'
        end as payType,
       CONCAT( ampoi.CURRENCY_SPECIES,'-', ampoi.CURRENCY_SPECIES_NAME) as currencySpecies,
        case amrel.PAY_EVENT
        when '1' then aosi.PRACTICAL_ORDER_PRICE
        when '4' then CONCAT('-',aosi.PRACTICAL_ORDER_PRICE)
        end as payOrRefundPrice,

        case amrel.PAY_EVENT
            when '1' then '已完成'
            when '4' then '已退款'
        end as orderStatus,

        case amrel.PAY_EVENT
            when '1' then '支付成功'
            when '4' then '退款成功'
        end as payStatus,
        if(amrel.PAY_EVENT='4', date_format(amroi.REFUND_END_TIME,'%Y-%m-%d %H:%i:%s'), date_format(ampoi.PAY_TIME,'%Y-%m-%d %H:%i:%s')) as payDate,
        aosi.PAX_NAME,
        ifnull(aosi.FFP_NO,'-') as ffpNo,
        ifnull(aosi.PAX_TYPE,'-') as paxType,
        ifnull(aosi.PAX_CATEGORY,'-') as paxCategory,
        amoi.CREATE_NO,
        amoi.CREATE_USER,
        amoi.CREATE_USER_DEPARTMENT as department,
        amoi.TURN_NUMBER,
        case amoi.TURN_STATUS
        when '1' then '缴纳中'
        when '2' then '缴纳失败'
        when '3' then '已缴纳'
        end as turnStatus,
        amoi.AREA_COMPANY
        from
        ao_merge_suborder_info  aosi

        inner join ao_merge_overweight_bkg  amob on aosi.id=amob.AO_MERGE_SUBORDER_INFO_ID
        left join ao_merge_order_info amoi on aosi.AO_MERGE_ORDER_INFO_ID =amoi.ID
        left join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
        left join ao_merge_report_event_log amrel on amrel.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        left join ao_merge_refund_order_info amroi on amroi.AO_MERGE_SUBORDER_INFO_ID =aosi.id
    </sql>
    <sql id="whereSql">
        where aosi.SUBORDER_ORDER_STATUS in ('1','2','3','4')

        <if test="dto.orderDateStart != null and dto.orderDateStart !=''">
            and amoi.CREATE_DATE  <![CDATA[ >= ]]> date_format(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.orderDateEnd != null and dto.orderDateEnd !=''">
            and amoi.CREATE_DATE  <![CDATA[ <= ]]> date_format(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.flightDateStart != null and dto.flightDateStart !=''">
            and aosi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.flightDateEnd != null and dto.flightDateEnd !=''">
            and aosi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.payTimeStart != null and dto.payTimeStart !=''">
            and ampoi.PAY_TIME  <![CDATA[ >= ]]>  date_format(#{dto.payTimeStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.payTimeEnd != null and dto.payTimeEnd !=''">
            and ampoi.PAY_TIME <![CDATA[ <= ]]> date_format(#{dto.payTimeEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.subOrderNo != null and dto.subOrderNo !=''">
            and aosi.SUBORDER_ORDER_NO like concat('%',#{dto.subOrderNo},'%')
        </if>

        <if test="dto.bankOrderNo != null and dto.bankOrderNo !=''">
            and ampoi.BANK_ORDER_NO like concat('%',#{dto.bankOrderNo},'%')
        </if>

        <if test="dto.flightNo != null and dto.flightNo !=''">
            and aosi.FLIGHT_NO =#{dto.flightNo}
        </if>

        <if test="dto.org != null and dto.org !=''">
            and aosi.ORG =#{dto.org}
        </if>

        <if test="dto.dst != null and dto.dst !=''">
            and aosi.DST =#{dto.dst}
        </if>

        <if test="dto.paxName != null and dto.paxName !=''">
            and aosi.PAX_NAME like concat('%',#{dto.paxName},'%')
        </if>

        <if test="dto.tktNo != null and dto.tktNo !=''">
            and amob.TKT_NO like concat('%',#{dto.tktNo},'%')
        </if>

        <if test="dto.flightType != null and dto.flightType !=''">
            and amob.FLIGHT_TYPE=#{dto.flightType}
        </if>

        <if test="dto.payType != null and dto.payType !=''">
            and find_in_set(ampoi.PAY_TYPE, #{dto.payType})
        </if>

        <if test="dto.payStatus != null and dto.payStatus !=''">
            and find_in_set(amrel.PAY_EVENT, #{dto.payStatus})
        </if>

        <if test="dto.department != null and dto.department !=''">
            and amoi.CREATE_USER_DEPARTMENT like concat('%',#{dto.department},'%')
        </if>

        <if test="dto.turnNumber != null and dto.turnNumber !=''">
            and amoi.TURN_NUMBER like concat('%',#{dto.turnNumber},'%')
        </if>

        <if test="dto.areaCompany != null and dto.areaCompany !=''">
            and amoi.AREA_COMPANY like concat('%',#{dto.areaCompany},'%')
        </if>
        <if test="dto.financeOrderNo != null and dto.financeOrderNo !=''">
            and aosi.FINANCE_ORDER_NO like concat('%',#{dto.financeOrderNo},'%')
        </if>
    </sql>

    <select id="getAccountsListPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
    </select>

    <select id="getAccountsList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
    </select>


    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto">
        select t.* from (
            select
            sum(CASE WHEN rs.PAY_TYPE = '0' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS wxMoney,
            sum(CASE WHEN rs.PAY_TYPE = '1' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
            sum(CASE WHEN rs.PAY_TYPE = '2'  THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS alipayMoney,
            sum(CASE WHEN (rs.PAY_TYPE = '3' or rs.PAY_TYPE = '4') THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end) AS cashMoney,
            sum(CASE WHEN rs.PAY_TYPE = '5' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end) AS worldPay,
            sum(rs.PRACTICAL_ORDER_PRICE  ) AS allMoney,
            case  rs.CURRENCY_SPECIES
            when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
            else   concat(rs.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
            end as currencySpecies
            from (
                select

                CASE amrel.PAY_EVENT
                when '4' then
                concat('-',PRACTICAL_ORDER_PRICE)
                else PRACTICAL_ORDER_PRICE
                end as PRACTICAL_ORDER_PRICE,
                ampoi.PAY_TYPE,
                ampoi.CURRENCY_SPECIES
                from
                ao_merge_suborder_info  aosi

                inner join ao_merge_overweight_bkg  amob on aosi.id=amob.AO_MERGE_SUBORDER_INFO_ID
                left join ao_merge_order_info amoi on aosi.AO_MERGE_ORDER_INFO_ID =amoi.ID
                left join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
                left join ao_merge_report_event_log amrel on amrel.AO_MERGE_SUBORDER_INFO_ID =aosi.id
                left join ao_merge_refund_order_info amroi on amroi.AO_MERGE_SUBORDER_INFO_ID =aosi.id
                <include refid="whereSql"></include>
            ) rs  group by rs.CURRENCY_SPECIES
        )t where t.allMoney > 0
    </select>


    <sql id="aoMergeOverweightInputTemplateSql">
        select
        '-' as batchGroupNumber,
        '-' as subBatchNumber,
        '-' as agentNumber,
        '876' as companyCode,
        SUBSTRING(amsi.FINANCE_ORDER_NO,1,3) as ticketType,
        '1' as tktCNo,
         date_format(amoi.CREATE_DATE,'%Y%m%d') as issueTime,
        amob.ORG as org,
        amob.DST as dst,
        SUBSTRING(amsi.FINANCE_ORDER_NO,-7,7) as tktNo,
        RIGHT(amob.FLIGHT_NO,4) AS flightNo,
        date_format(amob.FLIGHT_DATE,'%Y%m%d') as flightDate,
        (select sum(amobcd.NUMBER) from ao_merge_overweight_bkg_charge_details amobcd where amobcd.AO_MERGE_OVERWEIGHT_BKG_ID=amob.id and amobcd.CHARGE_ITEM='6') as overWeightBkgWeight,
        '-' as valuationCharge,
        amsi.PRACTICAL_ORDER_PRICE as denomination,
        '-' as standardHandlingRate,
        amsi.PRACTICAL_ORDER_PRICE/(select sum(amobcd.NUMBER) from ao_merge_overweight_bkg_charge_details amobcd where amobcd.AO_MERGE_OVERWEIGHT_BKG_ID=amob.id and amobcd.CHARGE_ITEM='6') as totalPrice,
        SUBSTRING(amsi.TKT_NO,1,3) AS passengerTicketCompanyCode,
        SUBSTRING(amsi.TKT_NO,4,3) AS passengerTicketType,
        RIGHT(amsi.TKT_NO,7)as passengerTktNo,
        amoi.AREA_COMPANY as areaCompany
        from ao_merge_suborder_info amsi
        left join   ao_merge_overweight_bkg amob on amob.AO_MERGE_SUBORDER_INFO_ID=amsi.id and amob.AO_MERGE_ORDER_INFO_ID=amsi.AO_MERGE_ORDER_INFO_ID
        inner join ao_merge_order_info amoi on amob.AO_MERGE_ORDER_INFO_ID=amoi.id
        inner join ao_merge_pay_order_info  ampoi on ampoi.id=amoi.ORDER_PAY_ID
        <where>
            amsi.ORDER_TYPE='3' and amsi.FLIGHT_TYPE='D' and amsi.SUBORDER_ORDER_STATUS='1'  and ampoi.CURRENCY_SPECIES='CNY'
            <if test="dto.flightStartDate != null and dto.flightStartDate !='' ">
                AND amsi.FLIGHT_DATE &gt;= date_format(#{dto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="dto.flightEndDate != null and dto.flightEndDate !='' ">
                AND amsi.FLIGHT_DATE &lt;= date_format(#{dto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="dto.orderStartDate != null and dto.orderStartDate !='' ">
                AND  amoi.CREATE_DATE &gt;= date_format(#{dto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="dto.orderEndDate != null and dto.orderEndDate !='' ">
                AND  amoi.CREATE_DATE &lt;= date_format(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>
            <if test="dto.flightNo != null and dto.flightNo !='' ">
                AND amsi.FLIGHT_NO  like concat('%',#{dto.flightNo},'%')
            </if>
            <if test="dto.org != null and dto.org !='' ">
                AND amsi.ORG = #{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst !='' ">
                AND amsi.DST = #{dto.dst}
            </if>


            <if test="dto.tktNo != null and dto.tktNo !='' ">
                AND amsi.TKT_NO   like concat('%',#{dto.tktNo},'%')
            </if>
            <if test="dto.areaCompany != null and dto.areaCompany !='' ">
                AND amoi.AREA_COMPANY  like concat('%',#{dto.areaCompany},'%')
            </if>
        </where>
        order by amoi.CREATE_DATE desc,amsi.FLIGHT_DATE desc,amsi.std desc
    </sql>
    <select id="getAoMergeOverweightInputTemplatePage"
            resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightInputTemplateReportVo">
        <include refid="aoMergeOverweightInputTemplateSql"></include>
    </select>

    <select id="getAoMergeOverweightInputTemplateList"
            resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightInputTemplateReportVo">
        <include refid="aoMergeOverweightInputTemplateSql"></include>
    </select>
</mapper>
