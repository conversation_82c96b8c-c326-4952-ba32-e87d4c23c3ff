package com.swcares.psi.aoMergeOrder.service;

import com.swcares.psi.aoMergeOrder.dto.AoMergeSelfInvoiceCacheDto;
import com.swcares.psi.aoMergeOrder.dto.InvoiceInfoMergeSaveDto;
import com.swcares.psi.aoMergeOrder.dto.MergeInvoiceInfoDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeInvoiceInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceDetailVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceInfoVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付开发票记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeInvoiceInfoService extends IService<AoMergeInvoiceInfo> {

    void saveInvoice(List<InvoiceInfoMergeSaveDto> dtos);
    void saveInvoiceByC(List<InvoiceInfoMergeSaveDto> dtos);

    void updateInvoiceStatus(AoMergeInvoiceInfo ele);


    PsiPage<AoMergeInvoiceInfoVo> getInvoiceInfoPage ( MergeInvoiceInfoDto dto);
    List<AoMergeInvoiceInfoVo> getInvoiceInfoList (MergeInvoiceInfoDto dto);
    AoMergeInvoiceDetailVo invoiceDetail ( String mergeInvoiceId);
    String getInvoiceSign(String mergeOrderNo);
    AoMergeSelfInvoiceCacheDto getInvoiceParam(String signId);
}
