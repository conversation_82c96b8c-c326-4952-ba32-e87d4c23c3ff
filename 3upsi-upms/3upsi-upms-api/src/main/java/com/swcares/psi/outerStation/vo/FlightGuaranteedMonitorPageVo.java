package com.swcares.psi.outerStation.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FlightGuaranteedMonitorPageVo extends OsmFlightInfoVo{

    @ApiModelProperty("实际起飞时间")
    @ExcelProperty(value = "起飞时间", index = 6)
    private String realTakeOffTime;

    @ApiModelProperty("航班类型code")
    @ExcelIgnore
    private String flightTypeCode;

    @ApiModelProperty("重要级别code")
    @ExcelIgnore
    private String importTypeCode;

    @ApiModelProperty("停机位code")
    @ExcelIgnore
    private String bayCode;

    @ApiModelProperty("延误原因code")
    @ExcelIgnore
    private String delayReasonCode;

    @ApiModelProperty("延误原因业务处理code")
    @ExcelIgnore
    private String flightDelayReasonNewCode;

    @ApiModelProperty("提交状态code")
    @ExcelIgnore
    private String subStatusCode;

    @ApiModelProperty("申诉状态code")
    @ExcelIgnore
    private String appealStatusCode;

    @ApiModelProperty("航班保障人code")
    @ExcelIgnore
    private String flightSupportStaffCode;

    @ApiModelProperty("航段")
    @ExcelProperty(value = "航段", index = 2)
    private String flightSegment;

    @ApiModelProperty("航班Id")
    @ExcelIgnore
    private String fltFlightId;

    @ApiModelProperty("航班Id")
    @ExcelIgnore
    private String flightId;

    @ApiModelProperty("提交人")
    @ExcelProperty(value = "提交人", index = 41)
    private String submitter;

    @ApiModelProperty("提交时间")
    @ExcelProperty(value = "提交时间", index = 42)
    private String subTime;


}
