package com.swcares.psi.ao.manySeats.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 一人多坐价格配置主类  (外币版本)
 */
@Data
@Builder
@TableName("ao_many_seats_price")
public class AoManySeatsPrice implements Serializable {
    /**
     * 禁用
     */
    public static final String STATUS_DISABLE="1";
    /**
     * 启用
     */
    public static final String STATUS_ENABLE="0";


    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键Id")
    private String id;

    @ApiModelProperty(value = "状态，0-启用、1-禁用")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty(value = "航线id")
    @TableField("ARI_LINE_ID")
    private String ariLineId;

    @ApiModelProperty(value = "修改人工号")
    @TableField("UPDATE_NO")
    private String updateNo;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATE_DATE")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人工号")
    @TableField("CREATE_USER")
    private String createUser;


}
