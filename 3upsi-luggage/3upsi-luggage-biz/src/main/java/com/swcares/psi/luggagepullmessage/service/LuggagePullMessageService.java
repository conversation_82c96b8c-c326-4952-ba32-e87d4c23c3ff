package com.swcares.psi.luggagepullmessage.service;

import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.luggagepullmessage.dto.LessLuggageDto;
import com.swcares.psi.luggagepullmessage.dto.LostItemsInfoDto;
import com.swcares.psi.luggagepullmessage.dto.MailInformationDto;
import com.swcares.psi.luggagepullmessage.dto.PirDto;
import com.swcares.psi.luggagepullmessage.vo.ArticleTypeDictNameVo;
import com.swcares.psi.luggagepullmessage.vo.LessLuggageVo;
import com.swcares.psi.luggagepullmessage.vo.LostItemPickupCity;
import com.swcares.psi.luggagepullmessage.vo.LostItemsInfoVo;
import com.swcares.psi.luggagepullmessage.vo.MailInformationVo;
import com.swcares.psi.luggagepullmessage.vo.PirVo;
import java.util.List;

public interface LuggagePullMessageService {
    List<ArticleTypeDictNameVo> queryArticleTypeDictName();

    PsiPage<LostItemsInfoVo> queryLostItemsInfo(LostItemsInfoDto lostItemsInfoDto);

    List<PirVo> queryPir(PirDto pirDto);

    List<MailInformationVo> queryMailInformation(MailInformationDto mailInformationDto);

    List<LessLuggageVo> queryLessLuggage(LessLuggageDto lessLuggageDto);

    List<LostItemPickupCity> queryLostItemPickupAirports();

}
