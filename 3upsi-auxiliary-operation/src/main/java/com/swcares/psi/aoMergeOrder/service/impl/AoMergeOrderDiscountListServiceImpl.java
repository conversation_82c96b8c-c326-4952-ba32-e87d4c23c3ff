package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountListSaveDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountQueryDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderDiscountList;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeOrderDiscountListMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderDiscountListService;
import com.swcares.psi.aoMergeOrder.utils.SortUtils;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.PsiPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合并支付订单折扣清单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Service
public class AoMergeOrderDiscountListServiceImpl extends ServiceImpl<AoMergeOrderDiscountListMapper, AoMergeOrderDiscountList> implements IAoMergeOrderDiscountListService {

    @Override
    public void saveConfig(AoMergeOrderDiscountListSaveDto dto) {
        if(StringUtils.isEmpty(dto.getItemsCode().trim())){
            throw new BusinessException(MessageCode.PARAM_INVALID_ERROR.getCode(), new String[]{"itemsCode"});
        }
        LocalDateTime now = LocalDateTime.now();
        //设置订单创建人工号姓名
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String username = authentication.getRealName();
        String itemCode = SortUtils.orderTypeSort(dto.getItemsCode());
        AoMergeOrderDiscountList one = this.lambdaQuery().eq(AoMergeOrderDiscountList::getItemsCode, itemCode)
                .eq(AoMergeOrderDiscountList::getIsDelete,AoMergeOrderDiscountList.DELETE_DISABLE)
                .one();
        AoMergeOrderDiscountList entity = new AoMergeOrderDiscountList();
        entity.setDiscount(dto.getDiscount());
        entity.setItemsCode(itemCode);
        entity.setItemsName(SortUtils.orderTypeNameSort(itemCode));
        entity.setRemark(dto.getRemark());
        entity.setStatus(dto.getStatus());
        entity.setIsDelete(AoMergeOrderDiscountList.DELETE_DISABLE);

        if(one!=null){
            if (!dto.getId().equals(one.getId())) {
                throw new BusinessException(MessageCode.DATA_EXIST.getCode());
            }
            entity.setId(dto.getId());
            entity.setUpdateDate(now);
            entity.setUpdateUser(userNo+"-"+username);
            this.saveOrUpdate(entity);
        }else{
            if(StringUtils.isEmpty(dto.getId())){
                entity.setCreateUser(userNo+"-"+username);
                entity.setCreateTime(now);
                this.save(entity);
            }else{
                entity.setId(dto.getId());
                entity.setUpdateDate(now);
                entity.setUpdateUser(userNo+"-"+username);
                this.saveOrUpdate(entity);
            }
        }

    }


    @Override
    public void deleteConfig(String id) {
        //设置订单创建人工号姓名
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String username = authentication.getRealName();
        log.info("{}-{}删除订单折扣配置{}",userNo,username,id);
        this.lambdaUpdate().set(AoMergeOrderDiscountList::getIsDelete,AoMergeOrderDiscountList.DELETE_ENABLE).eq(AoMergeOrderDiscountList::getId,id).update();
    }

    @Override
    public PsiPage<AoMergeOrderDiscountListVo> getPage(AoMergeOrderDiscountQueryDto dto) {
        getQueryList(dto);
        PsiPage<AoMergeOrderDiscountListVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.getPage(page,dto);
    }

    @Override
    public List<AoMergeOrderDiscountListVo> getList(AoMergeOrderDiscountQueryDto dto) {
        getQueryList(dto);
        return baseMapper.getList(dto);

    }

    private void getQueryList(AoMergeOrderDiscountQueryDto dto) {
        if(StringUtils.isEmpty(dto.getItemsCode())){
            return;
        }
        String replace = dto.getItemsCode().replace(",", "");
        String s = SortUtils.orderTypeSort(replace);
        dto.setItemsCode(s);
    }
}
