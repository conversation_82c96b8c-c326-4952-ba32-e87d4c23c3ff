package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsInputReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeManySeats;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsInputReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

/**
 * <p>
 * 合并支付一人多座产品详情订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeManySeatsService extends IService<AoMergeManySeats> {
    List<AoMergeManySeatsAccountsReportVo> getAccountsList(AoMergeManySeatsAccountsReportDto dto);
    PsiPage<AoMergeManySeatsAccountsReportVo> getAccountsListPage(AoMergeManySeatsAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeManySeatsAccountsReportDto dto);
    PsiPage<AoMergeManySeatsInputReportVo> getAoMergeManySeatsInputReportListPage(AoMergeManySeatsInputReportDto dto);
    List<AoMergeManySeatsInputReportVo> getAoMergeManySeatsInputReportList(AoMergeManySeatsInputReportDto dto);
}
