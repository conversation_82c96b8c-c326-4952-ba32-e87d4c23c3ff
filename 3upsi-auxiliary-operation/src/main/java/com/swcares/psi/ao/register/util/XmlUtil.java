package com.swcares.psi.ao.register.util;

import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.psi.ao.register.util.XmlUtil <br>;
 * Description：xml解析和组装工具类 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/1/21 12:54 <br>;
 * @version v1.0 <br>;
 */
@Slf4j
@Component("xmlUtil")
public class XmlUtil {
    //${abc}正则
    public static String varRegex = "\\$\\{\\s*(\\w+)\\s*(([\\+\\-])\\s*(\\d+)\\s*)?\\}";
    /**
     * xml解析成document对象
     *
     * @param xml
     * @return
     */
    public Document getDocument(String xml) {
        StringReader stringReader = new StringReader(xml);

        SAXReader saxReader = new SAXReader();

        Document document = null;

        try {
            document = saxReader.read(stringReader);
        } catch (DocumentException e) {
            log.error("xml解析出错->{}", e);
        }
        return document;
    }

    /**
     * xml与bean的相互转换
     *
     * @param element
     * @param direction 1：java2xml，2：xml2java
     * @param obj
     */
    public  void parseXml(Element element, String direction, Object obj) {
        //获取当前元素的所有子节点（在此我传入根元素）
        List<Element> elements = element.elements();
        //判断是否有子节点
        if (elements != null && elements.size() > 0)
        {
            //进入if说明有子节点
            //遍历
            for (Element e : elements)
            {
                //判断转换方向（1：java2xml；2：xml2java）
                if ("2".equals(direction)) //这里是xml转bean
                {
                    //声明Field
                    Field field = null;
                    try
                    {
                        //反射获取属性
                        field = obj.getClass().getDeclaredField(e.getName());
                    } catch (Exception e1)
                    {
                        log.error("xml解析出错->{}", e1);
                    }
                    //获取当前属性是否为list
                    if (field!=null&&List.class.getName().equals(field.getType().getName()))
                    {
                        //反射获取set方法
                        Method method = this.getDeclaredMethod(obj, "set".concat(this.toUpperCaseFirstOne(e.getName())), new Class[]{List.class});
                        //声明临时list
                        List temList = new ArrayList();
                        if (method!=null)
                        {
                            try
                            {
                                //反射调用obj的当前方法，可变参数为templist
                                method.invoke(obj, temList);
                            } catch (Exception e1) {
                                log.info("【{}】方法执行失败",method,e1);
                            }
                        }
                        //获取List的泛型参数类型
                        Type gType = field.getGenericType();
                        //判断当前类型是否为参数化泛型
                        if (gType instanceof ParameterizedType)
                        {
                            //转换成ParameterizedType对象
                            ParameterizedType pType = (ParameterizedType) gType;
                            //获得泛型类型的泛型参数（实际类型参数)
                            Type[] tArgs = pType.getActualTypeArguments();
                            if (tArgs!=null&&tArgs.length>0)
                            {
                                //获取当前元素的所有子元素
                                List<Element> elementSubList=e.elements();
                                //遍历
                                for (Element e1:elementSubList) {
                                    try
                                    {
                                        //反射创建对象
                                        Object tempObj = Class.forName(tArgs[0].getTypeName()).newInstance();
                                        temList.add(tempObj);
                                        //递归调用自身
                                        this.parseXml(e1, direction, tempObj);
                                    } catch (Exception e2)
                                    {
                                        log.error("【{}】对象构造失败",tArgs[0].getTypeName(),e2);
                                    }
                                }

                            }
                        }
                    }
                    else
                    {
                        //说明不是list标签，继续递归调用自身即可
                        this.parseXml(e, direction, obj);
                    }
                }
                else if("1".equals(direction))  //说明转换方向为：javabean转xml
                {
                    //递归调用自身
                    this.parseXml(e, direction, obj);
                }
                //此时还在for循环遍历根元素的所有子元素
            }
        }
        else
        {
            //说明无子节点
            //获取当前元素的名称
            String nodeName = element.getName();
            //获取当前元素的对应的值
            String nodeValue = element.getStringValue();

            //判断转换方向：1：java2xml、2：xml2java
            if ("1".equals(direction))//java2xml
            {
                if (nodeValue != null && nodeValue.matches(varRegex))
                {
                    /**
                     * 获取模板中各节点定义的变量名，例如<traceNo>${traceNo}</traceNo>
                     */
                    nodeValue = nodeValue.substring(nodeValue.indexOf("${") + 2, nodeValue.indexOf("}"));


                    Object value = null;
                    //根据解析出的变量名，调用obj对象的getXXX()方法获取变量值
                    Method method = this.getDeclaredMethod(obj, "get".concat(this.toUpperCaseFirstOne(nodeValue)), null);
                    if (method != null) {
                        try {
                            value = method.invoke(obj);
                        } catch (Exception e) {
                            log.error("方法【{}】调用异常", "get".concat(this.toUpperCaseFirstOne(nodeValue)));
                        }
                    }
                    //将变量值填充至xml模板变量名位置，例如<traceNo>${traceNo}</traceNo>
                    element.setText(value == null ? "" : value.toString());
                }
                //叶子节点
                log.debug("节点名【{}】，节点变量名【{}】",element.getName(),nodeValue);
            }
            else if ("2".equals(direction))//xml2java
            {
                if (nodeName != null && !"".equals(nodeName))
                {
                    //根据xml节点名，调用obj对象的setXXX()方法为obj设置变量值
                    Method method = this.getDeclaredMethod(obj, "set".concat(this.toUpperCaseFirstOne(nodeName)), new Class[]{String.class});
                    if(method!=null)
                    {
                        try
                        {
                            method.invoke(obj, nodeValue);
                        } catch (Exception e)
                        {
                            log.error("方法【{}】调用异常","set".concat(this.toUpperCaseFirstOne(nodeName)));
                        }
                    }
                }
            }
        }
    }


    private   Method getDeclaredMethod(Object object, String methodName, Class<?>[] parameterTypes)
    {

        for (Class<?> superClass = object.getClass(); superClass != Object.class; superClass = superClass.getSuperclass())
        {
            try
            {
                return superClass.getDeclaredMethod(methodName, parameterTypes);
            }
            catch (NoSuchMethodException e)
            {
                //Method 不在当前类定义, 继续向上转型
                log.error("xml解析出错, Method 不在当前类定义, 继续向上转型->{}", e);
            }
            //..
        }

        return null;
    }
    private  String toUpperCaseFirstOne(String s)
    {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs = s.toCharArray();
        cs[0] -= 32;
        return String.valueOf(cs);
    }

    public static void main(String[] args) {
//        String requestXml="<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
//                "<RQ1006>\n" +
//                "\t<channelType>${channelType}</channelType>\n" +
//                "\t<CN_LastName>${cnLastName}</CN_LastName>\n" +
//                "\t<CN_FirstName>${cnFirstName}</CN_FirstName>\n" +
//                "\t<EN_LastName>${enLastName}</EN_LastName>\n" +
//                "\t<EN_FirstName>${enFirstName}</EN_FirstName>\n" +
//                "\t<certific_Type>${certificType}</certific_Type>\n" +
//                "\t<certific_No>${certificNo}</certific_No>\n" +
//                "\t<birthDate>${birthDate}</birthDate>\n" +
//                "\t<mobile>${mobile}</mobile>\n" +
//                "\t<email>${email}</email>\n" +
//                "\t<enrollmentSource>${enrollmentSource}</enrollmentSource>\n" +
//                "\t<password>${password}</password>\n" +
//                "\t<sceneSource>${sceneSource}</sceneSource>\n" +
//                "\t<referenceId>${referenceId}</referenceId>\n" +
//                "\t<clientIp>${clientIp}</clientIp>\n" +
//                "\t<customerIp>${customerIp}</customerIp>\n" +
//                "\t<signature>${signature}</signature >\n" +
//                "</RQ1006>";
//
//        XmlUtil xmlUtil = new XmlUtil();
//        //获取document对象
//        Document document = xmlUtil.getDocument(requestXml);
//        //获取根元素
//        Element root = document.getRootElement();
//        //请求实体bean
//        VIPRegisterDto vipVerificationDto = new VIPRegisterDto();
//        vipVerificationDto.setBirthDate("dsadsadsa");
//        //解析xml，1：表示java2xml
//        xmlUtil.parseXml(root,"1",vipVerificationDto);
//        //输出请求报文
//        System.out.println(root.asXML());
    }
}
