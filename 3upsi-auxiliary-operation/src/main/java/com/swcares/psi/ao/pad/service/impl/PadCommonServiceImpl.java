package com.swcares.psi.ao.pad.service.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.github.wxpay.sdk.WXPayUtil;
import com.swcares.psi.ao.common.entity.*;
import com.swcares.psi.ao.common.service.AoPayRecordService;
import com.swcares.psi.ao.common.service.AoTempCashReturnService;
import com.swcares.psi.ao.common.service.IAoOrderInfoNewService;
import com.swcares.psi.ao.common.service.IAoPayOrderInfoService;
import com.swcares.psi.ao.cons.AoOrderConstantNew;
import com.swcares.psi.ao.midplatform.service.IAoOrderToMidplatformService;
import com.swcares.psi.ao.pad.entity.AoOlpCostUpgrades;
import com.swcares.psi.ao.pad.service.PadCommonService;
import com.swcares.psi.ao.pad.service.IAoOlpCostUpgradesService;
import com.swcares.psi.ao.pad.service.OlpCostUpgradesCashService;
import com.swcares.psi.ao.pad.utils.OlpUpgradeOrderUtil;
import com.swcares.psi.ao.payHander.AliPlanOnPayConfig;
import com.swcares.psi.ao.payHander.WxPlanOnPayConfig;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.api.entity.FltPassengerRealInfo;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.FltPassengerRealInfoService;
import com.swcares.psi.collect.qrCode.alipay.AliNativeProcess;
import com.swcares.psi.collect.qrCode.alipay.AliRequestCommonParam;
import com.swcares.psi.collect.qrCode.alipay.AliReturnConstans;
import com.swcares.psi.collect.qrCode.wx.WxNativeProcess;
import com.swcares.psi.collect.qrCode.wx.WxRequestCommonParam;
import com.swcares.psi.collect.qrCode.wx.WxReturnConstans;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.stimulate.dto.StimulateOrderBuilderParam;
import com.swcares.psi.stimulate.utils.StimulateOrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;

import static com.swcares.psi.base.util.DateUtils.parseStringToLocalDateTime;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.pad.service.impl.CommonServiceImpl <br>
 * Description：(用一句话描述这个类或者接口表示什么)<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022/4/20 18:41<br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class PadCommonServiceImpl implements PadCommonService {
    @Autowired
    private IAoOlpCostUpgradesService aoOlpCostUpgradesService;
    @Autowired
    private OlpCostUpgradesCashService olpCostUpgradesCashService;
    @Autowired
    private IAoOrderInfoNewService aoOrderInfoNewService;
    @Autowired
    private IAoPayOrderInfoService aoPayOrderInfoService;
    @Resource(name = "padWxPayConfig")
    private WxPlanOnPayConfig padWxPayConfig;
    @Autowired
    WxNativeProcess wxNativeProcess;
    @Autowired
    private AoPayRecordService aoPayRecordService;
    @Resource(name = "padAliPayConfig")
    private AliPlanOnPayConfig aliPlanOnPayConfig;
    @Autowired
    AliNativeProcess aliNativeProcess;
    @Autowired
    FltPassengerRealInfoService passengerService;
    @Autowired
    FltFlightRealInfoService fltFlightRealInfoService;

    @Autowired
    AoTempCashReturnService aoTempCashReturnService;

    @Autowired
    private IAoOrderToMidplatformService aoOrderToMidplatformService;

    @Autowired
    private StimulateOrderUtils stimulateOrderUtils;
    /**
     * 微信支付回调
     *
     * @param map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxpayNotifyUrl(Map<String, String> map) {
        if (map == null) {
            return;
        }
        String out_trade_no = map.get("out_trade_no");
        String return_code = map.get("return_code");
        String time_end = map.get("time_end");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        log.info("平板微信扫码支付回调订单触发:{}", JSON.toJSONString(map));
        log.info("平板微信扫码支付回调订单触发,订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        AoPayOrderInfo one = aoPayOrderInfoService.lambdaQuery().eq(AoPayOrderInfo::getPayOrderNo, out_trade_no).one();
        if (one == null) {
            return;
        }
        // 防支付成功回调多次触发支付成功流程
        if (AoOrderConstantNew.PAY_ORDER_STATUS_SUCCESS.equals(one.getPayStatus())) {
            log.info("平板微信扫码支付回调订单触发,订单{}支付状态已更新为成功", out_trade_no);
            return;
        }
        if ("SUCCESS".equals(return_code)) {

            aoPayOrderInfoService.lambdaUpdate().eq(AoPayOrderInfo::getId, one.getId())
                    .set(AoPayOrderInfo::getPayStatus, AoOrderConstantNew.PAY_ORDER_STATUS_SUCCESS)
                    .set(AoPayOrderInfo::getPayTime, parseStringToLocalDateTime(time_end, DateUtils.YYYY_MM_DD_HH_MM_SS))
                    .update();
            // 更新支付状态与单类型
            updateOlpUpgradeStatusSuccess(one.getAoOrderId());

            aoOrderToMidplatformService.toUpgradeOnboardMidplatformVo(out_trade_no);

            StimulateOrderBuilderParam stimulateOrderBuilderParam = new StimulateOrderBuilderParam();
            stimulateOrderBuilderParam.setOrderId(one.getAoOrderId());
            //激励
            new Thread(new Runnable() {
                @Override
                public void run() {
                    stimulateOrderUtils.stimulate(stimulateOrderBuilderParam);
                }
            }).start();
        }
//        createFinanceOrderNo(one.getAoOrderId());

    }


    /**
     * 微信退款回调
     *
     * @param map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxpayRefundNotifyUrl(Map<String, String> map) {

        log.info("机上升舱微信退款回调:{}", JSON.toJSONString(map));
        String return_code = map.get("return_code");
        if ("SUCCESS".equals(return_code)) {
            String req_info = AoUtils.wxRefundNotifyDecrypt(padWxPayConfig.getKey(), map.get("req_info"));
            if (req_info == null) {
                log.info("机上升舱微信退款回调返回值解密失败:{}", JSON.toJSONString(map));
                return;
            }
            Map<String, String> map1 = null;
            try {
                map1 = WXPayUtil.xmlToMap(req_info);
            } catch (Exception e) {
                log.error("机上升舱微信退款报文转换报错:" + JSON.toJSONString(map));
                e.printStackTrace();
            }
            String out_trade_no = map1.get("out_trade_no");
            String refund_status = map1.get("refund_status");
            String out_refund_no = map1.get("out_refund_no");
            AoPayOrderInfo one = aoPayOrderInfoService.lambdaQuery().eq(AoPayOrderInfo::getPayOrderNo, out_trade_no).one();
            if (one == null) {
                return;
            }
            if ("SUCCESS".equals(refund_status)) {
                aoOlpCostUpgradesService.lambdaUpdate()
                        .set(AoOlpCostUpgrades::getStatus, AoOrderConstantNew.PRODUCT_ORDER_STATUS_REFUNDSUCCESS)
                        .eq(AoOlpCostUpgrades::getRefundNo, out_refund_no)
                        .update();
                List<AoOlpCostUpgrades> list = aoOlpCostUpgradesService.lambdaQuery()
                        .eq(AoOlpCostUpgrades::getAoOrderId, one.getAoOrderId())
                        .list();
                int refundNumber = 0;

                for (AoOlpCostUpgrades ele : list) {
                    if (AoOrderConstantNew.PRODUCT_ORDER_STATUS_REFUNDSUCCESS.equals(ele.getAoOrderId())) {
                        refundNumber++;
                    }
                }
                String payStatus = AoOrderConstantNew.PAY_ORDER_STATUS_REFUND_PART;
                String orderStatus = AoOrderConstantNew.ORDER_STATUS_REFUND_PART;
                if (list.size() == refundNumber) {
                    payStatus = AoOrderConstantNew.PAY_ORDER_STATUS_REFUND_ALL;
                    orderStatus = AoOrderConstantNew.ORDER_STATUS_REFUND_ALL;
                }
                aoPayOrderInfoService.lambdaUpdate().eq(AoPayOrderInfo::getId, one.getId())
                        .set(AoPayOrderInfo::getPayStatus, payStatus)
                        .set(AoPayOrderInfo::getPayTime, LocalDateTime.now())
                        .update();
                aoOrderInfoNewService.lambdaUpdate().eq(AoOrderInfoNew::getId, one.getAoOrderId())
                        .set(AoOrderInfoNew::getOrderStatus, orderStatus)
                        .update();

                aoOrderToMidplatformService.toUpgradeOnboardMidplatformVo(out_trade_no);
            }

        } else {
            log.info("机上升舱微信退款状态未成功:" + JSON.toJSONString(map));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxpayTurnNotifyUrl(Map<String, String> map) {
        if (map == null) {
            return;
        }
        String out_trade_no = map.get("out_trade_no");
        String return_code = map.get("return_code");
        String time_end = map.get("time_end");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        log.info("平板微信缴纳支付回调订单触发:{}", JSON.toJSONString(map));
        log.info("平板微信缴纳支付回调订单触发,订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        List<AoTempCashReturnEntity> list = aoTempCashReturnService.lambdaQuery().eq(AoTempCashReturnEntity::getCallbackPayNo, out_trade_no)
                .list();
        if (list.size()<1) {
            return;
        }
        Set<String> set=new HashSet<>();
        list.stream().forEach(ele->{
            set.add(ele.getOrderNo());
        });
        if ("SUCCESS".equals(return_code)) {
            aoOlpCostUpgradesService.lambdaUpdate().in(AoOlpCostUpgrades::getId,set)
                    .set(AoOlpCostUpgrades::getTurnTime,LocalDateTime.now())
                    .set(AoOlpCostUpgrades::getTurnNumber,out_trade_no)
                    .update();
            aoTempCashReturnService.lambdaUpdate()
                    .set(AoTempCashReturnEntity::getTurnStatus, "2")
                    .in(AoTempCashReturnEntity::getOrderNo,set)
                    .eq(AoTempCashReturnEntity::getCallbackPayNo,out_trade_no)
                    .update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vaidatePayResults(List<AoOrderInfoNew> ali, List<AoOrderInfoNew> wx, List<AoOrderInfoNew> yee) {
        Set<String> set = new HashSet<>();
        for (AoOrderInfoNew orderinfo : ali) {
            AoPayOrderInfo byId = aoPayOrderInfoService.getById(orderinfo.getPayOrderId());
            if (byId == null) {
                continue;
            }
            AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
            requestCommonParam.setOutTradeNo(byId.getPayOrderNo());
            AlipayTradeQueryResponse query = aliNativeProcess.query(requestCommonParam, aliPlanOnPayConfig);
            if (query == null) {
                log.info("机上升舱支付宝支付结果查询失败,订单号:{}", orderinfo.getOrderNo());
                continue;
            }
            log.info("机上升舱支付宝支付结果验证,支付订单号:{}请求结果:{},支付结果:{}", byId.getPayOrderNo(), query.isSuccess(), query.getTradeStatus());
            log.info("机上升舱支付宝支付结果查询返回结果:" + JSON.toJSONString(query));
            //查询请求发送成功
            if (query.isSuccess()) {
                //等待支付状态不与处理（扫了二维码却没有支付的情况）
                if (AliReturnConstans.WAIT_BUYER_PAY.equals(query.getTradeStatus())) {
                    continue;
                }


                List<AoOlpCostUpgrades> list = aoOlpCostUpgradesService.lambdaQuery().eq(AoOlpCostUpgrades::getAoOrderId, orderinfo.getId())
                        .list();
                List<AoPayRecordEntity> recordEntities = new ArrayList<>();
                //查询结果为支付成功或者（交易结束，不可退款）
                if (AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus())) {
                    // 更新支付状态与单类型
                    updateOlpUpgradeStatusSuccess(orderinfo.getId());

                    byId.setPayStatus(AoOrderConstantNew.PAY_ORDER_STATUS_SUCCESS);
                    byId.setPayTime(LocalDateTime.now());
                    aoPayOrderInfoService.saveOrUpdate(byId);
                    for (AoOlpCostUpgrades ele : list) {
                        AoPayRecordEntity record = new AoPayRecordEntity();
                        record.setRecordFrom("机上升舱支付定时任务");
                        record.setOrderId(orderinfo.getId());
                        record.setOrderProductId(ele.getId());
                        record.setPayType(AoOrderConstantNew.PRODUCT_ORDER_STATUS_SUCCESS);
                        record.setOperationDate(LocalDateTime.now());
                        recordEntities.add(record);
                    }

                    aoOrderToMidplatformService.toUpgradeOnboardMidplatformVo(orderinfo.getOrderNo());
                }
                //未付款交易超时关闭，或支付完成后全额退款  不与处理  已退款交予退款定时任务处理
                else if (AliReturnConstans.TRADE_CLOSED.equals(query.getTradeStatus())) {
                    continue;
                }
                //支付失败
                else {

                }
                aoPayRecordService.saveBatch(recordEntities);
//                createFinanceOrderNo(orderinfo.getId());
            }else{
                //交易不存在(包含生成了二维码却没有扫码支付的情况(过期了))
                if (AliReturnConstans.ORDER_NOT_EXIST.equals(query.getSubCode())) {
                    continue;
                }
            }

        }
        for (AoOrderInfoNew orderinfo : wx) {
            AoPayOrderInfo byId = aoPayOrderInfoService.getById(orderinfo.getPayOrderId());
            if (byId == null) {
                continue;
            }
            WxRequestCommonParam param = new WxRequestCommonParam();
            param.setOutTradeNo(byId.getPayOrderNo());
            Map<String, String> query = wxNativeProcess.query(param, padWxPayConfig);
            if (query == null) {
                log.info("机上升舱微信支付结果查询失败,订单号:{}", orderinfo.getOrderNo());
                continue;
            }
            log.info("机上升舱微信支付结果验证,订单号:{}请求结果:{},订单状态:{}", byId.getPayOrderNo(), query.get("return_code"), query.get("trade_state"));
            log.info("机上升舱微信支付结果查询返回结果:" + JSON.toJSONString(query));
            //订单不存在不做处理
            if (WxReturnConstans.ORDER_NOT_EXIST.equals(query.get("err_code"))) {
                log.info("机上升舱微信支付订单不存在,订单号:{}", orderinfo.getOrderNo());
                continue;
            }
            //查询结果为支付成功
            if (WxReturnConstans.REQUEST_SUCCESS.equals(query.get("return_code"))) {
                //已退款   不与处理  已退款交予退款定时任务处理
                if (WxReturnConstans.REFUND_TRADE_STATE.equals(query.get("trade_state"))) {
                    continue;
                }
                List<AoOlpCostUpgrades> list = aoOlpCostUpgradesService.lambdaQuery().eq(AoOlpCostUpgrades::getAoOrderId, orderinfo.getId())
                        .list();
                List<AoPayRecordEntity> recordEntities = new ArrayList<>();
                //支付已成功
                if (WxReturnConstans.SUCCESS_TRADE_STATE.equals(query.get("trade_state"))) {
                    // 更新支付状态与单类型
                    updateOlpUpgradeStatusSuccess(orderinfo.getId());

                    byId.setPayStatus(AoOrderConstantNew.PAY_ORDER_STATUS_SUCCESS);
                    byId.setPayTime(LocalDateTime.now());
                    aoPayOrderInfoService.saveOrUpdate(byId);
                    for (AoOlpCostUpgrades ele : list) {
                        AoPayRecordEntity record = new AoPayRecordEntity();
                        record.setRecordFrom("机上升舱支付定时任务");
                        record.setOrderId(orderinfo.getId());
                        record.setOrderProductId(ele.getId());
                        record.setPayType(AoOrderConstantNew.PRODUCT_ORDER_STATUS_SUCCESS);
                        record.setOperationDate(LocalDateTime.now());
                        recordEntities.add(record);
                    }

                    aoOrderToMidplatformService.toMidplatformVo(orderinfo.getOrderNo());
                }
                //默认支付失败
                else {

                }
                aoPayRecordService.saveBatch(recordEntities);
//                createFinanceOrderNo(orderinfo.getId());
            }

        }
        for (AoOrderInfoNew orderinfo : yee) {
            //TODO 暂时没有此需求
        }
    }

    /**
     * 支付宝支付回调
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void alipayNotifyUrl(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = convertRequestParamsToMap(request);
        log.info("平板支付宝扫码支付回调订单触发:{}", JSON.toJSONString(map));
        try {

            boolean signVerified = AlipaySignature.rsaCheckV1(map, aliPlanOnPayConfig.getPublicKey(),
                    aliPlanOnPayConfig.getCharset(), aliPlanOnPayConfig.getSigType());
            if (!signVerified) {
                log.error("机上升舱支付宝回调签名认证失败,paramsJson:{}", map);
                return;
            }
        } catch (AlipayApiException e) {
            log.error("机上升舱支付宝回调签名认证异常,paramsJson:{},errorMsg:{}", map, e.getMessage(), e);
            return;
        }

        String total_amount = parameterMap.get("total_amount")[0];
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        String trade_status = parameterMap.get("trade_status")[0];
        String gmt_payment = parameterMap.get("gmt_payment")[0];
        String trade_no = parameterMap.get("trade_no")[0];
        AoPayOrderInfo one = aoPayOrderInfoService.lambdaQuery().eq(AoPayOrderInfo::getPayOrderNo, out_trade_no).one();
        if (one == null) {
            log.info("机上升舱支付宝扫码支付回调订单触发,订单{}不存在", out_trade_no);
            return;
        }
        // 防支付成功回调多次触发支付成功流程
        if (AoOrderConstantNew.PAY_ORDER_STATUS_SUCCESS.equals(one.getPayStatus())) {
            log.info("机上升舱支付宝扫码支付回调订单触发,订单{}支付状态已更新为成功 ", out_trade_no);
            return;
        }
        if ("TRADE_SUCCESS".equals(trade_status)) {

            aoPayOrderInfoService.lambdaUpdate().eq(AoPayOrderInfo::getId, one.getId())
                    .set(AoPayOrderInfo::getPayStatus, AoOrderConstantNew.PAY_ORDER_STATUS_SUCCESS)
                    .set(AoPayOrderInfo::getPayTime, parseStringToLocalDateTime(gmt_payment, DateUtils.YYYY_MM_DD_HH_MM_SS))
                    .update();
            // 更新支付状态与单类型
            updateOlpUpgradeStatusSuccess(one.getAoOrderId());

            aoOrderToMidplatformService.toUpgradeOnboardMidplatformVo(out_trade_no);

            StimulateOrderBuilderParam stimulateOrderBuilderParam = new StimulateOrderBuilderParam();
            stimulateOrderBuilderParam.setOrderId(one.getAoOrderId());
            //激励
            new Thread(new Runnable() {
                @Override
                public void run() {
                    stimulateOrderUtils.stimulate(stimulateOrderBuilderParam);
                }
            }).start();
        } else {
            log.info("机上升舱支付宝扫码支付回调订单触发,订单{}没有支付成功->>{}", out_trade_no, JSON.toJSONString(parameterMap));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void alipayTurnNotifyUrl(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = convertRequestParamsToMap(request);
        log.info("平板支付宝缴纳支付回调订单触发:{}", JSON.toJSONString(map));
        try {

            boolean signVerified = AlipaySignature.rsaCheckV1(map, aliPlanOnPayConfig.getPublicKey(),
                    aliPlanOnPayConfig.getCharset(), aliPlanOnPayConfig.getSigType());
            if (!signVerified) {
                log.error("机上升舱支付宝回调签名认证失败,paramsJson:{}", JSON.toJSONString(map));
                return;
            }
        } catch (AlipayApiException e) {
            log.error("机上升舱支付宝回调签名认证异常,paramsJson:{},errorMsg:{}", JSON.toJSONString(map), e.getMessage(), e);
            return;
        }

        String total_amount = parameterMap.get("total_amount")[0];
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        String trade_status = parameterMap.get("trade_status")[0];
        String gmt_payment = parameterMap.get("gmt_payment")[0];
        String trade_no = parameterMap.get("trade_no")[0];
        List<AoTempCashReturnEntity> list = aoTempCashReturnService.lambdaQuery().eq(AoTempCashReturnEntity::getCallbackPayNo, out_trade_no)
                .list();
        if (list.size()<1) {
            return;
        }
        Set<String> set=new HashSet<>();
        list.stream().forEach(ele->{
            set.add(ele.getOrderNo());
        });
        if ("TRADE_SUCCESS".equals(trade_status)) {
            aoOlpCostUpgradesService.lambdaUpdate().in(AoOlpCostUpgrades::getId,set)
                    .set(AoOlpCostUpgrades::getTurnTime,LocalDateTime.now())
                    .set(AoOlpCostUpgrades::getTurnNumber,trade_no)
                    .update();
            aoTempCashReturnService.lambdaUpdate()
                    .set(AoTempCashReturnEntity::getTurnStatus, "2")
                    .in(AoTempCashReturnEntity::getOrderNo,set)
                    .eq(AoTempCashReturnEntity::getCallbackPayNo,out_trade_no)
                    .update();
        } else {
            log.info("机上升舱支付宝扫码支付回调订单触发,订单{}没有支付成功->>{}", trade_status, JSON.toJSONString(parameterMap));
        }

    }

    private static Map<String, String> convertRequestParamsToMap(HttpServletRequest request) {
        Map<String, String> retMap = new HashMap<String, String>();

        Set<Map.Entry<String, String[]>> entrySet = request.getParameterMap().entrySet();

        for (Map.Entry<String, String[]> entry : entrySet) {
            String name = entry.getKey();
            String[] values = entry.getValue();
            int valLen = values.length;

            if (valLen == 1) {
                retMap.put(name, values[0]);
            } else if (valLen > 1) {
                StringBuilder sb = new StringBuilder();
                for (String val : values) {
                    sb.append(",").append(val);
                }
                retMap.put(name, sb.toString().substring(1));
            } else {
                retMap.put(name, "");
            }
        }

        return retMap;
    }


    /**
     * @deprecated 20220406生成规则调整：基于纸质+单类型。在查询时做相应处理。
     * 生成结算系统录入号
     * @param aoOrderId
     */
    public void createFinanceOrderNo(String aoOrderId){
        AoOrderInfoNew order = aoOrderInfoNewService.getById(aoOrderId);
        List<AoOlpCostUpgrades> list = aoOlpCostUpgradesService.lambdaQuery().eq(AoOlpCostUpgrades::getAoOrderId, aoOrderId).list();
        for (AoOlpCostUpgrades ele : list) {
            List<FltPassengerRealInfo> realInfos = passengerService.lambdaQuery()
                    .eq(FltPassengerRealInfo::getTicketNumber, ele.getTktNo())
                    .list();
            List<FltFlightRealInfo> allFilght = new ArrayList<>();
            for (FltPassengerRealInfo ff : realInfos) {
                //查询有几段航班
                List<FltFlightRealInfo> list1 = fltFlightRealInfoService.lambdaQuery()
                        .eq(FltFlightRealInfo::getFlightNumber, ff.getFlightNumber())
                        .eq(FltFlightRealInfo::getFlightDate, ff.getFlightDate())
                        .list();
                allFilght.addAll(list1);
            }
            Collections.sort(allFilght, new Comparator<FltFlightRealInfo>() {
                @Override
                public int compare(FltFlightRealInfo o1, FltFlightRealInfo o2) {
                    return o1.getStd().compareTo(o2.getStd());
                }
            });
            StringBuilder financeOrderNo = new StringBuilder();
            for (int i = 0; i < allFilght.size(); i++) {
                if (order.getOrg().equals(allFilght.get(i).getOrg()) && order.getDst().equals(allFilght.get(i).getDst())) {
                    switch (i) {
                        case 0:
                            financeOrderNo.append("JSS");
                            break;
                        case 1:
                            financeOrderNo.append("JJS");
                            break;
                        case 2:
                            financeOrderNo.append("JJJ");
                            break;
                        default:
                            financeOrderNo.append("SSS");
                    }
                    break;
                }
            }
            //客票后七位
            String tkt = ele.getTktNo().substring(ele.getTktNo().length() - 7);
            financeOrderNo.append(tkt);
            aoOlpCostUpgradesService.lambdaUpdate()
                    .set(AoOlpCostUpgrades::getFinanceOrderNo, financeOrderNo.toString())
                    .eq(AoOlpCostUpgrades::getAoOrderId, aoOrderId)
                    .update();
        }
    }


    @Override
    @Transactional
    public void vaidateTurnResults(Map<String, Set<String>> wxTurn,Map<String, Set<String>> aliTurn) {
        Iterator<Map.Entry<String, Set<String>>> iteratorWx = wxTurn.entrySet().iterator();
        Iterator<Map.Entry<String, Set<String>>> iteratorAli = aliTurn.entrySet().iterator();
        //支付宝缴纳
        while (iteratorAli.hasNext()) {
            Map.Entry<String, Set<String>> next = iteratorAli.next();
            String turnOrderNo = next.getKey();
            Set<String> paxIds = next.getValue();
            AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
            requestCommonParam.setOutTradeNo(turnOrderNo);
            AlipayTradeQueryResponse query = aliNativeProcess.query(requestCommonParam, aliPlanOnPayConfig);
            if (query == null) {
                log.info("机上升舱支付宝缴纳支付结果查询失败,缴纳订单号:{}", turnOrderNo);
                continue;
            }
            log.info("机上升舱支付宝缴纳支付结果验证,支付订单号:{}请求结果:{},支付结果:{}", turnOrderNo, query.isSuccess(), query.getTradeStatus());
            log.info("机上升舱支付宝缴纳支付结果查询返回结果:" + JSON.toJSONString(query));
            //查询请求发送成功
            if (query.isSuccess()) {

                //等待支付状态不与处理（扫了二维码却没有支付的情况）
                if (AliReturnConstans.WAIT_BUYER_PAY.equals(query.getTradeStatus())) {
                    log.info("机上升舱支付宝缴纳支付订单扫了二维码却没有支付,缴纳订单号:{}-{}", turnOrderNo,"扫了二维码却没有支付的情况");
                    List<AoTempCashReturnEntity> list = aoTempCashReturnService.lambdaQuery()
                            .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                            .list();
                    LocalDateTime now = LocalDateTime.now();
                    if (list != null && list.size() > 0 && list.get(0).getCreateTime().isBefore(now.minusHours(2L))) {
                        aoTempCashReturnService.lambdaUpdate()
                                .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                                .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                .update();
                    }
                    continue;
                }
                //查询结果为支付成功或者（交易结束，不可退款）
                if (AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus())) {
                    aoTempCashReturnService.lambdaUpdate()
                            .eq(AoTempCashReturnEntity::getCallbackPayNo,turnOrderNo)
                            .set(AoTempCashReturnEntity::getTurnStatus, "2")
                            .update();
                    aoOlpCostUpgradesService.lambdaUpdate()
                            .set(AoOlpCostUpgrades::getTurnTime,LocalDateTime.now())
                            .set(AoOlpCostUpgrades::getTurnNumber,turnOrderNo)
                            .in(AoOlpCostUpgrades::getId,paxIds)
                            .update();
                }
                //未付款交易超时关闭，或支付完成后全额退款  不与处理  已退款交予退款定时任务处理
                else if (AliReturnConstans.TRADE_CLOSED.equals(query.getTradeStatus())) {
                    aoTempCashReturnService.lambdaUpdate()
                            .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                            .set(AoTempCashReturnEntity::getTurnStatus, "1")
                            .update();
                    continue;
                }
                //支付失败
                else {

                }
            }else{
                //交易不存在(包含生成了二维码却没有扫码支付的情况(过期了))
                if (AliReturnConstans.ORDER_NOT_EXIST.equals(query.getSubCode())) {
                    log.info("机上升舱支付宝缴纳支付订单不存在,缴纳订单号:{}-{}", turnOrderNo,"交易不存在");
                    List<AoTempCashReturnEntity> list = aoTempCashReturnService.lambdaQuery()
                            .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                            .list();
                    LocalDateTime now = LocalDateTime.now();
                    if (list != null && list.size() > 0 && list.get(0).getCreateTime().isBefore(now.minusHours(2L))) {
                        aoTempCashReturnService.lambdaUpdate()
                                .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                                .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                .update();
                    }
                    continue;
                }
            }

        }
        while (iteratorWx.hasNext()) {
            Map.Entry<String, Set<String>> next = iteratorWx.next();
            String turnOrderNo = next.getKey();
            Set<String> paxIds = next.getValue();
            WxRequestCommonParam param = new WxRequestCommonParam();
            param.setOutTradeNo(turnOrderNo);
            Map<String, String> query = wxNativeProcess.query(param, padWxPayConfig);
            if (query == null) {
                log.info("机上升舱微信缴纳支付结果查询失败,订单号:{}", turnOrderNo);
                continue;
            }
            log.info("机上升舱微信缴纳支付结果验证,订单号:{}请求结果:{},订单状态:{}", turnOrderNo, query.get("return_code"), query.get("trade_state"));
            log.info("机上升舱微信缴纳支付结果查询返回结果:" + JSON.toJSONString(query));
            //订单不存在不做处理
            if (WxReturnConstans.ORDER_NOT_EXIST.equals(query.get("err_code"))) {
                log.info("机上升舱微信缴纳支付订单不存在,订单号:{}", turnOrderNo);
                aoTempCashReturnService.lambdaUpdate()
                        .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                        .set(AoTempCashReturnEntity::getTurnStatus, "1")
                        .update();
                continue;
            }
            //查询结果为支付成功
            if (WxReturnConstans.REQUEST_SUCCESS.equals(query.get("return_code"))) {
                //已退款   不与处理  已退款交予退款定时任务处理
                if (WxReturnConstans.REFUND_TRADE_STATE.equals(query.get("trade_state"))) {
                    aoTempCashReturnService.lambdaUpdate()
                            .eq(AoTempCashReturnEntity::getCallbackPayNo, turnOrderNo)
                            .set(AoTempCashReturnEntity::getTurnStatus, "1")
                            .update();
                    continue;
                }
                //支付已成功
                if (WxReturnConstans.SUCCESS_TRADE_STATE.equals(query.get("trade_state"))) {
                    aoTempCashReturnService.lambdaUpdate()
                            .eq(AoTempCashReturnEntity::getCallbackPayNo,turnOrderNo)
                            .set(AoTempCashReturnEntity::getTurnStatus, "2")
                            .update();
                    aoOlpCostUpgradesService.lambdaUpdate()
                            .set(AoOlpCostUpgrades::getTurnTime,LocalDateTime.now())
                            .set(AoOlpCostUpgrades::getTurnNumber,turnOrderNo)
                            .in(AoOlpCostUpgrades::getId,paxIds)
                            .update();
                    }
                }
                //默认支付失败
                else {

                }
            }
    }

    /**
     *  支付成功更新支付状态与单类型
     *  @date:20230406 之前生成规则是一个订单生成一个单类型共同使用，修改为一个订单下基于票号顺序生成唯一单类型。结算录入号生成规则同步调整为：纸质+单类型
     * @param aoOrderId
     */
    private void updateOlpUpgradeStatusSuccess(final String aoOrderId) {
        List<AoOlpCostUpgrades> list = aoOlpCostUpgradesService.lambdaQuery().eq(AoOlpCostUpgrades::getAoOrderId, aoOrderId).orderByDesc(AoOlpCostUpgrades::getTktNo).list();
        list.forEach(olp -> {
            String tktType = OlpUpgradeOrderUtil.createFinancialTktType(aoOrderId);

            aoOlpCostUpgradesService.lambdaUpdate()
                    .set(AoOlpCostUpgrades::getStatus, AoOrderConstantNew.PRODUCT_ORDER_STATUS_SUCCESS)
                    .set(AoOlpCostUpgrades::getTktType,tktType)
                    .eq(AoOlpCostUpgrades::getId, olp.getId())
                    .update();
        });
    }
}
