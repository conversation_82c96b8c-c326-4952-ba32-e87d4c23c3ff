<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.mapper.AoViproomItemConfMapper">

    <resultMap id="aoViproomItemConfResultMap" type="com.swcares.psi.ao.model.AoViproomItemConf">
        <id column="ID" property="id"/>
        <result column="PORT_CODE" property="portCode"/>
        <result column="NAME" property="name"/>
        <result column="PRICE" property="price"/>
        <result column="DESC_TEXT" property="descText"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>

    <select id="getAllDateByPortCode" resultType="com.swcares.psi.ao.model.vo.AoViproomItemConfVO">
        select
        item.ID,
        item.PORT_CODE,
        item.NAME,
        item.DESC_TEXT AS `desc`,
        (select com.CURRENCY_PRICE from ao_common_price_config com where com.BUSINESS_TABLE_ID =item.id and com.STATUS
        ='1' and com.CURRENCY_TYPE ='CNY'  order by com.CREATE_TIME DESC limit 1 ) as price,
        (
        select
        group_concat(
        concat(com.CURRENCY_PRICE, com.CURRENCY_TYPE, '(',com.CURRENCY_TYPE_NAME, ')')
        order by com.BUSINESS_TABLE_ID separator '\n'
        ) as xx
        from
        ao_common_price_config com
        where
        com.STATUS = '1'
        and com.CURRENCY_TYPE  <![CDATA[ <> ]]> 'CNY'
        and com.BUSINESS_TABLE_ID =item.id
        order by
        com.BUSINESS_TABLE_ID
        ) as foreignCurrencyPrice


        from ao_viproom_item_conf item
        <where>
            <if test="portCode != null and portCode!= '' ">
                item.PORT_CODE=#{portCode}
            </if>
        </where>
    </select>

</mapper>