<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.sceneSeats.mapper.AoSceneSeatsPriceConfigMapper">


    <resultMap id="resultType" type="com.swcares.psi.ao.sceneSeats.vo.AoSceneSeatsPriceConfigInfoVo">
        <result column="id" property="id" />
        <result column="cnyPrice" property="cnyPrice" />
        <result column="code" property="code" />
        <collection property="priceList" ofType="com.swcares.psi.ao.sceneSeats.vo.AoSceneSeatsPriceConfigInfoVo$PriceTypeClass" javaType="java.util.List"
        select="priceInfo" column="id">
            <result column="currencyType" property="currencyType"/>
            <result column="currencyPrice" property="currencyPrice"/>
            <result column="currencyTypeName" property="currencyTypeName"/>
        </collection>
    </resultMap>
    <select id="getPriceInfo" resultMap="resultType">
            select
                asspc.ID AS id,
                acpc.CURRENCY_PRICE as cnyPrice,
                asspc.SEAT_TYPE_CODE as code
            from  ao_scene_seats_price_config asspc
            left join ao_common_price_config acpc on asspc.ID=acpc.BUSINESS_TABLE_ID and acpc.CURRENCY_TYPE='CNY'


    </select>

    <select id="priceInfo" resultType="com.swcares.psi.ao.sceneSeats.vo.AoSceneSeatsPriceConfigInfoVo$PriceTypeClass">
            select
                cc.CURRENCY_TYPE as currencyType,
                cc.CURRENCY_TYPE_NAME as currencyTypeName,
                cc.CURRENCY_PRICE as currencyPrice
            from ao_common_price_config cc
            where cc.BUSINESS_TABLE_ID=#{id} and cc.CURRENCY_TYPE <![CDATA[  <> ]]> 'CNY'

    </select>


</mapper>