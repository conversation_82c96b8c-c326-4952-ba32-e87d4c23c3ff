package com.swcares.psi.undercharge.service.Impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.component.core.exception.ColumnAuthException;
import com.component.core.processor.FieldPermissionsProcessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.freemarker.FreemarkerUtils;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.luggage.dto.PkgInfoDto;
import com.swcares.psi.luggage.entity.PkgDetailInfo;
import com.swcares.psi.luggage.entity.PkgInfo;
import com.swcares.psi.luggage.service.PkgDetailInfoService;
import com.swcares.psi.luggage.service.PkgInfoService;
import com.swcares.psi.luggage.vo.*;
import com.swcares.psi.terminal.service.IDpPkgTerminalService;
import com.swcares.psi.undercharge.dto.UnderchargeLuggageHistorySearchDto;
import com.swcares.psi.undercharge.dto.UnderchargeLuggageMatchDto;
import com.swcares.psi.undercharge.dto.UnderchargeLuggageQuickSearchDto;
import com.swcares.psi.undercharge.dto.UnderchargeLuggageSearchListDto;
import com.swcares.psi.undercharge.dto.UnderchargeQuickSearchCountDto;
import com.swcares.psi.undercharge.mapper.UnderchargeLuggageMapper;
import com.swcares.psi.undercharge.service.UnderchargeLuggageService;
import com.swcares.psi.undercharge.vo.UnderchargeLuggageMatchInfoVo;
import com.swcares.psi.undercharge.vo.UnderchargeLuggagePrintVo;
import com.swcares.psi.undercharge.vo.UnderchargeLuggageQuickSearchVo;
import com.swcares.psi.undercharge.vo.UnderchargeLuggageQuickSearchVos;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * Created on  2021/1/29
 * Title:       [少收行李管理]
 * Description: [少收行李管理]
 *
 * @author: like
 * @version: 1.0
 */
@Service
@Slf4j
public class UnderchargeLuggageServiceImpl extends ServiceImpl<UnderchargeLuggageMapper, PkgInfo> implements UnderchargeLuggageService {

    @Autowired
    private UnderchargeLuggageMapper underchargeLuggageMapper;
    @Autowired
    private PkgInfoService pkgInfoService;
    @Autowired
    private PkgDetailInfoService pkgDetailInfoService;
    @Autowired
    private IDpPkgTerminalService pkgTerminalService;
    @Autowired
    private FreemarkerUtils freemarkerUtils;
    @Autowired
    private FieldPermissionsProcessor fieldPermissionsProcessor;

    @Override
    public PsiPage<PkgPageInfoVo> listUnderchargeLuggage(PsiPage<PkgPageInfoVo> page, UnderchargeLuggageSearchListDto underchargeLuggageSearchListDto) {
        return underchargeLuggageMapper.listUnderchargeLuggage(page, underchargeLuggageSearchListDto);
    }

    @Override
    @EncryptMethod
    public PsiPage<UnderchargeLuggageQuickSearchVo> listUnderchargeLuggageQuick(UnderchargeLuggageQuickSearchDto underchargeLuggageQuickSearchDto) {
        PsiPage<UnderchargeLuggageQuickSearchVo> page = new PsiPage<>(underchargeLuggageQuickSearchDto);
        //首先查询列表
        String idType = underchargeLuggageQuickSearchDto.getIdType();
        if (!StringUtils.isEmpty(idType)) {
            List<String> idTypeList = Arrays.asList(idType.split(","));
            underchargeLuggageQuickSearchDto.setIdTypeList(idTypeList);
        }
        String tktNo = StringUtils.isBlank(underchargeLuggageQuickSearchDto.getTktNo()) ? null : underchargeLuggageQuickSearchDto.getTktNo();
        String pkgNo = StringUtils.isBlank(underchargeLuggageQuickSearchDto.getPkgNo()) ? null : underchargeLuggageQuickSearchDto.getPkgNo();
        String idNo = StringUtils.isBlank(underchargeLuggageQuickSearchDto.getIdNo()) ? null : underchargeLuggageQuickSearchDto.getIdNo();
        if (StringUtils.isBlank(tktNo) && StringUtils.isBlank(pkgNo) && StringUtils.isBlank(idNo)) {
            throw new BusinessException(MessageCode.TKTNO_PKGNO_IDNO_LOST.getCode());
        }
        return underchargeLuggageMapper.listUnderchargeLuggageQuick(page, underchargeLuggageQuickSearchDto);
    }

    @Override
    @EncryptMethod
    public UnderchargeLuggageQuickSearchVos listUnderchargeLuggageQuickCount(UnderchargeQuickSearchCountDto underchargeQuickSearchCountDto) {
        UnderchargeLuggageQuickSearchVos underchargeLuggageQuickSearchVos = new UnderchargeLuggageQuickSearchVos();
        List<String> accidentType = new ArrayList<>();
        //在查询统计结果
        accidentType.add("2");
        List<PkgInfo> underchargeInfoCount = underchargeLuggageMapper.getPkgInfoCount(underchargeQuickSearchCountDto, accidentType);
        accidentType.clear();
        accidentType.add("6");
        List<PkgInfo> lostInfoCount = underchargeLuggageMapper.getPkgInfoCount(underchargeQuickSearchCountDto, accidentType);
        accidentType.clear();
        accidentType.add("1");
        accidentType.add("7");
        List<PkgInfo> breakInfoCount = underchargeLuggageMapper.getPkgInfoCount(underchargeQuickSearchCountDto, accidentType);
        int totalMoney = 0;
        if (null != underchargeInfoCount && !underchargeInfoCount.isEmpty()) {
            underchargeLuggageQuickSearchVos.setUnderchargeCount(String.valueOf(underchargeInfoCount.size()));
            Integer sumMoney = underchargeInfoCount.stream().map(pkgInfo -> {
                return underchargeLuggageMapper.getPkgAccidentTotalMoney(pkgInfo.getAccidentId());
            }).reduce(Integer::sum).get();
            totalMoney += sumMoney;
        } else {
            underchargeLuggageQuickSearchVos.setUnderchargeCount("0");
        }
        if (null != lostInfoCount && !lostInfoCount.isEmpty()) {
            underchargeLuggageQuickSearchVos.setLostCount(String.valueOf(lostInfoCount.size()));
            Integer sumMoney = lostInfoCount.stream().map(pkgInfo -> {
                return underchargeLuggageMapper.getPkgAccidentTotalMoney(pkgInfo.getAccidentId());
            }).reduce(Integer::sum).get();
            totalMoney += sumMoney;
        } else {
            underchargeLuggageQuickSearchVos.setLostCount("0");
        }
        if (null != breakInfoCount && !breakInfoCount.isEmpty()) {
            underchargeLuggageQuickSearchVos.setBreakCount(String.valueOf(breakInfoCount.size()));
            Integer sumMoney = breakInfoCount.stream().map(pkgInfo -> {
                return underchargeLuggageMapper.getPkgAccidentTotalMoney(pkgInfo.getAccidentId());
            }).reduce(Integer::sum).get();
            totalMoney += sumMoney;
        } else {
            underchargeLuggageQuickSearchVos.setBreakCount("0");
        }
        underchargeLuggageQuickSearchVos.setTotalPayMoney(String.valueOf(totalMoney));
        return underchargeLuggageQuickSearchVos;
    }

    @Override
    @EncryptMethod
    public PsiPage<UnderchargeLuggageQuickSearchVo> listUnderchargeLuggageHistory(PsiPage<PkgPageInfoVo> page, UnderchargeLuggageHistorySearchDto underchargeLuggageHistorySearchDto) {
        if (StringUtils.isNotBlank(underchargeLuggageHistorySearchDto.getAccidentType())) {
            String accidentType = underchargeLuggageHistorySearchDto.getAccidentType();
            List<String> accidentTypeList = Arrays.asList(accidentType.split(","));
            underchargeLuggageHistorySearchDto.setAccidentTypeList(accidentTypeList);
        }
        return underchargeLuggageMapper.listUnderchargeLuggageHistory(page, underchargeLuggageHistorySearchDto);
    }

    @Override
    public Map<String, Object> getDetailByAccidentId(String accidentId) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("fileReferenceNO", accidentId);
        resultMap.put("accidentCategory", "AHL");
        //旅客信息
        UnderchargeLuggagePrintVo printAccidentInfo = underchargeLuggageMapper.getPrintAccidentInfo(accidentId);
        //查询客票上行李件数和发生事故行李件数

        List<Map<String, Object>> paxTktPkgTotal = pkgInfoService.getPaxTktPkgTotal("", printAccidentInfo.getTktNo(), printAccidentInfo.getFlightNo(), "");

        List<Map<String, Object>> paxTktPkgTotalOccur = pkgInfoService.getPaxPkgOccurTotal(printAccidentInfo.getTktNo(), printAccidentInfo.getFlightNo(), printAccidentInfo.getAccidentType());
        String chackedPiecesInTicket = paxTktPkgTotal.size()+"";
        double chackedWeightInTicket =0;
        int nwMissingPieces =0;
        double nwMissingweight =0;
        DecimalFormat df = new DecimalFormat("0.##");

        for (Map<String, Object> map : paxTktPkgTotal){
            String pkgWeight = (String) map.get("pkgWeight");
            chackedWeightInTicket+=Double.parseDouble(pkgWeight);
        }

        for (Map<String, Object> map : paxTktPkgTotalOccur){
            String pkgWeight = (String) map.get("pkgWeight");
            String accidentType = (String) map.get("accidentType");
            if(printAccidentInfo.getAccidentType().equals(accidentType)){
                nwMissingPieces+=1;
                nwMissingweight +=Double.parseDouble(pkgWeight);
            }
            chackedWeightInTicket+=Double.parseDouble(pkgWeight);
        }

        if (null != printAccidentInfo) {
            resultMap.put("phoneNo", pkgTerminalService.getTerminalPhone(printAccidentInfo.getServiceCity()));
            resultMap.put("nmPassengerSurname", printAccidentInfo.getPaxName());
            resultMap.put("itInitials", printAccidentInfo.getPaxInitials());
            resultMap.put("ticketNo", printAccidentInfo.getTktNo());
            resultMap.put("flightNo", printAccidentInfo.getFlightNo());
            resultMap.put("rtRoutingToTraced", printAccidentInfo.getSegment());
            resultMap.put("cabin", printAccidentInfo.getCabin());
            resultMap.put("certificateNo", printAccidentInfo.getIdNo());
            resultMap.put("flightDate", DateUtils.formatDate(printAccidentInfo.getFlightDate(), DateUtils.YYYY_MM_DD));
            resultMap.put("date", DateUtils.formatDate(printAccidentInfo.getCreateTime(), DateUtils.YYYY_MM_DD));
            resultMap.put("companyOfficialSignature", printAccidentInfo.getCreateUser());
            resultMap.put("recipients", printAccidentInfo.getReceiveUser());
            resultMap.put("phoneNo1", printAccidentInfo.getTelephone());
            resultMap.put("phoneNo2", printAccidentInfo.getTelephoneTwo());
            resultMap.put("address", printAccidentInfo.getPostAddr());
        }
        //行李信息
        List<PkgDetailInfo> pkgDetailInfoByAccidentId = pkgDetailInfoService.getPkgDetailInfoByAccidentId(accidentId);
        if (null != pkgDetailInfoByAccidentId && !pkgDetailInfoByAccidentId.isEmpty()) {
            List<Map<String, String>> pkgInfoList = new ArrayList<>();
            for (PkgDetailInfo pkgDetailInfo : pkgDetailInfoByAccidentId) {
                PkgInfo one = pkgInfoService.lambdaQuery().eq(PkgInfo::getAccidentId, pkgDetailInfo.getAccidentId()).one();
                Map<String, String> pkgInfoMap = new HashMap<>();

                pkgInfoMap.put("chackedPiecesInTicket", one.getTktPkgCount());
                pkgInfoMap.put("chackedWeightInTicket", one.getTktPkgWeight());
                pkgInfoMap.put("nwMissingPieces", String.valueOf(nwMissingPieces));
                pkgInfoMap.put("nwMissingweight", df.format(nwMissingweight));

                pkgInfoMap.put("tnBaggageTagNamber", pkgDetailInfo.getPkgNo());
                pkgInfoMap.put("ctBaggageDescription", pkgDetailInfo.getPkgContent());
                pkgInfoMap.put("typeDescription", pkgDetailInfo.getPkgColor());
                pkgInfoMap.put("weight", pkgDetailInfo.getPkgWeight());
                pkgInfoList.add(pkgInfoMap);
            }
            resultMap.put("pkgInfoList", pkgInfoList);
        }
        return resultMap;
    }

    @Override
    public List<UnderchargeLuggageMatchInfoVo> matchAccident(UnderchargeLuggageMatchDto underchargeLuggageMatchDto) {
        return underchargeLuggageMapper.getMatchAccident(underchargeLuggageMatchDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String turnToDamageOrLost(PkgInfoDto pkgInfoDto) throws Exception {
        //首先查询事故单状态
        PkgInfo pkgInfo = pkgInfoService.getOne(Wrappers.<PkgInfo>lambdaQuery()
                .eq(PkgInfo::getAccidentId, pkgInfoDto.getAccidentId())
                .or(pkgInfoLambdaQueryWrapper -> pkgInfoLambdaQueryWrapper.eq(PkgInfo::getStatus, "0").eq(PkgInfo::getStatus, "2"))
                .ne(PkgInfo::getAccidentType, "5"));
        if (null == pkgInfo || StringUtils.isNotBlank(pkgInfo.getOutType())) {
            throw new BusinessException(MessageCode.TURN_TO_OTHER_ALREADY.getCode());
        }
        //将当前事故单状态结案
        pkgInfoService.update(Wrappers.<PkgInfo>lambdaUpdate()
                .set(PkgInfo::getOutType, "5")
                .eq(PkgInfo::getAccidentId, pkgInfoDto.getAccidentId()));
        //保存事故单
        pkgInfoDto.setAccidentId(null);
        if (StringUtils.isNotBlank(pkgInfoDto.getRecipientPhoneFirst()) && !pkgInfoDto.getRecipientPhoneFirst().contains("*")) {
            pkgInfoDto.setRecipientPhoneFirst(AesEncryptUtil.encrypt(pkgInfoDto.getRecipientPhoneFirst()));
        }
        if (StringUtils.isNotBlank(pkgInfoDto.getRecipientPhoneSecond()) && !pkgInfoDto.getRecipientPhoneSecond().contains("*")) {
            pkgInfoDto.setRecipientPhoneSecond(AesEncryptUtil.encrypt(pkgInfoDto.getRecipientPhoneSecond()));
        }
        return pkgInfoService.savePkgInfo(pkgInfoDto);
    }

    @Override
    public List<String> printAll(String accidentId, HttpServletResponse response, Integer imgWidthPx, Integer imgHeightPx, HttpServletRequest request) throws Exception {
        List<String> resultMap = new ArrayList<>();
        //事故单打印信息
        Map<String, Object> detailByAccidentId = getDetailByAccidentId(accidentId);
        AccidentInfoVo accidentInfoVo = new AccidentInfoVo();
        BeanUtil.fillBeanWithMap(detailByAccidentId, accidentInfoVo, false);
        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            accidentInfoVo = (AccidentInfoVo) fieldPermissionsProcessor.process(serverHttpRequest, accidentInfoVo);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            log.error("数据脱敏失败", e);
            throw new ColumnAuthException(e);
        }
        resultMap.add(freemarkerUtils.turnImage(imgWidthPx, imgHeightPx, true, "BatchLuggageIncidentTicket.ftl", accidentInfoVo, response));
        //赔偿单打印信息
        List<String> orderIdList = pkgInfoService.getOrderIdByAccidentId(accidentId);
        if (null != orderIdList && !orderIdList.isEmpty()) {
            for (String orderId : orderIdList) {
                Map<String, Object> orderPrintInfo = pkgInfoService.getOrderPrintInfo(orderId);
                OrderPrintVo orderPrintVo = new OrderPrintVo();
                BeanUtil.fillBeanWithMap(orderPrintInfo, orderPrintVo, false);
                try {
                    orderPrintVo = (OrderPrintVo) fieldPermissionsProcessor.process(serverHttpRequest, orderPrintVo);
                } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
                    log.error("数据脱敏失败", e);
                    throw new ColumnAuthException(e);
                }
                resultMap.add(freemarkerUtils.turnImage(2480, 3200, true, "luggageCompensateVoucherPayment.ftl", orderPrintVo, response));
            }
        }
        //图片打印信息
        List<Map<String, Object>> imgPrintInfoList = pkgInfoService.getImgPrintInfo(accidentId, request);
        for (Map<String, Object> imgPrint : imgPrintInfoList) {
            resultMap.add(freemarkerUtils.turnImage(imgWidthPx, imgHeightPx, true, "img.ftl", imgPrint, response));
        }
        //沟通记录信息
        Map<String, Object> communicate = pkgInfoService.downLoadCommunicate(accidentId);
        if (null != communicate) {
            CommunicateVo communicateVo = new CommunicateVo();
            BeanUtil.fillBeanWithMap(communicate, communicateVo, false);
            try {
                communicateVo = (CommunicateVo) fieldPermissionsProcessor.process(serverHttpRequest, communicateVo);
            } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
                log.error("数据脱敏失败", e);
                throw new ColumnAuthException(e);
            }
            resultMap.add(freemarkerUtils.turnImage(imgWidthPx, imgHeightPx, true, "recordCommunication.ftl", communicateVo, response));
        }
        return resultMap;
    }

}
