package com.swcares.psi.ao.register.util;

import org.apache.commons.lang3.StringUtils;

/**
 * ClassName：com.swcares.psi.ao.register.util.NameConversionUtil <br>;
 * Description：名字提取工具 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/1/21 17:24 <br>;
 * @version v1.0 <br>;
 */
public class NameConversionUtil {

    /**
     * Title：getCnLastName <br>;
     * Description：从名字中获取中文姓 <br>;
     * @param:  名字<br>;
     * @return:  中文姓<br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 17:29 <br>;
     * @throws  <br>;
     */
    public static String getCnLastName(String name) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(name = name.trim())) {
            return "";
        }

        return name.substring(0,1);
    }

    /**
     * Title：getCnLastName <br>;
     * Description：从名字中获取中文名 <br>;
     * @param:  名字<br>;
     * @return:  中文姓<br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 17:29 <br>;
     * @throws  <br>;
     */
    public static String getCnFirstName(String name) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(name = name.trim())) {
            return "";
        }
        return name.substring(1);
    }

    /**
     * Title：getEnLastName <br>;
     * Description：从名字中获取英文姓 <br>;
     * @param:  名字<br>;
     * @return:  中文姓<br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 17:29 <br>;
     * @throws  <br>;
     */
    public static String getEnLastName(String name) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(name = name.trim())) {
            return "";
        }
        name = name.substring(0, 1);
        if (StringUtils.isEmpty(name)) {
            return "";
        }
        return PinyinUtil.ToPinyin(name);
    }

    /**
     * Title：getEnFirstName <br>;
     * Description：从名字中获取英文名 <br>;
     * @param:  名字<br>;
     * @return:  中文姓<br>;
     * <AUTHOR> <br>;
     * date 2022/1/21 17:29 <br>;
     * @throws  <br>;
     */
    public static String getEnFirstName(String name) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(name = name.trim())) {
            return "";
        }
        name = name.substring(1);
        if (StringUtils.isEmpty(name)) {
            return "";
        }
        return PinyinUtil.ToPinyin(name);
    }

    public static void main(String[] args) {
        String name = "d s ";
        System.out.println(NameConversionUtil.getCnLastName(name));
        System.out.println(NameConversionUtil.getCnFirstName(name));
        System.out.println(NameConversionUtil.getEnLastName(name));
        System.out.println(NameConversionUtil.getEnFirstName(name));
    }
}
