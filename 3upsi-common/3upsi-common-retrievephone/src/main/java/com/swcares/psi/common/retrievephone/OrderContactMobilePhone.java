package com.swcares.psi.common.retrievephone;

import com.swcares.psi.common.retrievephone.config.PaxerPhoneRetrieveConfiguration;
import com.swcares.psi.common.retrievephone.v2.IContactMobileServiceProxy;
import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.rmi.RemoteException;
import java.util.Base64;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.io.SAXReader;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

/**
 * Created on Nov 30, 2016<br>
 * Title: [3UPSI]_[订单联系电话的获取]<br>
 * Description: [该类实现了从新旧接口中获取订单联系人电话。]<br>
 * Copyright: Copyright (c) 2016<br>
 * Company: 民航西南凯亚<br>
 * Department: 研发部<br>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ConditionalOnBean(PaxerPhoneRetrieveConfiguration.class)
@Component
public class OrderContactMobilePhone {

    @Resource
    private PaxerPhoneRetrieveConfiguration configuration;

    /** 旧官网接口。 */
//    private static ServicesSoapProxy ssp = new ServicesSoapProxy();

    /** 读取和解析返回数据 */
    private static SAXReader saxReader = new SAXReader();

    /**
     * Description:[从新老接口获取订单联系电话.]<br>
     *
     * <AUTHOR>
     * @update Nov 30, 2016
     * @param pnr
     *            pnr编码
     * @param org
     *            出发航站
     * @param dst
     *            到达航站
     * @param takeoffTime
     *            离港时间
     * @param cfgs
     *            可选的配置参数，第一个值用于指定渠道名。
     * @return 电话号码或null。
     * @throws Exception
     *             com.scal.www.Exception 接口定义的异常.
     * @throws RemoteException
     * @throws DocumentException
     * @throws UnsupportedEncodingException
     */
    public String getCellPhoneNum(String pnr, String org, String dst, String takeoffTime, String paxName,
            String... cfgs)
            throws Exception, RemoteException, DocumentException, UnsupportedEncodingException {

        String phoneNum = null;

        try {
            // 先从数据中心获取
            phoneNum = NewOrderContactMobilePhone.getCellPhoneNum(configuration.getDatacenterUrl(), paxName, pnr, org, dst, takeoffTime);
            if (StringUtils.isNotBlank(phoneNum)) {
                return decrypt(phoneNum, configuration.getDatacenterEncryptKey());
            }
        } catch (SocketException | SocketTimeoutException e) {
            log.info("从数据中心接口获取旅客电话超时，将继续调用官网接口，pnr:{},paxName:{}", pnr, paxName, e);
            log.error("从数据中心接口获取旅客电话失败，pnr:{},paxName:{}", pnr, paxName, e);
        } catch (java.lang.Exception e) {
            log.error("从数据中心接口获取旅客电话出错，pnr:{},paxName:{}", pnr, paxName, e);
            throw e;
        }
        try {
            // 再从数据中心备用接口获取
            phoneNum = NewOrderContactMobilePhone.getCellPhoneNum(configuration.getDatacenterUrlBackup(), paxName, pnr, org, dst, takeoffTime);
            // 调用数据中心备用接口正常则返回结果
            if (StringUtils.isNotBlank(phoneNum)) {
                phoneNum = decrypt(phoneNum, configuration.getDatacenterEncryptKey());
            }
            return phoneNum;
        } catch (SocketException | SocketTimeoutException e) {
            log.info("从数据中心备用接口获取旅客电话超时，将继续调用官网接口，pnr:{},paxName:{}", pnr, paxName, e);
            log.error("从数据中心备用接口获取旅客电话失败，pnr:{},paxName:{}", pnr, paxName, e);
        } catch (java.lang.Exception e) {
            log.error("从数据中心备用接口获取旅客电话出错，pnr:{},paxName:{}", pnr, paxName, e);
            throw e;
        }
        try {
            // 调用数据中心接口超时（网络异常），再从直销渠道官网获取
            phoneNum = callFromOffical(pnr, org, dst, takeoffTime, paxName, cfgs);
        } catch (Exception e) {
            log.error("调用官网接口获取旅客电话出错，pnr:{},paxName:{}", pnr, paxName, e);
        }
        return phoneNum;
    }

    /**
     * 调用直销渠道官网接口获取旅客电话
     *
     * @param pnr
     * @param org
     * @param dst
     * @param takeoffTime
     * @param paxName
     * @param cfgs
     * @return
     * @throws Exception
     */
    private String callFromOffical(String pnr, String org, String dst, String takeoffTime, String paxName,
                                          String... cfgs) throws Exception {
        // 渠道
        String cname = cfgs.length > 0 ? cfgs[0] : configuration.getOfficalChannel();
        // 构造接口调用入参。
        StringBuffer sb = new StringBuffer();
        sb.append("<Root>");
        sb.append("<CName>").append(cname).append("</CName>");
        sb.append("<PNR>").append(pnr).append("</PNR>");
        sb.append("<OrgCity>").append(org).append("</OrgCity>");
        sb.append("<DesCity>").append(dst).append("</DesCity>");
        sb.append("<TakeoffTime>").append(takeoffTime).append("</TakeoffTime>");
        String oldCmd = sb.toString() + "</Root>";
        // 签名
        String sign = ContactMoileEncoder
                .getEncryptRSA(pnr + configuration.getOfficalChannel() + org + dst + takeoffTime);
        sb.append("<Sign>").append(configuration.getOfficalIp()).append(sign).append("</Sign>");
        String newCmd = sb.toString() + "</Root>";
        log.info("从官网接口获取旅客电话，NewCmd:{}, pnr:{}, paxName", newCmd, pnr, paxName);

        String rtn = new IContactMobileServiceProxy(configuration.getOfficalUrl()).getOrderContactMobilePhone(newCmd);
        log.info("从官网接口获取旅客电话返回：{}，pnr:{}, paxName", rtn, pnr, paxName);
        Document document =
                OrderContactMobilePhone.saxReader.read(new ByteArrayInputStream(rtn.getBytes("UTF-8")));
        String code = document.selectSingleNode("/Root/Error/Code").getText();
        String msg = document.selectSingleNode("/Root/Error/Message").getText();

        String phoneNum = null;
        if (code.equals("0000") && msg.equals("操作成功") && document.selectSingleNode("/Root/Return/ContactMobilePhone") != null) {// 取电话号码
            phoneNum = document.selectSingleNode("/Root/Return/ContactMobilePhone").getText().trim();
            log.info("从官网获取到旅客电话：{}, pnr:{}, paxName:{}", phoneNum, pnr, paxName);
        } else {
            log.info("从官网获取到旅客电话失败, pnr:{}, paxName:{}", pnr, paxName);
        }

        return phoneNum;
    }

//    private static String callSSP(String oldCmd) throws RemoteException,
//            UnsupportedEncodingException, DocumentException {
//        String phoneNum = "";
//        log.info("OldCmd:" + oldCmd);
//        String rtn = OrderContactMobilePhone.ssp
//                .getOrderContactMobilePhone(oldCmd);
//        log.info("OldCmdRtn:" + rtn);
//        Document document = OrderContactMobilePhone.saxReader
//                .read(new ByteArrayInputStream(rtn.getBytes("UTF-8")));
//        String code = document.selectSingleNode("/Root/Error/Code").getText();
//        if (code.equals("0000")) {
//            phoneNum = document
//                    .selectSingleNode("/Root/Return/ContactMobilePhone")
//                    .getText().trim();
//        }
//        return phoneNum;
//    }

    /***
     * aes-128-gcm 解密
     * @return msg 返回字符串
     */
    public static String decrypt(String secret, String password) {
        try {
            byte[] sSrc = Base64.getDecoder().decode(secret);
            byte[] sKey = parseHexStr2Byte(password);

            GCMParameterSpec iv = new GCMParameterSpec(128, sSrc, 0, 12);
            Cipher cipher = Cipher.getInstance("AES/GCM/PKCS5Padding");
            SecretKey key2 = new SecretKeySpec(sKey, "AES");

            cipher.init(Cipher.DECRYPT_MODE, key2, iv);

            //这边和nodejs不同的一点是 不需要移除后面的16位
            byte[] decryptData = cipher.doFinal(sSrc, 12, sSrc.length - 12);

            return new String(decryptData);
        } catch (Exception ex) {
            log.error("调用数据中心获取电话接口对返回数据进行解密时出错！secret:{}", secret, ex);
            return secret;
        }
    }

    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1)
            return null;
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }


}
