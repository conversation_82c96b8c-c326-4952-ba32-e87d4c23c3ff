package com.swcares.psi.luggage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.luggage.dto.ContactInfoDto;
import com.swcares.psi.luggage.entity.ContactInfo;
import java.util.List;

/**
 * Created on  2021/2/26
 * Title:       [事故单联系记录]
 * Description: [事故单联系记录]
 *
 * @author: like
 * @version: 1.0
 */
public interface ContactInfoService extends IService<ContactInfo> {

    /**
     * 保存一条事故单联系记录
     * @param contactInfoDto
     * @return
     */
    Boolean saveContactInfo(ContactInfoDto contactInfoDto);

    /**
     * 获取事故单联系记录列表
     * @return
     */
    List<ContactInfo> listContactInfo(String accidentId);
    
    /**
     * 修改一条事故单联系记录
     * @param contactInfoDto
     * @return
     */
    Boolean updateContactInfo(ContactInfoDto contactInfoDto);
}
