package com.swcares.psi.modules.sys;

import com.alibaba.fastjson.JSON;
import com.swcares.psi.base.data.api.dto.*;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.vo.FlightInfoManagementVo;
import com.swcares.psi.base.data.api.vo.SysAirportInfoVo;
import com.swcares.psi.base.data.api.vo.SysY100PriceVo;
import com.swcares.psi.base.data.api.vo.ThirdAircraftInfoVo;
import com.swcares.psi.base.data.service.FlightInfoManagementService;
import com.swcares.psi.base.data.service.IAoCityAreaCompanyConfigService;
import com.swcares.psi.base.data.service.SysAirLineInfoService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.base.data.service.SysY100PriceService;
import com.swcares.psi.common.core.constant.ServiceNameConstants;
import com.swcares.psi.common.core.rpc.RpcRequest;
import com.swcares.psi.common.core.rpc.RpcUtil;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.common.utils.validatesign.TimestampInvalidationException;
import com.swcares.psi.common.utils.validatesign.ValidateSignatureUtil;
import com.swcares.psi.common.utils.validatesign.ValidationFailedException;
import com.swcares.psi.config.FlightInfoManagementMqSendUtil;
import com.swcares.psi.modules.sys.dto.FlightInfoManagementSupplyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.net.URLEncoder;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/7/8 15:47
 */
@RestController
@RequestMapping("/api/dp/sys/base/data")
@Slf4j
@Api(tags = "基础数据")
public class SysDataController {

    @Autowired
    SysAirportInfoService sysAirportInfoService;

    @Autowired
    SysAirLineInfoService sysAirLineInfoService;
    @Autowired
    IAoCityAreaCompanyConfigService aoCityAreaCompanyConfigService;

    @Autowired
    SysY100PriceService sysY100PriceService;
    @Autowired
    FlightInfoManagementMqSendUtil flightInfoManagementMqSendUtil;

    @Autowired
    private FlightInfoManagementService flightInfoManagementService;

    @PostMapping("/airport/saveUpdate")
    @ApiOperation(value = "航站基本信息保存")
    public RenderResult airportSaveUpdate(@RequestBody SysAirportInfoDto dto) {
        return RenderResult.success(sysAirportInfoService.saveOrUpdate(dto));
    }

    @GetMapping("/airport/getInfo")
    @ApiOperation(value = "查询航站基本信息")
    public SysAirportInfoEntity getAirportPage(String code) {
        SysAirportInfoEntity one = sysAirportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode,code)
                .eq(SysAirportInfoEntity::getIsUse,"0")
                .one();
        return one==null?new SysAirportInfoEntity():one;
    }


    @GetMapping("/airport/getInfoPage")
    @ApiOperation(value = "查询航站基本信息")
    public RenderResult getAirportPage(SysAirportInfoDto dto) {
        return RenderResult.success(sysAirportInfoService.getAirportInfoPage(dto));
    }

    @PostMapping("/airport/delete")
    @ApiOperation(value = "删除航站")
    public RenderResult airportDelete(@RequestBody List<String> ids) {
        sysAirportInfoService.airportDelete(ids);
        return RenderResult.success();
    }

    @ApiOperation(value = "航站导出")
    @GetMapping("/airport/export")
    public void export(SysAirportInfoDto dto, HttpServletResponse response) throws Exception {
        response.setContentType("application/csv");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("航站信息", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<SysAirportInfoVo> list = sysAirportInfoService.getAirportInfo(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), list, SysAirportInfoVo.class, "",
                null);
    }


    @PostMapping("/airLine/saveUpdate")
    @ApiOperation(value = "航线基本信息保存")
    public RenderResult airLineSaveUpdate(@RequestBody SysAirLineInfoDto dto) {
        return RenderResult.success(sysAirLineInfoService.saveOrUpdate(dto));
    }

    @GetMapping("/airLine/getInfo")
    @ApiOperation(value = "航线基本信息")
    public RenderResult getAirLine(SysAirLineInfoDto dto) {
        return RenderResult.success(sysAirLineInfoService.getAirLineInfo(dto));
    }

    @GetMapping("/selectCityArea")
    @ApiOperation(value = "行政归属下拉框数据")
    public RenderResult getSelectCityAreaCompanyInfo() {
        return RenderResult.success(aoCityAreaCompanyConfigService.getSelectCityAreaCompanyInfo());
    }

    @GetMapping("/getSelectCountry")
    @ApiOperation(value = "国家下拉框数据")
    public RenderResult getSelectCountry() {
        return RenderResult.success(sysAirportInfoService.getSelectCountry());
    }

    @GetMapping("/getSelectIntercontinental")
    @ApiOperation(value = "洲际下拉框数据")
    public RenderResult getSelectIntercontinental() {
        return RenderResult.success(sysAirportInfoService.getSelectIntercontinental());
    }


    @GetMapping("/getPriceInfoPage")
    @ApiOperation(value = "Y100运价查询")
    public RenderResult getPriceInfoPage(SysY100PriceDto dto) {
        return RenderResult.success(sysY100PriceService.getPriceInfoPage(dto));
    }

    @GetMapping("/getY100Price")
    @ApiOperation(value = "获取Y100价格")
    public RenderResult<SysY100PriceVo> getY100Price(String org, String dst) {
        SysY100PriceVo priceInfo = sysY100PriceService.getPriceInfo(org, dst);
        return RenderResult.success(priceInfo);
    }

    @PostMapping("/updateY100Price")
    @ApiOperation(value = "更改Y100价格")
    public RenderResult updateY100Price(@RequestBody SysY100PriceDto dto) {
        sysY100PriceService.updateY100Price(dto);
        return RenderResult.success();
    }


    @Autowired
    RpcUtil rpcUtil;

    @PostMapping("/refreshY100Price")
    public RenderResult refreshY100Price(@RequestBody List<String> lineIds) {
        RenderResult lineIds1 = rpcUtil.execute(new RpcRequest(ServiceNameConstants.CMD_SERVICE, HttpMethod.POST, "/fd/refreshY100Price").params("lineIds", String.join(",", lineIds)), RenderResult.class);
        return RenderResult.success();
    }


    @GetMapping("/getFlightInfoManagementPage")
    @ApiOperation(value = "飞机信息管理信息分页查询")
    public RenderResult<PsiPage<FlightInfoManagementVo>> getPage(FlightInfoManagementDto dto){
        return RenderResult.success(flightInfoManagementService.getPage(dto));
    }

    @PostMapping("/updateFlightInfoManagement")
    @ApiOperation(value = "飞机信息管理信息修改")
    public RenderResult updateFlightInfoManagement(@RequestBody FlightInfoManagementUpdateDto dto){
        flightInfoManagementService.updateCxygenCabin(dto);
        return RenderResult.success();
    }

    @PostMapping("/updatePaintingType")
    @ApiOperation(value = "涂装类型信息修改")
    public RenderResult updatePaintingType(@RequestBody FlightInfoUpdateDto dto){
        flightInfoManagementService.updateFlightInfo(dto);
        return RenderResult.success();
    }

    @PostMapping("/updateFlightWifi")
    @ApiOperation(value = "飞机信息管理wifi信息修改")
    public RenderResult updateFlightInfoManagement(@RequestBody FlightWifiUpdateDto dto){
        List<ThirdAircraftInfoVo> thirdAircraftInfoVos = flightInfoManagementService.updateWifi(dto);
        if (ObjectUtils.isNotEmpty(thirdAircraftInfoVos)) {
            thirdAircraftInfoVos.stream().forEach(e -> {
                flightInfoManagementMqSendUtil.send(JSON.toJSONString(e));
            });
        }
        return RenderResult.success();
    }

    @Value("${validateSign.flightInfoEx}")
    private String appKey;

    @PostMapping("/flightInfoEx")
    @ApiOperation(value = "三方飞机信息接口")
    public RenderResult<List<FlightInfoManagementVo>> flightInfo(@RequestBody FlightInfoManagementSupplyDto dto)throws Exception {
        log.info("【飞机信息查询】参数：{}", dto.toString());
        try {
            ValidateSignatureUtil.validateSignature(dto, appKey, false);
        } catch (ValidationFailedException e) {
            log.error("【飞机信息查询】接口请求失效：timeStamp={}", dto.getTimeStamp());
            return RenderResult.fail("接口非法访问");
        } catch (TimestampInvalidationException e) {
            log.error("【飞机信息查询】接口请求失效：timeStamp={}", dto.getTimeStamp());
            return RenderResult.fail("接口请求失效");
        }
        FlightInfoManagementDto managementDto = new FlightInfoManagementDto();
        BeanUtils.copyProperties(dto,managementDto);
        PsiPage<FlightInfoManagementVo> page = flightInfoManagementService.getPage(managementDto);
        return RenderResult.success(page.getRecords());
    }

}
