package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.dto.AoMergeViproomAccountsReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeViproomItemSell;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeViproomItemSellMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeViproomItemSellService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeViproomItemSellAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 合并支付贵宾厅套餐销售产品详情订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeViproomItemSellServiceImpl extends ServiceImpl<AoMergeViproomItemSellMapper, AoMergeViproomItemSell> implements IAoMergeViproomItemSellService {
    @Override
    public List<AoMergeViproomItemSellAccountsReportVo> getAccountsList(AoMergeViproomAccountsReportDto dto) {
        return baseMapper.getAccountsList(dto);
    }

    @Override
    public PsiPage<AoMergeViproomItemSellAccountsReportVo> getAccountsListPage(AoMergeViproomAccountsReportDto dto) {
        PsiPage<AoMergeViproomItemSellAccountsReportVo> page = new PsiPage<>(dto);
        return baseMapper.getAccountsListPage(page,dto);
    }

    @Override
    public List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeViproomAccountsReportDto dto) {
        return baseMapper.payTypesSummaryMoney(dto);
    }
}
