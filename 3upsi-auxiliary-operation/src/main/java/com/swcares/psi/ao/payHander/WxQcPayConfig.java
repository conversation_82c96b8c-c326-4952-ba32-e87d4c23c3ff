package com.swcares.psi.ao.payHander;


import com.swcares.psi.pay.bean.wxpay.WxpayClientConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.InputStream;


/**
 * <AUTHOR>
 * 微信二维码扫码支付配置
 * @date 2021/4/23 11:00
 */
@Data
@Configuration
@ConfigurationProperties(prefix ="aopayconfig.wxqc")
public class WxQcPayConfig extends WxpayClientConfig {

    /**
     * appID
     */
     String appID;


    /**
     * 商户号
     */
    private String mchID;

    /**
     * API 密钥
     */
    private String key;

    /**
     * API证路径
     */
    private String certPath;
    /**
     * 支付方式
     */
    private String tradeType;



    /**
     * 微信支付异步通知地址
     */
    private String notifyUrl;


    /**
     * 微信退款异步通知地址
     */
    private String refundNotifyUrl;
    /**
     * 缴纳支付异步通知地址
     */
    private String turnNotifyUrl;

    /**
     * 获取商户证书内容（这里证书需要到微信商户平台进行下载）
     *
     * @return 商户证书内容
     */
    @Override
    public InputStream getCertStream() {
        InputStream certStream  =getClass().getClassLoader().getResourceAsStream(certPath);
        return certStream;
    }

}
