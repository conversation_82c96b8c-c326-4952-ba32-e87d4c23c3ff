<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeCardTypeDiscountListMapper">
    <sql id="selectSql">
        select
        id,
        CARD_TYPE,
        CONCAT ((DISCOUNT * 10),'折') as discountStr,
        DISCOUNT as DISCOUNT,
        date_format(UPDATE_DATE,'%Y-%m-%d %H:%i:%s') as updateDate,
        ifnull(UPDATE_USER,'系统') as updateUser
        from AO_MERGE_CARD_TYPE_DISCOUNT_LIST
        <where>
            STATUS='1'

        </where>
        order by UPDATE_DATE desc
    </sql>


    <select id="getPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo">
        <include refid="selectSql"></include>
    </select>
    <select id="getList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo">
        <include refid="selectSql"></include>
    </select>
</mapper>
