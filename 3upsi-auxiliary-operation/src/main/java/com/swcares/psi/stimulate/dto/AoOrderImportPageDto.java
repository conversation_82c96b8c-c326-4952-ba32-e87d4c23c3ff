package com.swcares.psi.stimulate.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AoOrderImportPageDto extends BaseDto {

    @ApiModelProperty(value = "操作人")
    private String orderCreateUser;

    @ApiModelProperty(value = "操作人工号")
    private String orderCreateNo;


    @ApiModelProperty(value = "部门")
    private String orderCreateUserDepartment;

    @ApiModelProperty(value = "科室")
    private String orderCreateAdministrative;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "订单日期 开始")
    private String orderDateStart;
    @ApiModelProperty(value = "订单日期 结束")
    private String orderDateEnd;

    @ApiModelProperty(value = "航班日期 开始")
    private String flightDateStart;
    @ApiModelProperty(value = "航班日期 结束")
    private String flightDateEnd;

    @ApiModelProperty(value = "业务类型 逗号分隔   1一人多座,2现场座位销售(选座),3逾重行李,4贵宾室,5柜台升舱")
    private String orderType;

    @ApiModelProperty(value = "航线性质 逗号分隔 D 国内  I 国际 R地区")
    private String flightType;
}
