package com.swcares.psi.pay.service.wx;

import com.alibaba.fastjson.JSONObject;
import com.swcares.psi.common.security.exception.PayErrorException;
import com.swcares.psi.common.utils.HttpUtil;
import com.swcares.psi.pay.api.BasePayService;
import com.swcares.psi.pay.api.TransactionType;
import com.swcares.psi.pay.bean.Authentication;
import com.swcares.psi.pay.bean.wxpay.WxAuthentication;
import com.swcares.psi.pay.bean.wxpay.WxPayConfigStorage;
import com.swcares.psi.pay.bean.wxpay.WxTransferOrder;
import com.swcares.psi.pay.util.Utils;
import com.swcares.psi.pay.util.wx.WXPayConstants;
import com.swcares.psi.pay.util.wx.WXPayUtil;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import javax.net.ssl.SSLContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

/**
 * ClassName：com.swcares.psi.service.wxpay.WxPayService <br>
 * Description：微信-支付处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Getter
public class WxPayService extends BasePayService<WxTransferOrder> {

	private WxPayConfigStorage wxPayConfigStorage;

	private CloseableHttpClient httpClient;

    private CloseableHttpResponse response;

	public static final String FAILURE = "failure";

	private String appId;

	/** 请求超时*/
	private final static int REQ_TIME_OUT = 10000;
	/** 连接超时*/
	private final static int CON_TIME_OUT = 10000;

	/** 设置请求和传输超时时间*/
	private static final RequestConfig requestConfig =
			RequestConfig.custom().setSocketTimeout(REQ_TIME_OUT).setConnectTimeout(CON_TIME_OUT).build();


	public WxPayService(WxPayConfigStorage wxPayConfigStorageIrr) {
		Utils.validCompensatePayResource(wxPayConfigStorageIrr.getCertPath(), "服务补偿-微信商户-证书");
		this.wxPayConfigStorage = wxPayConfigStorageIrr;
		isOpenConnection();
	}



	/**
	 * Title：isOpenConnection <br>
	 * Description： 打开带证书的httpClient<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:37 <br>
	 * @param
	 * @return
	 */
	private void isOpenConnection(){
		if(StringUtils.isNotEmpty(wxPayConfigStorage.getCertPath())){
			try {
				openConnection();
			} catch (Exception e) {
				log.error("微信—创建http链接失败!{}",e);
				//throw new PayErrorException(FAILURE,"微信—创建http链接失败!"+e);
			}
		}
	}

	/**
	 * 创建链接{证书}
	 * @throws Exception
	 */
	private void openConnection() throws Exception {
        KeyStore ks = KeyStore.getInstance("PKCS12");
		String path = Utils.getCompensatePayCertAbsolutePath(wxPayConfigStorage.getCertPath());
		log.info("微信支付加载证书文件路径：" + path);
		InputStream  strem  = new FileInputStream(path);
	  	ks.load(strem, wxPayConfigStorage.getMchId().toCharArray());
        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(ks, wxPayConfigStorage.getMchId().toCharArray()).build();
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
                .register("http", PlainConnectionSocketFactory.INSTANCE).register("https", new SSLConnectionSocketFactory(sslcontext)).build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        httpClient = HttpClients.custom().setConnectionManager(connManager).setConnectionManagerShared(true).build();
    }

	/**
	 * Title：httpostBeltCert <br>
	 * Description： 发起带证书的请求<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:36 <br>
	 * @param  url 请求地址, data 参数xml
	 * @return java.lang.String
	 */
	private String httpostBeltCert(String url,String data) throws IOException{
		try {
			HttpPost httpost = new HttpPost(url);
			httpost.setConfig(requestConfig);
			httpost.setEntity(new StringEntity(data, "UTF-8"));
			response = httpClient.execute(httpost);
			try {
				HttpEntity entity = response.getEntity();
				String jsonStr = EntityUtils.toString(response.getEntity(), "UTF-8");
				EntityUtils.consume(entity);
				return jsonStr;
			} finally {
				httpost.releaseConnection();
			}
		} finally {
			if (response != null) {
				response.close();
			}
		}
	}

	/**
	 * Title：getPublicParameters <br>
	 * Description： 获取公共参数 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:31 <br>
	 * @param
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 */
	private Map<String, Object> getPublicParameters() {
        Map<String, Object> parameters = new TreeMap<>();
		this.appId = wxPayConfigStorage.getAppid();
        parameters.put(WXPayConstants.APPID, wxPayConfigStorage.getAppid());
        parameters.put(WXPayConstants.MCH_ID, wxPayConfigStorage.getMchId());
		parameters.put("mchid", wxPayConfigStorage.getMchId());
        parameters.put(WXPayConstants.NONCE_STR, WXPayUtil.generateNonceStr());
        return parameters;
    }


	/**
	 * Title：authCodeUrl <br>
	 * Description： 获取微信授权url<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:31 <br>
	 * @param
	 * @return java.lang.String
	 */
	@Override
	public String authCodeUrl() {
    	return new StringBuffer(WxTransactionType.AUTH_CODE.getMethod())
			.append("appid=").append(wxPayConfigStorage.getAppid())
			.append("&redirect_uri=").append(wxPayConfigStorage.getAuthRedirectuUri())
			.append("&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect").toString();
	}

	/**
	 * Title：getOpenid <br>
	 * Description： 获取openid<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:32 <br>
	 * @param  code 授权后code
	 * @return java.lang.String
	 */
	@Override
	public String getOpenid(String code) {
		String url = new StringBuffer(WxTransactionType.AUTH_OPENID.getMethod())
				.append("appid=").append(wxPayConfigStorage.getAppid())
				.append("&secret=").append(wxPayConfigStorage.getAppSecret())
				.append("&code=").append(code).append("&grant_type=authorization_code").toString();
		String result = HttpUtil.httpGet(url, null);
		log.info("微信获取openid---请求链接【"+url+"】,反馈结果："+result);
		JSONObject ob = JSONObject.parseObject(result);
		String openid = ob.getString("openid");
		if(StringUtils.isBlank(openid)){
			log.error("微信获取openid异常，请求参数{}，返回结果{}",url,result);
			throw new PayErrorException(FAILURE,"微信获取openid异常，请求参数{"+url+"}，返回结果{"+result+"}");
		}
		return openid;
	}


	@Override
	public String getRealNameAuthUrl(String scope){
		return new StringBuffer(WxTransactionType.PAY_IDENTITY_INDEX.getMethod())
				.append("mch_id=").append(wxPayConfigStorage.getMchId())
				.append("&appid=").append(wxPayConfigStorage.getAppid())
				.append("&redirect_uri=").append(wxPayConfigStorage.getAuthRedirectuUri())
				.append("&response_type=code&scope=").append(scope).append("&state=STATE#wechat_redirect").toString();
	}


	/**
	 * Title：getPayIdentityToken <br>
	 * Description： 实名认证-获取token<br>
	 * author：傅欣荣 <br>
	 * date：2020/6/22 18:55 <br>
	 * @param
	 * @return
	 */
	@Override
	public String getPayIdentityToken(String code,String openid){
		String sign = "";
		Map<String, Object> parameters = new TreeMap<>();
		parameters.put("mch_id",wxPayConfigStorage.getMchId());
		parameters.put("appid",wxPayConfigStorage.getAppid());
		parameters.put("openid",openid);
		parameters.put("code",code);
		parameters.put("scope","SCOPE");
		parameters.put("grant_type","authorization_code");
		parameters.put("sign_type","HMAC-SHA256");
		try {
			sign = WXPayUtil.generateSignature(parameters,wxPayConfigStorage.getApiKey(),WXPayConstants.HMACSHA256.toString());
		} catch (Exception e) {
			log.error("微信请求参数sign签名异常！ {}",e);
			throw new PayErrorException(FAILURE,"微信请求参数sign签名异常"+e);
		}
		parameters.put("sign",sign);
		String url = new StringBuffer(WxTransactionType.PAY_IDENTITY_TOKEN.getMethod())
				.append(WXPayUtil.getMapToString(parameters)).toString();
		String result = HttpUtil.httpGet(url, null);
		log.info("微信获取实名认证access_token---请求链接【"+url+"】,反馈结果："+result);
		JSONObject ob = JSONObject.parseObject(result);
		String access_token = ob.getString("access_token");
		if(StringUtils.isBlank(access_token) || !ob.getString("retcode").equals("0")){
			log.error("微信获取实名认证access_token异常，请求参数{}，返回结果{}",url,result);
			throw new PayErrorException(FAILURE,"微信获取实名认证access_token异常，请求参数{"+url+"}，返回结果{"+result+"}");
		}
		return access_token;

	}

	@Override
	public <A extends Authentication> Map<String, Object> authentication(A order) {
		WxAuthentication wxOder = (WxAuthentication)order;
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("version","1.0");
		parameters.put("real_name",order.getName());
		parameters.put("cred_id",order.getIdNo());
		parameters.put("cred_type","1");
		parameters.put("openid",wxOder.getOpenid());
		parameters.put("access_token",wxOder.getToken());
		parameters.put("sign_type",WXPayConstants.HMACSHA256);
		parameters.put("sign","");
		log.info("微信实名认证请求参数：{}", JSONObject.toJSONString(parameters));
		return initRequestByType(parameters, (WxTransactionType) wxOder.getTransactionType());
	}



	@Override
	public Map<String, Object> transfer(WxTransferOrder order) {
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("partner_trade_no", order.getOutNo());
		parameters.put("amount", Utils.conversionCentAmount(order.getAmount()));
		parameters.put("desc", order.getRemark());
		parameters.put("spbill_create_ip", wxPayConfigStorage.getSpbill_create_ip());
		if (null == order.getTransactionType()) {
			log.error("微信转账类型必填:transactionType！ ");
			throw new PayErrorException(FAILURE,"微信转账类型必填:transactionType");
		}
		((WxTransactionType) order.getTransactionType()).setAttribute(parameters, order);
		log.info("微信支付创建订单请求参数：{}", parameters);
		return initRequestByType(parameters, (WxTransactionType) order.getTransactionType());
	}

	@Override
	public Map<String, Object> query(String tradeNo, String outTradeNo, TransactionType transactionType) {
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("partner_trade_no",outTradeNo);
		/*parameters.remove(WXPayConstants.APPID);*/
		parameters.remove("mchid");
		log.info("微信支付查询支付结果请求参数：{}", parameters);
		return initRequestByType(parameters, (WxTransactionType) transactionType);
	}



	/**
	 * Title：initRequestByType <br>
	 * Description： 发起http请求 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:33 <br>
	 * @param  parameters 请求参数,
	 * @param transactionType 请求url，是否需要证书
	 * @return java.util.Map<java.lang.String,java.lang.Object> http响应参数
	 */
	private Map<String, Object> initRequestByType(Map<String, Object> parameters,WxTransactionType transactionType){
    	//将请求参数转换为xml格式
		String data;
		String returnData;
		Map<String, Object> returnMap = new HashMap<>();
		try {
			data = WXPayUtil.generateSignedXml(parameters, wxPayConfigStorage.getApiKey(),wxPayConfigStorage.getSignType());
		} catch (Exception e) {
			log.error("微信请求参数转为xml格式：转换异常！ ",e);
			throw new PayErrorException(FAILURE,"微信请求参数转为xml格式：转换异常！"+e);
		}
		//判断是否需要证书
		try {
			if(transactionType.isNeedCert()){
				returnData = httpostBeltCert(transactionType.getMethod(),data);
			}else{
				HttpUtil.HttpResult httpResult = HttpUtil.postBody(transactionType.getMethod(), null, data);
				if (httpResult.code != 200) {
                    throw new Exception("微信-发起HTTP请求响应失败！result:" + httpResult);
				}
				returnData = httpResult.content;
			}
			log.info("【服务补偿】-微信端原始返回结果：【{}】", returnData);
		} catch (Exception e) {
			log.error("微信-发起HTTP请求：异常 ！",e);
			throw new PayErrorException(FAILURE,"微信-发起HTTP请求：异常 ！"+e);
		}
		try {
			returnMap = WXPayUtil.xmlToMap(returnData);
			returnMap.put("mchid",this.appId);
			return returnMap;
		} catch (Exception e) {
			log.error("微信—反馈结果转换类型Map：转换异常！ ",e);
			throw new PayErrorException(FAILURE,"微信—反馈结果转换类型Map：转换异常！"+e);
		}
	}
/**
	public static void main(String[] args) throws Exception {

		//1.打开微信连接
		String CERTPATH = "wxpay/irrflight/cert/apiclient_cert.p12";
		String MCHID="1510023161";
		String MCHAPPID = "wx60cdc9e84eba975f";
		String  KEY ="H8CJhSK7dstK720J9Hhj8jh6nshjhSDF";
		String  check_name= "FORCE_CHECK";
		String  localIp ="***********";

		CloseableHttpClient httpClient ;
		CloseableHttpResponse response;

		KeyStore ks = KeyStore.getInstance("PKCS12");
		//System.out.println(new ClassPathResource(CERTPATH).getInputStream());
		ks.load(new ClassPathResource(CERTPATH).getInputStream(), MCHID.toCharArray());
		SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(ks, MCHID.toCharArray()).build();
		Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
				.register("http", PlainConnectionSocketFactory.INSTANCE).register("https", new SSLConnectionSocketFactory(sslcontext)).build();
		PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
		httpClient = HttpClients.custom().setConnectionManager(connManager).build();


		// 2.组装数据
		WxTransferOrder wxpt = new WxTransferOrder();
		wxpt.setOutNo("**********");
		wxpt.setPayeeAccount("ofSj-jklL8rRpdWter_soktpMgk0");//openId
		wxpt.setPayeeName("郑雯雯");
		wxpt.setRemark("航延补偿金");
		wxpt.setAmount(new BigDecimal(1.0));
		wxpt.setBatchNo("**********");

		Map<String, Object> parameters = new TreeMap<>();
		parameters.put("mch_appid", MCHAPPID);
		parameters.put("mchid", MCHID);
		parameters.put("partner_trade_no", wxpt.getOutNo());
		parameters.put("amount", Utils.conversionCentAmount(wxpt.getAmount()));
		parameters.put("desc", wxpt.getRemark());
		parameters.put("spbill_create_ip",localIp );
		parameters.put("openid",wxpt.getPayeeAccount() );
		parameters.put("check_name",check_name);
		parameters.put("re_user_name",wxpt.getPayeeName());



		String dataValue="";
		String  nonceStr= UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);

		String  signStr = new StringBuilder("amount=").append(wxpt.getAmount())
				.append("&check_name=").append(check_name)
				.append("&desc=").append(wxpt.getRemark())
				.append("&mch_appid=").append(MCHAPPID)
				.append("&mchid=").append(MCHID)
				.append("&nonce_str=").append(nonceStr)
				.append("&openid=").append(wxpt.getPayeeAccount())
				.append("&partner_trade_no=").append(wxpt.getOutNo())
				.append("&re_user_name=").append(wxpt.getPayeeName())
				.append("&spbill_create_ip=").append(localIp).toString();

		//3.封装MD5签名数据
		String data = signStr  + "&key=" + KEY;

		java.security.MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] array = md.digest(data.getBytes("UTF-8"));
		StringBuilder sb = new StringBuilder();
		for (byte item : array) {
			sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
		}
		//MD5转换后的字符串
		String  md5SignStr =  sb.toString().toUpperCase();
		md5SignStr = md5SignStr.toUpperCase();
		log.info("【微信签名封装参数】： "+md5SignStr);
		//获取随机字符串
		parameters.put("nonce_str" , WXPayUtil.generateNonceStr());
		parameters.put("sign",md5SignStr);

		//4.转换对象为微信支付的对象
		String data2 = WXPayUtil.generateSignedXml(parameters, KEY,"MD5");

		//5.远程调用
		try {
			HttpPost httpost = new HttpPost("https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers");
			httpost.setConfig(requestConfig);
			httpost.setEntity(new StringEntity(data2, "UTF-8"));
			response = httpClient.execute(httpost);
			try {
				HttpEntity entity = response.getEntity();
				String jsonStr = EntityUtils.toString(response.getEntity(), "UTF-8");
				EntityUtils.consume(entity);
				log.info(jsonStr) ;
			} finally {
				httpost.releaseConnection();
			}
		} finally {
			httpClient.close();
		}


		log.info("微信支付反馈结果： " + response);

	}
**/
}
