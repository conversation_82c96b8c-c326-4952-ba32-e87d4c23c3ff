package com.swcares.psi.aoMergeOrder.service;

import com.swcares.psi.aoMergeOrder.dto.AoMergeH5OrderListDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderManageDto;
import com.swcares.psi.aoMergeOrder.dto.AoOrderInfoListByCpaxDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.vo.*;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 辅营合并总订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeOrderInfoService extends IService<AoMergeOrderInfo> {


    List<VaidatePayOrderVo> vaidatePayOrderList();
    List<CashTurnQueryVo> cashTurnQuery(String servicePort ,List<String> mergeOrderIdList);
    PsiPage<AoMergeH5OrderListVo> h5OrderList(AoMergeH5OrderListDto dto);
    AoMergeOrderDetailsVo orderDetails(String mergeOrderId);
    PsiPage<AoMergeOrderManageVo> getMergeOrderInfo(AoMergeOrderManageDto dto);

    List<AoMergeOrderManageVo> getMergeOrderInfoList(AoMergeOrderManageDto dto);
    List<AoMergeOrderManageVo.AoMergeSubOrderManageVo> getSubOrderList(AoMergeOrderManageDto dto);

    PsiPage<AoOrderInfoListByCpaxVo> getMergeOrderInfoByCpax(AoOrderInfoListByCpaxDto dto);
    AoOrderDetailsByCpaxVo orderDetailsByCpax(String orderId);

    List<AoMergeOrderPaxVo> orderPaxList(String mergeOrderNo);
}
