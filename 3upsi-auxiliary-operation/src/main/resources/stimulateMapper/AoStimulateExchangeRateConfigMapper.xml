<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.stimulate.mapper.AoStimulateExchangeRateConfigMapper">

    <select id="dateLatestConfig" resultType="java.time.LocalDate">
        select
        max(`month`)
        from AO_STIMULATE_EXCHANGE_RATE_CONFIG
        where STATUS='1' and HISTORY='0'
    </select>


    <select id="pageList" resultType="com.swcares.psi.stimulate.vo.AoStimulateExchangeRatePageVo">
       select
        CURRENCY_SPECIES,
        CURRENCY_SPECIES_CODE,
        date_format(max(CREATE_TIME), '%Y-%m-%d %H:%i:%S')  as updateTime
        from AO_STIMULATE_EXCHANGE_RATE_CONFIG
        where STATUS='1' and HISTORY='0'
        <if test="currencySpeciesCode != null and currencySpeciesCode != ''">
            AND find_in_set(CURRENCY_SPECIES_CODE, #{currencySpeciesCode})
        </if>
        group by CURRENCY_SPECIES_CODE
    </select>


    <select id="getMonthConfig" resultType="com.swcares.psi.stimulate.vo.AoStimulateExchangeRatePageVo$MonthConfig">
        select
            a.VALIDITY,
            a.EXCHANGE_RATE as exchangeRate,
            date_format(a.MONTH, '%Y-%m-%d')  as monthDate
        from AO_STIMULATE_EXCHANGE_RATE_CONFIG a
        where a.STATUS='1' and a.HISTORY='0'  and a.MONTH between  date_format(#{startDate}, '%Y-%m-%d') and   date_format(#{endDate}, '%Y-%m-%d')
        <if test="currencySpeciesCode != null and currencySpeciesCode != ''">
            AND find_in_set(a.CURRENCY_SPECIES_CODE, #{currencySpeciesCode})
        </if>
        GROUP BY  a.MONTH  having a.VALIDITY=max(a.VALIDITY)
    </select>


</mapper>
