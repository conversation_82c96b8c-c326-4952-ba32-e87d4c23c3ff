package com.swcares.psi.ao.meals;

import com.swcares.psi.ao.model.dto.AirTrafficDataDto;
import com.swcares.psi.common.utils.validatesign.ValidateBaseDto;
import com.swcares.psi.ao.model.dto.MealsEsbDto;
import com.swcares.psi.ao.model.vo.AirTrafficDataVo;
import com.swcares.psi.ao.model.vo.PassengerSalesDataVo;
import com.swcares.psi.ao.service.MealsEsbService;
import com.swcares.psi.common.utils.validatesign.TimestampInvalidationException;
import com.swcares.psi.common.utils.validatesign.ValidateSignatureUtil;
import com.swcares.psi.common.utils.validatesign.ValidationFailedException;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：com.swcares.psi.ao.meals.MealsEsbController <br>;
 * Description：提供给餐食系统查询查询信息使用控制器 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/4/8 11:30 <br>;
 * @version v1.0 <br>;
 */
@Api(tags = "辅营旅客餐食控制器")
@Slf4j
@Valid
@RestController
@RequestMapping("/api/ao/public/esbservice")
public class MealsEsbController {

    @Resource
    private MealsEsbService mealsEsbService;

    @Value("${validateSign.appKey}")
    private String appKey;

    /**
     * Title：passengerSalesData <br>;
     * Description：餐食系统查询旅客销售数据 <br>;
     * @param:  <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult<com.swcares.psi.common.utils.query.PsiPage<com.swcares.psi.ao.model.vo.MealPaxInfoVO>> <br>;
     * <AUTHOR> <br>;
     * date 2022/4/8 11:38 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "餐食系统查询旅客销售数据")
    @GetMapping("/Passenger_Sales_Data")
    public RenderResult<PassengerSalesDataVo> passengerSalesData(@Valid final MealsEsbDto mealsEsbDto) throws IllegalAccessException {

        try {
            ValidateSignatureUtil.validateSignature(mealsEsbDto, appKey, false);
        } catch (ValidationFailedException e) {
            log.error("接口请求失效：timeStamp-》{}", mealsEsbDto.getTimeStamp());
            return RenderResult.fail("接口非法访问");
        } catch (TimestampInvalidationException e) {
            log.error("接口请求失效：timeStamp-》{}", mealsEsbDto.getTimeStamp());
            return RenderResult.fail("接口请求失效");
        }

        PassengerSalesDataVo passengerSalesDataVos = mealsEsbService.passengerSalesData(mealsEsbDto);
        return RenderResult.success(passengerSalesDataVos);
    }

    /**
     * Title：airTrafficData <br>;
     * Description：餐食系统目前从旅服dg库查询航变航班数据 <br>;
     * @param:  <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult<com.swcares.psi.common.utils.query.PsiPage<com.swcares.psi.ao.model.vo.MealPaxInfoVO>> <br>;
     * <AUTHOR> <br>;
     * date 2022/4/8 11:41 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "餐食系统目前从旅服dg库查询航变航班数据")
    @GetMapping("/Air_Traffic_Data")
    public RenderResult<List<AirTrafficDataVo>> airTrafficData(@Valid final AirTrafficDataDto airTrafficDataDto) throws IllegalAccessException {

        try {
            ValidateSignatureUtil.validateSignature(airTrafficDataDto, appKey, false);
        } catch (ValidationFailedException e) {
            log.error("接口请求失效：timeStamp-》{}", airTrafficDataDto.getTimeStamp());
            return RenderResult.fail("接口非法访问");
        } catch (TimestampInvalidationException e) {
            log.error("接口请求失效：timeStamp-》{}", airTrafficDataDto.getTimeStamp());
            return RenderResult.fail("接口请求失效");
        }

        return RenderResult.success(mealsEsbService.airTrafficData(airTrafficDataDto));
    }

    /**
     * Title：specificPassengerData <br>;
     * Description：餐食系统目前从旅服dg库查询特定旅客数据 <br>;
     * @param:  <br>;
     * @return: com.swcares.psi.common.utils.query.RenderResult<com.swcares.psi.common.utils.query.PsiPage<com.swcares.psi.ao.model.vo.MealPaxInfoVO>> <br>;
     * <AUTHOR> <br>;
     * date 2022/4/8 11:43 <br>;
     * @throws  <br>;
     */
    @ApiOperation(value = "餐食系统目前从旅服dg库查询特定旅客数据")
    @GetMapping("/Specific_Passenger_Data")
    public RenderResult<List<String>> specificPassengerData(@Valid ValidateBaseDto validateBaseDto) throws IllegalAccessException {

        try {
            ValidateSignatureUtil.validateSignature(validateBaseDto, appKey, false);
        } catch (ValidationFailedException e) {
            log.error("接口请求失效：timeStamp-》{}", validateBaseDto.getTimeStamp());
            return RenderResult.fail("接口非法访问");
        } catch (TimestampInvalidationException e) {
            log.error("接口请求失效：timeStamp-》{}", validateBaseDto.getTimeStamp());
            return RenderResult.fail("接口请求失效");
        }

        return RenderResult.success(mealsEsbService.specificPassengerData());
    }
}
