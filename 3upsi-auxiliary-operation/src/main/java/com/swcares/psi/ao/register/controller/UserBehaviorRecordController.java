package com.swcares.psi.ao.register.controller;

import com.swcares.psi.ao.register.dto.PadVipRegisterDto;
import com.swcares.psi.ao.register.dto.UserBehaviorRecordDto;
import com.swcares.psi.ao.register.service.UserBehaviorRecordService;
import com.swcares.psi.ao.register.vo.PadVipRegisterVo;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.core.util.WebUtils;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName：UserBehaviorRecordController
 * @Description：邀请入会-用户行为监控记录
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/3/27 15:32
 * @version： v1.0
 */
@Api(tags = "邀请入会-用户行为监控记录")
@RestController
@RequestMapping("/api/ao/userBehavior")
public class UserBehaviorRecordController {

    @Autowired
    UserBehaviorRecordService userBehaviorRecordService;

    @ApiOperation(value = "记录用户行为")
    @PostMapping("saveRecord")
    public RenderResult saveRecord(@RequestBody @Valid UserBehaviorRecordDto dto, HttpServletRequest request) {
        String ipAddress = WebUtils.getRemoteIp();
        PsiUser user = (PsiUser) AuthenticationUtil.getAuthentication();
        if(user != null){
            dto.setUserInfo(user.getRealName() + "-" + user.getUsername());
        }else {
            dto.setUserInfo(ipAddress);
        }
        userBehaviorRecordService.saveRecord(dto);
        return RenderResult.success();
    }


}
