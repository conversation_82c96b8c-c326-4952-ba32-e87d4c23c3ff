<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.seat.mapper.SvrGetSeatMapper">
    <update id="updateSvrGetSeat">
        update AO_SVR_GET_SEAT set status=#{params.newStatus}
        <if test="params.recordNo!=null">
            ,RECORD_NO=#{params.recordNo}
        </if>
        <if test="params.remarks!=null">
            ,REMARKS=#{params.remarks}
        </if>
        <if test="params.reson!=null">
            ,RESON=#{params.reson}
        </if>
        <if test="params.updateId!=null">
            ,UPDATE_ID=#{params.updateId}
        </if>
        <if test="params.updateTime!=null">
            ,UPDATE_Time=#{params.updateTime}
        </if>
        <where>
            <if test="params.oldStatus!=null">
                and binary STATUS = #{params.oldStatus}
            </if>
            <if test="params.oldStatusList!=null">
                and binary STATUS in (
                <foreach collection="params.oldStatusList" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>

            <if test="params.applyId!=null">
                and APPLY_ID = #{params.applyId}
            </if>
            <if test="params.applyIdList!=null">
                and APPLY_ID in (
                <foreach collection="params.applyIdList" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>

            <if test="params.sgsId!=null">
                and SGS_ID=#{params.sgsId}
            </if>
        </where>

    </update>
    <select id="getSvrGetSeat4Page" resultType="com.swcares.psi.ao.seat.vo.SvrGetSeatPageVo">

        select x.* from (
        SELECT A.SGS_ID sgsId,
        A.APPLY_ID applyId,
        A.APPLY_NO applyNo,
        A.ORG_CITY_AIRP orgCityCode,
        A.DST_CITY_AIRP dstCityCode,
        A.DEPART_CODE dptCode,
        A.RECORD_NO recordNo,
        A.FLIGHT_NO flightNo,
        A.FLIGHT_DATE flightDate,
        case
        when A.APPLY_TYPE = '0' then '经济舱'
        when A.APPLY_TYPE = '1' then '公务舱'
        end applyType,
        ifnull(p1.airport_name, ORG_CITY_AIRP) orgCityAirp,
        ifnull(p2.airport_name, DST_CITY_AIRP) dstCityAirp,
        ifnull(ifnull(D.SUF_DEP_ABBR, D.SUF_DEP_NAME), A.DEPART_CODE) departCode,
        A.PLAN_NUM planNum,
        A.ACTUAL_NUM actualNum,
        A.EMP_LIST empList,
        A.STATUS status,
        A.APPLY_EMP applyEmp,
        date_format(A.APPLY_TIME,'%Y-%m-%d %H:%i:%s') applyTime,
        date_format(A.CREATE_TIME,'%Y-%m-%d %H:%i:%s') createTime,
        A.RESON reson,
        A.profit_remark profitRemark,
        A.remarks remarks,
        A.local_flight_date localFlightDate,
        A.is_inter_line isInterLine,
        case
        when A.STATUS = 'F' then 4
        when A.STATUS = 'N' then 4
        when A.STATUS = 'E' then 4
        when A.STATUS = 'D' then 4
        when A.STATUS = 'o' then 3
        when A.STATUS = 'h' then 3
        when A.STATUS = 'z' then 3
        when A.STATUS = 'v' then 3
        when A.STATUS = 'R' then 2
        when A.STATUS = 'I' then 2
        when A.STATUS = 'W' then 2
        when A.STATUS = 'C' then 1
        when A.STATUS = 'H' then 1
        when A.STATUS = 'L' then 1
        when A.STATUS = 'X' then 1
        end statusNumber,
        A.PROFIT_REMARK,
        A.RECORD_NO,
        A.APPLY_NO,
        (SELECT COUNT(1)
        FROM AO_SVR_GET_SEAT B
        WHERE A.APPLY_ID &lt;&gt; B.APPLY_ID AND A.FLIGHT_NO=B.FLIGHT_NO
        AND A.FLIGHT_DATE=B.FLIGHT_DATE AND A.ORG_CITY_AIRP= B.ORG_CITY_AIRP
        AND A.DST_CITY_AIRP= B.DST_CITY_AIRP AND A.DEPART_CODE =B.DEPART_CODE
        AND A .APPLY_TYPE = B.APPLY_TYPE AND A.STATUS IN ('C','H','L')
        AND B.STATUS NOT IN ('R','N','I')) errRecord
        FROM AO_SVR_GET_SEAT A
        left join sys_airport_info p1 on p1.code=a.ORG_CITY_AIRP and p1.is_use='0'
        left join sys_airport_info p2 on p2.code=a.DST_CITY_AIRP and p2.is_use='0'
        left join AO_COMMON_DEPART_ABBR D on D.SUF_DEP_CODE=A.DEPART_CODE
        where 1=1<include refid="queryCondition"></include>
        ) x
        ORDER BY x.statusNumber desc ,x.PROFIT_REMARK!='', x.RECORD_NO!='',x.APPLY_NO ASC
    </select>

    <sql id="queryCondition">
        <if test="dto.departCode !=null and dto.departCode !=''">
            and ( DEPART_CODE LIKE concat("%",#{dto.departCode},"%") OR DEPART_CODE IN (
            (SELECT B.SUF_DEP_CODE FROM AO_COMMON_DEPART_ABBR B
            WHERE B.SUF_DEP_NAME LIKE concat("%",#{dto.departCode},"%")
            OR B.SUF_DEP_ABBR LIKE concat("%",#{dto.departCode},"%") )))
        </if>
        <if test="dto.applyNo !=null and dto.applyNo !=''">
            and APPLY_NO like concat('%',#{dto.applyNo},'%')
        </if>
        <if test="dto.recordNo !=null and dto.recordNo !=''">
            and RECORD_NO like concat('%',#{dto.recordNo},'%')
        </if>
        <if test="dto.depCd !=null and dto.depCd !='' and dto.depCd !='--请选择--'">
            and ORG_CITY_AIRP =#{dto.depCd}
        </if>
        <if test="dto.arrCd !=null and dto.arrCd !='' and dto.arrCd !='--请选择--'">
            and DST_CITY_AIRP =#{dto.arrCd}
        </if>
        <if test="dto.beginDate !=null and dto.beginDate !=''">
            and FLIGHT_DATE &gt;= STR_TO_DATE(#{dto.beginDate},"%Y-%m-%d")
        </if>
        <if test="dto.endDate !=null and dto.endDate !=''">
            and FLIGHT_DATE &lt;= STR_TO_DATE(#{dto.endDate},"%Y-%m-%d")
        </if>
        <choose>
            <when test="dto.applyBeginDate !=null and dto.applyBeginDate !='' and dto.applyEndDate !=null and dto.applyEndDate !=''">
                <choose>
                    <when test="dto.applyBeginDate == dto.applyEndDate">
                        and APPLY_TIME &gt;= STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d")
                        and APPLY_TIME &lt;= date_add(STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d"),interval '1'
                        day)
                    </when>
                    <otherwise>
                        and APPLY_TIME &gt;= STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d")
                        and APPLY_TIME &lt;=
                        date_add(STR_TO_DATE(#{dto.applyEndDate},"%Y-%m-%d"),interval '1' day)
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <if test="dto.applyBeginDate !=null and dto.applyBeginDate !=''">
                    and APPLY_TIME &gt;= STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d")
                </if>
                <if test="dto.applyEndDate !=null and dto.applyEndDate !=''">
                    and APPLY_TIME &lt;= STR_TO_DATE(#{dto.applyEndDate},"%Y-%m-%d")
                </if>
            </otherwise>
        </choose>
        <if test="dto.flightNo !=null and dto.flightNo !=''">
            and FLIGHT_NO LIKE concat('%',#{dto.flightNo},'%')
        </if>
        <if test="status !=null">
            and binary status in(
            <foreach collection="status" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.isRemark !=null and dto.isRemark !=''">
            <if test="dto.isRemark == 1">
                and profit_remark is not null and profit_remark &lt;&gt;''
            </if>
            <if test="dto.isRemark == 0">
                and (profit_remark is null or profit_remark ='')
            </if>
        </if>
        <if test="dto.filterCancell !=null and dto.filterCancell !='' and dto.filterCancell == 0">
            and PLAN_NUM &lt;&gt;0
        </if>
    </sql>

    <select id="getSvrGetSeat4List" resultType="com.swcares.psi.ao.seat.vo.SvrGetSeatExportVo">
        select A.APPLY_NO applyNo,
        A.RECORD_NO recordNo,
        A.FLIGHT_NO flightNo,
        date_format(A.FLIGHT_DATE,'%Y-%m-%d') flightDate,
        case
        when A.APPLY_TYPE='0' then '经济舱'
        when A.APPLY_TYPE='1' then '公务舱'
        end applyType,
        concat(A.ORG_CITY_AIRP,ifnull(p1.airport_name, ORG_CITY_AIRP)) orgCityAirp,
        concat(A.DST_CITY_AIRP,ifnull(p2.airport_name, DST_CITY_AIRP)) dstCityAirp,
        ifnull(ifnull(D.SUF_DEP_ABBR, D.SUF_DEP_NAME), A.DEPART_CODE) departCode,
        A.PLAN_NUM planNum,
        A.ACTUAL_NUM actualNum,
        A.EMP_LIST empList,
        A.APPLY_EMP applyEmp,
        A.APPLY_TIME applyTime,
        case binary A.STATUS
        when 'W' then '未处理'
        when 'o' then '系统占座中'
        when 'C' then '系统占座成功'
        when 'F' then '系统占座失败'
        when 'h' then '手动占座中'
        when 'H' then '手动占座成功'
        when 'D' then '手动占座失败'
        when 'R' then '占座取消'
        when 'L' then '线下占座成功'
        when 'N' then '无法完成控座'
        when 'I' then '线下取消占座'
        when 'E' then '空座不足'
        when 'Z' then '待超座'
        when 'V' then '已超座'
        when 'X' then '超座完成'
        end status,
        A.remarks remarks,
        date_format(A.local_flight_date,'%Y-%m-%d') localFlightDate,
        A.is_inter_line isInterLine,
        A.PROFIT_REMARK profitRemark
        from AO_SVR_GET_SEAT A
        left join sys_airport_info p1 on p1.code=a.ORG_CITY_AIRP and p1.is_use='0'
        left join sys_airport_info p2 on p2.code=a.DST_CITY_AIRP and p2.is_use='0'
        left join AO_COMMON_DEPART_ABBR D on D.SUF_DEP_CODE=A.DEPART_CODE
        <where>
            <if test="dto.departCode !=null and dto.departCode !=''">
                and ( DEPART_CODE LIKE concat("%",#{dto.departCode},"%") OR DEPART_CODE IN (
                (SELECT B.SUF_DEP_CODE FROM AO_COMMON_DEPART_ABBR B
                WHERE B.SUF_DEP_NAME LIKE concat("%",#{dto.departCode},"%")
                OR B.SUF_DEP_ABBR LIKE concat("%",#{dto.departCode},"%") )))
            </if>
            <if test="dto.applyNo !=null and dto.applyNo !=''">
                and APPLY_NO like concat('%',#{dto.applyNo},'%')
            </if>
            <if test="dto.recordNo !=null and dto.recordNo !=''">
                and RECORD_NO like concat('%',#{dto.recordNo},'%')
            </if>
            <if test="dto.depCd !=null and dto.depCd !='' and dto.depCd !='--请选择--'">
                and ORG_CITY_AIRP =#{dto.depCd}
            </if>
            <if test="dto.arrCd !=null and dto.arrCd !='' and dto.arrCd !='--请选择--'">
                and DST_CITY_AIRP =#{dto.arrCd}
            </if>
            <if test="dto.beginDate !=null and dto.beginDate !=''">
                and FLIGHT_DATE &gt;= STR_TO_DATE(#{dto.beginDate},"%Y-%m-%d")
            </if>
            <if test="dto.endDate !=null and dto.endDate !=''">
                and FLIGHT_DATE &lt;= STR_TO_DATE(#{dto.endDate},"%Y-%m-%d")
            </if>
            <choose>
                <when test="dto.applyBeginDate !=null and dto.applyBeginDate !='' and dto.applyEndDate !=null and dto.applyEndDate !=''">
                    <choose>
                        <when test="dto.applyBeginDate == dto.applyEndDate">
                            and APPLY_TIME &gt;= STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d")
                            and APPLY_TIME &lt;= date_add(STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d"),interval '1' day)
                        </when>
                        <otherwise>
                            and APPLY_TIME &gt;= STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d")
                            and APPLY_TIME &lt;= date_add(STR_TO_DATE(#{dto.applyEndDate},"%Y-%m-%d"),interval '1' day)
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    <if test="dto.applyBeginDate !=null and dto.applyBeginDate !=''">
                        and APPLY_TIME &gt;= STR_TO_DATE(#{dto.applyBeginDate},"%Y-%m-%d")
                    </if>
                    <if test="dto.applyEndDate !=null and dto.applyEndDate !=''">
                        and APPLY_TIME &lt;= STR_TO_DATE(#{dto.applyEndDate},"%Y-%m-%d")
                    </if>
                </otherwise>
            </choose>
            <if test="dto.flightNo !=null and dto.flightNo !=''">
                and FLIGHT_NO LIKE concat('%',#{dto.flightNo},'%')
            </if>
            <if test="status !=null">
                and binary status in(
                <foreach collection="status" item="item" index="index" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="dto.isRemark !=null and dto.isRemark !=''">
                <if test="dto.isRemark == 1">
                   and profit_remark is not null and profit_remark &lt;&gt;''
                </if>
                <if test="dto.isRemark == 0">
                    and (profit_remark is null or profit_remark ='')
                </if>
            </if>
            <if test="dto.filterCancell !=null and dto.filterCancell !='' and dto.filterCancell == 0">
                and PLAN_NUM &lt;&gt;0
            </if>
        </where>
        ORDER BY PROFITREMARK!='', RECORD_NO!='', APPLY_NO ASC
    </select>

    <select id="getSvrGetSeat4HisList" resultType="com.swcares.psi.ao.seat.entity.SvrGetSeatEntity">
        SELECT A.FLT_NO    "fltNo",
               A.APPLY_NO  "applyNo",
               A.APPLY_ID  "applyId",
               A.DEP_DATE  "depDate",
               A.RECORD_NO "recordNo",
               A.STATUS    "status"
        FROM AO_SVR_GET_SEAT A
        WHERE 1 = 1
          AND EXISTS
            (SELECT 1
             FROM SVR_GET_SEAT B
             WHERE A.FLT_NO = B.FLT_NO
               AND A.DEP_DATE = B.DEP_DATE
               AND A.DEP_CD = B.DEP_CD
               AND A.ARR_CD = B.ARR_CD
               AND A.DEPART_CODE = B.DEPART_CODE
               AND B.APPLY_ID = #{applyId})
    </select>
    <select id="getFltByParam" resultType="com.swcares.psi.base.data.api.entity.FltFlightRealInfo">
        select * from flt_flight_real_info
        where 1=1 and FLIGHT_NUMBER=#{flightNo}
        and FLIGHT_DATE=#{date}
        and ORG=#{orgCity}
        and DST=#{dstCity}
        limit 1
    </select>
    <select id="getFirstFltByParam" resultType="com.swcares.psi.base.data.api.entity.FltFlightRealInfo">
        select * from flt_flight_real_info
        where 1=1 and FLIGHT_NUMBER=#{flightNo}
          and FLIGHT_DATE=#{date}
          and DST=#{dstCity}
        limit 1
    </select>

    <select id="getEmployeeByRole" resultType="string">
        select e.TUNO from user_role ur
        join employee e
        on ur.EMPLOYEE_ID=e.ID
        where ROLE_ID in(
        <foreach collection="roleIds" item="roleId" separator=",">
         #{roleId}
        </foreach>
        )
    </select>
    <select id="getApplyRecord" resultType="com.swcares.psi.ao.seat.entity.SvrGetSeatEntity">
        select * from AO_SVR_GET_SEAT
        where binary status in ('C','H','L','X')
        <if test="entity.flightNo !=null and entity.flightNo !=''">
            and FLIGHT_NO=#{entity.flightNo}
        </if>
        <if test="entity.flightDate !=null">
            and FLIGHT_DATE=#{entity.flightDate}
        </if>
        <if test="entity.OrgCityAirp !=null and entity.OrgCityAirp !=''">
            and ORG_CITY_AIRP=#{entity.OrgCityAirp}
        </if>
        <if test="entity.DstCityAirp !=null and entity.DstCityAirp !=''">
            and DST_CITY_AIRP=#{entity.DstCityAirp}
        </if>
        <if test="entity.departCode !=null and entity.departCode !=''">
            and DEPART_CODE=#{entity.departCode}
        </if>
        <if test="entity.applyType !=null and entity.applyType !=''">
            and APPLY_TYPE=#{entity.applyType}
        </if>
    </select>
    <select id="getHisOverNum" resultType="com.swcares.psi.ao.seat.entity.SvrGetSeatEntity">
        select * from AO_SVR_GET_SEAT
        where date_format(FLIGHT_DATE,'%Y-%m-%d') = #{dto.lclDptDate}
        and FLIGHT_NO=#{dto.flightNo}
        and ORG_CITY_AIRP=#{dto.OrgCityAirp}
        and DST_CITY_AIRP=#{dto.DstCityAirp}
        and DEPART_CODE=#{dto.dptCode}
        and  binary status in('C','H','L','X')
    </select>

    <select id="getSvrGetSeatExternal" resultType="com.swcares.psi.ao.seat.vo.SvrGetSeatExternalVo">
        SELECT A.SGS_ID sgsId,
        A.APPLY_ID applyId,
        A.APPLY_NO applyNo,
        A.ORG_CITY_AIRP orgCityCode,
        A.DST_CITY_AIRP dstCityCode,
        A.DEPART_CODE dptCode,
        A.RECORD_NO recordNo,
        A.FLIGHT_NO flightNo,
        A.FLIGHT_DATE flightDate,
        case
        when A.APPLY_TYPE = '0' then '经济舱'
        when A.APPLY_TYPE = '1' then '公务舱'
        end applyType,
        ifnull(ifnull(D.SUF_DEP_ABBR, D.SUF_DEP_NAME), A.DEPART_CODE) departCode,
        A.PLAN_NUM planNum,
        A.ACTUAL_NUM actualNum,
        A.EMP_LIST empList,
        A.STATUS status,
        A.APPLY_EMP applyEmp,
        date_format(A.APPLY_TIME,'%Y-%m-%d %H:%i:%s') applyTime,
        date_format(A.CREATE_TIME,'%Y-%m-%d %H:%i:%s') createTime,
        A.RESON reson,
        A.profit_remark profitRemark,
        A.remarks remarks,
        A.local_flight_date localFlightDate,
        asgsei.ARG1,
        asgsei.ARG2,
        asgsei.ARG3,
        asgsei.ARG4,
        asgsei.ARG5,
        ifnull(asgsei.ARG6,-1) ARG6,
        asgsei.ARG7
        FROM AO_SVR_GET_SEAT A
        left join AO_COMMON_DEPART_ABBR D on D.SUF_DEP_CODE=A.DEPART_CODE
        inner join ao_svr_get_seat_external_interface asgsei on
        asgsei.ID = (select t.ID from ao_svr_get_seat_external_interface t where
        t.ARG2 = A.APPLY_ID and t.CREATE_TIME > #{updateTimeBegin} order by t.CREATE_TIME desc limit 1)
        where A.APPLY_ID in
        <foreach collection="applyIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="getSvrGetSuccessSeatAmount" resultType="java.lang.Integer">
        SELECT sum(ACTUAL_NUM)
        FROM AO_SVR_GET_SEAT
        where binary STATUS in ('C','H','L','X') <include refid="queryCondition"></include>
    </select>

</mapper>