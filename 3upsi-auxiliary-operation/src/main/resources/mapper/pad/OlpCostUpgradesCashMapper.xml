<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.pad.mapper.OlpCostUpgradesCashMapper">
    <sql id="querySql">
        select
        ocuc.id as id,
        sali.ID as ariLineId,
        sai1.AIRPORT_NAME as orgName,
        sai2.AIRPORT_NAME as dstName,
        sai1.code as org,
        sai2.code as dst,
        IFNULL(ocuc .cost,"-")  as  cost,
        IFNULL(ocuc .EFFECT_COST,"-")  as  effectCost,
        date_format(ocuc .COST_DATE,'%Y-%m-%d %H:%i:%S')  as  costDate,
        case ocuc .`status`
        when '1' then '启用'
        when '0' then '禁用'
        else '' end as status,
        if (ocuc.EFFECT_DATE is not  null and NOW()>=ocuc.EFFECT_DATE,ocuc .FOREIGN_CURRENCY_TYPE_EFFECT, ocuc .foreign_currency_type)   as foreignCurrencyType,
        if (ocuc.EFFECT_DATE is not  null and NOW()>=ocuc.EFFECT_DATE,ocuc .FOREIGN_CURRENCY_PRICE_EFFECT, ocuc .foreign_currency_price)   as foreignCurrencyPrice,
        if (ocuc.EFFECT_DATE is not  null and NOW()>=ocuc.EFFECT_DATE,null, ocuc .FOREIGN_CURRENCY_TYPE_EFFECT)   as foreignCurrencyTypeEffect,
        if (ocuc.EFFECT_DATE is not  null and NOW()>=ocuc.EFFECT_DATE,null, ocuc .FOREIGN_CURRENCY_PRICE_EFFECT)   as foreignCurrencyPriceEffect,
        if (ocuc.EFFECT_DATE is not  null and NOW()>=ocuc.EFFECT_DATE,null, date_format(ocuc.EFFECT_DATE,'%Y-%m-%d %H:%i:%S'))   as effectDate,
        em.TU_CNAME as updateName,
        date_format(ocuc.update_date,'%Y-%m-%d %H:%i:%S') as updateDate
        from
        sys_air_line_info sali
        left join olp_cost_upgrades_cash ocuc
        on sali.ID =ocuc .ARI_LINE_ID
        left join sys_airport_info sai1
        on sali .ORG =sai1 .code
        left join sys_airport_info sai2
        on sali .DST =sai2 .code
        left join employee em
        on em.TUNO = ocuc .update_id
        <where>
            sali.IS_USE='0'  and ocuc.id is  not  null
            <if test="dto.org != null and dto.org != ''">
                and sai1.CODE =#{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst != ''">
                and sai2.CODE =#{dto.dst}
            </if>
            <if test="dto.beginEffectDate != null and dto.beginEffectDate != ''">
               and ( ocuc.EFFECT_DATE  <![CDATA[ >= ]]> date_format(#{dto.beginEffectDate},'%Y-%m-%d 00:00:00') and ocuc.EFFECT_DATE  is not  null)
            </if>
            <if test="dto.endEffectDate != null and dto.endEffectDate != ''">
                and ( ocuc.EFFECT_DATE  <![CDATA[ <= ]]> date_format(#{dto.endEffectDate},'%Y-%m-%d 23:59:59') and ocuc.EFFECT_DATE  is not  null)
            </if>
        </where>
    </sql>
    <select id="getPage" resultType="com.swcares.psi.ao.pad.vo.OlpCostUpgradesCashVo" parameterType="com.swcares.psi.ao.pad.dto.OlpCostUpgradesCashDto">
        <include refid="querySql"></include>
    </select>

    <select id="getList" resultType="com.swcares.psi.ao.pad.vo.OlpCostUpgradesCashVo" parameterType="com.swcares.psi.ao.pad.dto.OlpCostUpgradesCashDto">
        <include refid="querySql"></include>
    </select>



</mapper>