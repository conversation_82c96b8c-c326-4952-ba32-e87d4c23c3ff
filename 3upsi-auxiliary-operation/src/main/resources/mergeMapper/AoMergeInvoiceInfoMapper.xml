<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeInvoiceInfoMapper">

    <sql id="pageSql">
        select
        invoice.ID as id,
        amerge.ORDER_NO as orderNo,
        invoice.SEQ_NO AS seqNo,
        invoice.INVOICE_NO AS invoiceNo,
        CASE
        WHEN invoice.PRODUCT_TYPE = '1' THEN
        '辅营产品'
        WHEN invoice.PRODUCT_TYPE = '2' THEN
        '机上销售 '
        ELSE
        invoice.PRODUCT_TYPE
        END   AS productType,
        CASE
        WHEN invoice.INVOICE_STATUS = '0' THEN '未开票'
        WHEN invoice.INVOICE_STATUS = '1' THEN '开票中 '
        WHEN invoice.INVOICE_STATUS = '2' THEN '开票成功 '
        WHEN invoice.INVOICE_STATUS = '3' THEN '开票失败 '
        ELSE
        invoice.INVOICE_STATUS
        END   AS invoiceStatus,
        CASE
        WHEN invoice.TITLE_TYPE = '1' THEN
        '企业'
        ELSE
        '个人'
        END  AS titleType,
        invoice.INVOICE_TITLE AS invoiceTitle,
        invoice.INVOICE_PRICE AS invoicePrice,
        invoice.PRODUCT_NAME AS productName,
        date_format(invoice.CREATE_TIME, '%Y-%m-%d %H:%i:%S') AS invoiceTime
        from ao_merge_invoice_info invoice
        inner join  ao_merge_sub_order_join_invoice_info sujii on sujii.SEQ_NO=invoice.SEQ_NO
        inner join ao_merge_suborder_info sub on sub.id=sujii.SUBORDER_ORDER_ID
        inner join ao_merge_order_info amerge on amerge.id=sujii.MERGE_ORDER_ID
        where invoice.INVOICE_STATUS <![CDATA[  <> ]]> '-1'
        <if test="dto.orderNo != null and dto.orderNo !=''">
            and amerge.ORDER_NO like concat('%',#{dto.orderNo},'%')
        </if>

        <if test="dto.seqNo != null and dto.seqNo !=''">
            and invoice.SEQ_NO like concat('%',#{dto.seqNo},'%')
        </if>

        <if test="dto.invoiceNo != null and dto.invoiceNo !=''">
            and invoice.INVOICE_NO like concat('%',#{dto.invoiceNo},'%')
        </if>

        <if test="dto.pushDateStart != null and dto.pushDateStart !=''">
            and invoice.CREATE_TIME  <![CDATA[  >= ]]> date_format(#{dto.pushDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.pushDateEnd != null and dto.pushDateEnd !=''">
            and invoice.CREATE_TIME  <![CDATA[  <= ]]> date_format(#{dto.pushDateEnd}, '%Y-%m-%d 23:59:59')
        </if>


        <if test="dto.productType != null and dto.productType !=''">
            AND find_in_set(invoice.PRODUCT_TYPE, #{dto.productType})
        </if>

        <if test="dto.invoiceStatus != null and dto.invoiceStatus !=''">
            AND find_in_set(invoice.INVOICE_STATUS, #{dto.invoiceStatus})
        </if>

        <if test="dto.titleType != null and dto.titleType !=''">
            AND find_in_set(invoice.TITLE_TYPE, #{dto.titleType})
        </if>

        <if test="dto.paxName != null and dto.paxName !=''">
            and sub.PAX_NAME like concat('%',#{dto.paxName},'%')
        </if>

        <if test="dto.flightNo != null and dto.flightNo !=''">
            and sub.FLIGHT_NO =#{dto.flightNo}
        </if>
        <if test="dto.invoiceTitle != null and dto.invoiceTitle !=''">
            and invoice.INVOICE_TITLE like concat('%',#{dto.invoiceTitle},'%')
        </if>
        group by invoice.ID
    </sql>
   <select id="getInvoiceInfoPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceInfoVo">
     <include refid="pageSql"></include>
   </select>

    <select id="getInvoiceInfoList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceInfoVo">
        <include refid="pageSql"></include>
    </select>



    <resultMap id="resultDetail" type="com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceDetailVo">
        <result column="orderNo" property="orderNo"/>
        <result column="seqNo" property="seqNo"/>
        <result column="orderNo" property="orderNo"/>
        <result column="invoiceNo" property="invoiceNo"/>
        <result column="productType" property="productType"/>
        <result column="invoiceStatus" property="invoiceStatus"/>
        <result column="titleType" property="titleType"/>
        <result column="invoiceTitle" property="invoiceTitle"/>
        <result column="taxNo" property="taxNo"/>
        <result column="invoicePrice" property="invoicePrice"/>
        <result column="productName" property="productName"/>
        <result column="productNumber" property="productNumber"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="purchaserPhone" property="purchaserPhone"/>
        <result column="purchaserAddress" property="purchaserAddress"/>
        <result column="purchaserBank" property="purchaserBank"/>
        <result column="purchaserBankNo" property="purchaserBankNo"/>
        <result column="causeFailure" property="causeFailure"/>
        <result column="createTime" property="createTime"/>
        <result column="invoiceTime" property="invoiceTime"/>
        <result column="sourceInformation" property="sourceInformation"/>
        <result column="userName" property="userName"/>
        <result column="userNo" property="userNo"/>
        <collection property="paxInfoList"
                    ofType="com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceDetailVo$PaxInfo"
                    javaType="java.util.List"
                    select="invoiceDetailPaxInfo" column="seqNo">

        </collection>
    </resultMap>
    <select id="invoiceDetail" resultMap="resultDetail">
        select
        invoice.SEQ_NO AS seqNo,
        invoice.INVOICE_NO AS invoiceNo,
        invoice.PRODUCT_TYPE AS productType,
        case invoice.INVOICE_STATUS
            when '0' then '未开发票'
            when '1' then '开票中'
            when '2' then '开票成功'
            when '3' then '开票失败'
        end  AS invoiceStatus,
        case invoice.TITLE_TYPE
            when '1' then '企业'
            when '2' then '个人'
        end  AS titleType,
        invoice.INVOICE_TITLE AS invoiceTitle,
        invoice.TAX_NO AS taxNo,
        invoice.INVOICE_PRICE AS invoicePrice,
        invoice.PRODUCT_NAME AS productName,
        invoice.PRODUCT_NUMBER AS productNumber,
        invoice.PHONE AS phone,
        invoice.EMAIL AS email,
        invoice.PURCHASER_PHONE AS purchaserPhone,
        invoice.PURCHASER_ADDRESS AS purchaserAddress,
        invoice.PURCHASER_BANK AS purchaserBank,
        invoice.PURCHASER_BANK_NO AS purchaserBankNo,
        invoice.CAUSE_FAILURE AS causeFailure,
        date_format(invoice.CREATE_TIME, '%Y-%m-%d %H:%i:%S')  AS createTime,
        date_format(invoice.INVOICE_TIME, '%Y-%m-%d %H:%i:%S')  AS invoiceTime,
            case invoice.SOURCE_INFORMATION
            when '1' then '辅营B端'
            when '2' then 'C端小程序'
            when '3' then '平板端'
        end  AS sourceInformation,
        invoice.USER_NAME AS userName,
        invoice.USER_NO AS userNo,
        (
        select
        distinct amoi.ORDER_NO
        from ao_merge_sub_order_join_invoice_info  amsojii
        left join ao_merge_order_info amoi on amsojii.MERGE_ORDER_ID=amoi.ID
        where amsojii.SEQ_NO=invoice.SEQ_NO


        ) as orderNo
        from ao_merge_invoice_info invoice
        where invoice.ID=#{mergeInvoiceId}
    </select>

    <select id="invoiceDetailPaxInfo" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeInvoiceDetailVo$PaxInfo">
        select
       amsi.PAX_NAME as paxName,
       amsi.PAX_NO as idNo,
       concat(get_city_name(amsi.ORG),'-',get_city_name(amsi.DST)) as segment,
       amsi.FLIGHT_NO as flightNo,
       date_format(amsi.FLIGHT_DATE, '%Y-%m-%d') as flightDate
        from ao_merge_sub_order_join_invoice_info  amxx
        left join  ao_merge_suborder_info  amsi on amsi.ID=amxx.SUBORDER_ORDER_ID
        where amxx.SEQ_NO=#{seqNo}

    </select>

</mapper>
