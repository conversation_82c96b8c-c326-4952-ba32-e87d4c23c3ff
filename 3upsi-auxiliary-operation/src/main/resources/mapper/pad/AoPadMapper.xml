<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.pad.mapper.AoPadMapper">

    <select id="getCrewList" resultType="com.swcares.psi.ao.pad.vo.CrewVo">
        SELECT
            FT2.P_CODE userName,
            CONCAT(CONCAT(CONCAT(CONCAT('乘务员' , '-' ), FT2.C_NAME ), ',') , FT2.P_CODE) as empName,
            FT2.P_CODE id
        FROM
            FOC50_T3005 FT
                INNER JOIN FOC50_T3017 FT2 ON FT2.P_CODE = FT.P_CODE
                INNER JOIN FOC50_T3020 FT3 ON ( FT3.RANK_NO = FT.RANK_NO )
                LEFT JOIN flt_flight_real_info F ON F.FLIGHT_GROUP = FT.CREW_LINK_LINE
        WHERE
          F.ID =#{flightId}
          AND FT3.rank_NO ='SC'
        ORDER BY
            FT2.C_NAME
    </select>




</mapper>