<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.register.mapper.PadVipRegisterMapper">

    <select id="getPadVipRegisterInfoByFlight" resultType="com.swcares.psi.ao.register.vo.PadVipRegisterVo">
        select pvr.* from pad_vip_register pvr,flt_passenger_real_info fpri,flt_flight_real_info ffri
         where 1=1
         and pvr.certific_no=fpri.id_number
         and fpri.flight_date=ffri.flight_date
         and fpri.flight_number=ffri.flight_number
         and (fpri.org=ffri.org or fpri.dst=ffri.dst)
         and ffri.id=#{flightId}
         and (fpri.seat_number is not null or fpri.ticket_number is not null)
         and ifnull(fpri.is_cancel,'N')='N'
         group by pvr.certific_type,pvr.certific_no
    </select>
</mapper>
