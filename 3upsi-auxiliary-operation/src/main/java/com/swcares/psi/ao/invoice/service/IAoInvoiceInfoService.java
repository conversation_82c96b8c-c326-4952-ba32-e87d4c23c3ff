package com.swcares.psi.ao.invoice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.ao.invoice.dto.InvoiceInfoDto;
import com.swcares.psi.ao.invoice.dto.InvoiceInfoSaveDto;
import com.swcares.psi.ao.invoice.entity.AoInvoiceInfoEntity;
import com.swcares.psi.ao.invoice.vo.AoInvoiceDetailVo;
import com.swcares.psi.ao.invoice.vo.AoInvoiceInfoVo;
import com.swcares.psi.ao.pad.dto.PadInvoiceInfoSaveDto;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-19
 */
public interface IAoInvoiceInfoService extends IService<AoInvoiceInfoEntity> {

    /**
     * 保存开发票记录
     * @param dto
     */
    void saveInvoice(List<InvoiceInfoSaveDto> dto);

    /**
     * 查询发票信息分页接口
     *
     * @param dto
     * @return
     */
    PsiPage<AoInvoiceInfoVo> findInvoiceInfoPage(InvoiceInfoDto dto);

    /**
     * 查询发票信息
     *
     * @param dto
     * @return
     */
    List<AoInvoiceInfoVo> findInvoiceInfo(InvoiceInfoDto dto);


    /**
     * 获取发票详情
     * @param invoiceId
     * @return
     */
    AoInvoiceDetailVo invoiceDetailInfo(String invoiceId);

    /**
     * 平板机上升舱  发票下载同步
     * @param flightNo
     * @param flightDate
     * @param org
     * @param dst
     * @return
     */
    List<PadInvoiceInfoSaveDto> findPadInvoiceInfo(String flightNo,String flightDate,String org,String dst);

    List<PadInvoiceInfoSaveDto> findPadInvoiceInfoByOrderNo(List<String> orderNoList);
}
