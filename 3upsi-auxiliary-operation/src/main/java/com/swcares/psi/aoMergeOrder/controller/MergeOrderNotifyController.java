package com.swcares.psi.aoMergeOrder.controller;

import com.alibaba.fastjson2.JSON;
import com.swcares.psi.ao.common.vo.ElectronicVoucherVo;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOverweightBkgService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeRefundOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.impl.AoMegerOrderNotifyLockService;
import com.swcares.psi.aoMergeOrder.vo.RefundSendTaskVo;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.common.worldpay.WorldpayUtil;
import com.swcares.psi.common.worldpay.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@Controller
@RequestMapping("/api/ao/notify/mergeOrder")
@Slf4j
public class MergeOrderNotifyController {
    @Autowired
    private WorldpayUtil worldpayUtil;
    @Autowired
    private IAoMergeOverweightBkgService aoMergeOverweightBkgService;

    @Value("${electronic_voucher.param.key}")
    private String key;

    @Autowired
    AoMegerOrderNotifyLockService aoMegerOrderNotifyLockService;
    @PostMapping(value = "worldPay", headers = "content-type=text/xml")
    public String wpNotify(@RequestBody String data) {
        WorldPayBaseVo notification = worldpayUtil.notification(data);
             log.info("WorldPay--{}-- Notify转换:{}",notification.getNotifyType(), JSON.toJSONString(notification));
        if (notification != null) {
            switch (notification.getNotifyType()) {
                case AUTHORISED:
                    aoMegerOrderNotifyLockService.worldpayNotifyUrl((WorldpayAuthorisedVo) notification);
                    break;
                case CANCELLED:
                    break;
                case CAPTURED:
                    aoMegerOrderNotifyLockService.worldpayCapturedNotifyUrl((WorldpayCapturedVo) notification);
                    break;
                case REFUNDED:
                    aoMegerOrderNotifyLockService.worldpayRefundNotifyUrl((WorldpayRefundedVo) notification);
                    break;
                case SETTLED:
                    break;
                default:
                    break;
            }
            return "ok";
        } else {
            return "no";
        }
    }

    //微信支付通知
    @PostMapping("/pass/wxpay_notify_url")
    @ResponseBody
    public String wxpay_notify_url(HttpServletRequest request, HttpServletResponse response) {
        aoMegerOrderNotifyLockService.wxpayNotifyUrl(request,response);
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
        sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        sb.append("</xml>");
        return sb.toString();
    }


    //微信退款的异步通知
    @PostMapping("/pass/wxpay_refund_notify_url")
    @ResponseBody
    public String wxpay_refund_notify_url(HttpServletRequest request, HttpServletResponse response) {
        aoMegerOrderNotifyLockService.wxpayRefundNotifyUrl(request,response);
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
        sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        sb.append("</xml>");
        return sb.toString();
    }


    //微信缴纳支付通知
    @PostMapping("/pass/wxpay_turn_notify_url")
    @ResponseBody
    public String wxpay_turn_notify_url(HttpServletRequest request, HttpServletResponse response) {
        aoMegerOrderNotifyLockService.wxpayTurnNotifyUrl(request,response);
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
        sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        sb.append("</xml>");
        return sb.toString();
    }



    //支付宝扫码支付后异步通知
    @PostMapping("/pass/alipay_notify_url")
    @ResponseBody
    public String alipay_notify_url(HttpServletRequest request) {
        aoMegerOrderNotifyLockService.alipayNotifyUrl(request);
        return "success";
    }

    //支付宝缴纳支付后异步通知
    @PostMapping("/pass/alipay_turn_notify_url")
    @ResponseBody
    public String alipay_turn_notify_url(HttpServletRequest request) {
        aoMegerOrderNotifyLockService.alipay_turn_notify_url(request);
        return "success";
    }


    //易宝支付的异步通知
    @PostMapping("/pass/yee_notify_url")
    @ResponseBody
    public String yee_notify_url(HttpServletRequest request) {
        aoMegerOrderNotifyLockService.yeePayNotify(request);
        return "SUCCESS";
    }


    @PostMapping("/getElectronicVoucher")
    @ResponseBody
    public RenderResult getElectronicVoucher(String suborderNo) {
        suborderNo = AesEncryptUtil.aesDecrypt(key, suborderNo);
        if (StringUtils.isEmpty(suborderNo)) {
            return RenderResult.fail();
        }
        return RenderResult.success(aoMergeOverweightBkgService.getElectronicVoucher(suborderNo));
    }


}
