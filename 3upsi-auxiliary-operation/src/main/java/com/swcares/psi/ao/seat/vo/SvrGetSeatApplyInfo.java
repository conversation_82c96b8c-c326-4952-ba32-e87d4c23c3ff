package com.swcares.psi.ao.seat.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.seat.vo.SvrGetSeatApplyInfo <br>
 * Description：(申请单信息)<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022-3-16 10:15<br>
 * @version v1.0 <br>
 */
@Data
public class SvrGetSeatApplyInfo {
    private List<Info> data = new ArrayList<>();

    @ApiModelProperty("申报编号")
    private String applyNo;

    @ApiModelProperty("ID编号")
    private Long applyId;

    @Data
    public class Info{
        @ApiModelProperty("记录编号")
        private String recordNo;

        @ApiModelProperty("状态所属分类,0-取消，1-成功，20-失败")
        private String revlType;

        @ApiModelProperty("申报编号")
        private String applyNo;
    }
}

