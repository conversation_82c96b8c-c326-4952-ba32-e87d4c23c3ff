<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeViproomItemSellMapper">


    <sql id="selectSql">
        select
        date_format(amoi.CREATE_DATE, '%Y-%m-%d') orderDate,
        date_format(aosi.FLIGHT_DATE, '%Y-%m-%d') flightDate,
        aosi.FLIGHT_NO as flightNo,
        aosi.SUBORDER_ORDER_NO as subOrderNo,
        ampoi.BANK_ORDER_NO as bankOrderNo,
        aosi.TKT_NO as tktNo,
        case aosi.FLIGHT_TYPE
            when 'I' then '国际'
            when 'D' then '国内'
            else '地区'
        end as flightType,
        case ampoi.PAY_TYPE
            when '0' then '微信'
            when '1' then '易宝'
            when '2' then '支付宝'
            when '3' then '现金'
            when '4' then 'pos机'
            else 'worldpay'
        end as payType,
          case amrel.PAY_EVENT
            when '1' then '已完成'
            when '4' then '已退款'
        end as orderStatus,

        case amrel.PAY_EVENT
            when '1' then '支付成功'
            when '4' then '退款成功'
        end as payStatus,
        amoi.TURN_NUMBER,
        aosi.SEGMENT ,
        (select sum(1) from  ao_merge_viproom_item_sell  amob where aosi.id=amob.AO_MERGE_SUBORDER_INFO_ID) as peopleNumber,
        case amrel.PAY_EVENT
             when '1' then aosi.PRACTICAL_ORDER_PRICE
             when '4' then CONCAT('-',aosi.PRACTICAL_ORDER_PRICE)
        end as payOrRefundPrice,

        if(amrel.PAY_EVENT='4', date_format(amroi.REFUND_END_TIME,'%Y-%m-%d %H:%i:%s'), date_format(ampoi.PAY_TIME,'%Y-%m-%d %H:%i:%s')) as payDate,
        aosi.PAX_NAME,
        ifnull(aosi.FFP_NO,'-') as ffpNo,
        ifnull(aosi.PAX_TYPE,'-') as paxType,
        ifnull(aosi.PAX_CATEGORY,'-') as paxCategory,
        amoi.CREATE_NO,
        amoi.CREATE_USER,
        amoi.CREATE_USER_DEPARTMENT as department,
        case amoi.TURN_STATUS
        when '1' then '缴纳中'
        when '2' then '缴纳失败'
        when '3' then '已缴纳'
        end as turnStatus,
        (
            select  distinct item.name
            from  ao_merge_viproom_item_sell vipis
            inner join ao_viproom_item_conf item  on vipis.ITEM_ID=item.id
            where vipis.AO_MERGE_SUBORDER_INFO_ID=aosi.id
        ) as remark,

--         (
--             select  CONCAT(distinct item.name,'(',GROUP_CONCAT(vipis.PAX_NAME SEPARATOR ','),')')
--             from  ao_merge_viproom_item_sell vipis
--             inner join ao_viproom_item_conf item  on vipis.ITEM_ID=item.id
--             where vipis.AO_MERGE_SUBORDER_INFO_ID=aosi.id
--             group by item.name
--         ) as remark,
        amoi.AREA_COMPANY
        from
        ao_merge_suborder_info  aosi
        left join ao_merge_order_info amoi on aosi.AO_MERGE_ORDER_INFO_ID =amoi.ID
        left join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
        left join ao_merge_report_event_log amrel on amrel.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        left join ao_merge_refund_order_info amroi on amroi.AO_MERGE_SUBORDER_INFO_ID =aosi.id
    </sql>
    <sql id="whereSql">
        where aosi.ORDER_TYPE='4' and   aosi.SUBORDER_ORDER_STATUS in ('1','2','3','4')

        <if test="dto.orderDateStart != null and dto.orderDateStart !=''">
            and amoi.CREATE_DATE  <![CDATA[ >= ]]> date_format(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.orderDateEnd != null and dto.orderDateEnd !=''">
            and amoi.CREATE_DATE  <![CDATA[ <= ]]> date_format(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.flightDateStart != null and dto.flightDateStart !=''">
            and aosi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.flightDateEnd != null and dto.flightDateEnd !=''">
            and aosi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.payTimeStart != null and dto.payTimeStart !=''">
            and ampoi.PAY_TIME  <![CDATA[ >= ]]>  date_format(#{dto.payTimeStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.payTimeEnd != null and dto.payTimeEnd !=''">
            and ampoi.PAY_TIME <![CDATA[ <= ]]> date_format(#{dto.payTimeEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.subOrderNo != null and dto.subOrderNo !=''">
            and aosi.SUBORDER_ORDER_NO like concat('%',#{dto.subOrderNo},'%')
        </if>

        <if test="dto.bankOrderNo != null and dto.bankOrderNo !=''">
            and ampoi.BANK_ORDER_NO like concat('%',#{dto.bankOrderNo},'%')
        </if>

        <if test="dto.flightNo != null and dto.flightNo !=''">
            and aosi.FLIGHT_NO =#{dto.flightNo}
        </if>

        <if test="dto.org != null and dto.org !=''">
            and aosi.ORG =#{dto.org}
        </if>

        <if test="dto.dst != null and dto.dst !=''">
            and aosi.DST =#{dto.dst}
        </if>

        <if test="dto.paxName != null and dto.paxName !=''">
            and aosi.PAX_NAME like concat('%',#{dto.paxName},'%')
        </if>

        <if test="dto.tktNo != null and dto.tktNo !=''">
            and aosi.TKT_NO like concat('%',#{dto.tktNo},'%')
        </if>

        <if test="dto.flightType != null and dto.flightType !=''">
            and aosi.FLIGHT_TYPE=#{dto.flightType}
        </if>

        <if test="dto.payType != null and dto.payType !=''">
            and find_in_set(ampoi.PAY_TYPE, #{dto.payType})
        </if>

        <if test="dto.payStatus != null and dto.payStatus !=''">
            and find_in_set(amrel.PAY_EVENT, #{dto.payStatus})
        </if>

        <if test="dto.department != null and dto.department !=''">
            and amoi.CREATE_USER_DEPARTMENT like concat('%',#{dto.department},'%')
        </if>

        <if test="dto.turnNumber != null and dto.turnNumber !=''">
            and amoi.TURN_NUMBER like concat('%',#{dto.turnNumber},'%')
        </if>

        <if test="dto.areaCompany != null and dto.areaCompany !=''">
            and amoi.AREA_COMPANY like concat('%',#{dto.areaCompany},'%')
        </if>
    </sql>

    <select id="getAccountsListPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeViproomItemSellAccountsReportVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeViproomAccountsReportDto">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
    </select>

    <select id="getAccountsList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeViproomItemSellAccountsReportVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeViproomAccountsReportDto">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
    </select>


    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeViproomAccountsReportDto">
        select t.* from (
        select
        sum(CASE WHEN rs.PAY_TYPE = '0' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN rs.PAY_TYPE = '1' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
        sum(CASE WHEN rs.PAY_TYPE = '2'  THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS alipayMoney,
        sum(CASE WHEN (rs.PAY_TYPE = '3' or rs.PAY_TYPE = '4') THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end) AS cashMoney,
        sum(CASE WHEN rs.PAY_TYPE = '5' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end) AS worldPay,
        sum(rs.PRACTICAL_ORDER_PRICE  ) AS allMoney,
        case  rs.CURRENCY_SPECIES
        when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
        else   concat(rs.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
        end as currencySpecies
        from (
        select

        CASE amrel.PAY_EVENT
        when '4' then
        concat('-',PRACTICAL_ORDER_PRICE)
        else PRACTICAL_ORDER_PRICE
        end as PRACTICAL_ORDER_PRICE,
        ampoi.PAY_TYPE,
        ampoi.CURRENCY_SPECIES
        from
        ao_merge_suborder_info  aosi
        left join ao_merge_order_info amoi on aosi.AO_MERGE_ORDER_INFO_ID =amoi.ID
        left join ao_merge_pay_order_info ampoi on ampoi.AO_MERGE_ORDER_INFO_ID=amoi.ID
        left join ao_merge_report_event_log amrel on amrel.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        left join ao_merge_refund_order_info amroi on amroi.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        <include refid="whereSql"></include>
        ) rs  group by rs.CURRENCY_SPECIES
        )t where t.allMoney > 0
    </select>




</mapper>
