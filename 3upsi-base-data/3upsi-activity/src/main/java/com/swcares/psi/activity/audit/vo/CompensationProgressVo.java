package com.swcares.psi.activity.audit.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.psi.audit.vo <br>
 * Description：H5- 补偿进度信息查询 -超售<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月26日 19:51 <br>
 * @version v1.0 <br>
 */
@Data
public class CompensationProgressVo {

    @ApiModelProperty(value = "补偿进度状态(0草稿、1审核中、2通过、3生效、4关闭,5未通过6驳回7待审核 8逾期 p8已领取 p9冻结)")
    private String cpsProgressStatus;

    @ApiModelProperty(value = "处理人信息 部门 - +姓名")
    private String handleUserInfo;

    @ApiModelProperty(value = "是否展示重新编辑按钮  当前登录人为发起人| 状态为驳回 、审核流程流转到发起人 1显示")
    private String isShowReEdit;

    @ApiModelProperty(value = "补偿单发起人")
    private String createUser;


}
