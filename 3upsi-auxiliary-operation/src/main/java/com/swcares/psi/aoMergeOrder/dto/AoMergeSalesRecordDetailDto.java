package com.swcares.psi.aoMergeOrder.dto;

import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AoMergeSalesRecordDetailDto  extends BaseDto {
    @ApiModelProperty(value = "订单开始时间")
    private String begin;
    @ApiModelProperty(value = "订单结束时间")
    private String end;
    @ApiModelProperty(value = "订单类型")
    private String orderType;
}
