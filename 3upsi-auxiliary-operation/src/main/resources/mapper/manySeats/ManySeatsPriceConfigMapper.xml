<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.manySeats.mapper.ManySeatsPriceConfigMapper">
    <sql id="sql">
        select price.id,
        line.id as airLineId,
        line.org,
        line.dst,
        line.type as lineType,
        price.PRICE_TYPE,
        CONCAT(line.org,'-',ifnull(po.AIRPORT_NAME,'')) as orgName,
        CONCAT(line.dst,'-',ifnull(pd.AIRPORT_NAME,'')) as dstName,
        price.IS_USE as isUse,
        date_format(price.CREATE_TIME,'%Y-%m-%d %H:%i:%s') as createTime,
        ce.TU_CNAME  as CREATE_USER,
        date_format(price.UPDATE_TIME,'%Y-%m-%d %H:%i:%s') as updateTime,
        ue.TU_CNAME  as UPDATE_USER,
        price.PRICE
        from sys_air_line_info line
        left join ao_many_seats_price_config price on line.id = price.AIR_LINE_ID
        left join sys_airport_info po on po.code = line.org
        left join sys_airport_info pd on pd.code = line.dst
        left join employee ue on ue.TUNO = price.CREATE_USER
        left join employee ce on ce.TUNO = price.UPDATE_USER
        <where>
            <if test="dto.org!= null and dto.org != ''">
                line.org=#{dto.org}
            </if>
            <if test="dto.dst!= null and dto.dst != ''">
                and line.dst=#{dto.dst}
            </if>
            <if test="dto.price!= null and dto.price != ''">
                and price.PRICEt=#{dto.price}
            </if>
            <if test="dto.orgCountry!= null and dto.orgCountry != ''">
                and po.COUNTRY_NAME=#{dto.orgCountry}
            </if>
            <if test="dto.dstCountry!= null and dto.dstCountry != ''">
                and pd.COUNTRY_NAME=#{dto.dstCountry}
            </if>
            <if test="dto.isUse!= null and dto.isUse != ''">
                and price.IS_USE=#{dto.isUse}
            </if>
            <if test="dto.priceType!= null and dto.priceType != ''">
                and price.PRICE_TYPE=#{dto.priceType}
            </if>

        </where>
    </sql>

    <select id="getPriceInfo" resultType="com.swcares.psi.ao.manySeats.vo.ManySeatsPriceConfigVo">
        <include refid="sql"/>
    </select>
    <select id="getPriceInfoPage" resultType="com.swcares.psi.ao.manySeats.vo.ManySeatsPriceConfigVo">
        <include refid="sql"/>
    </select>

    <select id="getPriceIdByLineId" resultType="com.swcares.psi.ao.manySeats.vo.ManySeatsLineToPriceMappingVo">
        select price.id as priceId,
        line.id as lineId
        from sys_air_line_info line
        left join ao_many_seats_price_config price on line.id =price.AIR_LINE_ID
        where line.id in
        <foreach collection="dto" item="ele" open="(" close=")" separator=",">
            #{ele}
        </foreach>
    </select>

</mapper>