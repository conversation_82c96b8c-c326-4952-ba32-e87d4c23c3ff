package com.swcares.psi.luggage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.base.data.api.vo.FltFlightRealInfoVo;
import com.swcares.psi.common.compensate.entity.OrderInfo;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.base.data.api.vo.PaxInfoParseVo;
import com.swcares.psi.luggage.dto.PkgAccidentCloseDto;
import com.swcares.psi.luggage.dto.PkgAccidentCloseListDto;
import com.swcares.psi.luggage.dto.PkgAuditParamInfoDto;
import com.swcares.psi.luggage.dto.PkgCreateDto;
import com.swcares.psi.luggage.dto.PkgInfoDto;
import com.swcares.psi.luggage.dto.PkgInfoQueryParamDto;
import com.swcares.psi.luggage.dto.PkgOrderPageDto;
import com.swcares.psi.luggage.dto.PkgQuickSearchDto;
import com.swcares.psi.luggage.dto.PkgUpdateInfoDto;
import com.swcares.psi.luggage.dto.PkgUpdateParamInfoDto;
import com.swcares.psi.luggage.entity.PkgInfo;
import com.swcares.psi.luggage.vo.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

/**
 * ClassName：com.swcares.psi.pkg.service <br>
 * Description：异常行李service <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 10:33 <br>
 * @version v1.0 <br>
 */
public interface PkgInfoService extends IService<PkgInfo> {
    /**
     * Title：authUserInfo <br>
     * Description：异常行李验证用户信息<br>
     * author：王建文 <br>
     * date：2020-3-20 10:36 <br>
     *
     * @return
     */
    public Map<String, Object> authUserInfo(PkgCreateDto pkgCreateDto);


    public List<Map<String, Object>> getPaxTktPkgTotal(String paxId,String tktNo, String flightNo,String accidentTypeCode);

    public List<Map<String, Object>> getPaxPkgOccurTotal(String tktNo, String flightNo,String accidentTypeCode);
    /**
     * Title：savePkgInfo <br>
     * Description： 异常行李保存<br>
     * author：王建文 <br>
     * date：2020-3-20 16:00 <br>
     *
     * @param pkgInfoDto 参数接收
     * @return
     */
    public String savePkgInfo(PkgInfoDto pkgInfoDto) throws Exception;

   /**
    * Title：auditPkgInfo <br>
    * Description： 异常行李走审核流程<br>
    * author：王建文 <br>
    * date：2020-5-15 9:37 <br>
    * @param  pkgAuditParamInfoDto 参数接收
    * @return
    */
    public String auditPkgInfo(PkgAuditParamInfoDto pkgAuditParamInfoDto) ;
    /**
     * Title：getPkgInfo <br>
     * Description：异常行李列表查询<br>
     * author：王建文 <br>
     * date：2020-3-22 11:08 <br>
     *
     * @param queryParamDto 查询参数接收
     * @return
     */
    public List<PkgInfoVo> getPkgInfo(PkgInfoQueryParamDto queryParamDto);

    /**
     * Title：getPkgDetailInfo <br>
     * Description： 获取异常行李详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:10 <br>
     *
     * @param accidentId 事故单号
     * @return
     */
    public Map<String, Object> getPkgDetailInfo(String accidentId);

    /**
     * Title：pkgCase <br>
     * Description： 事故单结案<br>
     * author：王建文 <br>
     * date：2020-3-22 15:43 <br>
     *
     * @param accidentIds 事故单id
     * @return
     */
    public void pkgCase(String[] accidentIds);

    /**
     * Title：getPkgInfoPage <br>
     * Description：后台异常行李列表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-22 17:16 <br>
     *
     * @param queryParamDto 查询参数
     * @return
     */
    public PsiPage<PkgPageInfoVo> getPkgInfoPage(PkgInfoQueryParamDto queryParamDto);



     /**
     * Description:[出港破损list查询]
     * @param
     * @return
     * <AUTHOR>
     * @Date 2021-3-17 15:28
     **/
    public List<DepartureDamagedPkgReportVo> getDepartureDamagedPkgInfoList(PkgInfoQueryParamDto queryParamDto);



    /**
     * Description:[进港破损list查询]
     * @param
     * @return
     * <AUTHOR>
     * @Date 2021-3-17 15:28
     **/
    public List<ArrivalDamagedPkgReportVo> getArrivalDamagedPkgInfoList(PkgInfoQueryParamDto queryParamDto);


    /**
     * Description:[进港破损list查询]
     * @param
     * @return
     * <AUTHOR>
     * @Date 2021-3-17 15:28
     **/
    public List<LossInternalItemsPkgReportVo> getLossInternalItemsPkgInfoList(PkgInfoQueryParamDto queryParamDto);


    /**
     * Description:[进港少收list查询]
     * @param
     * @return
     * <AUTHOR>
     * @Date 2021-3-17 15:28
     **/
    public List<UnderChargePkgReportVo> getUnderChargePkgInfoList(PkgInfoQueryParamDto queryParamDto);
    /**
     * Title：deleteDraftPkg <br>
     * Description： 删除草稿箱行李<br>
     * author：王建文 <br>
     * date：2020-4-8 15:56 <br>
     * @param  accidentIds 事故单号
     * @return
     */
    public void deleteDraftPkg(String accidentIds);
    /**
     * Title：savePkgInfoByExcel <br>
     * Description： 异常行李数据导入存储<br>
     * author：王建文 <br>
     * date：2020-4-20 16:33 <br>
     * @param  pkgInfoDto 行李信息
     * @param  paxInfoParseVo 旅客信息
     */
    public void savePkgInfoByExcel(PkgInfoDto pkgInfoDto, PaxInfoParseVo paxInfoParseVo) throws Exception;
    /**
     * Title：getExportPkgInfo <br>
     * Description： 异常行李数据导出<br>
     * author：王建文 <br>
     * date：2020-4-21 16:23 <br>
     * @param  queryParamDto 查询参数
     * @return
     */
    public List<PkgExportInfoVo> getExportPkgInfo(PkgInfoQueryParamDto queryParamDto);
    /**
     * Title：pkgOrderDetailInfo <br>
     * Description： 异常行李赔付单号<br>
     * author：王建文 <br>
     * date：2020-5-20 15:52 <br>
     * @param  orderId 赔付单号
     * @return
     */
    public PkgOrderDetailVo pkgOrderDetailInfo(String orderId);
    
    /**   
     * Title: pkgAuditTypeJudge
     * param: [PkgAuditParamInfoDto]
     * @return com.swcares.psi.luggage.vo.PkgAuditTypeJudgeVo       
     * Description: 异常行李审核流程判断
     * author: makai
     * date: 2020-09-17
     */ 
    public PkgAuditTypeJudgeVo pkgAuditTypeJudge(PkgAuditParamInfoDto PkgInfoDto)throws BusinessException;

    /**
     * Title：updatePkgInfoImg <br>
     * Description： 更新异常行李图片<br>
     * author：王建文 <br>
     * date：2020-8-26 11:02 <br>
     * @param  accidentId 事故单号
     * @param  imgUrl 图片地址
     * @return
     */
    public void updatePkgInfoImg(String accidentId,String imgUrl)throws Exception;

 /**
  * Title：parseData <br>
  * Description： 解析异常行李数据<br>
  * author：王建文 <br>
  * date：2020-4-20 14:49 <br>
  *
  * @param data         excel表格数据
  * @param accidentType 1破损行李,2少收行李，3多收行李，4内件缺失行李
  * @param workAirport 当前登录用户工作航站
  * @return
  */
    public Map<String, Object> parseData(List<Map<String, Object>> data, String accidentType, String workAirport) throws Exception ;

    /**
     * Title：judgeIsExit <br>
     * Description： 同一行李号同一类型判断是否重复,已结案不计算<br>
     * author：王建文 <br>
     * date：2020-3-22 9:19 <br>
     *
     * @param pkgNo   行李号
     * @param pkgType 行李类型
     * @return
     */
    boolean judgeIsExit(String pkgNo, String pkgType,boolean flag);

    /**
     * 获取赔偿单列表
     * @param
     * @return
     */
    PsiPage<PkgOrderPageVo> getOrderList(PsiPage<PkgOrderPageVo> page, PkgOrderPageDto pkgOrderPageDto);

    /**
     * 修改赔偿单信息
     * @param pkgUpdateParamInfoDto
     * @return
     */
    String updatePkgOrder(PkgUpdateParamInfoDto pkgUpdateParamInfoDto);

    /**
     * 获取PC端完整的事故单详情
     * @param accidentId
     * @return
     */
    PkgAccidentDetailInfoVos getPkgAccidentDetail(String accidentId);

    /**
     * 修改事故单
     * @param pkgInfoDto
     * @return
     */
    String updateAccident(PkgUpdateInfoDto pkgInfoDto) throws Exception;

    /**
     * 获取快速检索信息列表
     * @param page
     * @param pkgQuickSearchDto
     * @return
     */
    PkgQuickSearchVos listPkgQuickSearch(PsiPage<PkgQuickSearchVos> page, PkgQuickSearchDto pkgQuickSearchDto);

    /**
     * 事故单结案
     * @param pkgAccidentCloseDto
     * @return
     */
    Boolean accidentClose(PkgAccidentCloseDto pkgAccidentCloseDto);

    /**
     * 获取赔偿单打印数据
     * @param orderId
     * @return
     */
    Map<String, Object> getOrderPrintInfo(String orderId);

    /**
     * 获取图片打印列表数据
     * @param accidentId
     * @return
     */
    List<Map<String, Object>> getImgPrintInfo(String accidentId, HttpServletRequest request);

    /**
     * 获取打印聊天记录列表数据
     * @param accident
     * @return
     */
    Map<String, Object> downLoadCommunicate(String accident);

    /**
     * 判断当前行李号是否已经创建了事故单
     * @param pkgNo
     * @return
     */
    Integer judgePkgNo(String pkgNo,String accidentType,String flightNo,String flightDate);

    /**
     * 获取订单
     * @param orderId
     * @return
     */
    OrderInfo getOrderInfoByOrderId(String orderId);

    /**
     * 通过事故单id获取赔偿单id
     * @param accidentId
     * @return
     */
    List<String> getOrderIdByAccidentId(String accidentId);

    /**
     * 事故单批量结案
     * @param pkgAccidentCloseDto
     * @return
     */
    Boolean accidentCloseList(PkgAccidentCloseListDto pkgAccidentCloseDto);


    /**
     * 手工录入编辑
     * @param
     * @return
     */
    String updateAccidentArtificial(PkgUpdateInfoDto pkgInfoDto) throws Exception;


    FltFlightRealInfoVo getFlightInfo(String flightNumber, String flightDate, String org, String dst);

    Boolean haveOnLineBelow(String accidentId);
}
