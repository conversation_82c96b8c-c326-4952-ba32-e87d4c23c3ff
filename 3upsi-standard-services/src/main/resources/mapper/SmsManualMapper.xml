<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.modules.sms.mapper.SmsManualMapper">

    <sql id="paxInfoSql">
        SELECT
        IFNULL(CONT.ID,PAX.ID) AS id,
        PAX.ID AS paxId,
        IFNULL( PAX.PASSENGER_NAME, PAX.PASSENGER_NAME_EN ) AS paxName,
        GROUP_CONCAT(CONT.PHONE_NUMBER) AS phoneNo,
        PAX.ID_NUMBER AS idNo,
        PAX.FLIGHT_SEGMENT AS segment,
        PAX.ORG AS org,
        PAX.DST AS dst,
        DATE_FORMAT(PAX.FLIGHT_DATE,'%Y-%m-%d') AS flightDate,
        DATE_FORMAT(PAX.PNR_CREATE_DATE,'%Y-%m-%d %H:%i:%s') AS buyTicketDate,
        PAX.IS_CANCEL AS isCancel,
        CASE PAX.IS_CANCEL WHEN 'Y' THEN '是' WHEN 'N' THEN '否' END AS isCancelName,
        PAX.TICKET_NUMBER AS tktNo,
        PAX.CHECK_STATUS AS checkStatus,
        IF( PAX.CHECK_STATUS = 'AC', '是', '否' ) AS checkStatusName,
        IFNULL(PAX.CARRY_CABIN,PAX.SUB_CABIN) AS cabin,
        PAX.FLIGHT_NUMBER AS flightNo,
        PAX.PNR_REF AS pnr
        FROM
        flt_passenger_real_info PAX
        LEFT JOIN flt_passenger_contacts CONT ON PAX.ID = CONT.PASSR_ID
        WHERE
        1 = 1
    </sql>
    <select id="getCovid19PaxInfo" resultType="com.swcares.psi.base.data.api.vo.SmsFlightPaxSearchVo">
        SELECT
        PAX.ID AS id,
        PAX.ID AS paxId,
        IFNULL( PAX.PASSENGER_NAME, PAX.PASSENGER_NAME_EN ) AS paxName,
        PAX.ID_NUMBER AS idNo,
        PAX.FLIGHT_SEGMENT AS segment,
        PAX.ORG AS org,
        PAX.DST AS dst,
        DATE_FORMAT(PAX.FLIGHT_DATE,'%Y-%m-%d') AS flightDate,
        DATE_FORMAT(PAX.PNR_CREATE_DATE,'%Y-%m-%d %H:%i:%s') AS buyTicketDate,
        PAX.IS_CANCEL AS isCancel,
        CASE PAX.IS_CANCEL WHEN 'Y' THEN '是' WHEN 'N' THEN '否' END AS isCancelName,
        PAX.TICKET_NUMBER AS tktNo,
        PAX.CHECK_STATUS AS checkStatus,
        IF( PAX.CHECK_STATUS = 'AC', '是', '否' ) AS checkStatusName,
        IFNULL(PAX.CARRY_CABIN,PAX.SUB_CABIN) AS cabin,
        PAX.FLIGHT_NUMBER AS flightNo,
        PAX.PNR_REF AS pnr
        FROM
        flt_passenger_real_info PAX
        WHERE
        1 = 1
        AND  (PAX.IS_CANCEL='N' or PAX.IS_CANCEL IS null)
        AND  PAX.TICKET_NUMBER is not null
        AND  PAX.FLIGHT_NUMBER = #{flightNo}
        AND  PAX.FLIGHT_DATE  =  DATE_FORMAT(#{flightDate},'%Y-%m-%d')
        AND  PAX.ORG  =  #{org}
        AND  PAX.DST  =  #{dst}

    </select>
    <select id="getCovid19FlightInfoByDateAndNo" resultType="com.swcares.psi.base.data.api.vo.SmsFlightSearchVo">
        select
        flight.ID,
        flight.FLIGHT_NUMBER as flightNo,
        DATE_FORMAT(flight.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        flight.ORG,
        flight.DST,
        flight.STD
        from flt_flight_real_info flight
        <where>
            (flight.FLIGHT_STATE  <![CDATA[ <> ]]> 'C' or flight.FLIGHT_STATE  is  null )
            and flight.FLIGHT_NUMBER=#{flightNo}
            and flight.FLIGHT_DATE=DATE_FORMAT(#{flightDate} ,'%Y-%m-%d')
        </where>
    </select>
    <select id="getCovid19FlightInfoById" resultType="com.swcares.psi.base.data.api.vo.SmsFlightSearchVo">
        select
        flight.ID,
        flight.FLIGHT_NUMBER as flightNo,
        DATE_FORMAT(flight.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        flight.ORG,
        flight.DST,
        flight.STD
        from flt_flight_real_info flight
        <where>
            (flight.FLIGHT_STATE  <![CDATA[ <> ]]> 'C' or flight.FLIGHT_STATE  is  null )
            and flight.ID IN
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="getCovid19FlightInfo" resultType="com.swcares.psi.base.data.api.vo.SmsFlightSearchVo">
        select
        flight.ID,
        flight.FLIGHT_NUMBER as flightNo,
        DATE_FORMAT(flight.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        flight.ORG,
        flight.DST,
        flight.STD,
        port_org.AIRPORT_NAME as orgName,
        port_dst.AIRPORT_NAME as dstName
        from flt_flight_real_info flight
        left join sys_airport_info port_org on port_org.CODE=flight.ORG
        left join sys_airport_info port_dst on port_dst.CODE=flight.DST
        <where>
            flight.FLIGHT_DATE=DATE_FORMAT(#{flightDate},'%Y-%m-%d')
            and  (flight.FLIGHT_STATE  != 'C' or flight.FLIGHT_STATE is null )
            <if test="orgList != null and orgList.size()>0">
                  AND   flight.ORG IN
                    <foreach collection="orgList" item="org" open="(" close=")" separator=",">
                         #{org}
                    </foreach>
            </if>
            <if test="dstList != null and dstList.size()>0">
                    AND flight.DST IN
                    <foreach collection="dstList" item="dst" open="(" close=")" separator=",">
                        #{dst}
                    </foreach>
            </if>
        </where>

    </select>

    <select id="getPaxPage" resultType="com.swcares.psi.base.data.api.vo.SmsFlightPaxSearchVo">
        <include refid="paxInfoSql"></include>
        AND PAX.TICKET_NUMBER is not null
        AND PAX.TICKET_NUMBER !=''
        <if test="paramDto.flightNo != null and paramDto.flightNo != ''">
            AND  PAX.FLIGHT_NUMBER = #{paramDto.flightNo}
        </if>
        <if test="paramDto.paxSearch != null and paramDto.paxSearch != ''">
            AND  (PAX.TICKET_NUMBER = #{paramDto.tktNo} OR PAX.ID_NUMBER = #{paramDto.paxSearch})
        </if>
        <if test="paramDto.paxName != null and paramDto.paxName != ''">
            AND  (PAX.PASSENGER_NAME LIKE CONCAT('%',#{paramDto.paxName},'%') OR PAX.PASSENGER_NAME_EN LIKE CONCAT('%',#{paramDto.paxName},'%'))
        </if>
        <if test="paramDto.checkStatus != null and paramDto.checkStatus == 2">
            AND  (PAX.CHECK_STATUS <![CDATA[ <> ]]> 'AC' OR  PAX.CHECK_STATUS IS NULL)
        </if>
        <if test="paramDto.checkStatus != null and paramDto.checkStatus == 1">
            AND  PAX.CHECK_STATUS = 'AC'
        </if>
        <if test="paramDto.org != null and paramDto.org != ''">
            AND  PAX.ORG = #{paramDto.org}
        </if>
        <if test="paramDto.dst != null and paramDto.dst != ''">
            AND  PAX.DST = #{paramDto.dst}
        </if>
        <if test="paramDto.buyTicketStartDate != null and paramDto.buyTicketStartDate != ''">
            AND  PAX.PNR_CREATE_DATE <![CDATA[ >= ]]> CONCAT(#{paramDto.buyTicketStartDate},' 00:00:00')
        </if>
        <if test="paramDto.buyTicketEndDate != null and paramDto.buyTicketEndDate != ''">
            AND  PAX.PNR_CREATE_DATE <![CDATA[ <= ]]> CONCAT(#{paramDto.buyTicketEndDate},' 23:59:59')
        </if>
        <if test="paramDto.flightStartDate != null and paramDto.flightStartDate != ''">
            AND  PAX.FLIGHT_DATE  <![CDATA[ >= ]]>  CONCAT(#{paramDto.flightStartDate},' 00:00:00')
        </if>
        <if test="paramDto.flightEndDate != null and paramDto.flightEndDate != ''">
            AND  PAX.FLIGHT_DATE  <![CDATA[ <= ]]>  CONCAT(#{paramDto.flightEndDate},' 23:59:59')
        </if>
        <if test="paramDto.isCancel == null or paramDto.isCancel == ''">
            AND PAX.IS_CANCEL = 'N'
        </if>
            GROUP BY PAX.ID
            ORDER BY CONT.PHONE_NUMBER DESC
    </select>
    <select id="getSysManualPage" resultType="com.swcares.psi.modules.sms.vo.SmsManualPageVo">
        SELECT * FROM (
            SELECT
            MAN.ID,
            MAN.SMS_TYPE AS smsType,
            CASE MAN.SMS_TYPE WHEN '0' THEN '通知提醒' WHEN '1' THEN '增值服务' WHEN '2' THEN '调查问卷' END AS smsTypeName,
            MAN.SMS_SERVICE_TYPE AS smsServiceType,
            CASE MAN.SMS_SERVICE_TYPE WHEN '0' THEN '航班调整' WHEN '1' THEN '服务补偿'
            WHEN '2' THEN '商务礼遇' WHEN '3' THEN '重点旅客' WHEN '4' THEN '其他' WHEN '5' THEN '疫情提示'
            WHEN '6' THEN '关注航班' WHEN '7' THEN '关注旅客' WHEN '8' THEN '贵宾短信' END AS smsServiceTypeName,
            MAN.SMS_MISSION_NAME,
            MAN.SMS_MODEL_NAME,
            MAN.SMS_MODEL_CONTENT,
            DATE_FORMAT(MAN.CREATE_TIME,'%Y-%m-%d %H:%i:%s') AS createTime,
            get_user_name(MAN.CREATE_USER) AS createUser,
            DATE_FORMAT(MAN.COMPLETE_TIME,'%Y-%m-%d %H:%i:%s') AS completeTime,
            CASE WHEN
             MAN.INITIAL_FLAG=0 AND MAN.COMPLETE_TIME IS NULL THEN '0'
            WHEN
             MAN.INITIAL_FLAG=1 AND MAN.COMPLETE_TIME IS NULL THEN '1'
            WHEN
            MAN.COMPLETE_TIME IS NOT NULL AND MAN.SEND_SMS_TOTAL_ACTUAL <![CDATA[>]]> 0  and MAN.SEND_SMS_TOTAL_EXPECT = MAN.SEND_SMS_TOTAL_ACTUAL THEN '2'
            WHEN
            MAN.COMPLETE_TIME IS NOT NULL AND MAN.SEND_SMS_TOTAL_ACTUAL <![CDATA[>]]> 0  and MAN.SEND_SMS_TOTAL_EXPECT <![CDATA[>]]> MAN.SEND_SMS_TOTAL_ACTUAL THEN '3'
            WHEN
            MAN.COMPLETE_TIME IS NOT NULL AND MAN.SEND_SMS_TOTAL_ACTUAL = 0  THEN '4'
            END AS missionStatus
            FROM
            sms_manual AS MAN
        WHERE
            1=1
        <if test="paramDto.createStartTime != null and paramDto.createStartTime != ''">
            AND  MAN.CREATE_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.createStartTime},' 00:00:00')
        </if>
        <if test="paramDto.createEndTime != null and paramDto.createEndTime != ''">
            AND  MAN.CREATE_TIME <![CDATA[ <= ]]>  CONCAT(#{paramDto.createEndTime},' 23:59:59')
        </if>

        <if test="paramDto.smsServiceType != null and paramDto.smsServiceType != ''">
            AND MAN. SMS_SERVICE_TYPE = #{paramDto.smsServiceType}
        </if>
        <if test="paramDto.smsMissionName != null and paramDto.smsMissionName != ''">
            AND  MAN.SMS_MISSION_NAME = #{paramDto.smsMissionName}
        </if>
        <if test="paramDto.completeStartTime != null and paramDto.completeStartTime != ''">
            AND  MAN.COMPLETE_TIME IS NOT NULL
            AND  MAN.COMPLETE_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.completeEndTime},' 00:00:00')
        </if>
        <if test="paramDto.completeEndTime != null and paramDto.completeEndTime != ''">
            AND  MAN.COMPLETE_TIME IS NOT NULL
            AND  MAN.COMPLETE_TIME <![CDATA[ <= ]]>  CONCAT(#{paramDto.completeEndTime},' 23:59:59')
        </if>
        ORDER BY MAN.CREATE_TIME DESC
        )AS A
        WHERE 1=1
        <if test="paramDto.missionStatus != null and paramDto.missionStatus != ''">
            AND A.missionStatus IN
            <foreach collection="paramDto.missionStatusList" item="missionStatus" index="index" open="(" close=")" separator=",">
                #{missionStatus}
            </foreach>
        </if>
        <if test="paramDto.createUser != null and paramDto.createUser != ''">
            AND  A.createUser LIKE  concat('%',#{paramDto.createUser},'%')
        </if>
    </select>
    <sql id="getSysPax" >
        SELECT
            SPI.ID AS id,
            SPI.PAX_NAME AS paxName,
            SPI.PHONE_NO AS phoneNo,
            SPI.FLIGHT_NO AS flightNo,
            DATE_FORMAT(SPI.FLIGHT_DATE,'%Y-%m-%d') AS flightDate,
            PAX.FLIGHT_SEGMENT AS segment,
            CASE SPI.SEND_STATUS WHEN '0' THEN '待发送' WHEN '1' THEN '成功' WHEN '2' THEN '失败' END AS sendStatusName,
            SPI.SEND_STATUS  AS sendStatus,
            SSR.ERROR_INFO AS remark,
            SSR.MSG_CONTENT AS smsContent,
            SPI.TKT_NO AS tktNo,
            DATE_FORMAT(SSR.SEND_TIME,'%Y-%m-%d %H:%i:%s') as sendTime
        FROM
            SMS_PAX_INFO SPI
        LEFT JOIN FLT_PASSENGER_REAL_INFO PAX ON SPI.PAX_ID = PAX.ID
        LEFT JOIN SMS_SEND_RECORD SSR ON SSR.ID = SPI.SMS_RECORD_ID
        WHERE
            1 = 1
        and spi.phone_no &lt;&gt; ''
        AND   SPI.MANUAL_ID = #{paramDto.id}
        <if test="paramDto.sendStatus != null and paramDto.sendStatus != ''">
            AND SPI.SEND_STATUS IN
            <foreach collection="paramDto.sendStatusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
    </sql>

    <select id="getSysPaxPage" resultType="com.swcares.psi.modules.sms.vo.SmsManualExportVo">
        <include refid="getSysPax"/>
    </select>

    <select id="exportPaxPage" resultType="com.swcares.psi.modules.sms.vo.SmsManualExportVo">
        <include refid="getSysPax"/>
    </select>
    <select id="smsCovid19Count" resultType="com.swcares.psi.base.data.api.vo.SmsCovid19CountVo">
        SELECT
            MAN.SEND_PAX_TOTAL_EXPECT as allNumber,
            MAN.SEND_PAX_TOTAL_ACTUAL as successNumber,
            MAN.SEND_SMS_TOTAL_EXPECT as `all`,
            MAN.SEND_SMS_TOTAL_ACTUAL as success
         FROM
            sms_manual AS MAN
        where MAN.ID=#{id}
    </select>


    <select id="smsCovid19CountTble" resultType="com.swcares.psi.base.data.api.vo.SmsCovid19CountVo">
            SELECT
            sum(A.allNumber) as allNumber,
            sum(A.successNumber) as successNumber,
            sum(A.all) as `all`,
            sum(A.success) as success
            FROM (
            SELECT
            MAN.SEND_PAX_TOTAL_EXPECT as allNumber,
            MAN.SEND_PAX_TOTAL_ACTUAL as successNumber,
            MAN.SEND_SMS_TOTAL_EXPECT as `all`,
            MAN.SEND_SMS_TOTAL_ACTUAL as success,
            get_user_name(MAN.CREATE_USER) AS createUser,
            CASE WHEN
            MAN.INITIAL_FLAG=0 AND MAN.COMPLETE_TIME IS NULL THEN '0'
            WHEN
            MAN.INITIAL_FLAG=1 AND MAN.COMPLETE_TIME IS NULL THEN '1'
            WHEN
            MAN.COMPLETE_TIME IS NOT NULL AND MAN.SEND_SMS_TOTAL_ACTUAL > 0  and MAN.SEND_SMS_TOTAL_EXPECT = MAN.SEND_SMS_TOTAL_ACTUAL THEN '2'
            WHEN
            MAN.COMPLETE_TIME IS NOT NULL AND MAN.SEND_SMS_TOTAL_ACTUAL >0  and MAN.SEND_SMS_TOTAL_EXPECT > MAN.SEND_SMS_TOTAL_ACTUAL THEN '3'
            WHEN
            MAN.COMPLETE_TIME IS NOT NULL AND MAN.SEND_SMS_TOTAL_ACTUAL = 0  THEN '4'
            END AS missionStatus
            FROM
            sms_manual AS MAN
            WHERE
            1=1
            <if test="paramDto.createStartTime != null and paramDto.createStartTime != ''">
                AND  MAN.CREATE_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.createStartTime},' 00:00:00')
            </if>
            <if test="paramDto.createEndTime != null and paramDto.createEndTime != ''">
                AND  MAN.CREATE_TIME <![CDATA[ <= ]]>  CONCAT(#{paramDto.createEndTime},' 23:59:59')
            </if>

            <if test="paramDto.smsServiceType != null and paramDto.smsServiceType != ''">
                AND MAN. SMS_SERVICE_TYPE = #{paramDto.smsServiceType}
            </if>
            <if test="paramDto.smsMissionName != null and paramDto.smsMissionName != ''">
                AND  MAN.SMS_MISSION_NAME = #{paramDto.smsMissionName}
            </if>
            <if test="paramDto.completeStartTime != null and paramDto.completeStartTime != ''">
                AND  MAN.COMPLETE_TIME IS NOT NULL
                AND  MAN.COMPLETE_TIME <![CDATA[ >= ]]>  CONCAT(#{paramDto.completeEndTime},' 00:00:00')
            </if>
            <if test="paramDto.completeEndTime != null and paramDto.completeEndTime != ''">
                AND  MAN.COMPLETE_TIME IS NOT NULL
                AND  MAN.COMPLETE_TIME <![CDATA[ <= ]]>  CONCAT(#{paramDto.completeEndTime},' 23:59:59')
            </if>
            ORDER BY MAN.CREATE_TIME DESC
            )AS A
            WHERE 1=1
            <if test="paramDto.missionStatus != null and paramDto.missionStatus != ''">
                AND A.missionStatus IN
                <foreach collection="paramDto.missionStatusList" item="missionStatus" index="index" open="(" close=")" separator=",">
                    #{missionStatus}
                </foreach>
            </if>
            <if test="paramDto.createUser != null and paramDto.createUser != ''">
                AND  A.createUser LIKE  concat('%',#{paramDto.createUser},'%')
            </if>


    </select>

</mapper>
