package com.swcares.psi.ao.manySeats.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.ao.manySeats.dto.AoManySeatsPriceSaveDto;
import com.swcares.psi.ao.manySeats.dto.QueryManySeatsPriceDto;
import com.swcares.psi.ao.manySeats.entity.AoManySeatsPrice;
import com.swcares.psi.ao.manySeats.vo.AoManySeatsPriceInfoVo;
import com.swcares.psi.ao.manySeats.vo.AoManySeatsPriceVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

public interface AoManySeatsPriceService  extends IService<AoManySeatsPrice> {

    /**
     * 价格配置保存修改
     * @param dto
     */
    void saveOrUpdateEntity(List<AoManySeatsPriceSaveDto>  dto);

    /**
     * 价格配置列表查询
     * @param dto
     * @return
     */
    PsiPage<AoManySeatsPriceVo> getPage(QueryManySeatsPriceDto dto);
    List<AoManySeatsPriceVo> getList(QueryManySeatsPriceDto dto);

    /**
     * 根据航段查询价格
     * @param org
     * @param dst
     * @param status
     * @return
     */
    AoManySeatsPriceInfoVo getOne(String org, String dst,String status);
}
