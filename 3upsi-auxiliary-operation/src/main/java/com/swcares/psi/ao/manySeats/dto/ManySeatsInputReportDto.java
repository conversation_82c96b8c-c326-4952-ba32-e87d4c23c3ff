package com.swcares.psi.ao.manySeats.dto;

import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/8 13:50
 */
@Data
public class ManySeatsInputReportDto extends BaseDto {
    @ApiModelProperty(value = "订单起始日期")
    private String orderBeginDate;

    @ApiModelProperty(value = "订单结束日期")
    private String orderEndDate;

    @ApiModelProperty(value = "航班起始日期")
    private String flightBeginDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "出发地")
    private String org;

    @ApiModelProperty(value = "目的地")
    private String dst;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "行政区域")
    private String areaCompany;

}
