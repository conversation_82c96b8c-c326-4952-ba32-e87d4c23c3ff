<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.mapper.MealsSpecialMachineSupplyMapper">

    <select id="queryByMileageId" parameterType="Long" resultType="com.swcares.psi.ao.model.MealsSpecialMachineSupply">
        select
            msms.id id,
            msms.ticket_number ticketNumber,
            msms.mileage_id mileageId,
            msms.km km,
            msms.flight_no flightNo,
            msms.flight_date flightDate,
            msms.org org,
            msms.dst dst,
            msms.std std,
            msms.service_status serviceStatus,
            msms.special_category specialCategory,
            msms.is_cancel isCancel,
            msms.meals_code mealsCode,
            msms.meals_name mealsName,
            msms.create_time createTime,
            msms.update_time updateTime
        from meals_special_machine_supply msms
        where
        1 = 1
        <if test="mileageId != null and mileageId != ''">
            and msms.mileage_id = #{mileageId}
        </if>
        limit 1
    </select>
    <select id="queryMileageConversion" resultType="com.swcares.psi.ao.model.vo.MealsSpecialMachineSupplyVO">
        select
        msms.update_time,
            case
                when fpri.PASSENGER_NAME is null then fpri.PASSENGER_NAME_EN else fpri.PASSENGER_NAME
            end name,
            fpri.ID paxId,
            msms.ticket_number ticketNumber,
            msms.mileage_id mileageId,
            msms.service_status serviceStatus,
            case
                when fpri.IS_CANCEL = 'Y' or ffri.FLIGHT_STATE = 'C' then '0'
                when ffri.FLIGHT_STATE != 'C' and fpri.IS_CANCEL = 'N' then '1'
            end Status
        from meals_special_machine_supply msms
        inner join flt_passenger_real_info fpri on
	                msms.ticket_number = fpri.TICKET_NUMBER
	                and msms.flight_no = fpri.FLIGHT_NUMBER
	                and msms.flight_date = fpri.FLIGHT_DATE
	                and msms.org = fpri.ORG
	                and msms.dst = fpri.DST
        left join flt_flight_real_info ffri on
	                msms.flight_no = ffri.FLIGHT_NUMBER
	                and msms.flight_date = ffri.FLIGHT_DATE
	                and msms.org = ffri.ORG
	                and msms.dst = ffri.DST
        where msms.special_category = 'C'
        and msms.UPDATE_TIME is not null and (msms.SYNC_UPDATE_TIME is null or msms.UPDATE_TIME &lt;&gt; msms.SYNC_UPDATE_TIME)
    </select>
    <select id="queryPayOrderMeals" resultType="com.swcares.psi.ao.model.vo.MealsSpecialMachineSupplyVO">
        select
            msms.update_time,
            case
                when fpri.PASSENGER_NAME is null then fpri.PASSENGER_NAME_EN else fpri.PASSENGER_NAME
            end name,
            fpri.ID paxId,
            msms.ticket_number ticketNumber,
            msms.mileage_id mileageId,
            msms.service_status serviceStatus,
            case
                when fpri.IS_CANCEL = 'Y' or ffri.FLIGHT_STATE = 'C' then '0'
                when ffri.FLIGHT_STATE != 'C' and fpri.IS_CANCEL = 'N' then '1'
            end status
        from meals_special_machine_supply msms
        inner join flt_passenger_real_info fpri on
	                msms.ticket_number = fpri.TICKET_NUMBER
	                and msms.flight_no = fpri.FLIGHT_NUMBER
	                and msms.flight_date = fpri.FLIGHT_DATE
	                and msms.org = fpri.ORG
	                and msms.dst = fpri.DST
        left join flt_flight_real_info ffri on
	                msms.flight_no = ffri.FLIGHT_NUMBER
	                and msms.flight_date = ffri.FLIGHT_DATE
	                and msms.org = ffri.ORG
	                and msms.dst = ffri.DST
        where msms.special_category = 'P'
        and msms.UPDATE_TIME is not null and (msms.SYNC_UPDATE_TIME is null or msms.UPDATE_TIME &lt;&gt; msms.SYNC_UPDATE_TIME)
    </select>
    <select id="queryByPaxIdAndFlightAndCategory" parameterType="com.swcares.psi.ao.model.dto.PadConfirmMealsDto" resultType="com.swcares.psi.ao.model.MealsSpecialMachineSupply">
        select
        msms.*
        from meals_special_machine_supply msms,flt_passenger_real_info fpri
        where
        1 = 1 and msms.is_cancel = 'N' and msms.flight_no=fpri.flight_number and msms.flight_date=fpri.flight_date and msms.ticket_number=fpri.ticket_number
        <if test="dto.paxId != null and dto.paxId != ''">
            and fpri.id = #{dto.paxId}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and msms.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightDate != null and dto.flightDate != ''">
            and msms.flight_date = #{dto.flightDate}
        </if>
        <!--
        <if test="dto.org != null and dto.org != ''">
            and msms.org = #{dto.org}
        </if>
        <if test="dto.dst != null and dto.dst != ''">
            and msms.dst = #{dto.dst}
        </if>
        -->
        <if test="dto.specialCategory != null and dto.specialCategory != ''">
            and msms.special_category = #{dto.specialCategory}
        </if>
    </select>

    <update id="updateSyncUpdateTime">
        update meals_special_machine_supply set sync_update_time = #{updateTime} where MILEAGE_ID = #{mealId}
    </update>
</mapper>