<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.overweightbkg.mapper.AoOverweightReportMapper">

    <sql id="reportSelect">
        SELECT (<include refid="com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper.getPaxHedCategories"></include>) as paxTypeDesc,
        pax.FQTV_NUMBER as ffpNumber,
        date_format(aoi.ORDER_DATE,'%Y-%m-%d') as orderDate,
        aoi.ORDER_NO,
        aoi.COMPLETE_DEPARTMENT_INFO,
        aoi.BANK_ORDER_NO,
        aoi.FINANCE_ORDER_NO,
        date_format(aoi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        aoi.FLIGHT_NO,
        aoi.ORG,
        aoi.DST,
        aoi.TKT_NO,
        aoi.SEGMENT_TYPE,
        aob.OVERWEIGHT_BKG_WEIGHT,
        aob.OVER_NUMBER_BKG_NUMBER_TOTAL as overNumberBkgNumber,
        CASE
        WHEN aob.BKG_PROPERTY = 'P' THEN '普通行李'
        WHEN aob.BKG_PROPERTY = 'T' THEN '特殊行李'
        ELSE aob.BKG_PROPERTY
        END AS bkgProperty,
        CASE aoi.CHARGE_TYPE
        WHEN  '0' THEN '微信'
        WHEN  '1' THEN '银行卡'
        WHEN  '2' THEN '支付宝'
        WHEN  '3' THEN '现金'
        WHEN  '4' THEN 'pos机'
        when '5' then 'worldPay'
        ELSE aoi.CHARGE_TYPE
        END AS chargeType,
        CASE apr.PAY_TYPE
        WHEN  '0' THEN
        '未支付'
        WHEN  '1' THEN
        '支付完成'
        WHEN  '2' THEN
        '支付失败'
        WHEN  '3' THEN
        '退款成功'
        WHEN  '4' THEN
        '退款失败'
        END AS payStatus,
        aoi.TURN_NUMBER,
        CASE apr.PAY_TYPE
            when '3' then
                concat('-',aoi.ORDER_PRICE)
            else aoi.ORDER_PRICE
        end as ORDER_PRICE,
        aoi.ORDER_PRICE AS totalPrice,
        CASE apr.PAY_TYPE
        WHEN  '0' THEN
        '未支付'
        WHEN  '1' THEN
        '支付完成'
        WHEN  '2' THEN
        '支付失败'
        WHEN  '3' THEN
        '退款成功'
        WHEN  '4' THEN
        '退款失败'
        END AS orderStatus,
        date_format(aoi.PAY_TIME,'%Y-%m-%d %H:%i:%s') AS payTime,
        aoi.PAX_NAME,
        aoi.CELL_CHANNEL,
        aoi.USER_NO,
        aoi.USER_NAME,
        aoi.DEPARTMENT,
        aoi.DEPARTMENT_ID,
        CASE
        WHEN aoi.TURN_STATUS = '0' THEN '成功'
        WHEN aoi.TURN_STATUS = '1' THEN '失败'
        ELSE aoi.TURN_STATUS
        END AS turnStatus,
        aoi.CURRENCY_SPECIES,

        case aoi.CURRENCY_SPECIES
        when 'CNY' THEN CONCAT(aoi.CURRENCY_SPECIES,'(人民币)')
        ELSE
        (SELECT CONCAT(aoi.CURRENCY_SPECIES,'(', DATA_VALUE,')') FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=aoi.CURRENCY_SPECIES limit 1)
        end AS currencySpeciesName,


        aoi.AREA_COMPANY,
        if(
        (SELECT DATA_CODE  FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0' AND (DATA_CODE = aoi.org OR DATA_CODE = aoi.dst) limit 1 ) IS null,'N','Y'
        ) as isGat,
        date_format(apr.OPERATION_DATE,'%Y-%m-%d %H:%i:%s')as OPERATION_DATE
        FROM
        ao_overweight_bkg aob
        LEFT JOIN
        ao_order_info aoi
        ON
        aob.ORDER_NO = aoi.ORDER_NO
        LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
        left join flt_passenger_real_info pax on pax.id=aoi.pax_id
    </sql>
    <sql id="reportWhere">
        <where>
            apr.PAY_TYPE  in ('1','3','4')
            <if test="aoOverWeightAccountsReportDto.departmentName != null and aoOverWeightAccountsReportDto.departmentName !='' ">
                AND aoi.DEPARTMENT like concat('%',#{aoOverWeightAccountsReportDto.departmentName},'%')
            </if>
            <if test="aoOverWeightAccountsReportDto.areaCompany != null and aoOverWeightAccountsReportDto.areaCompany !='' ">
                AND aoi.AREA_COMPANY like concat('%',#{aoOverWeightAccountsReportDto.areaCompany},'%')
            </if>
            <if test="aoOverWeightAccountsReportDto.flightStartDate != null and aoOverWeightAccountsReportDto.flightStartDate !='' ">
                AND aoi.FLIGHT_DATE &gt;= date_format(#{aoOverWeightAccountsReportDto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="aoOverWeightAccountsReportDto.flightEndDate != null and aoOverWeightAccountsReportDto.flightEndDate !='' ">
                AND aoi.FLIGHT_DATE &lt;= date_format(#{aoOverWeightAccountsReportDto.flightEndDate},'%Y-%m-%d')
            </if>

            <if test="aoOverWeightAccountsReportDto.orderStartDate != null and aoOverWeightAccountsReportDto.orderStartDate !=''">
                and aoi.ORDER_DATE &gt;= date_format(#{aoOverWeightAccountsReportDto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="aoOverWeightAccountsReportDto.orderEndDate != null and aoOverWeightAccountsReportDto.orderEndDate !='' ">
                and aoi.ORDER_DATE &lt;= date_format(#{aoOverWeightAccountsReportDto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="aoOverWeightAccountsReportDto.payBeginDate != null and aoOverWeightAccountsReportDto.payBeginDate !=''">
                and apr.OPERATION_DATE &gt;= date_format(#{aoOverWeightAccountsReportDto.payBeginDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="aoOverWeightAccountsReportDto.payEndDate != null and aoOverWeightAccountsReportDto.payEndDate !='' ">
                and apr.OPERATION_DATE &lt;= date_format(#{aoOverWeightAccountsReportDto.payEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="aoOverWeightAccountsReportDto.payType != null and aoOverWeightAccountsReportDto.payType !='' ">
                AND FIND_IN_SET(apr.PAY_TYPE,#{aoOverWeightAccountsReportDto.payType}) >0
            </if>
            <if test="aoOverWeightAccountsReportDto.flightNo != null and aoOverWeightAccountsReportDto.flightNo !='' ">
                AND aoi.FLIGHT_NO = #{aoOverWeightAccountsReportDto.flightNo}
            </if>
            <if test="aoOverWeightAccountsReportDto.orderNo != null and aoOverWeightAccountsReportDto.orderNo !='' ">
                AND aoi.ORDER_NO = #{aoOverWeightAccountsReportDto.orderNo}
            </if>
            <if test="aoOverWeightAccountsReportDto.bankOrderNo != null and aoOverWeightAccountsReportDto.bankOrderNo !='' ">
                AND aoi.BANK_ORDER_NO = #{aoOverWeightAccountsReportDto.bankOrderNo}
            </if>
            <if test="aoOverWeightAccountsReportDto.org != null and aoOverWeightAccountsReportDto.org !='' ">
                AND aoi.ORG = #{aoOverWeightAccountsReportDto.org}
            </if>
            <if test="aoOverWeightAccountsReportDto.dst != null and aoOverWeightAccountsReportDto.dst !='' ">
                AND aoi.DST = #{aoOverWeightAccountsReportDto.dst}
            </if>
            <if test="aoOverWeightAccountsReportDto.tktNo != null and aoOverWeightAccountsReportDto.tktNo !='' ">
                AND aoi.TKT_NO = #{aoOverWeightAccountsReportDto.tktNo}
            </if>
            <if test="aoOverWeightAccountsReportDto.segmentType != null and aoOverWeightAccountsReportDto.segmentType !='' ">
                AND aoi.SEGMENT_TYPE = #{aoOverWeightAccountsReportDto.segmentType}
            </if>
            <if test="aoOverWeightAccountsReportDto.chargeType != null and aoOverWeightAccountsReportDto.chargeType !='' ">
                AND FIND_IN_SET(aoi.CHARGE_TYPE,#{aoOverWeightAccountsReportDto.chargeType}) >0
            </if>
            <if test="aoOverWeightAccountsReportDto.payStatus != null and aoOverWeightAccountsReportDto.payStatus !='' ">
                AND FIND_IN_SET(aoi.PAY_STATUS,#{aoOverWeightAccountsReportDto.payStatus}) >0
            </if>
            <if test="aoOverWeightAccountsReportDto.cellChannel != null and aoOverWeightAccountsReportDto.cellChannel !='' ">
                AND aoi.CELL_CHANNEL = #{aoOverWeightAccountsReportDto.cellChannel}
            </if>
            <if test="aoOverWeightAccountsReportDto.turnNumber != null and aoOverWeightAccountsReportDto.turnNumber !='' ">
                AND aoi.TURN_NUMBER = #{aoOverWeightAccountsReportDto.turnNumber}
            </if>
            <if test="aoOverWeightAccountsReportDto.financeOrderNo != null and aoOverWeightAccountsReportDto.financeOrderNo !='' ">
                and aoi.FINANCE_ORDER_NO  like concat('%',#{aoOverWeightAccountsReportDto.financeOrderNo},'%')
            </if>
        </where>
    </sql>

    <sql id="payTypesSummaryMoneySql">
        select t.* from (
        SELECT
        sum(CASE WHEN rs.chargeType = '0' THEN rs.ORDER_PRICE ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN rs.chargeType = '1' THEN rs.ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
        sum(CASE WHEN rs.chargeType = '2'  THEN rs.ORDER_PRICE ELSE 0 end ) AS alipayMoney,
        sum(CASE WHEN (rs.chargeType = '3' or rs.chargeType = '4') THEN rs.ORDER_PRICE ELSE 0 end) AS cashMoney,
        sum(CASE WHEN rs.chargeType = '5' THEN rs.ORDER_PRICE ELSE 0 end) AS worldPay,
        sum(rs.ORDER_PRICE  ) AS allMoney,
        case  rs.CURRENCY_SPECIES
        when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
        else   concat(rs.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
        end as currencySpecies
        from(
        SELECT
        aoi.CURRENCY_SPECIES,
        aoi.CHARGE_TYPE AS chargeType,
        CASE apr.PAY_TYPE
        when '3' then
        concat('-',aoi.ORDER_PRICE)
        else aoi.ORDER_PRICE
        end as ORDER_PRICE,
        aoi.ORDER_PRICE AS totalPrice
        FROM
        ao_overweight_bkg aob
        LEFT JOIN
        ao_order_info aoi
        ON
        aob.ORDER_NO = aoi.ORDER_NO
        LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
        <include refid="reportWhere"></include>
        ) rs
        group by rs.CURRENCY_SPECIES
        ) t where t.allMoney > 0
    </sql>


    <select id="getOverweightAccountsReportPage" resultType="com.swcares.psi.ao.overweightbkg.vo.AoOverWeightAccountsReportVo">
        <include refid="reportSelect"></include>
        <include refid="reportWhere"></include>
    </select>

    <select id="getOverweightAccountsReportList" resultType="com.swcares.psi.ao.overweightbkg.vo.AoOverWeightAccountsReportVo">
        <include refid="reportSelect"></include>
        <include refid="reportWhere"></include>
    </select>

    <sql id="templateReportSql">
        SELECT
            aob.ORDER_NO,
            SUBSTRING(aoi.FINANCE_ORDER_NO,1,3) AS ticketType,
            SUBSTRING(aoi.FINANCE_ORDER_NO,-7,7) AS tktNo,
            aoi.BANK_ORDER_NO,
            aoi.ORG,
            aoi.DST,
            date_format(aoi.FLIGHT_DATE,'%Y%m%d') as flightDate,
            RIGHT(aoi.FLIGHT_NO,4) AS flightNo,
            aob.OVERWEIGHT_BKG_WEIGHT,
            aoi.ORDER_PRICE/aob.OVERWEIGHT_BKG_WEIGHT AS totalPrice,
            aoi.ORDER_PRICE AS denomination,
            aoi.AREA_COMPANY AS areaCompany,
            date_format(aoi.ORDER_DATE,'%Y%m%d') AS issueTime,
            SUBSTRING(aoi.TKT_NO,4,3) AS passengerTicketType,
            SUBSTRING(aoi.TKT_NO,1,3) AS passengerTicketCompanyCode,
            RIGHT(aoi.TKT_NO,7) AS passengerTktNo
        FROM
            ao_overweight_bkg aob
                LEFT JOIN
            ao_order_info aoi
            ON
                aob.ORDER_NO = aoi.ORDER_NO
        <where>
            aoi.ORDER_STATUS ='2'
            AND aoi.SEGMENT_TYPE = '国内' and aoi.CURRENCY_SPECIES='CNY'
            AND (SELECT DATA_CODE  FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0' AND (DATA_CODE = aoi.org OR DATA_CODE = aoi.dst) limit 1 ) is null
            <if test="aoOverweightInputTemplateReportDto.flightStartDate != null and aoOverweightInputTemplateReportDto.flightStartDate !='' ">
                AND aoi.FLIGHT_DATE &gt;= date_format(#{aoOverweightInputTemplateReportDto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="aoOverweightInputTemplateReportDto.flightEndDate != null and aoOverweightInputTemplateReportDto.flightEndDate !='' ">
                AND aoi.FLIGHT_DATE &lt;= date_format(#{aoOverweightInputTemplateReportDto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="aoOverweightInputTemplateReportDto.orderStartDate != null and aoOverweightInputTemplateReportDto.orderStartDate !='' ">
                AND  aoi.ORDER_DATE &gt;= date_format(#{aoOverweightInputTemplateReportDto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="aoOverweightInputTemplateReportDto.orderEndDate != null and aoOverweightInputTemplateReportDto.orderEndDate !='' ">
                AND  aoi.ORDER_DATE &lt;= date_format(#{aoOverweightInputTemplateReportDto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>
            <if test="aoOverweightInputTemplateReportDto.flightNo != null and aoOverweightInputTemplateReportDto.flightNo !='' ">
                AND aoi.FLIGHT_NO = #{aoOverweightInputTemplateReportDto.flightNo}
            </if>
            <if test="aoOverweightInputTemplateReportDto.org != null and aoOverweightInputTemplateReportDto.org !='' ">
                AND aoi.ORG = #{aoOverweightInputTemplateReportDto.org}
            </if>
            <if test="aoOverweightInputTemplateReportDto.dst != null and aoOverweightInputTemplateReportDto.dst !='' ">
                AND aoi.DST = #{aoOverweightInputTemplateReportDto.dst}
            </if>
            <if test="aoOverweightInputTemplateReportDto.orderNo != null and aoOverweightInputTemplateReportDto.orderNo !='' ">
                AND aoi.ORDER_NO = #{aoOverweightInputTemplateReportDto.orderNo}
            </if>
            <if test="aoOverweightInputTemplateReportDto.bankOrderNo != null and aoOverweightInputTemplateReportDto.bankOrderNo !='' ">
                AND aoi.BANK_ORDER_NO = #{aoOverweightInputTemplateReportDto.bankOrderNo}
            </if>
            <if test="aoOverweightInputTemplateReportDto.tktNo != null and aoOverweightInputTemplateReportDto.tktNo !='' ">
                AND RIGHT(aoi.TKT_NO,7) = #{aoOverweightInputTemplateReportDto.tktNo}
            </if>
            <if test="aoOverweightInputTemplateReportDto.areaCompany != null and aoOverweightInputTemplateReportDto.areaCompany !='' ">
                AND aoi.AREA_COMPANY  like concat('%',#{aoOverweightInputTemplateReportDto.areaCompany},'%')
            </if>
        </where>
    </sql>
    <select id="getAoOverweightInputTemplatePage" resultType="com.swcares.psi.ao.overweightbkg.vo.AoOverweightInputTemplateReportVo">
        <include refid="templateReportSql"></include>
    </select>
    <select id="getAoOverweightInputTemplateList" resultType="com.swcares.psi.ao.overweightbkg.vo.AoOverweightInputTemplateReportVo">
        <include refid="templateReportSql"></include>
    </select>
    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo">
        <include refid="payTypesSummaryMoneySql"></include>
    </select>
</mapper>