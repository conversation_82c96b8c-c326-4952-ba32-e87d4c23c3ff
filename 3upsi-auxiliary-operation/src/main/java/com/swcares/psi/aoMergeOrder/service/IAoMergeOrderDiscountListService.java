package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountListSaveDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountQueryDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOrderDiscountList;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

/**
 * <p>
 * 合并支付订单折扣清单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeOrderDiscountListService extends IService<AoMergeOrderDiscountList> {

    void saveConfig(AoMergeOrderDiscountListSaveDto dto);
    void deleteConfig(String id);

    PsiPage<AoMergeOrderDiscountListVo> getPage(AoMergeOrderDiscountQueryDto dto);
    List<AoMergeOrderDiscountListVo> getList(AoMergeOrderDiscountQueryDto dto);

}
