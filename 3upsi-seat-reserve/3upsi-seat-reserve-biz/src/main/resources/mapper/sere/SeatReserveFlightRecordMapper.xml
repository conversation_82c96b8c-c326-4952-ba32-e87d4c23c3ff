<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.sere.mapper.SeatReserveFlightRecordMapper">
    
    <resultMap type="com.swcares.psi.sere.entity.SeatReserveFlightRecord" id="SeatReserveFlightRecordResult">
        <result property="id"    			column="id"    />
        <result property="taskCode"    		column="task_code"    />
        <result property="flightTaskCode"   column="flight_task_code"    />
        <result property="flightId"    		column="flight_id"    />
        <result property="flightNumber"    	column="flight_number"    />
        <result property="flightDate"    	column="flight_date"    />
        <result property="org"    			column="org"    />
        <result property="dst"    			column="dst"    />
        <result property="peopleNumber"    	column="people_number"    />
        <result property="state"    		column="state"    />
        <result property="remark"    		column="remark"    />
        <result property="startTime"    	column="start_time"    />
        <result property="endTime"    		column="end_time"    />
    </resultMap>

    <sql id="selectSeatReserveFlightRecordVo">
        select id, task_code, flight_task_code, flight_id, flight_number, flight_date, org, dst, 
        	   people_number, state, remark, start_time, end_time from ser_seat_reserve_flight_record
    </sql>

    <select id="selectSeatReserveFlightRecordList" parameterType="com.swcares.psi.sere.entity.SeatReserveFlightRecord" resultMap="SeatReserveFlightRecordResult">
        <include refid="selectSeatReserveFlightRecordVo"/>
        <where>  
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="flightTaskCode != null  and flightTaskCode != ''"> and flight_task_code = #{flightTaskCode}</if>
            <if test="flightNumber != null  and flightNumber != ''"> and flight_number = #{flightNumber}</if>
            <if test="flightDate != null "> and flight_date = #{flightDate}</if>
            <if test="org != null  and org != ''"> and org = #{org}</if>
            <if test="dst != null  and dst != ''"> and dst = #{dst}</if>
            <if test="peopleNumber != null "> and people_number = #{peopleNumber}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>
    
    <select id="selectTaskFlight" parameterType="String" resultType="com.swcares.psi.sere.vo.FlightInfoVO" >
        SELECT t.* FROM (
        SELECT t.ID AS flight_id, t.FOC_ID, t.FLIGHT_DATE, t.FLIGHT_NUMBER, t.ORG, t.DST, t.STD, t.STA FROM flt_flight_real_info t
        WHERE
        <foreach collection="flightDate" close=")" open="(" item="item" separator="or">
            t.FLIGHT_DATE = #{item}
        </foreach>
        AND t.ATD IS NULL
        AND t.STD > SYSDATE()
        AND t.FLIGHT_TYPE = 'D'
        AND (t.FLIGHT_STATE != 'C' OR t.FLIGHT_STATE IS NULL)
        AND t.ORG IN (SELECT r.airport_code FROM ser_seat_reserve_rule r WHERE r.STATE = 1 AND r.deleted != 1 AND (r.FLIGHT_NUMBER IS NULL OR r.FLIGHT_NUMBER = ''))
        AND NOT EXISTS (SELECT 1 FROM ser_seat_reserve_rule r WHERE r.STATE = 0
        AND r.FLIGHT_NUMBER IS NOT NULL AND r.flight_number != ''
        AND r.deleted != 1 AND r.FLIGHT_NUMBER = t.FLIGHT_NUMBER)
        UNION
        SELECT t.ID, t.FOC_ID, t.FLIGHT_DATE, t.FLIGHT_NUMBER, t.ORG, t.DST, t.STD, t.STA FROM flt_flight_real_info t
        WHERE
        <foreach collection="flightDate" close=")" open="(" item="item" separator="or">
            t.FLIGHT_DATE = #{item}
        </foreach>
        AND t.ATD IS NULL
        AND t.STD > SYSDATE()
        AND t.FLIGHT_TYPE = 'D'
        AND (t.FLIGHT_STATE != 'C' OR t.FLIGHT_STATE IS NULL)
        AND EXISTS (SELECT 1 FROM ser_seat_reserve_rule r WHERE r.STATE = 1
        AND r.FLIGHT_NUMBER IS NOT NULL AND r.flight_number != '' AND r.deleted != 1
        AND NOT EXISTS (SELECT 1 FROM ser_seat_reserve_rule s WHERE s.state = 0 AND s.deleted != 1 AND (s.FLIGHT_NUMBER IS NULL OR s.FLIGHT_NUMBER = '') AND s.AIRPORT_CODE = r.AIRPORT_CODE)
        AND r.FLIGHT_NUMBER = t.FLIGHT_NUMBER AND r.AIRPORT_CODE = t.ORG)
        ) t ORDER BY t.FLIGHT_DATE,t.STD
    </select>
    
    <select id="selectTaskFlightPassengerBackup" parameterType="com.swcares.psi.sere.vo.FlightInfoVO" resultType="com.swcares.psi.sere.vo.FlightPassengerInfoVO" >
    	SELECT p.ID AS passr_id, p.FLIGHT_DATE, p.FLIGHT_NUMBER, p.ORG, p.DST, p.PASSENGER_NAME, p.PASSENGER_NAME_EN, p.ID_NUMBER, 
			 	p.PNR_REF,p.MAIN_CABIN, p.SUB_CABIN, p.CARRY_CABIN, p.FQTV_NUMBER, p.LOYAL_LEVEL, p.PNR_CREATE_DATE, p.TICKET_NUMBER, 
			 	p.IS_CANCEL
			FROM flt_passenger_real_info p 
				WHERE p.FLIGHT_DATE = #{flt.flightDate} 
					AND p.FLIGHT_NUMBER = #{flt.flightNumber} 
					AND p.ORG = #{flt.org}
					AND p.MAIN_CABIN = 'Y'
					AND COALESCE(p.CARRY_CABIN, p.SUB_CABIN) IN ('Y', 'T', 'H', 'G', 'S', 'L','E','V','R','K','N')
					AND p.LOYAL_LEVEL IN('V','T','P','G')
					AND (p.IS_CANCEL = 'N' OR p.IS_CANCEL IS NULL)
					AND p.SEAT_NUMBER IS NULL
					AND p.TICKET_NUMBER IS NOT NULL
					AND NOT EXISTS (SELECT 1 FROM ser_seat_reserve_passenger_record r WHERE (r.state = 1 or r.state = 2) AND r.PASSR_ID = p.ID)
    </select>

    <select id="selectTaskFlightPassenger" parameterType="com.swcares.psi.sere.vo.FlightInfoVO" resultType="com.swcares.psi.sere.vo.FlightPassengerInfoVO" >
        SELECT p.ID AS passr_id, p.FLIGHT_DATE, p.FLIGHT_NUMBER, p.ORG, p.DST, p.PASSENGER_NAME, p.PASSENGER_NAME_EN, p.ID_NUMBER,
        p.PNR_REF,p.MAIN_CABIN, p.SUB_CABIN, p.CARRY_CABIN, p.FQTV_NUMBER, p.LOYAL_LEVEL, p.PNR_CREATE_DATE, p.TICKET_NUMBER,
        p.IS_CANCEL,p.TICKET_ISSUE_DATE
        FROM flt_passenger_real_info p
        WHERE p.FLIGHT_DATE = #{flt.flightDate}
        AND p.MAIN_CABIN = 'Y'
        AND p.FLIGHT_NUMBER = #{flt.flightNumber}
        AND p.ORG = #{flt.org}
        AND (p.CHECK_STATUS <![CDATA[ <> ]]> 'AC' OR p.CHECK_STATUS IS NULL OR p.CHECK_STATUS = '')
        AND p.LOYAL_LEVEL IN('V','T','P','G')
        AND (p.IS_CANCEL = 'N' OR p.IS_CANCEL IS NULL)
        AND p.SEAT_NUMBER IS NULL
        AND p.TICKET_NUMBER IS NOT NULL
        AND NOT EXISTS (SELECT 1 FROM ser_seat_reserve_passenger_record r WHERE (r.state = 1 or r.state = 2) AND r.PASSR_ID = p.ID)
    </select>

    <select id="selectPassengerLabels" parameterType="String" resultType="com.swcares.psi.sere.vo.PassengerLabelVO">
    	SELECT p.ID_NO, p.PAX_NAME, c.`NAME` AS label_name, c.label_key FROM label_passenger p 
			INNER JOIN (SELECT * FROM label_config c WHERE c.PID = (SELECT ID FROM label_config WHERE label_key = 'seat')) c ON c.ID = p.LABEL_ID
			LEFT JOIN label_passenger_audit pa ON pa.LABEL_PAX_ID = p.ID
			WHERE 1=1 
				AND pa.AUDIT_STATUS = 2 
				AND p.IS_DELETE = 0 
				AND p.ID_NO IN 
				<foreach item="idNo" collection="array" open="(" separator="," close=")">
		            #{idNo}
		        </foreach>
    </select>
    
    <select id="selectSeatReserveFlightRecordById" parameterType="String" resultMap="SeatReserveFlightRecordResult">
        <include refid="selectSeatReserveFlightRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSeatReserveFlightRecord" parameterType="com.swcares.psi.sere.entity.SeatReserveFlightRecord">
        insert into ser_seat_reserve_flight_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="flightTaskCode != null and flightTaskCode != ''">flight_task_code,</if>
            <if test="flightId != null and flightId != ''">flight_id,</if>
            <if test="flightNumber != null">flight_number,</if>
            <if test="flightDate != null">flight_date,</if>
            <if test="org != null">org,</if>
            <if test="dst != null">dst,</if>
            <if test="peopleNumber != null">people_number,</if>
            <if test="state != null">state,</if>
            <if test="remark != null">remark,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="flightTaskCode != null and flightTaskCode != ''">#{flightTaskCode},</if>
            <if test="flightId != null and flightId != ''">#{flightId},</if>
            <if test="flightNumber != null">#{flightNumber},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="org != null">#{org},</if>
            <if test="dst != null">#{dst},</if>
            <if test="peopleNumber != null">#{peopleNumber},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null">#{remark},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
         </trim>
    </insert>

    <update id="updateSeatReserveFlightRecord" parameterType="com.swcares.psi.sere.entity.SeatReserveFlightRecord">
        update ser_seat_reserve_flight_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="flightTaskCode != null and flightTaskCode != ''">flight_task_code = #{flightTaskCode},</if>
            <if test="flightId != null and flightId != ''">flight_id = #{flightId},</if>
            <if test="flightNumber != null">flight_number = #{flightNumber},</if>
            <if test="flightDate != null">flight_date = #{flightDate},</if>
            <if test="org != null">org = #{org},</if>
            <if test="dst != null">dst = #{dst},</if>
            <if test="peopleNumber != null">people_number = #{peopleNumber},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeatReserveFlightRecordById" parameterType="String">
        delete from ser_seat_reserve_flight_record where id = #{id}
    </delete>

    <delete id="deleteSeatReserveFlightRecordByIds" parameterType="String">
        delete from ser_seat_reserve_flight_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>