<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.overweightbkg.mapper.AoSpecialOverweightConfigMapper">

    <sql id="listSql">
        select
            up.*,
            (
            select
                group_concat(
                concat(com.CURRENCY_PRICE, com.CURRENCY_TYPE, '(',com.CURRENCY_TYPE_NAME, ')/KG ')
                order by com.BUSINESS_TABLE_ID separator '\n'
                ) as xx
                from
                ao_common_price_config com
                where
                com.STATUS = '1'
                and com.BUSINESS_TABLE_ID =up.id
                order by
                com.BUSINESS_TABLE_ID
            ) as cnyPirce

        from ao_special_overweight_config up
            <where>
                up.is_delete='0'
                <if test="orgPlace != null and orgPlace != ''">
                    and up.ORG_PLACE=#{orgPlace}
                </if>
                <if test="dstPlace != null and dstPlace != ''">
                    and up.DST_PLACE=#{dstPlace}
                </if>

            </where>
    </sql>
    <select id="getPage" resultType="com.swcares.psi.ao.overweightbkg.vo.AoSpecialOverweightConfigPageVo">
            <include refid="listSql"></include>
    </select>
    <select id="getList" resultType="com.swcares.psi.ao.overweightbkg.vo.AoSpecialOverweightConfigPageVo">
            <include refid="listSql"></include>
    </select>




</mapper>