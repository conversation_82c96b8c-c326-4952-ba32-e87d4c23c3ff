<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeCashReturnPayOrderInfoMapper">

    <select id="validateTurnOrder" resultType="java.lang.Integer">
     select
        count(crpoi.id)
    from  ao_merge_cash_return_pay_order_info crpoi
    inner join  ao_merge_sub_order_join_cash_return_pay  joinlist on joinlist.CASH_RETURN_PAY_ID =crpoi.id
    where joinlist.SUBORDER_ORDER_ID in
        <foreach collection="subOrderIdList" item="ele" open="(" close=")" separator=",">
            #{ele}
        </foreach>
    </select>

    <select id="turnNotifyOrderNoLock" resultType="java.lang.String">
    select
        distinct ord.ORDER_NO
    from  ao_merge_cash_return_pay_order_info crpoi
    inner join  ao_merge_sub_order_join_cash_return_pay  joinlist on joinlist.CASH_RETURN_PAY_ID =crpoi.id
    inner join  ao_merge_order_info  ord on ord.id=joinlist.MERGE_ORDER_ID
    where crpoi.PAY_STATUS='0' and crpoi.STATUS='1'
     and (ord.TURN_STATUS='1' or ord.TURN_STATUS='2' )
     and (ord.MERGE_ORDER_STATUS='1' or ord.MERGE_ORDER_STATUS='2' )
     and crpoi.PAY_ORDER_NO=#{turnOrderNo}
    </select>




</mapper>
