package com.swcares.psi.ao.seat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.seat.entity.DepartAbbrEntity <br>
 * Description：(用一句话描述这个类或者接口表示什么)<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date  2022-3-7 15:53:08<br>
 * @version v1.0 <br>
 */

@Data
@TableName("ao_common_depart_abbr")
public class DepartAbbrEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Integer cdaId;

    @TableField("PRE_DEP_CODE")
    @ApiModelProperty("大部门代码")
    private String preDepCode;

    @TableField("SUF_DEP_CODE")
    @ApiModelProperty("对应部门代码")
    private String sufDepCode;

    @TableField("SUF_DEP_NAME")
    @ApiModelProperty("对应部门名称")
    private String sufDepName;

    @TableField("SUF_DEP_ABBR")
    @ApiModelProperty("对应部门简称")
    private String sufDepAbbr;

    @TableField("UPDATE_ID")
    @ApiModelProperty("修改者")
    private String updateId;

    @TableField("UPDATE_TIME")
    @ApiModelProperty("修改时间")
    private Date updateTime;
}

