package com.swcares.psi.aoMergeOrder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="AoMergeSubOrderJoinInvoiceInfo对象", description="子订单关联发票单关系表")
@TableName("ao_merge_sub_order_join_invoice_info")
public class AoMergeSubOrderJoinInvoiceInfo {

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "子订单ID")
    @TableField("SUBORDER_ORDER_ID")
    private String suborderOrderId;


    @ApiModelProperty(value = "总订单ID")
    @TableField("MERGE_ORDER_ID")
    private String mergeOrderId;

    @ApiModelProperty(value = "发票流水号")
    @TableField("SEQ_NO")
    private String seqNo;

}
