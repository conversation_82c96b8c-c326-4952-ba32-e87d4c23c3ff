package com.swcares.psi.stimulate.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AoStimulateSaveDto {
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "1一人多座,2现场座位销售(选座),3逾重行李,4贵宾室,5柜台升舱6机上升舱")
    private String orderType;

    @ApiModelProperty(value = "国内税率（百分比数字  12.36）")
    private String internalTaxRate;

    @ApiModelProperty(value = "国内税率（百分比数字  12.36）")
    private String internationalTaxRate;

    @ApiModelProperty(value = "激励占比(12.66)")
    private String stimulate;

    @ApiModelProperty(value = "个人激励比( 7)")
    private String personStimulate;

    @ApiModelProperty(value = "部门激励比( 3)")
    private String departmentStimulate;

    @ApiModelProperty(value = "状态 0 禁用 1启用")
    private String status;
}
