package com.swcares.psi.ao.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.wxpay.sdk.WXPayUtil;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.entity.AoPayRecordEntity;
import com.swcares.psi.ao.common.entity.AoTempCashReturnEntity;
import com.swcares.psi.ao.common.mapper.CommonMapper;
import com.swcares.psi.ao.common.service.AoOrderInfoService;
import com.swcares.psi.ao.common.service.AoPayRecordService;
import com.swcares.psi.ao.common.service.AoTempCashReturnService;
import com.swcares.psi.ao.common.service.CommonService;
import com.swcares.psi.ao.common.service.ContinentCountryViewService;
import com.swcares.psi.ao.common.service.UpgradeThreadService;
import com.swcares.psi.ao.common.util.OrderUtil;
import com.swcares.psi.ao.common.vo.PaxInfoVo;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeOrderService;
import com.swcares.psi.ao.manySeats.entity.AoManySeatsEntity;
import com.swcares.psi.ao.manySeats.service.ManySeatsService;
import com.swcares.psi.ao.midplatform.service.IAoOrderToMidplatformService;
import com.swcares.psi.ao.model.AoViproomItemSell;
import com.swcares.psi.ao.overweightbkg.entity.AoOverweightBkg;
import com.swcares.psi.ao.overweightbkg.service.AoOverweightBkgService;
import com.swcares.psi.ao.pad.mapper.AoPadMapper;
import com.swcares.psi.ao.payHander.WxQcPayConfig;
import com.swcares.psi.ao.register.util.PhoneNumberUtil;
import com.swcares.psi.ao.sceneSeats.entity.AoSceneSeatsEntity;
import com.swcares.psi.ao.sceneSeats.service.SceneSeatsService;
import com.swcares.psi.ao.service.AoViproomItemSellService;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.base.data.api.enums.FlightTypeEnum;
import com.swcares.psi.combine.constant.CommonConstants;
import com.swcares.psi.common.enums.OrderTypeBankOrderEnum;
import com.swcares.psi.common.enums.OrderTypeEnum;
import com.swcares.psi.common.enums.PayTypeToPayCodeEnum;
import com.swcares.psi.common.utils.AoCommonUtils;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.base.data.api.common.SmsBusinessTypeEnum;
import com.swcares.psi.base.data.api.common.SmsSendType;
import com.swcares.psi.base.data.api.dto.FltPaxInfoPageDto;
import com.swcares.psi.base.data.api.entity.AoDataPermissionsEntity;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.api.entity.FltPassengerRealInfo;
import com.swcares.psi.base.data.api.entity.SmsSendRecord;
import com.swcares.psi.base.data.api.entity.SmsTemplate;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.vo.AoDataPermissionsVo;
import com.swcares.psi.base.data.api.vo.FltPaxInfoPageVo;
import com.swcares.psi.base.data.api.vo.PaxInfoParseVo;
import com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper;
import com.swcares.psi.base.data.mapper.SysDictMapper;
import com.swcares.psi.base.data.service.AoDataPermissionsService;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.FltPassengerRealInfoService;
import com.swcares.psi.base.data.service.SmsService;
import com.swcares.psi.base.data.service.SmsTemplateService;
import com.swcares.psi.base.data.service.SysAirLineInfoService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.collect.h5.yee.YeeProcess;
import com.swcares.psi.collect.h5.yee.YeeRequestCommonParam;
import com.swcares.psi.collect.qrCode.alipay.AliNativeProcess;
import com.swcares.psi.collect.qrCode.alipay.AliRequestCommonParam;
import com.swcares.psi.collect.qrCode.alipay.AliReturnConstans;
import com.swcares.psi.collect.qrCode.wx.WxNativeProcess;
import com.swcares.psi.collect.qrCode.wx.WxRequestCommonParam;
import com.swcares.psi.collect.qrCode.wx.WxReturnConstans;
import com.swcares.psi.combine.constant.CacheConstants;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.redis.RedisService;
import com.swcares.psi.common.retrievephone.PaxerPhoneUtil;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.common.utils.airport.AirportUtil;
import com.swcares.psi.common.worldpay.WorldpayUtil;
import com.swcares.psi.common.worldpay.vo.WorldpayAuthorisedVo;
import com.swcares.psi.common.worldpay.vo.WorldpayCancelledVo;
import com.swcares.psi.common.worldpay.vo.WorldpayRefundedVo;
import com.swcares.psi.pay.bean.alipay.AlipayClientConfig;
import com.swcares.psi.pay.bean.wxpay.WxpayClientConfig;
import com.swcares.psi.stimulate.utils.StimulateOrderUtils;
import com.travelsky.hub.model.input.QueryPassengerInfoInputBean;
import com.travelsky.hub.model.output.QueryPassengerInfoOutputBean;
import com.travelsky.hub.model.peentity.CancelOrderOutPutBean;
import com.travelsky.hub.model.peentity.UpgOrderDetail;
import com.travelsky.hub.svc.ICheckInService;
import com.travelsky.hub.util.APISvcException;
import com.travelsky.hub.util.HubServiceException;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 业务公共数据处理Service
 *
 * <AUTHOR>
 * @date 2020/11/19 11:03
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;

    @Autowired
    private AoDataPermissionsService aoDataPermissionsService;

    @Autowired
    private WorldpayUtil worldpayUtil;

    @Autowired
    CommonMapper dao;
    @Autowired
    AoOrderInfoService orderInfoService;

    @Autowired
    private PaxerPhoneUtil paxerPhoneUtil;

    @Autowired
    AliNativeProcess aliNativeProcess;
    @Resource(name = "aliPayConfig")
    AlipayClientConfig alipayClientConfig;

    @Autowired
    WxNativeProcess wxNativeProcess;

    @Autowired
    YeeProcess yeeProcess;


    @Autowired
    WxQcPayConfig wxpayClientConfig;

    @Autowired
    RedisService redisService;

    @Autowired
    AoTempCashReturnService aoTempCashReturnService;
    @Autowired
    ContinentCountryViewService continentCountryViewService;

    @Autowired
    AoPayRecordService aoPayRecordService;
    @Autowired
    ManySeatsService manySeatsService;
    @Autowired
    SceneSeatsService sceneSeatsService;

    @Autowired
    AoCounterUpgradeOrderService aoCounterUpgradeOrderService;
    @Autowired
    AoOverweightBkgService aoOverweightBkgService;
    @Autowired
    AoViproomItemSellService aoViproomItemSellService;

    @Autowired
    AoOrderInfoService aoOrderInfoService;

    @Autowired
    FltPassengerRealInfoService passengerRealInfoService;
    @Autowired
    SysAirportInfoService sysAirportInfoService;

    @Autowired
    private SmsTemplateService smsTemplateService;
    @Autowired
    private AirportUtil airportUtil;


    @Autowired
    private SysDictMapper sysDictMapper;

    @Value("${spring.profiles.active}")
    private String env;
    @Value("${electronic_voucher.param.key}")
    private String key;
    @Value("${electronic_voucher.sms.path}")
    private String path;

    @Value("${aopayconfig.worldpay.notify_front}")
    private String notifyFront;

    @Autowired
    private ICheckInService iCheckInService;

    @Autowired
    private SmsService smsService;
    @Autowired
    SysAirLineInfoService sysAirLineInfoService;
    @Autowired
    private Redisson redisson;
    @Autowired
    private UpgradeThreadService upgradeService;
    @Autowired
    private FltPassengerRealInfoService fltPassengerRealInfoService;


    @Autowired
    private IAoOrderToMidplatformService aoOrderToMidplatformService;

    //升舱状态，升舱成功
    private static final String UPGRADE_STATUS_SUCCESS = "2";
    //升舱状态，升舱失败
    private static final String UPGRADE_STATUS_FAIL = "3";
    //订单类型，升舱
    private static final String ORDER_TYPE_UPGRADE = "5";
    //订单状态，退款成功
    private static final String PAY_STATUS_REFUSE_SUCCESS = "3";

    @Autowired
    AoPadMapper aoPadMapper;

    @Autowired
    FltPassengerRealInfoMapper fltPassengerRealInfoMapper;

    @Autowired
    private StimulateOrderUtils stimulateOrderUtils;

    @Override
    public List<PaxInfoVo> getPaxInfoByScan(String tktNo, String flitghtDate, String flitghtNo, String boardingNumber, String org) {

        List<PaxInfoVo> paxInfoVoList = dao.getPaxInfoByTktNo(tktNo, flitghtDate, flitghtNo, boardingNumber, org);
        if(ObjectUtils.isEmpty(paxInfoVoList) && StringUtils.isNotEmpty(flitghtNo)){
            try {
                QueryPassengerInfoInputBean inputBean = new QueryPassengerInfoInputBean();
                inputBean.setAirlineCode(flitghtNo.substring(0, 2));
                inputBean.setFlightNumber(flitghtNo.substring(2));
                inputBean.setDptAptCode(org);
                inputBean.setFlightDate(flitghtDate.replace("-", "").trim());
                inputBean.setBoardingNumber(boardingNumber);
                log.info("PE接口查询旅客信息参数-{}",JSON.toJSONString(inputBean));
                QueryPassengerInfoOutputBean outputBean = iCheckInService.queryPassengerInfo(inputBean);
                tktNo = outputBean.getPrPassengerInfo().getTicketNumber();
                paxInfoVoList = dao.getPaxInfoByTktNo(tktNo, flitghtDate, flitghtNo, null, org);
            }catch (APISvcException e) {
                log.error("调用PE查询旅客信息接口出错-APISvcException:{}-{}-{}-{}-{}",e.getErrorCode(), e.getMessage(), e.getNewCode(), e.getNewMsg(), e.getExtraInfo(), e);
            } catch (HubServiceException e) {
                log.error("调用PE查询旅客信息接口出错-HubServiceException:{}-{}-{}-{}-{}",e.getErrorCode(), e.getMessage(), e.getNewCode(), e.getNewMsg(), e.getExtraInfo(), e);
            }catch (Exception e){
                log.error("PE接口查询旅客信息未知异常-{}",e.getMessage(),e);
            }
        }

        for (PaxInfoVo paxInfoVo : paxInfoVoList) {
            List<String> aoCardTypeList = dao.getAoCardTypeList(paxInfoVo.getPaxId());
            paxInfoVo.setCardType(aoCardTypeList.get(0));
            paxInfoVo.setAoCardTypeList(aoCardTypeList);
            //航线类型判断
            Set<String> set = new HashSet<>();
            if (StringUtils.isNotEmpty(paxInfoVo.getOrg())) {
                set.add(paxInfoVo.getOrg());
            }
            if (StringUtils.isNotEmpty(paxInfoVo.getDst())) {
                set.add(paxInfoVo.getDst());
            }

            List<SysAirportInfoEntity> list = sysAirportInfoService.lambdaQuery()
                    .in(SysAirportInfoEntity::getCode, set)
                    .list();
            if (list.size() != 2) {
                throw new BusinessException(MessageCode.NOT_FIND_AIR_PORT.getCode(), new String[]{paxInfoVo.getOrg() + "," + paxInfoVo.getDst()});
            }
            SysAirportInfoEntity orgE = list.get(0);
            SysAirportInfoEntity dstE = list.get(1);

            if (StringUtils.isBlank(orgE.getIntercontinentalName()) || StringUtils.isBlank(dstE.getIntercontinentalName())) {
                throw new BusinessException(MessageCode.NOT_FIND_AIR_PORT.getCode(), new String[]{paxInfoVo.getOrg() + "," + paxInfoVo.getDst()});
            }

            String intercontinental;
            if ("亚洲".equals(orgE.getIntercontinentalName()) && "亚洲".equals(dstE.getIntercontinentalName())) {
                intercontinental = "亚洲航线";
            } else {
                intercontinental = "洲际航线";
            }
            paxInfoVo.setLineType(intercontinental);

            //获取当前旅客手机号
            Map<String, String> paxersPhones = paxerPhoneUtil.getPaxersPhones(Collections.singletonList(paxInfoVo.getPaxId()));
            if (paxersPhones != null){
                String phoneNumbers = paxersPhones.get(paxInfoVo.getPaxId());
                if (phoneNumbers != null){
                    String[] phones = phoneNumbers.split(",");
                    if (phones != null){
                        for (String phone : phones) {
                            if (PhoneNumberUtil.isPhone(phone)){
                                paxInfoVo.setPhoneNumber(phone);
                                break;
                            }
                        }
                    }
                }
            }
            FltPaxInfoPageDto pageDto = new FltPaxInfoPageDto();
            List<FltPaxInfoPageVo> paxInfoList = fltPassengerRealInfoMapper.getPaxInfoList(pageDto, Arrays.asList(paxInfoVo.getPaxId()));
            if(paxInfoList.size()>0){
                paxInfoVo.setPaxTypeMerge(paxInfoList.get(0).getType());
                paxInfoVo.setPaxCategoryMerge(paxInfoList.get(0).getCategory());
            }
            String segmentTypeBySegment = airportUtil.getSegmentTypeBySegment(paxInfoVo.getSegment());
            switch (segmentTypeBySegment){
                case "I" :paxInfoVo.setSegmentType("国际");break;
                case "D" :paxInfoVo.setSegmentType("国内");break;
                case "R" :paxInfoVo.setSegmentType("地区");break;
            }

        }
        return paxInfoVoList;
    }

    /**
     * 修改订单收款方式
     *
     * @param orderNo
     * @param payType
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPayType(String orderNo, String payType) {
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .ne(AoOrderInfoEntity::getChargeType, payType)
                .one();
        if (one == null) {
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }

        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String username = authentication.getRealName();
        log.info(username + ">修改订单" + orderNo + "收款方式:" + one.getChargeType() + ">>>" + payType);
        LambdaUpdateChainWrapper<AoOrderInfoEntity> wrapper = orderInfoService.lambdaUpdate().eq(AoOrderInfoEntity::getOrderNo, orderNo);
        String newQrCodePath = "";

        //修改成现金支付只改订单表支付方式和 上次切换的支付方式
        if (AoOrderConstant.ORDER_CHARGE_TYPE_CASH.equals(payType)) {
            wrapper.set(AoOrderInfoEntity::getChargeType, payType)
                    .set(AoOrderInfoEntity::getLastChangeChargeTypeNo, one.getChargeType()+"_"+one.getBankOrderNo())
                    .set(AoOrderInfoEntity::getQrcodePath, null)
                    .set(AoOrderInfoEntity::getQrcodeDate, null)
                    .set(AoOrderInfoEntity::getBankOrderNo, null)
                    .update();
            return;
        }else if((!one.getCurrencySpecies().equals("CNY"))  && (!AoOrderConstant.ORDER_CHARGE_TYPE_WORLD_PAY.equals(payType))){
            throw new BusinessException(MessageCode.NOT_SUPPORT_PAY_TYPE.getCode());
        }

        String ordertype =one.getOrderTypeCode();
        //上收款业务类型 1-升舱费,2-逾重行李费,3-改期费
        switch (ordertype){
            case "01":ordertype=AoOrderConstant.ORDER_TYPE_CODE_UPGRADE;break;
            case "02":ordertype=AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG;break;
        }


        String bankOrderNo = AoCommonUtils.getBankOrderNo(
                FlightTypeEnum.findCode(one.getSegmentType()),
                PayTypeToPayCodeEnum.findEnumByPayType(payType).getPayTypeOrderCode(),
                OrderTypeEnum.findEnumByOrderTypeCode(ordertype).getOrderIdentityCode(),
                one.getFlightNo()
        );


        //先关闭
        if(one.getChargeType().equals(AoOrderConstant.ORDER_CHARGE_TYPE_WX)){
            WxRequestCommonParam wxParam = new WxRequestCommonParam();
            String wxPrice = one.getOrderPrice().toString();
            wxParam.setTotalFee(wxPrice);
            wxParam.setOutTradeNo(one.getBankOrderNo());
            Map<String, String> clase = wxNativeProcess.clase(wxParam, wxpayClientConfig);
            if(!"SUCCESS".equals(clase.get("return_code"))){
                throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
            }
        }

        if(one.getChargeType().equals(AoOrderConstant.ORDER_CHARGE_TYPE_ALIPAY)){
            AliRequestCommonParam aliparam = new AliRequestCommonParam();
            aliparam.setOutTradeNo(one.getBankOrderNo());
            String yuan = AoUtils.pointsToYuan(one.getOrderPrice().toString());
            //分转元
            aliparam.setTotalAmount(yuan);
            try{
                AlipayTradeCloseResponse close = aliNativeProcess.close(aliparam, alipayClientConfig);
               if(!close.isSuccess()  && !("ACQ.TRADE_NOT_EXIST".equals(close.getSubCode())) ){
                    throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
                }
            }catch (Exception e){
                throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
            }
        }


        if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(payType)) {
            WxRequestCommonParam wxParam = new WxRequestCommonParam();
            String wxPrice = one.getOrderPrice().toString();
            wxParam.setTotalFee(wxPrice);
            wxParam.setBody(wxParam.getBody() + "-" + one.getOrderType());
            wxParam.setOutTradeNo(bankOrderNo);
            newQrCodePath = wxNativeProcess.precreate(wxParam, wxpayClientConfig);
        }
        if (AoOrderConstant.ORDER_CHARGE_TYPE_ALIPAY.equals(payType)) {
            AliRequestCommonParam aliparam = new AliRequestCommonParam();
            String yuan = AoUtils.pointsToYuan(one.getOrderPrice().toString());
            //分转元
            aliparam.setTotalAmount(yuan);
            aliparam.setSubject(aliparam.getSubject() + "-" + one.getOrderType());
            aliparam.setOutTradeNo(bankOrderNo);
            newQrCodePath = aliNativeProcess.precreate(aliparam, alipayClientConfig);
        }
        if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(payType)) {
            YeeRequestCommonParam param = new YeeRequestCommonParam();
            //分转元
            String yuan = AoUtils.pointsToYuan(one.getOrderPrice().toString());
            param.setAmount(yuan);
            param.setGoodsName(param.getGoodsName() + "-" + one.getOrderType());
            param.setOrderNo(bankOrderNo);
            newQrCodePath = yeeProcess.precreate(param);
        }
        if (AoOrderConstant.ORDER_CHARGE_TYPE_WORLD_PAY.equals(payType)) {
           String precreate = worldpayUtil.orderCreateHosted(bankOrderNo, one.getCurrencySpecies(), one.centToYuan(), "ALL");
            if(StringUtils.isNotEmpty(precreate)){
                newQrCodePath=precreate+notifyFront;
            }else{
                throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
            }
        }
        //切换支付方式失败返回响应
        if (newQrCodePath == null) {
            throw new BusinessException(MessageCode.CHANGE_PAY_TYPE.getCode());
        }



        //如果更改了扫码支付方式
        if (StringUtils.isNotEmpty(newQrCodePath)) {
            //更新二维码生成时间
            wrapper.set(AoOrderInfoEntity::getQrcodeDate, LocalDateTime.now())
                    //更新二维码地址
                    .set(AoOrderInfoEntity::getQrcodePath, newQrCodePath)
                    //更新订单号
                    .set(AoOrderInfoEntity::getBankOrderNo, bankOrderNo)
                    .set(AoOrderInfoEntity::getChargeType, payType)
                    .set(AoOrderInfoEntity::getLastChangeChargeTypeNo, one.getChargeType()+"_"+one.getBankOrderNo())
                    .update();
        }

    }

    /**
     * 取消订单
     *
     * @param orderNo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(String orderNo) {
        // 查询当前订单是否还在升舱中
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String username = authentication.getRealName();
        log.info(username + ">发起订单" + orderNo + ">取消");
        doBeforeCancle(orderNo);
//        doBeforeRefund(orderNo);
        orderInfoService.lambdaUpdate().eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .eq(AoOrderInfoEntity::getOrderStatus,AoOrderConstant.ORDER_STATUS_UNPAY)
                .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_CANCEL)
                .update();

        aoOrderToMidplatformService.toMidplatformVo(orderNo);
        AoOrderInfoEntity one = orderInfoService.lambdaQuery().eq(AoOrderInfoEntity::getOrderNo, orderNo).one();
        if(one!=null && AoOrderConstant.ORDER_CHARGE_TYPE_WORLD_PAY.equals(one.getChargeType())){
            try {
                worldpayUtil.orderCancel(orderNo);

            } catch (Exception e) {
                log.info("{}订单取消,worldPay取消异常{}", orderNo, e.getMessage(), e);
            }
            try {
                worldpayUtil.orderRefund(one.getOrderNo(), one.getCurrencySpecies(), new BigDecimal("100").multiply(new BigDecimal(one.getOrderPrice().toString())).toString(), null);
            } catch (Exception e) {
                log.info("{}订单取消,worldPay退款异常{}", orderNo, e.getMessage(), e);
            }
        }

        aoOrderToMidplatformService.toMidplatformVo(orderNo);
    }

    /**
     * 发起退款
     *
     * @param orderNo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startRefund(String orderNo) {
        // 查询当前订单是否还在升舱中
        //doBeforeRefund(orderNo);
        AoOrderInfoEntity one = orderInfoService.lambdaQuery().eq(AoOrderInfoEntity::getOrderNo, orderNo).one();
        if(one ==null){
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }

        if (!AoOrderConstant.ORDER_STATUS_SUCCESS.equals(one.getOrderStatus())) {
            throw new BusinessException(MessageCode.ORDER_REFUND_FAIL.getCode());
        }
        FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                .eq(FltFlightRealInfo::getFlightDate, one.getFlightDate())
                .eq(FltFlightRealInfo::getOrg, one.getOrg())
                .eq(FltFlightRealInfo::getFlightNumber, one.getFlightNo().substring(0,6))
                .one();
        if(flightRealInfo==null){
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        Instant instant = flightRealInfo.getStd().toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime().plusHours(48);
        if(LocalDateTime.now().isAfter(localDateTime)){
            throw new BusinessException(MessageCode.REFUND_TIME_OVER.getCode());
        }
        if(AoOrderConstant.ORDER_TYPE_CODE_UPGRADE.equals(one.getOrderTypeCode())){
            AoCounterUpgrade one1 = aoCounterUpgradeOrderService.lambdaQuery().eq(AoCounterUpgrade::getOrderNo,orderNo).one();
            if(StringUtils.isNotEmpty(one1.getUpgradeUpdateStatus())){
                throw new BusinessException(MessageCode.UPGRADE_STATUS_ORDER_REFUND.getCode());
            }
        }
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String username = authentication.getRealName();
        log.info(username + ">发起订单{}>退款", orderNo);
        orderInfoService.lambdaUpdate().eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_USER_REFUND)
                .set(AoOrderInfoEntity::getRefundStartTime, LocalDateTime.now())
                .update();
    }

    /**
     * 退款
     *
     * @param orderNo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String refund(String orderNo, String refundSause, String refundExplain) {
        // 查询当前订单是否还在升舱中
        doBeforeRefund(orderNo);
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_USER_REFUND)
                .one();
        if(one ==null){
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }
        FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.lambdaQuery()
                .eq(FltFlightRealInfo::getFlightDate, one.getFlightDate())
                .eq(FltFlightRealInfo::getOrg, one.getOrg())
                .eq(FltFlightRealInfo::getFlightNumber, one.getFlightNo().substring(0,6))
                .one();
        if(flightRealInfo==null){
            throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
        }
        Instant instant = flightRealInfo.getStd().toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime().plusHours(48);
        if(LocalDateTime.now().isAfter(localDateTime)){
            throw new BusinessException(MessageCode.REFUND_TIME_OVER.getCode());
        }
        boolean setnx = redisService.setnx(CacheConstants.AO_ORDER_REFUND + one.getOrderNo(), 600L);
        try {
            if (!setnx) {
                log.info("订单{}已有服务器执行退款任务!!!", one.getOrderNo());
                return one.getId();
            }

            String chargeType = one.getChargeType();
            LocalDateTime now = LocalDateTime.now();
            LambdaUpdateChainWrapper<AoOrderInfoEntity> wrapper = orderInfoService.lambdaUpdate()
                    .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                    .set(AoOrderInfoEntity::getRefundPrice, one.getOrderPrice());
            if (StringUtils.isNotEmpty(refundSause)) {
                wrapper.set(AoOrderInfoEntity::getRefundSause, refundSause);
            } else {
                throw new BusinessException(MessageCode.REFUND_SAUSE_NOT_NULL.getCode());
            }

            if (StringUtils.isNotEmpty(refundExplain)) {
                wrapper.set(AoOrderInfoEntity::getRefundExplain, refundExplain);
            }
            //不是现金支付走线上退款逻辑
            if (!AoOrderConstant.ORDER_CHARGE_TYPE_CASH.equals(chargeType) &&
                    !AoOrderConstant.ORDER_CHARGE_TYPE_POS.equals(chargeType)) {
                //todo-hc 退款业务没写完
                if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(chargeType)) {
                    WxRequestCommonParam requestCommonParam = new WxRequestCommonParam();
                    requestCommonParam.setOutTradeNo(one.getBankOrderNo());
                    requestCommonParam.setOutRequestNo(one.getBankOrderNo());
                    requestCommonParam.setTotalFee(one.getOrderPrice().toString());
                    requestCommonParam.setRefundAmount(one.getOrderPrice().toString());
                    Map<String, String> refund = wxNativeProcess.refund(requestCommonParam, wxpayClientConfig);
                    if (refund == null) {
                        log.info("微信退款失败 支付订单号{}", one.getBankOrderNo());
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDFAIL);

                    } else {
                        String result_code = refund.get("result_code");
                        //微信接收退款业务成功
                        if (WxReturnConstans.REQUEST_SUCCESS.equals(result_code)) {
                            //退款中  微信的退款结果要通过退款回调 或者 主动发起退款查询
                            wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND);//请求成功才有值
                        } else {
                            //退款失败
                            wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDFAIL);
                            //订单支付记录
                            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                            aoPayRecordEntity.setOrderId(one.getId());
                            aoPayRecordEntity.setRecordFrom("微信退款");
                            aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL);
                            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                            aoPayRecordService.save(aoPayRecordEntity);
                        }

                        aoOrderToMidplatformService.toMidplatformVo(orderNo);
                    }

                } else if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(chargeType)) {
                    YeeRequestCommonParam param = new YeeRequestCommonParam();
                    param.setOrderNo(one.getBankOrderNo());
                    param.setAmount(one.centToYuan());
                    YopResponse refund = yeeProcess.refund(param);
                    JSONObject jsonObject = JSON.parseObject(refund.getStringResult());
                    if ("0000".equals(jsonObject.get("retCode"))) {
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND);//请求成功才有值
                    } else {
                        //退款失败
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDFAIL);
                        //订单支付记录
                        AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                        aoPayRecordEntity.setRecordFrom("易宝退款");
                        aoPayRecordEntity.setOrderId(one.getId());
                        aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL);
                        aoPayRecordService.save(aoPayRecordEntity);
                    }
                    aoOrderToMidplatformService.toMidplatformVo(orderNo);
                } else if (AoOrderConstant.ORDER_CHARGE_TYPE_WORLD_PAY.equals(chargeType)){
                    try {
                        worldpayUtil.orderCancel(one.getBankOrderNo());

                    } catch (Exception e) {
                        log.info("{}订单退款,worldPay取消异常{}", one.getBankOrderNo(), e.getMessage(), e);
                    }
                    try {
                        worldpayUtil.orderRefund(one.getBankOrderNo(), one.getCurrencySpecies(), (new BigDecimal(one.getOrderPrice()).divide(new BigDecimal("100"))).toString(), null);
                    } catch (Exception e) {
                        log.info("{}订单退款,worldPay退款异常{}", orderNo, e.getMessage(), e);
                    }
                    wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND);//请求成功才有值
                    aoOrderToMidplatformService.toMidplatformVo(orderNo);
                }  else {
                    //支付宝退款
                    AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                    requestCommonParam.setOutTradeNo(one.getBankOrderNo());
                    requestCommonParam.setRefundAmount(one.centToYuan());
                    AlipayTradeRefundResponse refund = aliNativeProcess.refund(requestCommonParam, alipayClientConfig);
                    if (refund == null) {
                        log.info("支付宝退款失败 订单号{}", one.getOrderNo());
                        return one.getId();
                    }

                    //退款请求成功
                    if (refund.isSuccess()) {
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND);//请求成功才有值
                    } else {
                        //退款失败
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDFAIL);
                        //订单支付记录
                        AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                        aoPayRecordEntity.setOrderId(one.getId());
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL);
                        aoPayRecordEntity.setRecordFrom("支付宝退款");
                        aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                        aoPayRecordService.save(aoPayRecordEntity);

                    }

                    aoOrderToMidplatformService.toMidplatformVo(orderNo);
                }

            } else {
                wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDSUCCESS)
                        .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS)
                        //现金退款完成时间
                        .set(AoOrderInfoEntity::getRefundEndTime, now);
                //订单支付记录
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setOrderId(one.getId());
                aoPayRecordEntity.setRecordFrom("现金退款");
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS);
                aoPayRecordEntity.setOperationDate(now);
                aoPayRecordService.save(aoPayRecordEntity);
            }
            PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
            String currentUser = authentication.getUsername();
            String userName = authentication.getRealName();
            String deptName = authentication.getDeptName();
            wrapper.set(AoOrderInfoEntity::getCashRefundUserId, currentUser)
                    .set(AoOrderInfoEntity::getCashRefundUser, userName)
                    .set(AoOrderInfoEntity::getCashRefundDepartment, deptName)
                    .update();

            aoOrderToMidplatformService.toMidplatformVo(orderNo);
           return one.getId();
        } finally {
            if (setnx) {
                redisService.deleteKey(CacheConstants.AO_ORDER_REFUND + one.getOrderNo());
            }
        }
    }

    @Override
    public void cancelRefund(String orderNo) {
        boolean setnx = redisService.setnx(CacheConstants.AO_ORDER_REFUND + orderNo, 600L);
        try {
            if (!setnx) {
                throw new BusinessException(MessageCode.NO_CAMCEL_REFUND.getCode());
            }
            orderInfoService.lambdaUpdate()
                    .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_USER_REFUND)
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getRefundStartTime, null)
                    .update();
            PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
            String currentUser = authentication.getUsername();
            String userName = authentication.getRealName();
            log.info("{}-{}:订单{}取消退款", currentUser,userName,orderNo);

            aoOrderToMidplatformService.toMidplatformVo(orderNo);
        } finally {
            if (setnx) {
                redisService.deleteKey(CacheConstants.AO_ORDER_REFUND + orderNo);
            }
        }
    }

    /**
     * 现金收款
     *
     * @param orderNo
     * @param posOrderNo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String cash(String orderNo, String posOrderNo) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String currentUser = authentication.getUsername();
        String userName = authentication.getRealName();
        String deptName = authentication.getDeptName();
        if(!dao.isDirector(authentication.getId())){
            throw new BusinessException(MessageCode.DIRECTOR_BLANK_RESOURCES.getCode());
        }
        //订单支付记录
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .one();
        if(ObjectUtils.isEmpty(one)){
             log.info("{}-{}-{}发起现金收款失败 订单{}不存在",currentUser,userName,deptName,orderNo);
             return null;
        }
        if (!((AoOrderConstant.ORDER_STATUS_UNPAY.equals(one.getOrderStatus()) || AoOrderConstant.ORDER_STATUS_PAYFAIL.equals(one.getOrderStatus()))
                && (AoOrderConstant.ORDER_CHARGE_TYPE_CASH.equals(one.getChargeType()) || AoOrderConstant.ORDER_CHARGE_TYPE_POS.equals(one.getChargeType())))

        ) {
            log.info("{}-{}-{}发起现金收款失败 订单{}支付方式{}状态为{}", currentUser, userName, deptName, orderNo, one.getChargeType(), one.getOrderStatus());
            return one.getId();
        }
        LambdaUpdateChainWrapper<AoOrderInfoEntity> wrapper = orderInfoService.lambdaUpdate().eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                .set(AoOrderInfoEntity::getPayTime, LocalDateTime.now());
        //如果是pos机收款 回填pos机单号
        if (StringUtils.isNotEmpty(posOrderNo)) {
            wrapper.set(AoOrderInfoEntity::getBankOrderNo, posOrderNo);
            wrapper.set(AoOrderInfoEntity::getChargeType, AoOrderConstant.ORDER_CHARGE_TYPE_POS);
        }
        wrapper.set(AoOrderInfoEntity::getCashReceiveUserId, currentUser)
                .set(AoOrderInfoEntity::getCashReceiveUser, userName)
                .set(AoOrderInfoEntity::getCashReceiveDepartment, deptName)
                .ne(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_CANCEL)
                .eq(AoOrderInfoEntity::getOrderNo, orderNo);
        wrapper.update();
        if (one != null) {
            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
            aoPayRecordEntity.setOrderId(one.getId());
            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
            aoPayRecordEntity.setRecordFrom("现金支付");
            aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
            aoPayRecordService.save(aoPayRecordEntity);
            //判断是升舱订单，如果是就处理升舱逻辑
            if (StringUtils.equals(one.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        upgradeService.upgradeThreadFunction(one.getOrderNo(), one.getFlightNo());
                    }
                }).start();
            }
        }
        //给国内逾重旅客发送短信
        if (one != null) {
            smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
        }

        if (one != null) {
            aoOrderToMidplatformService.toMidplatformVo(orderNo);
        }
        return one.getId();
    }

    /**
     * 获取订单支付二维码
     *
     * @param orderNo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getShowQrCode(String orderNo) {
        AoOrderInfoEntity aoOrderInfoEntity = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .eq(AoOrderInfoEntity::getOrderStatus,AoOrderConstant.ORDER_STATUS_UNPAY)
                .one();
        if (aoOrderInfoEntity == null) {
            throw new BusinessException(MessageCode.ORDER_NOT_EXIST.getCode());
        }
        LocalDateTime qrcodeDate = aoOrderInfoEntity.getQrcodeDate();
        LocalDateTime now = LocalDateTime.now();
        //二维码过期 或者未生成过二维码  二维码有效期2小时
        if (StringUtils.isEmpty(aoOrderInfoEntity.getQrcodePath()) || (qrcodeDate != null && (now.minusHours(2L).compareTo(qrcodeDate) > 0))) {
            String bankOrderNo = aoOrderInfoEntity.getBankOrderNo();
            if (qrcodeDate != null && (now.minusHours(2L).compareTo(qrcodeDate) > 0)) {

                String ordertype =aoOrderInfoEntity.getOrderTypeCode();
                //上收款业务类型 1-升舱费,2-逾重行李费,3-改期费
                switch (ordertype){
                    case "01":ordertype=AoOrderConstant.ORDER_TYPE_CODE_UPGRADE;break;
                    case "02":ordertype=AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG;break;
                }

                bankOrderNo = AoCommonUtils.getBankOrderNo(
                        FlightTypeEnum.findCode(aoOrderInfoEntity.getSegmentType()),
                        PayTypeToPayCodeEnum.findEnumByPayType(aoOrderInfoEntity.getChargeType()).getPayTypeOrderCode(),
                        OrderTypeBankOrderEnum.findEnumByOrderTypeCode(ordertype).getOrderIdentityCode(),
                        aoOrderInfoEntity.getFlightNo()
                );
            }
            //todo-hc  重新获取二维码
            String chargeType = aoOrderInfoEntity.getChargeType();
            //临时存放二维码文件名
            String precreate = null;
            if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(chargeType)) {
                WxRequestCommonParam wxrequestParam = new WxRequestCommonParam();
                wxrequestParam.setOutTradeNo(bankOrderNo);
                wxrequestParam.setTotalFee(aoOrderInfoEntity.getOrderPrice().toString());
                wxrequestParam.setBody(wxrequestParam.getBody() + "-" + aoOrderInfoEntity.getOrderType());
                precreate = wxNativeProcess.precreate(wxrequestParam, wxpayClientConfig);
            } else if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(chargeType)) {
                YeeRequestCommonParam param = new YeeRequestCommonParam();
                param.setAmount(aoOrderInfoEntity.centToYuan());
                param.setOrderNo(bankOrderNo);
                param.setGoodsName(param.getGoodsName() + "-" + aoOrderInfoEntity.getOrderType());
                precreate = yeeProcess.precreate(param);
            } else if (AoOrderConstant.ORDER_CHARGE_TYPE_ALIPAY.equals(chargeType)) {
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(bankOrderNo);
                requestCommonParam.setTotalAmount(aoOrderInfoEntity.centToYuan());
                requestCommonParam.setSubject(requestCommonParam.getSubject() + "-" + aoOrderInfoEntity.getOrderType());
                precreate = aliNativeProcess.precreate(requestCommonParam, alipayClientConfig);
            } else if (AoOrderConstant.ORDER_CHARGE_TYPE_WORLD_PAY.equals(chargeType)) {
                precreate = worldpayUtil.orderCreateHosted(bankOrderNo, aoOrderInfoEntity.getCurrencySpecies(), aoOrderInfoEntity.centToYuan(), "ALL");
                if(StringUtils.isNotEmpty(precreate)){
                    precreate=precreate+notifyFront;
                }
                log.info("worldPay三方返回地址{}",precreate);
            }

            if (null == precreate) {
                return null;
            }
            //修改
            orderInfoService.lambdaUpdate()
                    .set(AoOrderInfoEntity::getQrcodePath, precreate)
                    .set(AoOrderInfoEntity::getQrcodeDate, now)
                    .set(AoOrderInfoEntity::getBankOrderNo, bankOrderNo)
                    .eq(AoOrderInfoEntity::getOrderNo, aoOrderInfoEntity.getOrderNo())
                    .update();
            return precreate;
        } else {
            return aoOrderInfoEntity.getQrcodePath();
        }
    }

    /**
     * 保存值班主任发起的缴纳转账订单
     *
     * @param orderType  订单类型
     * @param chargeType 缴纳支付方式
     * @param orderNo    缴纳订单号
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> getReturnQrCode(String orderType, String chargeType, String orderNo) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String cashReceiveUserId = authentication.getUsername();


        //查询出支付完成并且是现金支付的
        LambdaQueryChainWrapper<AoOrderInfoEntity> wrapper = orderInfoService.lambdaQuery();
        wrapper.eq(AoOrderInfoEntity::getCashReceiveUserId, cashReceiveUserId)
                .eq(AoOrderInfoEntity::getChargeType, AoOrderConstant.ORDER_CHARGE_TYPE_CASH)
                .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                .in(AoOrderInfoEntity::getOrderTypeCode, orderType.split(","));

        //如果没有传缴纳订单号  查询orderType类型的全部需缴纳的订单
        if (StringUtils.isNotEmpty(orderNo)) {
            wrapper.in(AoOrderInfoEntity::getOrderNo, orderNo.split(","));
        }
        List<AoOrderInfoEntity> listAll = wrapper.list();
        if (listAll.size() < 1) {
            throw new BusinessException(MessageCode.RETURN_ORDER_NOT_EXIST.getCode());
        }
        Set<String> setOrderNo = new HashSet<>();
        for (AoOrderInfoEntity all : listAll) {
            setOrderNo.add(all.getOrderNo());
        }

        List<AoTempCashReturnEntity> returnEntityList = aoTempCashReturnService.lambdaQuery().in(AoTempCashReturnEntity::getOrderNo, setOrderNo)
                .in(AoTempCashReturnEntity::getTurnStatus, new String[]{"0", "2"})
                .list();
        List<AoOrderInfoEntity> list = new ArrayList<>();
        pel : for (AoOrderInfoEntity all : listAll) {
                for (AoTempCashReturnEntity turn : returnEntityList) {
                    if(turn.getOrderNo().equals(all.getOrderNo())){
                        continue pel;
                    }
                }
                list.add(all);
        }
        //订单中的航班航线信息必须一致（国内、国际）
//        String filghtType=null;
//        for (AoOrderInfoEntity ele : list) {
//            if(filghtType==null){
//                filghtType=ele.getSegmentType();
//                continue;
//            }
//            if(filghtType.equals(ele.getSegmentType())){
//                continue;
//            }
//            throw new BusinessException(MessageCode.FILGHT_SEGMENT_TYPE_NOT_SAME.getCode(),new String[]{filghtType,ele.getSegmentType()});
//
//        }
        //线上支付缴纳  标识为0
        if(orderType.contains("0")){
            orderType="0";
        }
        //生成唯一订单号
        String callbackPayNo = OrderUtil.createTurnNoOrderId(orderType, list.get(0).getPaxId(), chargeType);
        List<AoTempCashReturnEntity> saveList = new ArrayList<>();
        Integer integer = new Integer(0);
        LocalDateTime now = LocalDateTime.now();
        for (AoOrderInfoEntity ele : list) {
            //计算缴纳转账金额
            integer += ele.getOrderPrice();
            AoTempCashReturnEntity aoTempCashReturnEntity = new AoTempCashReturnEntity();
            aoTempCashReturnEntity.setCallbackPayNo(callbackPayNo);
            aoTempCashReturnEntity.setOrderNo(ele.getOrderNo());
            aoTempCashReturnEntity.setAmount(ele.getOrderPrice());
            aoTempCashReturnEntity.setCreateTime(now);
            aoTempCashReturnEntity.setCashReceiveUserId(cashReceiveUserId);
            aoTempCashReturnEntity.setChargeType(chargeType);
            aoTempCashReturnEntity.setTurnStatus("0");
            aoTempCashReturnEntity.setOrderType(orderType);
            saveList.add(aoTempCashReturnEntity);
        }

        String precreate = "";
        switch (chargeType) {
            case AoOrderConstant.ORDER_CHARGE_TYPE_WX:
                WxRequestCommonParam param = new WxRequestCommonParam();
                param.setTotalFee(integer.toString());
                param.setOutTradeNo(callbackPayNo);
                WxpayClientConfig myConfig = new WxpayClientConfig();
                BeanUtils.copyProperties(this.wxpayClientConfig, myConfig);
                //更换回调通知地址
                myConfig.setNotifyUrl(myConfig.getTurnNotifyUrl());
                precreate = wxNativeProcess.precreate(param, myConfig);
                if (null == precreate) {
                    return null;
                }
                break;
            case AoOrderConstant.ORDER_CHARGE_TYPE_YEE:
                break;
            case AoOrderConstant.ORDER_CHARGE_TYPE_ALIPAY:
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(callbackPayNo);
                requestCommonParam.setAuthCallbackUrl(alipayClientConfig.getTurnNotifyUrl());//设置回调通知为缴纳回调
                //分转元
                String s = AoUtils.pointsToYuan(integer);
                requestCommonParam.setTotalAmount(s);

                precreate = aliNativeProcess.precreate(requestCommonParam, alipayClientConfig);
                if (null == precreate) {
                    return null;
                }
                break;
            default:
                return null;
        }
        //保存二维码存放位置
        for (AoTempCashReturnEntity ele : saveList) {
            ele.setQrcodePath(precreate);
        }
        aoTempCashReturnService.saveBatch(saveList);
        HashMap<String, String> map = new HashMap<>();
        map.put("payNo", callbackPayNo);
        map.put("qrCode", precreate);
        return map;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean worldpayNotifyUrl(WorldpayAuthorisedVo request) {
        log.info("worldPay扫码支付回调订单,订单{} ", JSON.toJSONString(request));
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                .in(AoOrderInfoEntity::getOrderStatus, new String[]{AoOrderConstant.ORDER_STATUS_UNPAY,AoOrderConstant.ORDER_STATUS_SUCCESS_ING})
                .eq(AoOrderInfoEntity::getBankOrderNo, request.getOrderNo())
                .one();

        if (one == null) {
            log.info("worldPay扫码支付回调订单触发,订单{}不存在 ", request.getOrderNo());
            return true;
        }
        orderInfoService.lambdaUpdate()
                .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                .set(AoOrderInfoEntity::getPayTime, LocalDateTime.now())
                .in(AoOrderInfoEntity::getOrderStatus, new String[]{AoOrderConstant.ORDER_STATUS_UNPAY,AoOrderConstant.ORDER_STATUS_SUCCESS_ING})
                .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                .eq(AoOrderInfoEntity::getBankOrderNo,  request.getOrderNo())//todo-hc H5支付处理
                .update();
        if (AoOrderConstant.ORDER_PAY_STATUS_UNPAY.equals(one.getPayStatus()) &&
                (AoOrderConstant.ORDER_STATUS_UNPAY.equals(one.getOrderStatus())||AoOrderConstant.ORDER_STATUS_SUCCESS_ING.equals(one.getOrderStatus()))) {
            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
            aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
            aoPayRecordEntity.setOrderId(one.getId());
            aoPayRecordEntity.setRecordFrom("worldPay支付回调");
            aoPayRecordService.save(aoPayRecordEntity);
            //给国内逾重旅客发送短信
            smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
            //处理支付成功之后的订单处理,捕获异常
            // 查询当前订单
            if (StringUtils.equals(one.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        upgradeService.upgradeThreadFunction(one.getOrderNo(), one.getFlightNo());
                    }
                }).start();
            }
        }
        return false;
    }


    @Override
    public Boolean worldpayCancelledNotify(WorldpayCancelledVo request) {
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getBankOrderNo, request.getOrderNo())
                .one();

        if (one == null) {
            log.info("worldPay取消回调订单触发,订单{}不存在 ", request.getOrderNo());
            return true;
        }
        if(one.getOrderStatus().equals(AoOrderConstant.ORDER_STATUS_REFUND)){
            orderInfoService.lambdaUpdate().eq(AoOrderInfoEntity::getBankOrderNo, request.getOrderNo())
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDSUCCESS)
                    .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS)
                    .set(AoOrderInfoEntity::getRefundEndTime,LocalDateTime.now())
                    .update();
            //订单支付记录
            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
            aoPayRecordEntity.setOrderId(one.getId());
            aoPayRecordEntity.setRecordFrom("worldPay退款");
            aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS);
            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
            aoPayRecordService.save(aoPayRecordEntity);
        } else {
            orderInfoService.lambdaUpdate().eq(AoOrderInfoEntity::getBankOrderNo, request.getOrderNo())
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_CANCEL)
                    .update();
        }
        return false;
    }

    /**
     * 支付宝扫码支付后异步通知
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String alipayNotifyUrl(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = convertRequestParamsToMap(request);
        try {

            boolean signVerified = AlipaySignature.rsaCheckV1(map, alipayClientConfig.getPublicKey(),
                    alipayClientConfig.getCharset(), alipayClientConfig.getSigType());
            if (!signVerified) {
                log.error("支付宝回调签名认证失败,paramsJson:{}", map);
                return null;
            }
        } catch (AlipayApiException e) {
            log.error("支付宝回调签名认证异常,paramsJson:{},errorMsg:{}", map, e.getMessage(), e);
            return null;
        }

        String total_amount = parameterMap.get("total_amount")[0];
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        String trade_status = parameterMap.get("trade_status")[0];
        String gmt_payment = parameterMap.get("gmt_payment")[0];
        String trade_no = parameterMap.get("trade_no")[0];
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                .eq(AoOrderInfoEntity::getBankOrderNo, out_trade_no)
                .one();

        if (one == null) {
            log.info("支付宝扫码支付回调订单触发,支付订单{}不存在 ", out_trade_no);
            return null;
        }
        //支付成功
        if ("TRADE_SUCCESS".equals(trade_status)) {
            orderInfoService.lambdaUpdate()
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getPayTime, LocalDateTime.parse(gmt_payment, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getOrderNo, one.getOrderNo())//todo-hc H5支付处理
                    .update();
            if (AoOrderConstant.ORDER_PAY_STATUS_UNPAY.equals(one.getPayStatus()) &&
                    AoOrderConstant.ORDER_STATUS_UNPAY.equals(one.getOrderStatus())) {
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                aoPayRecordEntity.setOrderId(one.getId());
                aoPayRecordEntity.setRecordFrom("支付宝支付回调");
                aoPayRecordService.save(aoPayRecordEntity);
                //给国内逾重旅客发送短信
                smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
                //处理支付成功之后的订单处理,捕获异常
                // 查询当前订单
                if (StringUtils.equals(one.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            upgradeService.upgradeThreadFunction(one.getOrderNo(), one.getFlightNo());
                        }
                    }).start();
                }

                aoOrderToMidplatformService.toMidplatformVo(one.getOrderNo());

            }
        } else {
            log.info("支付宝扫码支付回调订单触发,订单{}没有支付成功->>{}", trade_status, JSON.toJSONString(parameterMap));
        }
     return one.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void alipayTurnNotifyUrl(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> map = convertRequestParamsToMap(request);
        try {

            boolean signVerified = AlipaySignature.rsaCheckV1(map, alipayClientConfig.getPublicKey(),
                    alipayClientConfig.getCharset(), alipayClientConfig.getSigType());
            if (!signVerified) {
                log.error("支付宝回调签名认证失败,paramsJson:{}", map);
                return;
            }
        } catch (AlipayApiException e) {
            log.error("支付宝回调签名认证异常,paramsJson:{},errorMsg:{}", map, e.getMessage(), e);
            return;
        }
        String trade_status = parameterMap.get("trade_status")[0];
        String total_amount = parameterMap.get("total_amount")[0];
        String gmt_payment = parameterMap.get("gmt_payment")[0];
        String out_trade_no = parameterMap.get("out_trade_no")[0];
        String trade_no = parameterMap.get("trade_no")[0];
        log.info("支付宝扫码缴纳支付回调订单触发,订单号-{} ,订单金额-{} ,支付结果-{} ,支付宝付款完成时间-{}", out_trade_no, total_amount, trade_status, gmt_payment);
        if ("TRADE_SUCCESS".equals(trade_status)) {
            log.info("支付宝扫码支付回调订单触发,订单号-{},支付成功 ", out_trade_no);
            turnHander(out_trade_no, trade_no, AoOrderConstant.ORDER_TURN_STATUS_SUCCESS);
        } else {
            //支付失败不做处理
            log.info("缴纳转账订单号-{}失败:{}", out_trade_no, JSON.toJSONString(parameterMap));
        }
    }

    /**
     * 修改业务订单状态
     *
     * @param out_trade_no 缴纳订单号
     * @param trade_no     银行流水号
     * @param turnStatus   缴纳订单状态
     */
    private void turnHander(String out_trade_no, String trade_no, String turnStatus) {
        //同一笔缴纳的订单
        List<AoTempCashReturnEntity> list = aoTempCashReturnService.lambdaQuery()
                .eq(AoTempCashReturnEntity::getCallbackPayNo, out_trade_no)
                .eq(AoTempCashReturnEntity::getTurnStatus, "0")
                .list();
        HashSet<String> set = new HashSet<>();
        for (AoTempCashReturnEntity ele : list) {
            set.add(ele.getOrderNo());
        }
        if (set.size() == 0) {
            log.info("缴纳回调订单号为null ");
            return;
        }
        //默认缴纳的都是同一种订单
        AoOrderInfoEntity order = aoOrderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, new ArrayList<>(set).get(0))
                .one();
        List<AoOrderInfoEntity> orderList = orderInfoService.lambdaQuery()
                .in(AoOrderInfoEntity::getOrderNo, set)
                .list();
        if (orderList == null || orderList.size() == 0) {
            log.info("缴纳转账回调-订单中不存在:缴纳单号{}", out_trade_no);
            return;
        }

        String turnNoPrefix = OrderUtil.createTurnNoPrefix(order.getOrderTypeCode(),list.get(0).getChargeType(), order.getSegmentType());
        orderInfoService.lambdaUpdate()
                .set(AoOrderInfoEntity::getTurnNumber, turnNoPrefix + trade_no)
                .set(AoOrderInfoEntity::getTurnStatus, turnStatus)
                .set(AoOrderInfoEntity::getTurnTime, LocalDateTime.now())
                .eq(AoOrderInfoEntity::getCashReceiveUserId, orderList.get(0).getCashReceiveUserId())
                .in(AoOrderInfoEntity::getOrderNo, set)
                .update();

        ArrayList<String> orderDelList = new ArrayList<>(set);
        //缴纳成功
        aoTempCashReturnService.lambdaUpdate().eq(AoTempCashReturnEntity::getCallbackPayNo, out_trade_no)
                .in(AoTempCashReturnEntity::getOrderNo,orderDelList)
                .set(AoTempCashReturnEntity::getTurnStatus, "2")
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String wxpayNotifyUrl(Map<String, String> map) {
        if (map == null) {
            return null;
        }
        String out_trade_no = map.get("out_trade_no");
        String return_code = map.get("return_code");
        String time_end = map.get("time_end");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        log.info("微信扫码支付回调订单触发,支付订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        //支付成功
        if ("SUCCESS".equals(return_code)) {
            AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                    .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getBankOrderNo, out_trade_no)
                    .one();
            if (one == null) {
                log.info("微信支付回调,支付订单不存在,订单号{}", out_trade_no);
                return null;
            }
            if (AoOrderConstant.ORDER_PAY_STATUS_UNPAY.equals(one.getPayStatus()) &&
                    AoOrderConstant.ORDER_STATUS_UNPAY.equals(one.getOrderStatus())) {
                orderInfoService.lambdaUpdate()
                        .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                        .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                        .set(AoOrderInfoEntity::getPayTime, LocalDateTime.parse(time_end, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))
                        .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                        .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                        .eq(AoOrderInfoEntity::getOrderNo, one.getOrderNo())
                        .update();
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                aoPayRecordEntity.setOrderId(one.getId());
                aoPayRecordEntity.setRecordFrom("微信支付回调");
                aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                aoPayRecordService.save(aoPayRecordEntity);
                //给国内逾重旅客发送短信
                smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
                if (StringUtils.equals(one.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            upgradeService.upgradeThreadFunction(one.getOrderNo(), one.getFlightNo());
                        }
                    }).start();
                }

                aoOrderToMidplatformService.toMidplatformVo(one.getOrderNo());
                return one.getId();
            }
        } else {
            //todo-hc  其他情况待处理
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxpayH5NotifyUrl(Map<String, String> map) {
        if (map == null) {
            return;
        }
        String out_trade_no = map.get("out_trade_no");
        String return_code = map.get("return_code");
        String time_end = map.get("time_end");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        log.info("微信扫码支付回调订单触发,订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        //支付成功
        if ("SUCCESS".equals(return_code)) {
            AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                    .eq(AoOrderInfoEntity::getOrderNo, out_trade_no)
                    .one();
            if (one == null) {
                return;
            }
            orderInfoService.lambdaUpdate()
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getBankOrderNo, "")
                    .set(AoOrderInfoEntity::getPayTime, LocalDateTime.parse(time_end, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))
                    .eq(AoOrderInfoEntity::getOrderNoCterminal, out_trade_no)
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                    .update();
            if (AoOrderConstant.ORDER_PAY_STATUS_UNPAY.equals(one.getPayStatus()) &&
                    AoOrderConstant.ORDER_STATUS_UNPAY.equals(one.getOrderStatus())) {
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                aoPayRecordEntity.setRecordFrom("微信H5支付回调");
                aoPayRecordEntity.setOrderId(one.getId());
                aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                aoPayRecordService.save(aoPayRecordEntity);

                aoOrderToMidplatformService.toMidplatformVo(out_trade_no);
            }

        } else {
            //todo-hc  其他情况待处理
        }
    }

    @Override
    public String wxpayRefundNotifyUrl(Map<String, String> map1) {
            String out_trade_no = null;
            try {
                out_trade_no = map1.get("out_trade_no");
                String refund_status = map1.get("refund_status");
                AoOrderInfoEntity one = aoOrderInfoService.lambdaQuery()
                        .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                        .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND)
                        .isNotNull(AoOrderInfoEntity::getRefundStartTime)
                        .eq(AoOrderInfoEntity::getBankOrderNo, out_trade_no)
                        .one();
                if (one == null) {
                    log.info("微信退款订单状态已发生改变不予处理:{}", JSON.toJSONString(map1));
                    return null;
                }
                if (StringUtils.isNotEmpty(out_trade_no) && StringUtils.isNotEmpty(refund_status)) {
                    LambdaUpdateChainWrapper<AoOrderInfoEntity> wrapper = orderInfoService.lambdaUpdate()
                            .set(AoOrderInfoEntity::getRefundEndTime, LocalDateTime.now())
                            .eq(AoOrderInfoEntity::getBankOrderNo, out_trade_no);
                    if ("SUCCESS".equals(refund_status)) {
                        log.info("微信退款回调成功,支付订单号{}", out_trade_no);
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDSUCCESS)
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS);

                        if ((AoOrderConstant.ORDER_PAY_STATUS_SUCCESS.equals(one.getPayStatus()) &&
                                AoOrderConstant.ORDER_STATUS_REFUND.equals(one.getOrderStatus()))) {
                            log.info("微信退款回调>>微信退款成功,添加支付操作记录,支付订单号{}", out_trade_no);
                            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                            aoPayRecordEntity.setOrderId(one.getId());
                            aoPayRecordEntity.setRecordFrom("微信退款回调");
                            aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS);
                            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                            aoPayRecordService.save(aoPayRecordEntity);

                        }
                    } else {
                        log.info("微信退款回调失败,支付订单号{}", out_trade_no);
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDFAIL)
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL);
                        if ((AoOrderConstant.ORDER_PAY_STATUS_SUCCESS.equals(one.getPayStatus()) &&
                                AoOrderConstant.ORDER_STATUS_REFUND.equals(one.getOrderStatus()))) {
                            log.info("微信退款回调>>微信退款失败,添加支付操作记录,支付订单号{}", out_trade_no);
                            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                            aoPayRecordEntity.setOrderId(one.getId());
                            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                            aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL);
                            aoPayRecordEntity.setRecordFrom("微信退款回调");
                            aoPayRecordService.save(aoPayRecordEntity);
                        }
                    }
                    wrapper.update();

                    aoOrderToMidplatformService.toMidplatformVo(one.getOrderNo());
                }
                return one.getId();
            } catch (Exception e) {
                e.printStackTrace();
            }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxpayTurnNotifyUrl(Map<String, String> map) {
        if (map == null) {
            return;
        }
        String return_code = map.get("return_code");
        String out_trade_no = map.get("out_trade_no");
        String total_fee = map.get("total_fee");
        String transaction_id = map.get("transaction_id");
        String time_end = map.get("time_end");
        log.info("微信扫码缴纳汇款回调触发,订单号-{} ,订单金额-{}分 ,支付结果-{} ,微信付款完成时间-{}", out_trade_no, total_fee, return_code, time_end);
        if ("SUCCESS".equals(return_code)) {
            //同一笔缴纳的订单
            turnHander(out_trade_no, transaction_id, AoOrderConstant.ORDER_TURN_STATUS_SUCCESS);
        } else {
            //支付失败不做处理
            log.info("缴纳转账订单号-{}失败:{}", out_trade_no, JSON.toJSONString(map));

        }
    }

    /**
     * 微信回调通知参数获取
     *
     * @param request
     * @param response
     * @return
     */
    public Map<String, String> getStreamToMap(HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("Content-type", "text/html;charset=UTF-8");
        Map<String, String> map = null;
        try {
            InputStream in = request.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "UTF-8"));
            StringBuffer result = new StringBuffer();
            String line = "";
            while ((line = br.readLine()) != null) {
                result.append(line);
            }
            map = WXPayUtil.xmlToMap(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }


    @Override
    public Boolean getReturnStatus(String orderNo, String chargeType) {
        if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(chargeType)) {
            WxRequestCommonParam param = new WxRequestCommonParam();
            param.setOutTradeNo(orderNo);
            Map<String, String> query = wxNativeProcess.query(param, wxpayClientConfig);
            if (query == null) {
                log.info("微信缴纳支付查询失败,订单号:{}", orderNo);
                return false;
            }
            log.info("微信缴纳支付结果验证,订单号:{}请求结果:{},支付结果:{},订单状态:{}", query.get("out_trade_no"), query.get("return_code"), query.get("result_code"), query.get("trade_state"));
            //订单不存在不做处理
            if (WxReturnConstans.ORDER_NOT_EXIST.equals(query.get("err_code"))) {
                return false;
            }
            if (WxReturnConstans.SUCCESS_TRADE_STATE.equals(query.get("trade_state"))) {
                return true;
            }
            if (WxReturnConstans.SUCCESS_NOT_PAY.equals(query.get("trade_state"))) {
                return null;
            }
            if (WxReturnConstans.REQUEST_SUCCESS.equals(query.get("trade_state"))) {
                return true;
            }
        }

        if (AoOrderConstant.ORDER_CHARGE_TYPE_ALIPAY.equals(chargeType)) {
            AliRequestCommonParam param = new AliRequestCommonParam();
            param.setOutTradeNo(orderNo);
            AlipayTradeQueryResponse query = aliNativeProcess.query(param, alipayClientConfig);
            if (query == null) {
                log.info("支付宝缴纳支付查询失败,订单号:{}", orderNo);
                return false;
            }
            log.info("支付宝缴纳支付结果验证,订单号:{}请求结果:{},支付结果:{}", orderNo, query.isSuccess(), query.getTradeStatus());
            //查询请求发送成功
            if (query.isSuccess()) {
                //交易不存在(包含生成了二维码却没有扫码支付的情况)
                if (AliReturnConstans.ORDER_NOT_EXIST.equals(query.getSubCode())) {
                    return null;
                }
                //等待支付状态不与处理（扫了二维码却没有支付的情况）
                if (AliReturnConstans.WAIT_BUYER_PAY.equals(query.getTradeStatus())) {
                    return null;
                }
                //查询结果为支付成功或者（交易结束，不可退款）
                if (AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus()) ||
                        AliReturnConstans.TRADE_FINISHED.equals(query.getTradeStatus())) {
                    return true;
                }
                //未付款交易超时关闭，或支付完成后全额退款  不与处理  已退款交予退款定时任务处理
                else if (AliReturnConstans.TRADE_CLOSED.equals(query.getTradeStatus())) {
                    return false;
                }

            }
        }
        if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(chargeType)) {

        }

        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> payByC(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNo)
                .one();

        if (one == null) {
            throw new BusinessException(MessageCode.ORDER_INFO_NOT_EXIST.getCode());
        }
        Map<String, String> precreate = null;

        return precreate;
    }

    @Override
    public Integer unDisposeOrder(String orderStatus, String chargeType) {
        if (StringUtils.isEmpty(orderStatus) && StringUtils.isEmpty(chargeType)) {
            return 0;
        }
        String[] os = orderStatus.split(",");
        String[] ct = chargeType.split(",");
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userId = authentication.getId();
        return dao.unDisposeOrder(Arrays.asList(os), Arrays.asList(ct), userNo, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void yeePayNotify(JSONObject jsonObject) {

        String customerRequestNo = jsonObject.get("customerRequestNo").toString();
        String retCode = jsonObject.get("retCode").toString();
        String accountAmount = jsonObject.get("accountAmount").toString();
        String bankOrderNo = jsonObject.get("bankOrderNo").toString();
        log.info("易宝支付回调订单触发,订单号-{} ,订单金额-{} ,支付结果-{},银行订单号-{} ", customerRequestNo, accountAmount, "0000".equals(retCode), bankOrderNo);
        AoOrderInfoEntity one = orderInfoService.lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, customerRequestNo)
                .one();

        if (one == null) {
            log.info("易宝支付回调订单触发,订单{}不存在 ", customerRequestNo);
            return;
        }

        if ("0000".equals(retCode)) {
            orderInfoService.lambdaUpdate()
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                    .set(AoOrderInfoEntity::getBankOrderNo, customerRequestNo)
                    .set(AoOrderInfoEntity::getPayTime, LocalDateTime.now())
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                    .eq(AoOrderInfoEntity::getOrderNo, customerRequestNo)
                    .update();
            if (AoOrderConstant.ORDER_PAY_STATUS_UNPAY.equals(one.getPayStatus()) &&
                    AoOrderConstant.ORDER_STATUS_UNPAY.equals(one.getOrderStatus())) {
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                aoPayRecordEntity.setOrderId(one.getId());
                aoPayRecordEntity.setRecordFrom("易宝支付回调");
                aoPayRecordService.save(aoPayRecordEntity);

                aoOrderToMidplatformService.toMidplatformVo(customerRequestNo);
            }
            //给国内逾重旅客发送短信
            smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());

            //处理支付成功之后的升舱功能处理
            if (StringUtils.equals(one.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        upgradeService.upgradeThreadFunction(one.getOrderNo(), one.getFlightNo());
                    }
                }).start();
            }


        } else {
            //todo-hc 支付失败
            log.info("易宝支付失败回调触发:{} ", jsonObject.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDataPermissions(String roleId, String dataRoleId,String permissionType) {
        if (StringUtils.isEmpty(roleId) || StringUtils.isEmpty(dataRoleId)) {
            throw new BusinessException(MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
        }
        //删除已有配置
        aoDataPermissionsService.lambdaUpdate().eq(AoDataPermissionsEntity::getRoleId, roleId).remove();
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        LocalDateTime now = LocalDateTime.now();
        String[] split = dataRoleId.split(",");
        List<AoDataPermissionsEntity> entityList = new ArrayList();
        for (String ele : split) {
            AoDataPermissionsEntity entity = new AoDataPermissionsEntity();
            entity.setRoleId(roleId);
            entity.setPermissionsRoleId(ele);
            entity.setTypeExplain(permissionType);
            entity.setCreateUser(userNo);
            entity.setCreateTime(now);
            entityList.add(entity);
        }
        //保存新配置
        aoDataPermissionsService.saveBatch(entityList);

    }

    @Override
    public List<AoDataPermissionsVo> getPermission(String roleId) {
        if (StringUtils.isEmpty(roleId)) {
            return new ArrayList<>();
        }
        return aoDataPermissionsService.getPermissionByRoleId(roleId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vaidatePayResults(List<AoOrderInfoEntity> list) {
        for (AoOrderInfoEntity ele : list) {
            LambdaUpdateChainWrapper<AoOrderInfoEntity> wrapper = aoOrderInfoService.lambdaUpdate()
                    .eq(AoOrderInfoEntity::getOrderNo, ele.getOrderNo());
            AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
            aoPayRecordEntity.setOrderId(ele.getId());
            aoPayRecordEntity.setOperationDate(LocalDateTime.now());
            aoPayRecordEntity.setRecordFrom("订单支付结果查询定时任务");
            log.info("订单支付结果查询定时任务,订单号{}", ele.getOrderNo());
            if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(ele.getChargeType())) {
                WxRequestCommonParam param = new WxRequestCommonParam();
                param.setOutTradeNo(ele.getBankOrderNo());
                Map<String, String> query = wxNativeProcess.query(param, wxpayClientConfig);
                if (query == null) {
                    log.info("微信支付结果查询失败,订单号:{}", ele.getOrderNo());
                    continue;
                }
                log.info("微信支付结果验证,订单号:{}请求结果:{},订单状态:{}", ele.getOrderNo(), query.get("return_code"), query.get("trade_state"));
                //订单不存在不做处理
                if (WxReturnConstans.ORDER_NOT_EXIST.equals(query.get("err_code"))) {
                    log.info("微信支付订单不存在,订单号:{}", ele.getOrderNo());
                    continue;
                }
                //查询结果为支付成功
                if (WxReturnConstans.REQUEST_SUCCESS.equals(query.get("return_code")) && WxReturnConstans.RESULT_CODE_SUCCESS.equals(query.get("trade_state"))) {
                    //已退款   不与处理  已退款交予退款定时任务处理
                    if (WxReturnConstans.REFUND_TRADE_STATE.equals(query.get("trade_state"))) {
                        continue;
                    }
                    //支付已成功
                    if (WxReturnConstans.SUCCESS_TRADE_STATE.equals(query.get("trade_state"))) {
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                                .set(AoOrderInfoEntity::getPayTime, LocalDateTime.now())
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                                .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                                .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                                .update();

                        //订单支付记录
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                        aoPayRecordService.save(aoPayRecordEntity);
                        //给国内逾重旅客发送短信
                        AoOrderInfoEntity one = aoOrderInfoService.lambdaQuery().eq(AoOrderInfoEntity::getOrderNo, ele.getOrderNo()).one();
                        if (one != null) {
                            smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
                        }
                        if (StringUtils.equals(ele.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                            new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    upgradeService.upgradeThreadFunction(ele.getOrderNo(), ele.getFlightNo());
                                }
                            }).start();
                        }

                        aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());

                    }
                    //默认支付失败
                    else {
                        //如果本身订单就已经是支付失败的状态  不做处理
                        if (AoOrderConstant.ORDER_STATUS_PAYFAIL.equals(ele.getOrderType())) {
                            continue;
                        }

                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_PAYFAIL)
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_FAIL)
                                .update();
                        //订单支付记录
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_FAIL);
                        aoPayRecordService.save(aoPayRecordEntity);

                        aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());
                    }

                }

            } else if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(ele.getChargeType())) {
                YopResponse query = yeeProcess.query(ele.getBankOrderNo());
                JSONObject jsonObject = JSON.parseObject(query.getStringResult());
                log.info("易宝订单查询:{}", jsonObject.toString());
                String retCode = jsonObject.get("retCode").toString();
                if ("1001".equals(retCode)) {
                    log.info("定时任务,易宝订单不存在,订单号{}", ele.getOrderNo());
                    continue;
                }
                String orderStatus = jsonObject.get("orderStatus").toString();
                //支付成功
                if ("SUCCESS".equals(orderStatus)) {
                    wrapper.eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                            .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                            .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                            .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                            .set(AoOrderInfoEntity::getPayTime, LocalDateTime.now())
                            .update();

                    //订单支付记录
                    aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                    aoPayRecordService.save(aoPayRecordEntity);
                    //给国内逾重旅客发送短信
                    AoOrderInfoEntity one = aoOrderInfoService.lambdaQuery().eq(AoOrderInfoEntity::getOrderNo, ele.getOrderNo()).one();
                    if (one != null) {
                        smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
                    }
                    //处理支付成功之后的升舱功能处理
                    if (StringUtils.equals(ele.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                upgradeService.upgradeThreadFunction(ele.getOrderNo(), ele.getFlightNo());
                            }
                        }).start();
                    }

                    aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());

                }
                //支付失败
                else if ("PAY_FAIL".equals(orderStatus)) {
                    wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_PAYFAIL)
                            .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_FAIL)
                            .update();
                    //订单支付记录
                    aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_FAIL);
                    aoPayRecordService.save(aoPayRecordEntity);

                    aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());
                } else {
                    //todo-hc其他状态不予处理
                }

            } else {
                AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                requestCommonParam.setOutTradeNo(ele.getBankOrderNo());
                AlipayTradeQueryResponse query = aliNativeProcess.query(requestCommonParam, alipayClientConfig);
                if (query == null) {
                    log.info("支付宝支付结果查询失败,订单号:{}", ele.getOrderNo());
                    continue;
                }
                log.info("支付宝支付结果验证,订单号:{}请求结果:{},支付结果:{}", ele.getOrderNo(), query.isSuccess(), query.getTradeStatus());
                log.info("支付宝支付结果查询返回结果:" + JSON.toJSONString(query));
                //查询请求发送成功
                if (query.isSuccess()) {

                    //等待支付状态不与处理（扫了二维码却没有支付的情况）
                    if (AliReturnConstans.WAIT_BUYER_PAY.equals(query.getTradeStatus())) {
                        continue;
                    }
                    //查询结果为支付成功或者（交易结束，不可退款）
                    if (AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus())) {
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_SUCCESS)
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                                .set(AoOrderInfoEntity::getPayTime, LocalDateTime.now())
                                .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_UNPAY)
                                .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_UNPAY)
                                .update();
                        //订单支付记录
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_SUCCESS);
                        aoPayRecordService.save(aoPayRecordEntity);
                        //给国内逾重旅客发送短信
                        AoOrderInfoEntity one = aoOrderInfoService.lambdaQuery().eq(AoOrderInfoEntity::getOrderNo, ele.getOrderNo()).one();
                        if (one != null) {
                            smsOverWeighLectronicVoucher(one.getPaxId(), one.getOrderNo(), one.getOrderTypeCode());
                        }
                        //处理支付成功之后的升舱功能处理
                        if (StringUtils.equals(ele.getOrderTypeCode(), ORDER_TYPE_UPGRADE)) {
                            new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    upgradeService.upgradeThreadFunction(ele.getOrderNo(), ele.getFlightNo());
                                }
                            }).start();
                        }

                        aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());

                    }
                    //未付款交易超时关闭，或支付完成后全额退款  不与处理  已退款交予退款定时任务处理
                    else if (AliReturnConstans.TRADE_CLOSED.equals(query.getTradeStatus())) {
                        continue;
                    }
                    //支付失败
                    else {
                        //如果本身订单就已经是支付失败的状态  不做处理
                        if (AoOrderConstant.ORDER_STATUS_PAYFAIL.equals(ele.getOrderType())) {
                            continue;
                        }
                        log.info("支付宝支付失败,订单号:{}修改订单状态为1支付状态为2", ele.getOrderNo());
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_PAYFAIL)
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_FAIL)
                                .update();
                        //订单支付记录
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_FAIL);
                        aoPayRecordService.save(aoPayRecordEntity);

                        aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());
                    }
                }else{
                    //交易不存在(包含生成了二维码却没有扫码支付的情况(过期了))
                    if (AliReturnConstans.ORDER_NOT_EXIST.equals(query.getSubCode())) {
                        continue;
                    }
                }
            }
        }
    }

    /**
     * 给国内逾重旅客发送短信
     *
     * @param paxId     旅客id
     * @param orderNo   订单号
     * @param orderType 订单类型编码
     */
    private void smsOverWeighLectronicVoucher(String paxId, String orderNo, String orderType) {

        if (!AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(orderType)) {
            return;
        }
        PaxInfoParseVo passengerById = passengerRealInfoService.getPassengerById(paxId);

        List<FltFlightRealInfo> list = fltFlightRealInfoService.lambdaQuery()
                .eq(FltFlightRealInfo::getFlightDate, DateUtils.parseStrToDate(passengerById.getFlightDate(), DateUtils.YYYY_MM_DD))
                .eq(FltFlightRealInfo::getFlightNumber, passengerById.getFlightNum())
                .list();
        if(list.size()<1 ||  !"D".equals(list.get(0).getFlightType())) {
            return;
        }

        log.info("开始发送国内逾重电子凭证短信通知,旅客-{},paxid-{}", passengerById.getPsgName(), paxId);
        String paxPhoneNum =null;
        Map<String, String> paxersPhones = paxerPhoneUtil.getPaxersPhones(Collections.singletonList(paxId));
        if(paxersPhones.get(paxId)==null){
            log.info("国内逾重电子凭证短信电话号码获取失败,旅客-{},paxid-{}",passengerById.getPsgName(), paxId);
            return;
        }else{
            paxPhoneNum=paxersPhones.get(paxId);
        }
        SmsTemplate smsTemplate;
        try {
            smsTemplate = smsTemplateService.getOneSmsTemplateByCode(SmsBusinessTypeEnum.AO_OVER_WEIGHT_ELECTRONIC_VOUCHER.getCode());
        } catch (BusinessException bue) {
            log.info("国内逾重电子凭证短信模板不存在,旅客-{},paxid-{}", passengerById.getPsgName(), paxId);
            return;
        }
        String templateContent = smsTemplate.getTemplateContent();
        String orderNoParam = AesEncryptUtil.aesEncrypt(key, orderNo);
        //nacos配置path必须以参数`param=`结尾
        String content = templateContent + path + orderNoParam;
        try {
            if (StringUtils.isNotEmpty(paxPhoneNum)) {
                log.info("旅客-{},paxid-{} 获取联系方式成功！{},发送国内逾重电子凭证短信通知", passengerById.getPsgName(), paxId, paxPhoneNum);

                SmsSendRecord smsSendRecord = smsService.send(smsTemplate, content, paxPhoneNum.split(","), false, SmsSendType.MANUAL, sendRecord -> {
                    sendRecord.setReciverName(passengerById.getPsgName());
                    sendRecord.setPnrRef(passengerById.getPnrRef());
                    sendRecord.setFlightNo(passengerById.getFlightNum());
                    sendRecord.setOrg(passengerById.getOrig());
                    sendRecord.setDst(passengerById.getDest());
                    sendRecord.setTktNo(passengerById.getEtNum());
                    DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime flightDate = LocalDateTime.parse(passengerById.getFlightDate() + " 00:00:00", dtf2);
                    sendRecord.setFlightDate(flightDate);
                    sendRecord.setSendUser(CommonConstants.SMS_DEFAULT_ADMIN);
                    sendRecord.setSegment(passengerById.getOrig() + "-" + passengerById.getDest());
                });
                if (smsSendRecord == null || !"0".equals(smsSendRecord.getSendState())) {
                    log.info("国内逾重电子凭证短信发送失败,旅客 {} 联系方式{}", passengerById.getPsgName(), paxPhoneNum);
                }
            } else {
                log.info("旅客-{},paxid-{} 获取联系方式失败！,不执行国内逾重电子凭证短信发送通知", passengerById.getPsgName(), paxId);
            }
        } catch (Exception e) {
            log.error("国内逾重电子凭证短信发送异常：{}", e.getMessage(), e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getCashReceiveTransfer(List<AoTempCashReturnEntity> list) {
        for (AoTempCashReturnEntity ele : list) {
            try {
                log.info("同一笔缴纳转账:" + ele.getCallbackPayNo() + "开始执行");
                List<AoTempCashReturnEntity> orderList = aoTempCashReturnService.lambdaQuery()
                        .eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                        .eq(AoTempCashReturnEntity::getTurnStatus,"0")
                        .list();
                if(orderList.size()<1){
                    continue;
                }
                List<String> orderNoList=new ArrayList<>();
                for (AoTempCashReturnEntity oele : orderList){
                    orderNoList.add(oele.getOrderNo());
                }
                //默认缴纳的都是同一种订单
                AoOrderInfoEntity order = aoOrderInfoService.lambdaQuery()
                        .eq(AoOrderInfoEntity::getOrderNo, orderNoList.get(0))
                        .one();
                if(order==null){
                    continue;
                }
                String turnNoPrefix = OrderUtil.createTurnNoPrefix(order.getOrderTypeCode().contains("0")?"0":order.getOrderTypeCode(), ele.getChargeType(), order.getSegmentType());
                boolean success = false;
                String transaction_id = "";
                LocalDateTime result_time = null;
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                //微信
                if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(ele.getChargeType())) {
                    WxRequestCommonParam param = new WxRequestCommonParam();
                    param.setOutTradeNo(ele.getCallbackPayNo());
                    Map<String, String> query = wxNativeProcess.query(param, wxpayClientConfig);
                    log.info("微信缴纳单号-{},返回报文-{}", ele.getCallbackPayNo(), JSON.toJSONString(query));
                    if (query == null) {
                        log.info("微信缴纳支付查询失败 订单号{},缴纳人工号{}", ele.getOrderNo(), ele.getCashReceiveUserId());
                        continue;
                    }
                    String return_code = query.get("return_code");
                    String result_code = query.get("result_code");
                    String total_fee = query.get("total_fee");
                    transaction_id = query.get("transaction_id");
                    String time_end = query.get("time_end");
                    String trade_state_desc = query.get("trade_state_desc");
                    if ("订单未支付".equals(trade_state_desc)) {
                        if (ele.getCreateTime().isBefore(LocalDateTime.now().minusHours(2L))) {
                            aoTempCashReturnService.lambdaUpdate()
                                    .eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                                    .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                    .update();
                        }
                        log.info("缴纳单号{}未支付", ele.getCallbackPayNo());
                        continue;
                    }
                    //订单不存在不做处理
                    if (WxReturnConstans.ORDER_NOT_EXIST.equals(query.get("err_code"))) {
                        log.info("机上升舱微信缴纳支付订单不存在,订单号:{}", ele.getCallbackPayNo());
                        aoTempCashReturnService.lambdaUpdate()
                                .eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                                .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                .update();
                        continue;
                    }
                    log.info("缴纳转账验证微信请求结果:{},缴纳转账流水号:{},返回结果{},缴纳金额:{}", return_code, transaction_id, result_code, total_fee);
                    //查询请求发送成功
                    if (WxReturnConstans.REQUEST_SUCCESS.equals(return_code)) {
                        //查询结果为支付成功
                        if (WxReturnConstans.RESULT_CODE_SUCCESS.equals(result_code)) {
                            result_time = LocalDateTime.parse(time_end, df);
                            success = true;
                        }
                    }
                }
                //易宝
                else if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(ele.getChargeType())) {

                }
                //支付宝
                else {
                    AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
                    requestCommonParam.setOutTradeNo(ele.getCallbackPayNo());
                    AlipayTradeQueryResponse query = aliNativeProcess.query(requestCommonParam, alipayClientConfig);
                    log.info("支付宝缴纳单号-{},返回报文-{}", ele.getCallbackPayNo(), JSON.toJSONString(query));
                    if (query == null) {
                        log.info("支付宝缴纳支付查询失败 订单号{},缴纳人工号{}", ele.getOrderNo(), ele.getCashReceiveUserId());
                        continue;
                    }
                    log.info("缴纳转账验证支付宝请求结果:{},缴纳转账流水号:{},返回结果{},缴纳金额:{}", query.isSuccess(), query.getTradeNo(), query.getTradeStatus(), query.getPayAmount());
                    //查询请求发送成功
                    if (query.isSuccess()) {
                        //等待支付
                        if (AliReturnConstans.WAIT_BUYER_PAY.equals(query.getTradeStatus())) {
                            if (ele.getCreateTime().isBefore(LocalDateTime.now().minusHours(2L))) {
                                log.info("缴纳支付超时->>{}",ele.getCallbackPayNo());
                                aoTempCashReturnService.lambdaUpdate()
                                        .eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                                        .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                        .update();
                            }
                            continue;
                        }
                        //支付已退款
                        if (AliReturnConstans.TRADE_CLOSED.equals(query.getTradeStatus())) {
                            if (ele.getCreateTime().isBefore(LocalDateTime.now().minusHours(2L))) {
                                log.info("缴纳支付已退款->>{}",ele.getCallbackPayNo());
                                aoTempCashReturnService.lambdaUpdate()
                                        .eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                                        .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                        .update();
                            }
                            continue;
                        }

                        //查询结果为支付成功或者（交易结束，不可退款）
                        if (AliReturnConstans.TRADE_SUCCESS.equals(query.getTradeStatus()) ||
                                AliReturnConstans.TRADE_FINISHED.equals(query.getTradeStatus())) {
                            Date date = query.getSendPayDate();
                            Instant instant = date.toInstant();
                            ZoneId zoneId = ZoneId.systemDefault();

                            result_time = instant.atZone(zoneId).toLocalDateTime();
                            transaction_id = query.getTradeNo();
                            success = true;
                        }
                    }else{
                        //订单不存在
                        if (AliReturnConstans.ORDER_NOT_EXIST.equals(query.getSubCode())) {
                            if (ele.getCreateTime().isBefore(LocalDateTime.now().minusHours(2L))) {
                                log.info("缴纳支付超时订单不存在->>{}",ele.getCallbackPayNo());
                                aoTempCashReturnService.lambdaUpdate()
                                        .eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                                        .set(AoTempCashReturnEntity::getTurnStatus, "1")
                                        .update();
                            }
                            continue;
                        }
                    }
                }
                if (success) {
                    //回填订单缴纳状态 缴纳流水号
                    aoOrderInfoService.lambdaUpdate()
                            .set(AoOrderInfoEntity::getTurnStatus, AoOrderConstant.ORDER_TURN_STATUS_SUCCESS)
                            .set(AoOrderInfoEntity::getTurnNumber, turnNoPrefix + transaction_id)
                            .set(AoOrderInfoEntity::getTurnTime, result_time)
                            .eq(AoOrderInfoEntity::getCashReceiveUserId, ele.getCashReceiveUserId())
                            .in(AoOrderInfoEntity::getOrderNo, orderNoList)
                            .update();
                    //缴纳成功
                    aoTempCashReturnService.lambdaUpdate().eq(AoTempCashReturnEntity::getCallbackPayNo, ele.getCallbackPayNo())
                            .set(AoTempCashReturnEntity::getTurnStatus, "2")
                            .update();
                }
            } catch (Exception e) {
                log.info("缴纳单{}异常:{}", ele.getCallbackPayNo(),e.getMessage(),e);
                continue;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vaidateRefundResults(List<AoOrderInfoEntity> list) {
        List<AoOrderInfoEntity> successList = new ArrayList();//退款成功的订单
        List<AoOrderInfoEntity> failList = new ArrayList();//退款失败的订单
        for (AoOrderInfoEntity ele : list) {
            String realOrderNo = ele.getOrderNoCterminal() != null ? ele.getOrderNoCterminal() : ele.getOrderNo();
            if (ele.getOrderNoCterminal() != null) {
                log.info(ele.getOrderNo() + ">>c端订单号:" + ele.getOrderNoCterminal() + "开始执行退款结果查询");
            } else {
                log.info(ele.getOrderNo() + ">>订单号:" + ele.getOrderNo() + "开始执行退款结果查询");
            }
            if (AoOrderConstant.ORDER_CHARGE_TYPE_WX.equals(ele.getChargeType())) {
                wxRefund(successList, failList, ele, realOrderNo);
            } else if (AoOrderConstant.ORDER_CHARGE_TYPE_YEE.equals(ele.getChargeType())) {
                yeeRefund(successList, failList, ele, realOrderNo);
            } else {
                aliRefund(successList, failList, ele, realOrderNo);
            }
        }
        if (successList.size() > 0) {
            List<String> successIdList = new ArrayList();
            List<AoPayRecordEntity> recordList = new ArrayList();
            for (AoOrderInfoEntity element : successList) {
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setOrderId(element.getId());
                aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                aoPayRecordEntity.setRecordFrom("订单退款结果查询定时任务");
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS);
                recordList.add(aoPayRecordEntity);
                successIdList.add(element.getId());
            }
            //修改退款成功的订单状态
            aoOrderInfoService.lambdaUpdate()
                    .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS)
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDSUCCESS)
                    .set(AoOrderInfoEntity::getRefundEndTime, LocalDateTime.now())
                    .in(AoOrderInfoEntity::getId, successIdList)
                    .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND)
                    .update();
            //保存支付记录
            if (recordList.size() > 0) {
                aoPayRecordService.saveBatch(recordList);
            }

            successList.forEach(ele -> {
                aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo());

            });
        }
        if (failList.size() > 0) {
            List<String> failIdList = new ArrayList();
            List<AoPayRecordEntity> recordList = new ArrayList();
            for (AoOrderInfoEntity element : failList) {
                AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                aoPayRecordEntity.setOrderId(element.getId());
                aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                aoPayRecordEntity.setRecordFrom("订单退款结果查询定时任务");
                aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL);
                recordList.add(aoPayRecordEntity);
                failIdList.add(element.getId());
            }
            //修改退款失败的订单状态
            aoOrderInfoService.lambdaUpdate()
                    .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDFAIL)
                    .set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDFAIL)
                    .set(AoOrderInfoEntity::getRefundEndTime, LocalDateTime.now())
                    .eq(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_SUCCESS)
                    .eq(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUND)
                    .in(AoOrderInfoEntity::getId, failIdList)
                    .update();
            //保存支付记录
            if (recordList.size() > 0) {
                aoPayRecordService.saveBatch(recordList);
            }

            failList.forEach(ele -> aoOrderToMidplatformService.toMidplatformVo(ele.getOrderNo()));
        }

    }

    private void wxRefund(List<AoOrderInfoEntity> successList, List<AoOrderInfoEntity> failList, AoOrderInfoEntity ele, String realOrderNo) {
        WxRequestCommonParam param = new WxRequestCommonParam();
        param.setOutTradeNo(ele.getBankOrderNo());
        Map<String, String> query = wxNativeProcess.refund_query(param, wxpayClientConfig);
        if (query == null) {
            log.info("微信退款查询失败 订单号{}", ele.getOrderNo() + ">>" + realOrderNo);
            return;
        }
        String return_code = query.get("return_code");
        String result_code = query.get("result_code");
        log.info("微信退款验证请求结果:{},转账单号:{},返回结果{}", return_code, realOrderNo, result_code);
        if (WxReturnConstans.REQUEST_SUCCESS.equals(return_code)) {
            //退款结果为成功
            if (WxReturnConstans.RESULT_CODE_SUCCESS.equals(result_code)) {
                log.info("微信退款成功:" + ele.getOrderNo() + ">>" + realOrderNo);
                successList.add(ele);
            }
            if (WxReturnConstans.RESULT_CODE_FAIL.equals(result_code)) {
                String err_code_des = query.get("err_code_des");
                String err_code = query.get("err_code");
                log.info("微信退款失败:" + ele.getOrderNo() + ">>" + realOrderNo + "{}-退款失败原因:{},错误代码:{}", realOrderNo, err_code_des, err_code);
                //不存在的退款  不做操作
                if (WxReturnConstans.REFUND_NOT_EXIST.equals(err_code)) {
                    return;
                }
                //退款失败
                failList.add(ele);
            }

        }
    }

    private void yeeRefund(List<AoOrderInfoEntity> successList, List<AoOrderInfoEntity> failList, AoOrderInfoEntity ele, String realOrderNo) {
        YeeRequestCommonParam param = new YeeRequestCommonParam();
        param.setOrderNo(ele.getBankOrderNo());
        YopResponse refund = yeeProcess.refundQuery(param);
        JSONObject jsonObject = JSON.parseObject(refund.getStringResult());
        String status = jsonObject.get("status").toString();
        if ("DEBIT_SUCCESS".equals(status) || "ACCEPT_PROCESSING".equals(status) || "ACCEPT_SUCCESS".equals(status)) {
            log.info("易宝退款成功:" + ele.getOrderNo() + ">>" + realOrderNo);
            successList.add(ele);
        } else if ("REFUND_FAILED".equals(status)) {
            //退款失败
            log.info("易宝退款失败:" + ele.getOrderNo() + ">>" + realOrderNo);
            failList.add(ele);
        }
    }

    private void aliRefund(List<AoOrderInfoEntity> successList, List<AoOrderInfoEntity> failList, AoOrderInfoEntity ele, String realOrderNo) {
        AliRequestCommonParam requestCommonParam = new AliRequestCommonParam();
        requestCommonParam.setOutTradeNo(ele.getBankOrderNo());
        requestCommonParam.setOutRequestNo(ele.getBankOrderNo());
        AlipayTradeFastpayRefundQueryResponse query = aliNativeProcess.refund_query(requestCommonParam, alipayClientConfig);
        if (query == null) {
            log.info("退款查询失败 订单号{}", ele.getOrderNo() + ">>" + realOrderNo);
            return;
        }
        log.info("支付宝退款验证请求结果:{},转账单号:{},返回结果{}", query.isSuccess(), realOrderNo, query.getRefundStatus());
        //不存在的退款  不做操作
        if (StringUtils.isNotEmpty(query.getSubCode())) {
            log.info("支付宝退款失败:" + JSON.toJSONString(query));
            return;
        }
        //查询请求发送成功
        if (query.isSuccess()) {
            //退款成功
            if (null == query.getRefundStatus() || "REFUND_SUCCESS".equals(query.getRefundStatus())) {
                log.info("支付宝退款成功:" + ele.getOrderNo() + ">>" + realOrderNo);
                successList.add(ele);
            } else {
                //退款失败
                log.info("支付宝退款失败:" + ele.getOrderNo() + ">>" + realOrderNo);
                failList.add(ele);

            }
        }
    }

    private static Map<String, String> convertRequestParamsToMap(HttpServletRequest request) {
        Map<String, String> retMap = new HashMap<String, String>();

        Set<Map.Entry<String, String[]>> entrySet = request.getParameterMap().entrySet();

        for (Map.Entry<String, String[]> entry : entrySet) {
            String name = entry.getKey();
            String[] values = entry.getValue();
            int valLen = values.length;

            if (valLen == 1) {
                retMap.put(name, values[0]);
            } else if (valLen > 1) {
                StringBuilder sb = new StringBuilder();
                for (String val : values) {
                    sb.append(",").append(val);
                }
                retMap.put(name, sb.toString().substring(1));
            } else {
                retMap.put(name, "");
            }
        }

        return retMap;
    }

    /**
     * 退款前判断升舱订单是否支持退款
     *
     * @param orderNum
     * @return
     */
    private void doBeforeRefund(String orderNum) {
        // 查询当前订单,不处理升舱之外的订单
        AoOrderInfoEntity currentOrder = orderInfoService.getOne(Wrappers.<AoOrderInfoEntity>lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNum).eq(AoOrderInfoEntity::getOrderTypeCode, ORDER_TYPE_UPGRADE));
        if (null == currentOrder) {
            return;
        }
        // 查询当前升舱订单结果：
        AoCounterUpgrade upgradeOrder = aoCounterUpgradeOrderService.getOne(Wrappers.<AoCounterUpgrade>lambdaQuery()
                .eq(AoCounterUpgrade::getOrderNo, orderNum));
        if (null == upgradeOrder) {
            throw new BusinessException(MessageCode.COUNTER_UPGRADE_REFUND_FAIL.getCode());
        }

        //在退款前，需要判断是否还在进行升舱的操作，如果没有的话，才可以进行退款操作
        RLock lock = redisson.getLock(CacheConstants.ORDER_UPGRADE + upgradeOrder.getUpgradeOrder());
        try {
            if (!lock.tryLock()) {
                log.info("当前订单：{}正在升舱中!!!", orderNum);
                throw new BusinessException(MessageCode.COUNTER_UPGRADE_REFUND_FAIL.getCode());
            } else {
                UpgOrderDetail orderTetail = PeUpgradeUtils.getOrderTetail(upgradeOrder.getUpgradeOrder());
                if  (null != orderTetail && (orderTetail.getOrderStatus().equals(PeUpgradeUtils.PE_OPERATION_SUCCESS) || orderTetail.getOrderStatus().equals(PeUpgradeUtils.PE_OPERATION_FAIL) || orderTetail.getOrderStatus().equals(PeUpgradeUtils.REFUND_TICKET_FAIL))) {
                    //判断当前的客票状态是否为Used
                    List<FltPassengerRealInfo> passengerRealInfoList = fltPassengerRealInfoService.list(Wrappers.<FltPassengerRealInfo>lambdaQuery()
                            .eq(FltPassengerRealInfo::getId, currentOrder.getPaxId())
                            .eq(FltPassengerRealInfo::getFlightId, currentOrder.getFlightId())
                            .eq(FltPassengerRealInfo::getIsCancel, "N"));
                    if (null != passengerRealInfoList && !passengerRealInfoList.isEmpty()) {
                        if (StringUtils.equals(passengerRealInfoList.get(0).getPnrActionCode(), "USED")) {
                            log.info("当前客票为USED状态，无法发起退款-{}", orderNum);
                            throw new BusinessException(MessageCode.UPGRADE_ORDER_TKT_STATUS_USED.getCode());
                        }
                    }
                    try {
                        aoCounterUpgradeOrderService.refundOrder(upgradeOrder.getUpgradeOrder());

                    } catch (Exception e) {
                        log.info("PE退票异常：{}", e.getMessage(), e);
                        throw new BusinessException(MessageCode.UPGRADE_CUSTOMIZE.getCode(),new String[]{e.getMessage()});
                    }
                }else  if (null != orderTetail && ( orderTetail.getOrderStatus().equals(PeUpgradeUtils.INIT_SUCCESS_UNPAY) )) {
                    //取消订单
                    try {
                        aoCounterUpgradeOrderService.cancelOrder(upgradeOrder.getUpgradeOrder());
                    }catch (Exception e) {
                        log.info("PE取消异常：{}", e.getMessage(), e);
                        throw new BusinessException(MessageCode.UPGRADE_CUSTOMIZE.getCode(),new String[]{e.getMessage()});
                    }
                }else if (null != orderTetail && orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL)){
                    throw new BusinessException(MessageCode.SYSTEM_BUSY.getCode());

                }else{
                    log.info("PE订单{}状态{}不做操作", orderNum, null != orderTetail?orderTetail.getOrderStatus():" PE订单详情异常 ");
                }
                //2021-12-20 退款后将升舱状态改为升舱失败
                aoCounterUpgradeOrderService.update(Wrappers.<AoCounterUpgrade>lambdaUpdate()
                        .set(AoCounterUpgrade::getUpgradeStatus, UPGRADE_STATUS_FAIL)
                        .eq(AoCounterUpgrade::getOrderNo, orderNum));
                return;
            }
        } catch (Exception e) {
            log.info("升舱退款前处理升舱订单处理错误信息-" + e.getMessage());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void doBeforeCancle(String orderNum){
        // 查询当前订单,不处理升舱之外的订单
        AoOrderInfoEntity currentOrder = orderInfoService.getOne(Wrappers.<AoOrderInfoEntity>lambdaQuery()
                .eq(AoOrderInfoEntity::getOrderNo, orderNum).eq(AoOrderInfoEntity::getOrderTypeCode, ORDER_TYPE_UPGRADE));
        if (null == currentOrder) {
            return;
        }
        // 查询当前升舱订单结果：
        AoCounterUpgrade upgradeOrder = aoCounterUpgradeOrderService.getOne(Wrappers.<AoCounterUpgrade>lambdaQuery()
                .eq(AoCounterUpgrade::getOrderNo, orderNum));

        if (currentOrder.getOrderStatus().equals(AoOrderConstant.ORDER_STATUS_SUCCESS)) {
            throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_PAY_SUCCESS.getCode());
        }

        //在退款前，需要判断是否还在进行升舱的操作，如果没有的话，才可以进行退款操作
        RLock lock = redisson.getLock(CacheConstants.ORDER_UPGRADE + upgradeOrder.getUpgradeOrder());
        try {
            if (!lock.tryLock()) {
                log.info("当前订单：{}正在升舱中!!!", orderNum);
                throw new BusinessException(MessageCode.SYSTEM_BUSY.getCode());
            } else {
                UpgOrderDetail orderTetail = PeUpgradeUtils.getOrderTetail(upgradeOrder.getUpgradeOrder());
                if(null == orderTetail){
                    throw new BusinessException(MessageCode.DATA_NOT_EXIST.getCode());
                }

                if(orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL)){
                    throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_APPLY.getCode());
                }else if(orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL_SUCCESS) ){
                    aoCounterUpgradeOrderService.update(Wrappers.<AoCounterUpgrade>lambdaUpdate()
                            .set(AoCounterUpgrade::getUpgradeStatus, UPGRADE_STATUS_FAIL)
                            .eq(AoCounterUpgrade::getOrderNo, orderNum));
                   return;
                }else if(orderTetail.getOrderStatus().equals(PeUpgradeUtils.INIT_FAIL)){
                    aoCounterUpgradeOrderService.update(Wrappers.<AoCounterUpgrade>lambdaUpdate()
                            .set(AoCounterUpgrade::getUpgradeStatus, UPGRADE_STATUS_FAIL)
                            .eq(AoCounterUpgrade::getOrderNo, orderNum));
                    return;
                } else if(!(orderTetail.getOrderStatus().equals(PeUpgradeUtils.INIT_SUCCESS_UNPAY) || orderTetail.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL_FAIL) )){
                    throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_AGAIN.getCode());
                }

                try {
                    CancelOrderOutPutBean cancelOrderOutPutBean = aoCounterUpgradeOrderService.cancelOrder(upgradeOrder.getUpgradeOrder());
                    if(cancelOrderOutPutBean.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL)){
                        throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_APPLY.getCode());
                    }else if(cancelOrderOutPutBean.getOrderStatus().equals(PeUpgradeUtils.ORDER_APPLY_CANCEL_FAIL)){
                        throw new BusinessException(MessageCode.UPGRADE_ORDER_CANCEL_FAIL_AGAIN.getCode());
                    }
                }catch (BusinessException e) {
                    throw e;
                }catch (Exception e) {
                    log.info("PE取消异常：{}", e.getMessage(), e);
                    throw new BusinessException(MessageCode.UPGRADE_CUSTOMIZE.getCode(),new String[]{e.getMessage()});
                }


                //2021-12-20 退款后将升舱状态改为升舱失败
                aoCounterUpgradeOrderService.update(Wrappers.<AoCounterUpgrade>lambdaUpdate()
                        .set(AoCounterUpgrade::getUpgradeStatus, UPGRADE_STATUS_FAIL)
                        .eq(AoCounterUpgrade::getOrderNo, orderNum));
                return;
            }
        } catch (Exception e) {
            log.info("升舱PE订单取消异常-" + e.getMessage());
            throw e;
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }



    }






    @Override
    public Boolean worldpayRefundNotifyUrl(WorldpayRefundedVo vo) {
        log.info("worldPay退款回调:{}", JSON.toJSONString(vo));
        String out_trade_no = vo.getOrderNo();
            try {
                AoOrderInfoEntity one = aoOrderInfoService.lambdaQuery()
                        .eq(AoOrderInfoEntity::getBankOrderNo, vo.getOrderNo())
                        .one();
                if (one == null) {
                    log.info("worldPay退款订单不存在不予处理:{}", JSON.toJSONString(vo));
                    return true;
                }
                if (StringUtils.isNotEmpty(out_trade_no) ) {
                    LambdaUpdateChainWrapper<AoOrderInfoEntity> wrapper = orderInfoService.lambdaUpdate()
                            .set(AoOrderInfoEntity::getRefundEndTime, LocalDateTime.now())
                            .eq(AoOrderInfoEntity::getOrderNo, one.getOrderNo());
                        log.info("worldPay退款回调成功,订单号{}", out_trade_no);
                    if ((AoOrderConstant.ORDER_PAY_STATUS_SUCCESS.equals(one.getPayStatus()) &&
                            AoOrderConstant.ORDER_STATUS_REFUND.equals(one.getOrderStatus()))) {
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_REFUNDSUCCESS)
                                .set(AoOrderInfoEntity::getPayStatus, AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS)
                                .set(AoOrderInfoEntity::getRefundEndTime,LocalDateTime.now());
                        log.info("worldPay退款回调>>worldPay退款成功,添加支付操作记录,订单号{}", out_trade_no);
                        AoPayRecordEntity aoPayRecordEntity = new AoPayRecordEntity();
                        aoPayRecordEntity.setOrderId(one.getId());
                        aoPayRecordEntity.setRecordFrom("worldPay退款回调");
                        aoPayRecordEntity.setPayType(AoOrderConstant.ORDER_PAY_STATUS_REFUNDSUCCESS);
                        aoPayRecordEntity.setOperationDate(LocalDateTime.now());
                        aoPayRecordService.save(aoPayRecordEntity);
                    } else {
                        wrapper.set(AoOrderInfoEntity::getOrderStatus, AoOrderConstant.ORDER_STATUS_CANCEL);
                    }

                    wrapper.update();
                }
            } catch (Exception e) {
                log.error("worldPay退款回调异常,订单号{}", out_trade_no,e);
                e.printStackTrace();

            }
            return false;

    }
}
