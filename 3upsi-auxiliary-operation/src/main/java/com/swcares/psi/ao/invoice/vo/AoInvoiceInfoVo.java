package com.swcares.psi.ao.invoice.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.swcares.psi.common.utils.ExcelBaseVo;
import com.swcares.psi.common.utils.encryption.Encryption;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/1/19 13:50
 */
@Data
public class AoInvoiceInfoVo implements ExcelBaseVo {

    @ExcelIgnore
    @ApiModelProperty(value = "主键")
    private String id;

    @ExcelProperty(value = "订单号")
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ExcelProperty(value = "发票号")
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;

    @ExcelProperty(value = "产品类别")
    @ApiModelProperty(value = "产品类别")
    private String productType;

    @ExcelProperty(value = "开票状态")
    @ApiModelProperty(value = "开票状态(0未开票1开票成功2开票失败)")
    private String invoiceStatus;

    @ExcelProperty(value = "抬头类型")
    @ApiModelProperty(value = "抬头类型 0企业 1个人或者非企业")
    private String titleType;

    @ExcelProperty(value = "发票抬头")
    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    @ExcelProperty(value = "发票金额(元)")
    @ApiModelProperty(value = "发票金额")
    private String invoicePrice;

    @ExcelProperty(value = "商品名称")
    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ExcelProperty(value = "提交时间")
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ExcelIgnore
    @ApiModelProperty(value = "税号")
    private String taxNo;

    @ExcelIgnore
    private String productPrice;
    @ExcelIgnore
    @ApiModelProperty(value = "商品金额导出值")
    private String productPriceExcel;

    @ExcelIgnore
    @ApiModelProperty(value = "商品数量")
    private Integer productNumber;

    @ExcelIgnore
    @ApiModelProperty(value = "电话号码")
    @Encryption
    private String phone;

    @ExcelIgnore
    @Encryption
    @ApiModelProperty(value = "购买方电话")
    private String purchaserPhone;
    @ExcelIgnore
    @ApiModelProperty(value = "购买方地址")
    private String purchaserAddress;
    @ExcelIgnore
    @ApiModelProperty(value = "购买方银行")
    private String purchaserBank;
    @ExcelIgnore
    @ApiModelProperty(value = "购买方银行账号")
    private String purchaserBankNo;


    @ExcelIgnore
    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ExcelIgnore
    @ApiModelProperty(value = "失败原因")
    private String causeFailure;


    @ExcelIgnore
    @ApiModelProperty(value = "开票结果返回时间")
    private String invoiceTime;

    @ExcelIgnore
    @ApiModelProperty(value = "信息来源 1 辅营B端 2  C端小程序 3  ipad")
    private String sourceInformation;

    @ExcelIgnore
    @ApiModelProperty(value = "操作人名称")
    private String userName;

    @ExcelIgnore
    @ApiModelProperty(value = "操作人工号")
    private String userNo;
    @ExcelIgnore
    @ApiModelProperty(value = "订单类型")
    private String orderType;
    @ExcelIgnore
    @ApiModelProperty(value = "航班类型 D-国内|I-国际'")
    private String flightType;

    @Override
    public void valueTypeConvers() {
//        this.productPriceExcel = new BigDecimal(productPrice);
        //将字段改为String类型是因为导出时需要进行脱敏
        this.productPriceExcel = productPrice;
    }
}
