<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.order_v2.mapper.AoMergeLinePaymentMapper">

    <select id="getAoLinePaymentAccountsPage" resultType="com.swcares.psi.ao.order_v2.vo.AoMergeLinePaymentPageVo">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
        <include refid="orderBySql"></include>
    </select>
    <select id="getAoLinePaymentAccountsList" resultType="com.swcares.psi.ao.order_v2.vo.AoMergeLinePaymentPageVo">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
        <include refid="orderBySql"></include>
    </select>
    <sql id="orderBySql">
        order by aoi.FLIGHT_DATE desc,aoi.ORDER_DATE desc ,aoi.FLIGHT_NO desc
    </sql>
    <sql id="selectSql">
         select
            date_format(aoi.ORDER_DATE, '%Y-%m-%d') as orderDate,
            aoi.ORDER_NO as orderNo,
            aoi.COMPLETE_DEPARTMENT_INFO,
            aoi.BANK_ORDER_NO as payNo,
            aoi.FINANCE_ORDER_NO as financeOrderNo,
            cr.CALLBACK_PAY_NO as turnNumber,
            aoi.ORDER_PRICE as orderPrice,
            case  aoi.CURRENCY_SPECIES
            when 'CNY' then  concat(aoi.CURRENCY_SPECIES,'(人民币)')
            else   concat(aoi.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=aoi.CURRENCY_SPECIES limit 1),')')
            end as currencySpeciesName,
            case
                aoi.CHARGE_TYPE
                           when '0' then
                               '微信'
                when '1' then
                               '银行卡'
                when '2' then
                               '支付宝'
                when '3' then
                               '现金'
                when '5' then 'worldPay'
                else
                               'pos机'
            end as chargeType,
            case
                apr.PAY_TYPE
                           when '0' then
                               '未支付'
                when '1' then
                               '支付完成'
                when '2' then
                               '支付失败'
                when '3' then
                               '退款成功'
                when '4' then
                               '退款失败'
            end as payStatus,
            date_format(aoi.FLIGHT_DATE, '%Y-%m-%d') as flightDate,
            aoi.FLIGHT_NO as flightNo,
            concat((select AIRPORT_NAME from sys_airport_info where CODE = aoi.ORG and IS_USE = '0' limit 1), aoi.org) as orgName,
            concat((select AIRPORT_NAME from sys_airport_info where CODE = aoi.DST and IS_USE = '0' limit 1), aoi.DST) as dstName,
            aoi.PAX_NAME as paxName,
            aoi.AREA_COMPANY,
            aoi.TKT_NO as tktNo,
            linement.PAYMENT_REMARK as paymentRemark,
             case
                aoi.ORDER_TYPE_CODE
                when '01' then
                               '线上收款-升舱费'
                when '02' then
                               '线上收款-逾重行李费'
                when '03' then
                               '线上收款-改期费'
            end as type,
            aoi.USER_NO,
            aoi.USER_NAME,
            aoi.DEPARTMENT,
            aoi.DEPARTMENT_ID
        from
            ao_order_info aoi
        left join ao_pay_record apr on
            aoi.ID = apr.ORDER_ID
        left join ao_temp_cash_return cr on
            aoi.ORDER_NO = cr.ORDER_NO
            and cr.TURN_STATUS <![CDATA[ <> ]]> '0'
        left join ao_merge_line_payment linement on linement.AO_MERGE_SUBORDER_INFO_ID=aoi.ID


    </sql>

    <sql id="whereSql">
        <where>
            aoi.ORDER_TYPE_CODE in ('01','02','03') and apr.PAY_TYPE in ('1','3','4')
            <if test="dto.orderBeginDate != null and dto.orderBeginDate !=''">
                and aoi.ORDER_DATE &gt;= date_format(#{dto.orderBeginDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="dto.orderEndDate != null and dto.orderEndDate !='' ">
                and aoi.ORDER_DATE &lt;= date_format(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>
            <if test="dto.flightBeginDate != null and dto.flightBeginDate !='' ">
                and aoi.FLIGHT_DATE &gt;= date_format(#{dto.flightBeginDate},'%Y-%m-%d')
            </if>
            <if test="dto.flightEndDate != null and dto.flightEndDate !='' ">
                and aoi.FLIGHT_DATE &lt;= date_format(#{dto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="dto.flightNo != null and dto.flightNo !='' ">
                and aoi.FLIGHT_NO like concat('%',#{dto.flightNo},'%')
            </if>
             <if test="dto.orderType != null and dto.orderType !='' ">
                 AND find_in_set(aoi.ORDER_TYPE_CODE, #{dto.orderType})
            </if>
            <if test="dto.payType != null and dto.payType !='' ">
                AND find_in_set(apr.PAY_TYPE, #{dto.payType})
            </if>
        </where>
    </sql>

    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo">
        <include refid="payTypesSummaryMoneySql"></include>
    </select>

    <sql id="payTypesSummaryMoneySql">
        select t.* from (
            SELECT
            sum(CASE WHEN rs.chargeType = '0' THEN rs.ORDER_PRICE ELSE 0 end ) AS wxMoney,
            sum(CASE WHEN rs.chargeType = '1' THEN rs.ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
            sum(CASE WHEN rs.chargeType = '2' THEN rs.ORDER_PRICE ELSE 0 end ) AS alipayMoney,
            sum(CASE WHEN (rs.chargeType = '3' or rs.chargeType = '4') THEN rs.ORDER_PRICE ELSE 0 end) AS cashMoney,
            sum(CASE WHEN rs.chargeType = '5' THEN rs.ORDER_PRICE ELSE 0 end) AS worldPay,
            sum(rs.ORDER_PRICE ) AS allMoney,
            case rs.CURRENCY_SPECIES
            when 'CNY' then concat(rs.CURRENCY_SPECIES,'(人民币)')
            else concat(rs.CURRENCY_SPECIES,'(',(SELECT DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND
            DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
            end as currencySpecies
            from(
            SELECT
            aoi.CURRENCY_SPECIES,
            aoi.CHARGE_TYPE AS chargeType,
            CASE apr.PAY_TYPE
            when '3' then
            concat('-',aoi.ORDER_PRICE)
            else aoi.ORDER_PRICE
            end as ORDER_PRICE,
            aoi.ORDER_PRICE AS totalPrice
            FROM
            ao_merge_line_payment acu
            LEFT JOIN
            ao_order_info aoi
            ON
            acu.AO_MERGE_SUBORDER_INFO_ID = aoi.ID
            LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
            <include refid="whereSql"></include>
            ) rs
            group by rs.CURRENCY_SPECIES
        ) t where t.allMoney > 0
    </sql>
</mapper>
