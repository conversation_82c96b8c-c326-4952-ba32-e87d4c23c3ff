package com.swcares.psi.admin.controller;

import com.swcares.psi.admin.api.config.HumanResourceSyncConfiguration;
import com.swcares.psi.admin.api.dto.HRSOrgCreateDto;
import com.swcares.psi.admin.api.dto.HRSOrgDeleteDto;
import com.swcares.psi.admin.api.dto.HRSOrgQueryDto;
import com.swcares.psi.admin.api.dto.HRSOrgUpdateDto;
import com.swcares.psi.admin.api.dto.HRSUserCreateDto;
import com.swcares.psi.admin.api.dto.HRSUserDeleteDto;
import com.swcares.psi.admin.api.dto.HRSUserQueryDto;
import com.swcares.psi.admin.api.dto.HRSUserUpdateDto;
import com.swcares.psi.admin.api.dto.HumanResourceSyncDto;
import com.swcares.psi.admin.api.vo.HRSAllOrgIdVo;
import com.swcares.psi.admin.api.vo.HRSAllUserIdVo;
import com.swcares.psi.admin.api.vo.HRSDataFieldsVo;
import com.swcares.psi.admin.api.vo.HRSOrgQueryVo;
import com.swcares.psi.admin.api.vo.HRSUserQueryVo;
import com.swcares.psi.admin.api.vo.HumanResourceSyncVo;
import com.swcares.psi.admin.service.SaHumanResourceSyncService;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.admin.controller.SaHumanResourceSyncController <br>
 * Description：人力资源数据同步控制器（提供外部调用）<br>
 * @note: 接口基于对方的规范进行定义，返回结果通过 {@code hrsAop} AOP进行差异化处理
 * {@link com.swcares.psi.outerStation.handle.BadRequestExceptionHandler} 接口产生异常处理
 * @note: 同步删除接口实际不被调用
 * Copyright © 2023/6/13 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * @date 2023/6/13 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RequestMapping("/api/hrsync")
@RestController
public class SaHumanResourceSyncController {

    @Resource
    private HumanResourceSyncConfiguration humanResourceSyncConfiguration;

    @Autowired
    private SaHumanResourceSyncService humanResourceSyncService;

    @ApiOperation(value = "需同步的用户、部门的属性列表查询")
    @PostMapping("/SchemaService")
    public HRSDataFieldsVo dataFields(@RequestBody HumanResourceSyncDto dto) {
        return humanResourceSyncService.dataFields(dto);
    }

    @ApiOperation(value = "基于唯一标识查询用户")
    @PostMapping("/QueryUserByIdService")
    public HRSUserQueryVo queryUser(@RequestBody HRSUserQueryDto dto) {
        return humanResourceSyncService.queryUser(dto);
    }

    @ApiOperation(value = "查询已有用户，返回用户唯一标识列表")
    @PostMapping("/QueryAllUserIdsService")
    public HRSAllUserIdVo queryAllUserIds(@RequestBody HumanResourceSyncDto dto) {
        return humanResourceSyncService.queryAllUserIds(dto);
    }

    @ApiOperation(value = "创建用户")
    @PostMapping("/UserCreateService")
    public HumanResourceSyncVo userCreate(@RequestBody HRSUserCreateDto dto) {
        return humanResourceSyncService.userCreate(dto);
    }

    @Deprecated
    @ApiOperation(value = "删除用户")
    @PostMapping("/UserDeleteService")
    public HumanResourceSyncVo userDelete(@RequestBody HRSUserDeleteDto dto) {
        return humanResourceSyncService.userDelete(dto);
    }

    @ApiOperation(value = "更新用户")
    @PostMapping("/UserUpdateService")
    public HumanResourceSyncVo userUpdate(@RequestBody HRSUserUpdateDto dto) {
        return humanResourceSyncService.userUpdate(dto);
    }

    @ApiOperation(value = "基于部门唯一标识查询部门")
    @PostMapping("/QueryOrgByIdService")
    public HRSOrgQueryVo queryOrg(@RequestBody HRSOrgQueryDto dto) {
        return humanResourceSyncService.queryOrg(dto);
    }

    @ApiOperation(value = "查询已有部门，返回部门唯一标识列表")
    @PostMapping("/QueryAllOrgIdsService")
    public HRSAllOrgIdVo queryAllOrgIds(@RequestBody HumanResourceSyncDto dto) {
        return humanResourceSyncService.queryAllOrgIds(dto);
    }

    @ApiOperation(value = "创建部门")
    @PostMapping("/OrgCreateService")
    public HumanResourceSyncVo orgCreate(@RequestBody HRSOrgCreateDto dto) {
        return humanResourceSyncService.orgCreate(dto);
    }

    @Deprecated
    @ApiOperation(value = "删除部门")
    @PostMapping("/OrgDeleteService")
    public HumanResourceSyncVo orgDelete(@RequestBody HRSOrgDeleteDto dto) {
        return humanResourceSyncService.orgDelete(dto);
    }

    @ApiOperation(value = "更新部门")
    @PostMapping("/OrgUpdateService")
    public HumanResourceSyncVo orgUpdate(@RequestBody HRSOrgUpdateDto dto) {
        return humanResourceSyncService.orgUpdate(dto);
    }

}
