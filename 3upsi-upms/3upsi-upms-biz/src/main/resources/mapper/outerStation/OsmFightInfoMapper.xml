<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.outerStation.mapper.OsmFightInfoMapper">
    <update id="updateFightInfo">
        UPDATE osm_flight_info
        <trim prefix="set" suffixOverrides=",">
            BAY=#{info.bay}, FLIGHT_MODEL=#{info.flightModel}, PLANE_CODE=#{info.planeCode},
            INCOMING_FLIGHT_NO=#{info.incomingFlightNo}, INCOMING_PAX_NUM=#{info.incomingPaxNum}, INCOMING_UM_PAX_NUM=#{info.incomingUmPaxNum},
            INCOMING_WC_PAX_NUM=#{info.incomingWcPaxNum}, INCOMING_SC_PAX_NUM=#{info.incomingScPaxNum}, OPEN_HATCH_TIME=#{info.openHatchTime},
            CLOSE_HATCH_TIME=#{info.closeHatchTime}, INCOMING_DATE=#{info.incomingDate}, REAl_LANDING_TIME=#{info.realLandingTime},
            FLT_BOARDING_DATE=#{info.fltBoardingDate}, GUEST_NUM=#{info.guestNum}, CHECK_REMARK=#{info.checkRemark},COBT_CDM=#{info.cobtCdm},
            LEAVE_STATION_NUM=#{info.leaveStationNum}, PASS_NUM=#{info.passNum},
            OUT_UM_PAX_NUM=#{info.outUmPaxNum}, OUT_WC_PAX_NUM=#{info.outWcPaxNum}, OUT_SC_PAX_NUM=#{info.outScPaxNum},
            OUT_VIP_NUM=#{info.outVipNum}, OVERBOOKING_REAL_NUM=#{info.overbookingRealNum}, OVER_PAY_MONEY=#{info.overPayMoney},
            OVERBOOKING_REMARK=#{info.overbookingRemark}, ORGAN_TRANSPORT=#{info.organTransport}, IMPORT_TYPE=#{info.importType},
            DELAY_TIME=#{info.delayTime}, DELAY_REASON=#{info.delayReason}, DELAY_REMARK=#{info.delayRemark}, IMPORTANT_REMARK=#{info.importantRemark},
            OA_REMARK=#{info.oaRemark}, FLIGHT_SUPPORT_STAFF=#{info.flightSupportStaff},
            DELAY_TYPE=#{info.delayType}, FLIGHT_TYPE=#{info.flightType},
            <if test="info.appealReason != null and info.appealReason != ''">
                APPEAL_REASON=#{info.appealReason},
            </if>
            <if test="info.updater != null and info.updater != ''">
                UPDATER=#{info.updater},
            </if>
            <if test="info.updateTime != null">
                UPDATE_TIME=#{info.updateTime},
            </if>
            <if test="info.appealStatus != null and info.appealStatus != ''">
                APPEAL_STATUS=#{info.appealStatus},
            </if>
            <if test="info.submitter != null and info.submitter != ''">
                SUBMITTER=#{info.submitter},
            </if>
            <if test="info.subTime != null">
                SUB_TIME=#{info.subTime},
            </if>
            <if test="info.subStatus != null">
                SUB_STATUS=#{info.subStatus},
            </if>
        </trim>
        WHERE ID=#{info.id}
    </update>

    <select id="flightGuaranteedPage" resultType="com.swcares.psi.outerStation.vo.OsmFlightInfoPageVo">
        SELECT
        FLT.ID AS id,
        FLT.FLIGHT_NUMBER AS flightNo,
        FLT.STD AS std,
        FLT.STA AS sta,
        FLT.ORG AS org,
        FLT.DST AS dst,
        CONCAT(get_city_name(FLT.ORG),FLT.ORG) AS orgName,
        CONCAT(get_city_name(FLT.DST),FLT.DST) AS dstName,
        IFNULL(OFI.SUB_STATUS, '0') AS subStatus,
        IFNULL(OFI.APPEAL_STATUS, '0') AS appealStatus
        FROM
        FLT_FLIGHT_REAL_INFO FLT
        LEFT join
        (SELECT * FROM OSM_FLIGHT_INFO OFI
        INNER JOIN
        (SELECT OFI.FLT_FLIGHT_ID AS FLIGHT_ID,MAX(OFI.APPEAL_STATUS) AS STATUS
        FROM OSM_FLIGHT_INFO OFI
        GROUP BY OFI.FLT_FLIGHT_ID) OFIS
        ON OFI.FLT_FLIGHT_ID = OFIS.FLIGHT_ID
        AND OFI.APPEAL_STATUS = OFIS.STATUS) OFI ON FLT.ID = OFI.FLT_FLIGHT_ID
        WHERE
            FLT.FLIGHT_DATE = #{param.flightDate}
            AND (FLT.ORG = #{param.terminal} OR FLT.DST = #{param.terminal})
    </select>

    <select id="flightGuaranteedEntryData" parameterType="java.lang.String" resultType="com.swcares.psi.outerStation.vo.OsmFlightInfoVo">
        SELECT
        FT1.FLIGHT_NO AS flightNo,
        FT1.FLIGHT_NO AS incomingFlightNo,
        DATE_FORMAT(FT1.FLIGHT_DATE, '%Y%m%d') AS flightDate,
        FT1.DST AS dst,
        FT1.AC_REG AS planeCode,
        FT1.AC_TYPE AS flightModel,
        DATE_FORMAT(FT1.ATA, '%Y-%m-%d') AS incomingDate,
        DATE_FORMAT(FT1.ATA, '%H:%i') AS realLandingTime,
        COUNT(1) AS incomingPaxNum,
        CASE WHEN (FT1.ORG = #{terminal} AND FT1.FLIGHT_STATE = 'D' AND (FT1.ETD is not null OR FT1.ATD is not null)  AND FT1.STD is not null) THEN IF(ISNULL(FT1.ATD) = 1, timediff(FT1.ETD, FT1.STD), timediff(FT1.ATD, FT1.STD))
        WHEN (FT1.DST = #{terminal} AND FT1.FLIGHT_STATE = 'D' AND (FT1.ETA is not null OR FT1.ATA is not null) AND FT1.STA is not null) THEN IF(ISNULL(FT1.ATA) = 1, timediff(FT1.ETA, FT1.STA), timediff(FT1.ATA, FT1.STA))
        ELSE '' END AS delayTime,
        IFNULL(FT2.ABNORMAL_CODE, IFNULL(FT3.ABNORMAL_CODE, '')) AS delayReason
        FROM
        (SELECT FT1.*, FLT.ID AS FLTID, FLT.DST, FLT.ORG,FLT.FLIGHT_STATE
        FROM FOC50_T2001 FT1
        LEFT JOIN FLT_FLIGHT_REAL_INFO flt ON FT1.FLIGHT_ID = flt.FOC_ID
        WHERE FLT.ID = #{flightId}
        ORDER BY FT1.LOAD_TIME DESC LIMIT 1) FT1
        LEFT JOIN FLT_PASSENGER_REAL_INFO FPRI ON
        (FT1.FLIGHT_NO = FPRI.FLIGHT_NUMBER
        AND FT1.FLIGHT_DATE = FPRI.FLIGHT_DATE
        AND FT1.DST = FPRI.DST
        AND FT1.ORG = FPRI.ORG)
        LEFT JOIN
        (SELECT ABNORMAL_CODE, FLIGHT_ID FROM FOC50_T2002 FT2
        LEFT JOIN FOC50_T2006 FT26 ON FT2.DELAY_CODE1 = FT26.ABNORMAL_CODE) FT2
        ON FT2.FLIGHT_ID = FT1.ID
        LEFT JOIN
        (SELECT ABNORMAL_CODE, FLIGHT_ID FROM FOC50_T2003 FT3
        LEFT JOIN FOC50_T2006 FT36 ON FT3.CANCEL_CODE = FT36.ABNORMAL_CODE) FT3
        ON FT3.FLIGHT_ID = FT1.ID
        WHERE FT1.FLTID = #{flightId}
    </select>
    <select id="flightSupportStaff" resultType="com.swcares.psi.outerStation.vo.OsmDropConfigVo">
        SELECT
            E.TUNO AS code,
            E.TU_CNAME AS name
        FROM
            employee E
        WHERE
            E.CITY_3CODE = #{terminal}
            AND E.USER_STATE = 0
            AND E.TOID = '000032014001'
    </select>
    <select id="flightSupportStaffAll" resultType="com.swcares.psi.outerStation.vo.OsmDropConfigVo">
        SELECT
        IFNULL(E.TUNO, '') AS code,
        IFNULL(E.TU_CNAME, '') AS name
        FROM
        employee E
        WHERE
        E.USER_STATE = 0
        AND E.TOID = '000032014001'
    </select>
    <select id="getFlightDelayReason" resultType="com.swcares.psi.outerStation.vo.OsmDropConfigVo">
        SELECT
            FT.ABNORMAL_CODE AS code,
            FT.ABNORMAL_CH_NAME AS name
        FROM
            foc50_t2006 FT
    </select>
    <select id="getOsmDrop" resultType="com.swcares.psi.outerStation.vo.OsmDropConfigVo">
        SELECT
            SD.DATA_VALUE AS code,
            SD.DATA_REMARK AS name
        FROM
            sys_dict SD
        WHERE
            SD.DATA_CODE LIKE #{code}
            AND SD.DATA_STATUS = '0'
    </select>
    <select id="flightGuaranteedMonitorPage" resultType="com.swcares.psi.outerStation.vo.FlightGuaranteedMonitorPageVo">
        SELECT
        OFI.ID AS id,
        FLT.DST AS dst,
        OFI.BAY AS bayCode,
        SD1.DATA_REMARK AS bay,
        SD2.DATA_REMARK AS flightType,
        OFI.FLIGHT_TYPE AS flightTypeCode,
        OFI.FLT_FLIGHT_ID AS fltFlightId,
        OFI.FLT_FLIGHT_ID AS flightId,
        OFI.INCOMING_FLIGHT_NO AS incomingFlightNo,
        FLT.FLIGHT_NUMBER AS flightNo,
        DATE_FORMAT(FLT.FLIGHT_DATE, '%Y-%m-%d') AS flightDate,
        OFI.PLANE_CODE AS planeCode,
        OFI.FLIGHT_MODEL AS flightModel,
        DATE_FORMAT(FLT.ATD, '%H:%i') AS realTakeOffTime,
        DATE_FORMAT(OFI.INCOMING_DATE, '%Y-%m-%d') AS incomingDate,
        OFI.REAl_LANDING_TIME AS realLandingTime,
        OFI.INCOMING_PAX_NUM AS incomingPaxNum,
        OFI.INCOMING_UM_PAX_NUM AS incomingUmPaxNum,
        OFI.INCOMING_WC_PAX_NUM AS incomingWcPaxNum,
        OFI.INCOMING_SC_PAX_NUM AS incomingScPaxNum,
        OFI.OPEN_HATCH_TIME AS openHatchTime,
        OFI.CLOSE_HATCH_TIME AS closeHatchTime,
        OFI.FLT_BOARDING_DATE AS fltBoardingDate,
        OFI.GUEST_NUM AS guestNum,
        OFI.CHECK_REMARK AS checkRemark,
        OFI.COBT_CDM AS cobtCdm,
        OFI.LEAVE_STATION_NUM AS leaveStationNum,
        OFI.PASS_NUM AS passNum,
        OFI.OUT_UM_PAX_NUM AS outUmPaxNum,
        OFI.OUT_WC_PAX_NUM AS outWcPaxNum,
        OFI.OUT_SC_PAX_NUM AS outScPaxNum,
        OFI.OUT_VIP_NUM AS outVipNum,
        OFI.OVERBOOKING_REAL_NUM AS overbookingRealNum,
        OFI.OVER_PAY_MONEY AS overPayMoney,
        OFI.OVERBOOKING_REMARK AS overbookingRemark,
        CASE
        WHEN OFI.ORGAN_TRANSPORT = '0' THEN '否'
        WHEN OFI.ORGAN_TRANSPORT = '1' THEN '是'
        END AS organTransportStatus,
        OFI.ORGAN_TRANSPORT AS organTransport,
        CASE
        WHEN OFI.IMPORT_TYPE = '0' THEN '一级'
        WHEN OFI.IMPORT_TYPE = '1' THEN '二级'
        WHEN OFI.IMPORT_TYPE = '2' THEN '三级'
        END AS importType,
        OFI.IMPORT_TYPE AS importTypeCode,
        OFI.DELAY_TIME AS delayTime,
        OFI.DELAY_REMARK AS delayRemark,
        OFI.IMPORTANT_REMARK AS importantRemark,
        OFI.OA_REMARK AS oaRemark,
        OFI.FLIGHT_SUPPORT_STAFF AS flightSupportStaffCode,
        CASE WHEN OFI.SUB_STATUS = '0' THEN '未提交'
        WHEN OFI.SUB_STATUS = '1' THEN '已提交' END AS subStatus,
        OFI.SUB_STATUS AS subStatusCode,
        OFI.APPEAL_STATUS AS appealStatusCode,
        OFI.DELAY_REASON AS delayReasonCode,
        FT.ABNORMAL_CH_NAME AS delayReason,
        CASE WHEN OFI.APPEAL_STATUS = '0' THEN '未申诉'
        WHEN OFI.APPEAL_STATUS = '1' THEN '未处理'
        WHEN OFI.APPEAL_STATUS = '2' THEN '同意'
        WHEN OFI.APPEAL_STATUS = '3' THEN '不同意' END AS appealStatus,
        OFI.APPEAL_REASON AS appealReason,
        E.TU_CNAME AS submitter,
        OFI.SUB_TIME AS subTime
        from
        OSM_FLIGHT_INFO OFI
        INNER JOIN
        (SELECT OFI.FLT_FLIGHT_ID,MAX(OFI.APPEAL_STATUS) AS APPEAL_STATUS
        FROM OSM_FLIGHT_INFO OFI
        GROUP BY OFI.FLT_FLIGHT_ID) OFIS
        ON OFI.FLT_FLIGHT_ID = OFIS.FLT_FLIGHT_ID
        AND OFI.APPEAL_STATUS = OFIS.APPEAL_STATUS
        LEFT JOIN EMPLOYEE E ON E.TUNO = OFI.SUBMITTER
        LEFT JOIN FLT_FLIGHT_REAL_INFO FLT ON FLT.ID = OFI.FLT_FLIGHT_ID
        LEFT JOIN SYS_DICT SD2 ON (SD2.DATA_CODE LIKE 'osm_config_flight_type%'
        AND SD2.DATA_STATUS = '0'
        AND SD2.DATA_VALUE = OFI.FLIGHT_TYPE)
        LEFT JOIN SYS_DICT SD1 ON
        (SD1.DATA_CODE like 'osm_config_bay%'
        AND SD1.DATA_STATUS = '0'
        AND SD1.DATA_VALUE = OFI.BAY)
        LEFT JOIN FOC50_T2006 FT ON
        FT.ABNORMAL_CODE = OFI.DELAY_REASON
        <where>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                FLT.FLIGHT_NUMBER = #{dto.flightNo}
            </if>
            <if test="dto.org != null and dto.org != ''">
                AND FLT.ORG = #{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst != ''">
                AND FLT.DST = #{dto.dst}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND FLT.FLIGHT_DATE <![CDATA[>=]]> DATE_FORMAT(#{dto.startTime}, '%Y-%m-%d')
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND FLT.FLIGHT_DATE <![CDATA[<=]]> DATE_FORMAT(#{dto.endTime}, '%Y-%m-%d')
            </if>
            <if test="dto.appealStatus != null and dto.appealStatus != ''">
                AND OFI.APPEAL_STATUS = #{dto.appealStatus}
            </if>
            <if test="dto.subStatus != null and dto.subStatus != ''">
                AND OFI.SUB_STATUS = #{dto.subStatus}
            </if>
            <if test="dto.subUserName != null and dto.subUserName != ''">
                AND OFI.SUB_STATUS = '1'
                AND E.TU_CNAME LIKE CONCAT('%',#{dto.subUserName},'%')
            </if>
        </where>
        ORDER BY FLT.FLIGHT_DATE DESC, FLT.FLIGHT_NUMBER DESC
    </select>
    <select id="flightGuaranteedData" resultType="com.swcares.psi.outerStation.vo.OsmFlightInfoVo">
        SELECT
        FT1.FLIGHT_NO AS flightNo,
        OFI.INCOMING_FLIGHT_NO AS flightNo,
        DATE_FORMAT(FT1.FLIGHT_DATE, '%Y%m%d') AS flightDate,
        FT1.DST AS dst,
        OFI.PLANE_CODE AS planeCode,
        OFI.FLIGHT_MODEL AS flightModel,
        DATE_FORMAT(OFI.INCOMING_DATE, '%Y-%m-%d') AS incomingDate,
        OFI.REAl_LANDING_TIME AS realLandingTime,
        OFI.FLIGHT_TYPE AS flightType,
        OFI.BAY AS bay,
        OFI.ID AS id,
        OFI.INCOMING_FLIGHT_NO AS incomingFlightNo,
        OFI.INCOMING_PAX_NUM AS incomingPaxNum,
        OFI.INCOMING_UM_PAX_NUM AS incomingUmPaxNum,
        OFI.INCOMING_WC_PAX_NUM AS incomingWcPaxNum,
        OFI.INCOMING_SC_PAX_NUM AS incomingScPaxNum,
        OFI.OPEN_HATCH_TIME AS openHatchTime,
        OFI.CLOSE_HATCH_TIME AS closeHatchTime,
        OFI.FLT_BOARDING_DATE AS fltBoardingDate,
        OFI.GUEST_NUM AS guestNum,
        OFI.CHECK_REMARK AS checkRemark,
        OFI.COBT_CDM AS cobtCdm,
        OFI.LEAVE_STATION_NUM AS leaveStationNum,
        OFI.PASS_NUM AS passNum,
        OFI.OUT_UM_PAX_NUM AS outUmPaxNum,
        OFI.OUT_WC_PAX_NUM  AS outWcPaxNum,
        OFI.OUT_SC_PAX_NUM AS outScPaxNum,
        OFI.OUT_VIP_NUM AS outVipNum,
        OFI.OVERBOOKING_REAL_NUM AS overbookingRealNum,
        OFI.OVER_PAY_MONEY AS overPayMoney,
        OFI.OVERBOOKING_REMARK AS overbookingRemark,
        OFI.ORGAN_TRANSPORT AS organTransport,
        OFI.IMPORT_TYPE AS importType,
        OFI.DELAY_TIME AS delayTime,
        OFI.DELAY_TYPE AS delayType,
        OFI.SUB_STATUS AS subStatus,
        OFI.APPEAL_REASON AS appealReason,
        OFI.OA_REMARK AS oaRemark,
        OFI.DELAY_REMARK AS delayRemark,
        OFI.IMPORTANT_REMARK AS importantRemark,
        OFI.APPEAL_STATUS AS appealStatus,
        OFI.DELAY_REASON AS delayReason,
        OFI.FLIGHT_SUPPORT_STAFF AS flightSupportStaff
        FROM
        (SELECT FT1.*, FLT.ID AS FLTID, FLT.DST, FLT.ORG,FLT.FLIGHT_STATE
        FROM FOC50_T2001 FT1
        LEFT JOIN FLT_FLIGHT_REAL_INFO flt ON FT1.FLIGHT_ID = flt.FOC_ID
        WHERE FLT.ID = #{flightId}
        ORDER BY FT1.LOAD_TIME DESC LIMIT 1) FT1
        LEFT JOIN OSM_FLIGHT_INFO OFI ON FT1.FLTID = OFI.FLT_FLIGHT_ID
        WHERE FT1.FLTID = #{flightId}
        ORDER BY OFI.APPEAL_STATUS DESC
        LIMIT 1
    </select>
    <select id="osmModelDropConfig" resultType="com.swcares.psi.outerStation.vo.OsmDropConfigVo">
        SELECT
            DISTINCT
            AC_TYPE AS code,
            AC_TYPE AS name
        FROM
            FOC50_T2001
        WHERE
            AC_TYPE IS NOT NULL
    </select>
    <select id="getFlightSegment" resultType="com.swcares.psi.outerStation.dto.FlightSegmentDto">
        SELECT
        F1.ID AS flightId,
        CASE
        WHEN F1.ORG = F2.DST THEN CONCAT(F2.ORG, get_city_name(F2.ORG), '-', F2.DST, get_city_name(F2.DST), ',', F1.ORG, get_city_name(F1.ORG), '-', F1.DST, get_city_name(F1.DST))
        WHEN F2.ORG = F1.DST THEN CONCAT(F1.ORG, get_city_name(F1.ORG), '-', F1.DST, get_city_name(F1.DST), ',', F2.ORG, get_city_name(F2.ORG), '-', F2.DST, get_city_name(F2.DST))
        WHEN ISNULL(F1.ID) = 1 THEN  CONCAT(F2.ORG, get_city_name(F2.ORG), '-', F2.DST, get_city_name(F2.DST))
        WHEN ISNULL(F2.ID) = 1 THEN  CONCAT(F1.ORG, get_city_name(F1.ORG), '-', F1.DST, get_city_name(F1.DST)) END AS flightSegment
        FROM FLT_FLIGHT_REAL_INFO F1
        LEFT JOIN FLT_FLIGHT_REAL_INFO F2 ON F1.FLIGHT_NUMBER = F2.FLIGHT_NUMBER
        AND F1.FLIGHT_DATE = F2.FLIGHT_DATE
        AND (F1.ORG = F2.DST OR F1.DST = F2.ORG)
        WHERE
            F1.ID IN
            <foreach collection="list" item="dto" separator="," open="(" close=")">
                #{dto.flightId}
            </foreach>
    </select>
    <select id="selectFlightGuaranteedEntryData"
            resultType="com.swcares.psi.outerStation.entity.OsmFlightInfo">
        SELECT
        *
        FROM OSM_FLIGHT_INFO OFI
        INNER JOIN
        (SELECT OFI.FLT_FLIGHT_ID,MAX(OFI.APPEAL_STATUS) AS APPEAL_STATUS
        FROM OSM_FLIGHT_INFO OFI
        GROUP BY OFI.FLT_FLIGHT_ID) OFIS
        ON OFI.FLT_FLIGHT_ID = OFIS.FLT_FLIGHT_ID
        AND OFI.APPEAL_STATUS = OFIS.APPEAL_STATUS
        WHERE OFI.FLT_FLIGHT_ID = #{flightId}
    </select>

</mapper>
