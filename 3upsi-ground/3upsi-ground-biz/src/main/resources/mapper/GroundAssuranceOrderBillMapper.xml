<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ground.assurance.mapper.GroundAssuranceOrderBillMapper">

    <sql id = "search" >
        t4.STATE = 1  and t5.ORDER_TYPE ='0'
        <if test="dto.flightDateStar != null and dto.flightDateStar !='' ">
            and t5.FLIGHT_DATE <![CDATA[ >=  ]]> #{dto.flightDateStar}
        </if>
        <if test="dto.flightDateEnd != null and dto.flightDateEnd !='' ">
            and t5.FLIGHT_DATE <![CDATA[ <=  ]]> #{dto.flightDateEnd}
        </if>
        <if test="dto.serviceAirports != null and dto.serviceAirports.size() > 0 ">
            and t5.SERVICE_AIRPORT in
            <foreach collection="dto.serviceAirports" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.serviceItemIds != null and dto.serviceItemIds.size() > 0 ">
            and t8.SERVICE_ITEM_ID in
            <foreach collection="dto.serviceItemIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.paxName != null and dto.paxName !='' ">
            and UPPER(t4.passr_name) like CONCAT('%',UPPER(#{dto.paxName}),'%')
        </if>
        <if test="dto.tktNo != null and dto.tktNo !='' ">
            and t4.TICKET_NUMBER = #{dto.tktNo}
        </if>
        <if test="dto.confirmDateStar != null and dto.confirmDateStar !='' ">
            and  t3.CONFIRM_STATUS &lt;&gt; 2
            and t3.CONFIRM_TIME <![CDATA[ >=  ]]> #{dto.confirmDateStar}
        </if>
        <if test="dto.confirmDateEnd != null and dto.confirmDateEnd !='' ">
            and t3.CONFIRM_TIME <![CDATA[ <=  ]]> concat(#{dto.confirmDateEnd},' 23:59:59')
        </if>
        <if test="dto.states != null and dto.states.size() > 0 ">
            and t5.state in
            <foreach collection="dto.states" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="dpAssuranceOrderBillAuditVoSql">
        select
        t.ID as id,
        t.SUPPLIER_ID as supplierId,
        t1.SUPPLIER as supplierName,
        t.BILL_NO as billNo,
        t.TOTAL_PRICE as totalPrice,
        t.BILL_BEGIN_DATE as billBeginDate,
        t.BILL_END_DATE as billEndDate,
        t.BILL_STATUS as billStatus,
        t.CREATE_TIME as createTime
        from
        ground_assurance_order_bill t
        join ground_service_supplier t1 on t.SUPPLIER_ID = t1.ID
    </sql>
    <select id="queryAuditPage" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBillAuditVo">
        <include refid="dpAssuranceOrderBillAuditVoSql"/>
        <where>
            <if test="paramDto.supplierId != null and paramDto.supplierId != ''">
                and t.SUPPLIER_ID in
                <foreach collection="paramDto.supplierIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="paramDto.startCreateDateTime != null ">
                and t.CREATE_TIME &gt;= #{paramDto.startCreateDateTime}
            </if>
            <if test="paramDto.endCreateDateTime != null ">
                and t.CREATE_TIME &lt;= #{paramDto.endCreateDateTime}
            </if>
            <choose>
                <when test="paramDto.isFinanceOperated == 0">
                        and t.BILL_STATUS in
                        <foreach collection="paramDto.billStatuses" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                </when>
                <when test="paramDto.isFinanceOperated == 1">
                and (  1&lt;&gt;1
                    <if test='paramDto.billStatuses.contains("1")'>
                        or   (t.BILL_STATUS = '1')
                    </if>
                    <if test='paramDto.billStatuses.contains("2")'>
                        or   (t.BILL_STATUS = '2')
                    </if>
                    <if test='paramDto.billStatuses.contains("0")'>
                        or  (t.BILL_STATUS = '0' and IS_FINANCE_OPERATED = '1')
                    </if>
                    <if test='paramDto.billStatuses.contains("3")'>
                        or  (t.BILL_STATUS = '3' and IS_FINANCE_OPERATED = '1')
                    </if>
                    )
                </when>
            </choose>
        </where>
        order by t.BILL_STATUS,t.CREATE_TIME
    </select>

    <select id="queryAuditBillById" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBillAuditVo">
        <include refid="dpAssuranceOrderBillAuditVoSql"/>
        where t.id = #{billId}
    </select>

    <update id="clearAssuranceOrderContractItemsBillId">
        update ground_assurance_order_contract_items set CURRENT_BILL_ID = null where CURRENT_BILL_ID = #{billId}
    </update>


<sql id="businessQuery" >
    select
    t5.FLIGHT_DATE as flightDate,
    t5.FLIGHT_NUMBER as flightNo,
    t5.ORG as org,
    t5.DST as dst,
    t5.SERVICE_AIRPORT as serviceAirport,
    t6.DELAY_REASON as delayReason,
    t6.DELAY_TYPE as delayType,
    t5.REMARK as remark,
    t5.ORDER_NO as orderNo,
    (case
    when ( t5.STATE = '2'
    or t5.STATE = '3') then '生效'
    when t5.STATE = '11' then '已逾期'
    when t5.STATE = '12' then '已关闭'
    end
    ) state,
    (case when t4.USER_TYPE = 0 then t4.PASSR_NAME
    when t4.USER_TYPE = 2 then ft.C_NAME
    end ) paxName,
    t4.TICKET_NUMBER as ticketNumber,
    t8.SERVICE_ITEM_NAME as serviceItemName,
    t8.SERVICE_ITEM_ID as serviceItemId,
    t3.CONFIRM_STATUS as confirmStatus,
    t2.SUPPLIER_ID as supplierId,
    t2.SUPPLIER_NAME as supplierName,
    t3.PRICE as price,
    (case when e1.USER_TYPE = 12 then TU_CNAME
    else  concat(e1.TUNO, '-', e1.TU_CNAME)
    end
    ) confirmUser,
    t3.CONFIRM_TYPE as confirmType,
    DATE_FORMAT(t3.CONFIRM_TIME,'%Y-%m-%d %H:%i:%S') as confirmTime
    from
    ground_assurance_order_user_service_item t3
    join ground_assurance_order_service_item t8 on t3.ORDER_SERVICE_ITEM_ID = t8.ID
    join ground_assurance_order_user_detail t4 on
    t3.USER_DETAIL_ID = t4.ID
    left join foc50_t3017 ft on t4.P_CODE = ft.P_CODE
    join ground_assurance_order t5 on
    t4.ORDER_ID = t5.id
    and t5.ORDER_TYPE = 0
    and t5.STATE not in (0, 1)
    join ground_assurance_dp_order t6 on
    t6.ORDER_ID = t5.ID
    left join ground_assurance_order_contract_items t2 on
    t2.USER_CATEGORY_ITEM_ID = t3.ID
    left join employee as e1 on t3.CONFIRM_USER = e1.id
    where
   <include refid="search"/>
    order by t5.FLIGHT_DATE desc ,t3.CONFIRM_STATUS desc ,t3.CONFIRM_TIME desc
</sql>

    <sql id="summaryBusinessQuery">
        select
    DATE_FORMAT(t5.FLIGHT_DATE, '%Y-%m-%d') flightDate,
    t5.flight_number flightNo,
    t8.SERVICE_ITEM_NAME serviceItem,
    t3.SUPPLIER_NAME supplier,
    sum(ifnull(t3.PRICE,0)) price,
    count(*) serviceCount
        <include refid="summaryBusinessForm"/>
    </sql>

    <sql id="summaryBusinessForm">
        from
        ground_assurance_order_user_service_item t3
        join ground_assurance_order_service_item t8 on t3.ORDER_SERVICE_ITEM_ID = t8.ID
        join ground_assurance_order_user_detail t4 on
        t3.USER_DETAIL_ID = t4.ID
        join ground_assurance_order t5 on
        t4.ORDER_ID = t5.id
        and t5.ORDER_TYPE = 0
        and t5.STATE not in (0, 1)
        join ground_assurance_dp_order t6 on
        t6.ORDER_ID = t5.ID
        left join ground_assurance_order_contract_items t2 on
        t2.USER_CATEGORY_ITEM_ID = t3.ID
        where
        <include refid="search"/>
    </sql>

    <select id="queryBusinessReportPage" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBusinessReportVo">
        <include refid="businessQuery"/>
    </select>

    <select id="downBusinessReport" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBusinessReportVo">
        select
        date_format( t5.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        t5.FLIGHT_NUMBER as flightNo,
        ifnull((select concat(s.AIRPORT_NAME,'-',s.CODE)  from sys_airport_info s where s.CODE=t5.ORG),t5.ORG) org,
        ifnull((select concat(s.AIRPORT_NAME,'-',s.CODE)  from sys_airport_info s where s.CODE=t5.DST),t5.DST) dst,
        ifnull((select concat(s.AIRPORT_NAME,'-',s.CODE)  from sys_airport_info s where s.CODE=t5.SERVICE_AIRPORT),t5.SERVICE_AIRPORT) serviceAirport,
        t6.DELAY_REASON as delayReason,
        t6.DELAY_TYPE as delayType,
        t5.REMARK as remark,
        t5.ORDER_NO as orderNo,
        (case
        when ( t5.STATE = '2'
        or t5.STATE = '3') then '生效'
        when t5.STATE = '11' then '已逾期'
        when t5.STATE = '12' then '已关闭'
        end
        ) state,
        (case when t4.USER_TYPE = 0 then t4.PASSR_NAME
        when t4.USER_TYPE = 2 then ft.C_NAME
        end ) paxName,
        t4.TICKET_NUMBER as ticketNumber,
        t8.SERVICE_ITEM_NAME as serviceItemName,
        t8.SERVICE_ITEM_ID as serviceItemId,
        t3.CONFIRM_STATUS as confirmStatus,
        t2.SUPPLIER_ID as supplierId,
        t2.SUPPLIER_NAME as supplierName,
        t3.PRICE as price,
        (case when e1.USER_TYPE = 12 then TU_CNAME
        else  concat(e1.TUNO, '-', e1.TU_CNAME)
        end
        ) confirmUser,
        (case when t3.CONFIRM_STATUS = 2 then null else t3.CONFIRM_TYPE end ) as confirmType,
        (case when t3.CONFIRM_STATUS = 2 then null else DATE_FORMAT(t3.CONFIRM_TIME,'%Y-%m-%d %H:%i:%S') end )  as confirmTime
        from
        ground_assurance_order_user_service_item t3
        join ground_assurance_order_user_detail t4 on
        t3.USER_DETAIL_ID = t4.ID
        join ground_assurance_order_service_item t8 on t3.ORDER_SERVICE_ITEM_ID = t8.ID
        left join foc50_t3017 ft on t4.P_CODE = ft.P_CODE
        join ground_assurance_order t5 on
        t4.ORDER_ID = t5.id
        and t5.ORDER_TYPE = 0
        and t5.STATE not in (0, 1)
        join ground_assurance_dp_order t6 on
        t6.ORDER_ID = t5.ID
        left join ground_assurance_order_contract_items t2 on
        t2.USER_CATEGORY_ITEM_ID = t3.ID
        left join employee as e1 on t3.CONFIRM_USER = e1.id
        where
        <include refid="search"/>
        order by t5.FLIGHT_DATE desc ,t3.CONFIRM_STATUS desc ,t3.CONFIRM_TIME desc
    </select>

    <resultMap id="summaryBySupplierMap" type="com.swcares.psi.ground.assurance.vo.BusinessSummaryBySupplierVO">
            <result column="supplier" property="supplier"/>
            <collection property="children" ofType="com.swcares.psi.ground.assurance.vo.FlightBySupplierVO">
                <result column="flightNo" property="flightNo"/>
                <result column="flightDate" property="flightDate"/>
                <collection property="children" ofType="com.swcares.psi.ground.assurance.vo.ServiceItemVO">
                    <result column="serviceItem" property="serviceItem"/>
                    <result column="serviceCount" property="serviceCount"/>
                    <result column="price" property="price"/>
                </collection>
            </collection>
    </resultMap>

    <select id="businessReportSummaryBySupplier" resultMap="summaryBySupplierMap">
        <include refid="summaryBusinessQuery"/>
        and t3.CONFIRM_STATUS = 1
        group by flightDate,flightNo,t2.SUPPLIER_NAME,t8.SERVICE_ITEM_NAME
    </select>

    <resultMap id="summaryByServiceItemMap" type="com.swcares.psi.ground.assurance.vo.BusinessSummaryByServiceItemVO">
            <result column="serviceItem" property="serviceItem"/>
            <collection property="children" ofType="com.swcares.psi.ground.assurance.vo.FlightByServiceItemVO">
                <result column="flightNo" property="flightNo"/>
                <result column="flightDate" property="flightDate"/>
                <collection property="children" ofType="com.swcares.psi.ground.assurance.vo.SupplierItemByServiceItemVO">
                    <result column="supplier" property="supplier"/>
                    <result column="serviceCount" property="serviceCount"/>
                    <result column="price" property="price"/>
                </collection>
            </collection>
    </resultMap>

    <select id="businessReportSummaryByServiceItem" resultMap="summaryByServiceItemMap">
        <include refid="summaryBusinessQuery"/>
        and t3.CONFIRM_STATUS = 1
        group by flightDate,flightNo,t2.SUPPLIER_NAME,t8.SERVICE_ITEM_NAME
    </select>

    <resultMap id="summaryByFlightNoMap" type="com.swcares.psi.ground.assurance.vo.BusinessSummaryByFlightVO">
            <result column="flightNo" property="flightNo"/>
            <result column="flightDate" property="flightDate"/>
         <collection property="children" ofType="com.swcares.psi.ground.assurance.vo.SupplierNameByFlightVO">
             <result column="supplier" property="supplier"/>
             <collection property="children" ofType="com.swcares.psi.ground.assurance.vo.ServiceItemVO">
                 <result column="serviceItem" property="serviceItem"/>
                 <result column="serviceCount" property="serviceCount"/>
                 <result column="price" property="price"/>
             </collection>
         </collection>
    </resultMap>

    <select id="businessReportSummaryByFlightNo" resultMap="summaryByFlightNoMap">
        <include refid="summaryBusinessQuery"/>
        and t3.CONFIRM_STATUS = 1
        group by flightDate,flightNo,t2.SUPPLIER_NAME,t8.SERVICE_ITEM_NAME
        order by flightDate
    </select>

    <select id="getContractItemsIdsByOrderId" resultType="string">
        select id from ground_assurance_order_contract_items where USER_CATEGORY_ITEM_ID in  <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
    </foreach>
    </select>

    <select id="getTaxByContractItems" resultType="BigDecimal">
       select
	c.TAX_RATE
from
	ground_assurance_order_contract_items a
left join ground_service_items b on
	a.SERVICE_ITEM_ID = b.ID
left join ground_supplier_contract_tax c on
	c.SERVICE_ITEMS_BUSINESS_TYPE = b.BUSINESS_TYPE
left join ground_supplier_contract d on d.ID = c.SUPPLIER_CONTRACT_ID
	where a.id = #{itemsId} and d.SUPPLIER_ID = #{supplierId}
    </select>


    <select id="sumSummary" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBusinessSummaryVo">
        select
        count(distinct DATE_FORMAT(t5.FLIGHT_DATE, '%Y-%m-%d'),t5.flight_number) sumFlight,
        count(distinct t8.SERVICE_ITEM_ID) sumServiceItem,
        count(*) sumServiceCount,
        sum(ifnull(t3.PRICE,0)) sumPrice
        <include refid="summaryBusinessForm"/>
        and t3.CONFIRM_STATUS = 1
    </select>

    <sql id="dpAssuranceOrderFlightStatsReportSql">
        select
        (select t6.delay_type from ground_assurance_dp_order t6 where t6.ORDER_ID = t.maxOrderId order by t6.id desc limit 1) as delayType,
        (select t6.delay_reason from ground_assurance_dp_order t6 where t6.ORDER_ID = t.maxOrderId order by t6.id desc limit 1) as delayReason,
            t.* from
        (
        select
            max(t5.id) maxOrderId,
            date_format(t5.flight_date, '%Y-%m-%d') as flightDate,
            t5.flight_number as flightNo,
            t5.org,
            t5.dst,
            concat(get_city_name(t5.org),'-',t5.org) as orgName,
            concat(get_city_name(t5.dst),'-',t5.dst) as dstName,
            flt.planecode as planeCode,
            TIMESTAMPDIFF(MINUTE, flt.std, ifnull(flt.atd, flt.etd)) as delayMinutes,
            count(distinct t4.passr_name, t4.id_number) as assuranceSum,
            count(distinct if(t3.confirm_Status=1, concat(t4.passr_name, t4.id_number), null)) as confirmSum
        from ground_assurance_order_user_service_item t3
        join ground_assurance_order_service_item t8 on t3.ORDER_SERVICE_ITEM_ID = t8.ID
        join ground_assurance_order_user_detail t4 on
        t3.USER_DETAIL_ID = t4.ID
        left join foc50_t3017 ft on t4.P_CODE = ft.P_CODE
        join ground_assurance_order t5 on
        t4.ORDER_ID = t5.id
        and t5.ORDER_TYPE = 0
        and t5.STATE not in (0, 1)
        join ground_assurance_dp_order t6 on
        t6.ORDER_ID = t5.ID
        left join flt_flight_real_info flt on t5.flight_date=flt.flight_date and t5.flight_number=flt.flight_number and t5.org=flt.org and t5.dst=flt.dst
        <where>
            t4.STATE = 1  and t5.ORDER_TYPE ='0'
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and t5.flight_number = #{dto.flightNo}
            </if>
            <if test="dto.flightDateBegin != null and dto.flightDateBegin != ''">
                and t5.flight_date &gt;= #{dto.flightDateBegin}
            </if>
            <if test="dto.flightDateEnd != null and dto.flightDateEnd != ''">
                and t5.flight_date &lt;= #{dto.flightDateEnd}
            </if>
            <if test="dto.org != null and dto.org != ''">
                and t5.org = #{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst != ''">
                and t5.dst = #{dto.dst}
            </if>
            <if test="dto.planeCode != null and dto.planeCode != ''">
                and flt.planecode = #{dto.planeCode}
            </if>
            <if test="dto.delayType != null and dto.delayType.size() > 0">
                and t6.delay_type in
                <foreach collection="dto.delayType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by t5.flight_date,t5.flight_number,t5.org,t5.dst
        ) t
    </sql>

    <select id="dpAssuranceOrderFlightStatsReportPage" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderFlightStatsReportVo">
        <include refid="dpAssuranceOrderFlightStatsReportSql"></include>
    </select>

    <select id="dpAssuranceOrderFlightStatsReportList" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderFlightStatsReportVo">
        <include refid="dpAssuranceOrderFlightStatsReportSql"></include>
    </select>

    <sql id="dpAssuranceOrderStatsReportSql">
        select
        date_format(t5.flight_date, '%Y-%m-%d') as flightDate,
        t5.flight_number as flightNo,
        t5.org,
        t5.dst,
        t5.service_airport,
        case t5.TYPE when 'D' then '国内' when 'I' then '国际' when 'R' then '地区' else '' end as segmentType,
        t5.remark,
        round(avg(ifnull(t3.price,0)), 2) as price,
        count(0) as confirmSum,
        sum(ifnull(t3.price,0)) as priceSum,
        t6.delay_time,
        t6.delay_type,
        t6.delay_reason,
        t8.service_item_name as serviceName,
        t8.category_name,
        if(t4.user_type='0','旅客','机组') servicePassengerType,
        t8.service_item_id serviceItemId
        from ground_assurance_order_user_service_item t3
        join ground_assurance_order_service_item t8 on t3.ORDER_SERVICE_ITEM_ID = t8.ID
        join ground_assurance_order_user_detail t4 on
        t3.USER_DETAIL_ID = t4.ID
        left join foc50_t3017 ft on t4.P_CODE = ft.P_CODE
        join ground_assurance_order t5 on
        t4.ORDER_ID = t5.id
        and t5.ORDER_TYPE = 0
        and t5.STATE not in (0, 1)
        join ground_assurance_dp_order t6 on
        t6.ORDER_ID = t5.ID
        <where>
            t4.STATE = 1  and t5.ORDER_TYPE ='0' and t3.CONFIRM_STATUS = 1
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and t5.flight_number = #{dto.flightNo}
            </if>
            <if test="dto.flightDateBegin != null and dto.flightDateBegin != ''">
                and t5.flight_date &gt;= #{dto.flightDateBegin}
            </if>
            <if test="dto.flightDateEnd != null and dto.flightDateEnd != ''">
                and t5.flight_date &lt;= #{dto.flightDateEnd}
            </if>
            <if test="dto.org != null and dto.org != ''">
                and t5.org = #{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst != ''">
                and t5.dst = #{dto.dst}
            </if>
            <if test="dto.serviceAirport != null and dto.serviceAirport != ''">
                and t5.service_airport = #{dto.serviceAirport}
            </if>
            <if test="dto.segmentType != null and dto.segmentType != ''">
                and t5.TYPE = #{dto.segmentType}
            </if>
            <if test="dto.servicePassengerType != null and dto.servicePassengerType != ''">
                and t4.user_type = #{dto.servicePassengerType}
            </if>
            <if test="dto.delayType != null and dto.delayType != ''">
                and t6.delay_type in
                <foreach collection="dto.delayType.split(',')" item="item" open="(" separator="," close=") ">
                    #{item}
                </foreach>
            </if>
            <if test="dto.delayTime != null and dto.delayTime != ''">
                and ifnull(t6.delay_time,#{dto.delayTimeNo}) in
                <foreach collection="dto.delayTimes" item="item" open="(" separator="," close=") ">
                    #{item}
                </foreach>
            </if>
            <if test="dto.delayReason != null and dto.delayReason != ''">
                and t6.delay_reason in
                <foreach collection="dto.delayReason.split(',')" item="item" open="(" separator="," close=") ">
                    #{item}
                </foreach>
            </if>
        </where>
        group by t5.flight_date,t5.flight_number,t5.org,t5.dst,t5.service_airport,t8.service_item_id,t4.user_type
    </sql>

    <select id="dpAssuranceOrderStatsReportPage" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderStatsReportVo">
        <include refid="dpAssuranceOrderStatsReportSql"></include>
    </select>

    <select id="dpAssuranceOrderStatsReportList" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderStatsReportVo">
        <include refid="dpAssuranceOrderStatsReportSql"></include>
    </select>

    <select id="dpAssuranceOrderStatsSumReport" resultType="com.swcares.psi.ground.assurance.vo.DpAssuranceOrderStatsSumReportVo$ServiceType">
        select gd.serviceName as serviceType,
        group_concat(distinct concat_ws('|',gd.flightDate,gd.flightNo,gd.org,gd.dst)) as flightSub,
        count(distinct gd.serviceItemId) as serviceItemSub,
        sum(gd.confirmSum) as servicePeopleSub,
        sum(gd.priceSum) as priceSub
        from (<include refid="dpAssuranceOrderStatsReportSql"></include>) gd
        <!-- left join ground_service_items gsi on gsi.ID = gd.serviceItemId
        left join sys_dict dt2 ON dt2.DATA_TYPE_CODE = 'business_type' and dt2.DATA_VALUE = gsi.BUSINESS_TYPE -->
        <where>
            1=1
            <!-- group by gd.BUSINESS_TYPE -->
            group by gd.serviceItemId
        </where>
    </select>

</mapper>
