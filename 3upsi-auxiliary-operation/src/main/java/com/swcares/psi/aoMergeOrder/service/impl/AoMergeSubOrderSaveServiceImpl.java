package com.swcares.psi.aoMergeOrder.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderValidataDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSubOrderValidataDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSuborderInfoDto;
import com.swcares.psi.aoMergeOrder.entity.*;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.aoMergeOrder.utils.MergeOrderUtils;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.api.entity.SysDict;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.common.enums.OrderTypeEnum;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.base.data.api.entity.FltPassengerRealInfo;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.enums.SysConfigEnum;
import com.swcares.psi.base.data.service.FltPassengerRealInfoService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.base.util.DateUtils;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.vo.PePsr;
import com.travelsky.hub.model.input.SeatChartQueryBean;
import com.travelsky.hub.model.output.SeatChartResultBean;
import com.travelsky.hub.model.peentity.*;
import com.travelsky.hub.svc.ICheckInService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Service
public class AoMergeSubOrderSaveServiceImpl {
    @Autowired
    private ICheckInService iCheckInService;

    @Autowired
    private FltPassengerRealInfoService fltPassengerRealInfoService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;

    @Autowired
    IAoMergeManySeatsService aoMergeManySeatsService;
    @Autowired
    IAoMergeSceneSeatsService aoMergeSceneSeatsService;
    @Autowired
    IAoMergeOverweightBkgService aoMergeOverweightBkgService;
    @Autowired
    IAoMergeViproomItemSellService aoMergeViproomItemSellService;
    @Autowired
    IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    AoMergeOverweightBkgChargeDetailsService aoMergeOverweightBkgChargeDetailsService;
    @Autowired
    SysAirportInfoService airportInfoService;
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;
    //川航二字码
    private static final String AIRLINE_CODE = "3U";

//    @Transactional(propagation = Propagation.REQUIRED , rollbackFor = Exception.class)
//    public void subOrderSave(AoMergeSubOrderValidataDto ele, AoMergeOrderValidataDto dto,  AoMergeOrderInfo mergeOrderInfo, CountDownLatch latch){
//
//        try {
//            AoMergeSuborderInfo mergeSuborderInfo = new AoMergeSuborderInfo();
//            BeanUtils.copyProperties(ele, mergeSuborderInfo);
//            String subOrderNo = MergeOrderUtils.createSubOrderNo(ele.getFlightType(), dto.getPayType(), ele.getOrderType(), ele.getFlightNo());
//            mergeSuborderInfo.setSuborderOrderNo(subOrderNo);
//            mergeSuborderInfo.setAoMergeOrderInfoId(mergeOrderInfo.getId());
//            LocalDate localDate = DateUtils.parseStringToLocalDate(ele.getFlightDate(), DateUtils.YYYY_MM_DD);
//            mergeSuborderInfo.setFlightDate(localDate);
//            LocalDateTime std = DateUtils.parseStringToLocalDateTime(ele.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
//            mergeSuborderInfo.setStd(std);
//            mergeSuborderInfo.setPracticalOrderPrice(new BigDecimal(ele.getPracticalOrderPrice()));
//            mergeSuborderInfo.setOrderPrice(new BigDecimal(ele.getOrderPrice()));
//            mergeSuborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_UNPAY);
//            mergeSuborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_UNPAY);
//            mergeSuborderInfo.setPermitRefund(AoMergeCons.SUBORDER_PERMIT_REFUND_NO);
//            mergeSuborderInfo.setTktNo(ele.getPayPaxTktNo());
//            mergeSuborderInfo.setCardType(ele.getDisCountCardType());
//            mergeSuborderInfo.setOrderTypeName(AoMergeCons.getOrderTypeName(ele.getOrderType()));
//            StringBuilder segment = new StringBuilder();
//            SysAirportInfoEntity org = airportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, ele.getOrg())
//                    .eq(SysAirportInfoEntity::getIsUse, "0")
//                    .one();
//            segment.append(ele.getOrg());
//            if(org==null){
//                segment.append("null");
//            }else{
//                segment.append(org.getAirportName());
//            }
//            segment.append("-");
//            segment.append(ele.getDst());
//            SysAirportInfoEntity dst = airportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, ele.getDst())
//                    .eq(SysAirportInfoEntity::getIsUse, "0")
//                    .one();
//            if(dst==null){
//                segment.append("null");
//            }else{
//                segment.append(dst.getAirportName());
//            }
//            mergeSuborderInfo.setSegment(segment.toString());
//            mergeSuborderInfo.setPaxType(ele.getPaxTypeMerge());
//            mergeSuborderInfo.setPaxCategory(ele.getPaxCategoryMerge());
//            mergeSuborderInfo.setPaxNo(AesEncryptUtil.encrypt(ele.getPaxNo()));
//            aoMergeSuborderInfoService.save(mergeSuborderInfo);
//            if (AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS.equals(ele.getOrderType())) {
//                AoMergeSuborderInfoDto.AoMergeManySeatsDetailDto aoMergeManySeatsDetailDto = ele.getAoMergeManySeatsDetailDto();
//                AoMergeManySeats mergeManySeats = new AoMergeManySeats();
//                BeanUtils.copyProperties(aoMergeManySeatsDetailDto, mergeManySeats);
//                mergeManySeats.setAoMergeOrderInfoId(mergeOrderInfo.getId());
//                mergeManySeats.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
//                mergeManySeats.setPaxNo(AesEncryptUtil.encrypt(aoMergeManySeatsDetailDto.getPaxNo()));
//                mergeManySeats.setPaxPhone(AesEncryptUtil.encrypt(aoMergeManySeatsDetailDto.getPaxNo(),","));
//                mergeManySeats.setPaxType(aoMergeManySeatsDetailDto.getPaxTypeMerge());
//                mergeManySeats.setPaxCategory(aoMergeManySeatsDetailDto.getPaxCategoryMerge());
//                aoMergeManySeatsService.save(mergeManySeats);
//                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_MANYSEAT_SEQUENCE, AoMergeCons.MANY_SEATS_FINANCE_ORDER_NO_KEY);
//                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);
//
//            } else if (AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS.equals(ele.getOrderType())) {
//                AoMergeSuborderInfoDto.AoMergeSceneSeatsDetailDto aoMergeSceneSeatsDetailDtos = ele.getAoMergeSceneSeatsDetailDtos();
//                AoMergeSceneSeats aoMergeSceneSeats = new AoMergeSceneSeats();
//                BeanUtils.copyProperties(aoMergeSceneSeatsDetailDtos, aoMergeSceneSeats);
//                aoMergeSceneSeats.setAoMergeOrderInfoId(mergeOrderInfo.getId());
//                aoMergeSceneSeats.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
//                aoMergeSceneSeats.setPaxNo(AesEncryptUtil.encrypt(aoMergeSceneSeatsDetailDtos.getPaxNo()));
//                aoMergeSceneSeats.setPaxPhone(AesEncryptUtil.encrypt(aoMergeSceneSeatsDetailDtos.getPaxNo(),","));
//                aoMergeSceneSeats.setPaxType(aoMergeSceneSeatsDetailDtos.getPaxTypeMerge());
//                aoMergeSceneSeats.setPaxCategory(aoMergeSceneSeatsDetailDtos.getPaxCategoryMerge());
//                aoMergeSceneSeatsService.save(aoMergeSceneSeats);
//                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_SCENESEAT_SEQUENCE, AoMergeCons.SCEN_SEATS_FINANCE_ORDER_NO_KEY);
//                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);
//
//            } else if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
//                AoMergeSuborderInfoDto.AoMergeOverweightBkgDetailDto aoMergeOverweightBkg = ele.getAoMergeOverweightBkg();
//                AoMergeOverweightBkg overweightBkg = new AoMergeOverweightBkg();
//                BeanUtils.copyProperties(aoMergeOverweightBkg, overweightBkg);
//                overweightBkg.setAoMergeOrderInfoId(mergeOrderInfo.getId());
//                overweightBkg.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
//                overweightBkg.setPaxNo(AesEncryptUtil.encrypt(aoMergeOverweightBkg.getPaxNo()));
//                overweightBkg.setPaxPhone(AesEncryptUtil.encrypt(aoMergeOverweightBkg.getPaxNo(),","));
//                overweightBkg.setPaxType(aoMergeOverweightBkg.getPaxTypeMerge());
//                overweightBkg.setPaxCategory(aoMergeOverweightBkg.getPaxCategoryMerge());
//                aoMergeOverweightBkgService.save(overweightBkg);
//                List<AoMergeOverweightBkgChargeDetails> aoMergeOverweightBkgChargeDetails = ele.getAoMergeOverweightBkgChargeDetails();
//                for(AoMergeOverweightBkgChargeDetails detail : aoMergeOverweightBkgChargeDetails){
//                    detail.setAoMergeOverweightBkgId(overweightBkg.getId());
//                }
//
//                aoMergeOverweightBkgChargeDetailsService.saveBatch(aoMergeOverweightBkgChargeDetails);
//                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_OVERWEIGHT_SEQUENCE, AoMergeCons.OVERWEIGHT_FINANCE_ORDER_NO_KEY);
//                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);
//            } else if (AoMergeCons.ORDER_TYPE_CODE_VIP.equals(ele.getOrderType())) {
//                List<AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto> aoMergeViproomItemSellDetailDtos = ele.getAoMergeViproomItemSellDetailDtos();
//
//
//                for (AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto eleInfo : aoMergeViproomItemSellDetailDtos) {
//                    AoMergeViproomItemSell viproomItemSell = new AoMergeViproomItemSell();
//                    BeanUtils.copyProperties(eleInfo, viproomItemSell);
//                    viproomItemSell.setAoMergeOrderInfoId(mergeOrderInfo.getId());
//                    viproomItemSell.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
//                    viproomItemSell.setPaxNo(AesEncryptUtil.encrypt(eleInfo.getPaxNo()));
//                    viproomItemSell.setPaxPhone(AesEncryptUtil.encrypt(eleInfo.getPaxNo(),","));
//                    viproomItemSell.setPaxType(eleInfo.getPaxTypeMerge());
//                    viproomItemSell.setPaxCategory(eleInfo.getPaxCategoryMerge());
//                    aoMergeViproomItemSellService.save(viproomItemSell);
//                }
//
//            } else if (AoMergeCons.ORDER_TYPE_CODE_UPGRADE.equals(ele.getOrderType())) {
//                AoMergeSuborderInfoDto.AoMergeCounterUpgradeDetailDto aoMergeCounterUpgradeDetailDto = ele.getAoMergeCounterUpgradeDetailDto();
//
//                AoMergeCounterUpgrade aoMergeCounterUpgrade = new AoMergeCounterUpgrade();
//                BeanUtils.copyProperties(aoMergeCounterUpgradeDetailDto, aoMergeCounterUpgrade);
//                aoMergeCounterUpgrade.setAoMergeOrderInfoId(mergeOrderInfo.getId());
//                aoMergeCounterUpgrade.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
//
//                // 已经有订单的判断
//                //增加重复生成升舱订单的处理，有除了退款成功之外的订单不让升舱
//                List<AoMergeSuborderInfo> orderList = aoMergeSuborderInfoService.lambdaQuery()
//                        .eq(AoMergeSuborderInfo::getFlightDate, ele.getFlightDate())
//                        .eq(AoMergeSuborderInfo::getOrg, ele.getOrg())
//                        .eq(AoMergeSuborderInfo::getDst, ele.getDst())
//                        .eq(AoMergeSuborderInfo::getFlightNo, ele.getFlightNo())
//                        .eq(AoMergeSuborderInfo::getOrderType, AoOrderConstant.ORDER_TYPE_CODE_UPGRADE)
//                        .ne(AoMergeSuborderInfo::getId,mergeSuborderInfo.getId())
//                        .notIn(AoMergeSuborderInfo::getSuborderOrderStatus, new String[]{AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS_ING, AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS})
//                        .list();
//                if (null != orderList && !orderList.isEmpty()) {
//                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
//                    i18nCodeInfo.setCode(MessageCode.UPGRADE_ORDER_EXSIST.getCode());
//                    dto.getCodes().add(i18nCodeInfo);
//                    return;
//                }
//// todo-houchuan 联调屏蔽
//
////                UpgFlt upgFlts = getUpgFlts(ele.getFlightNo(), AIRLINE_CODE, ele.getOrg());
////                //2021-12-01 处理当前可升舱航班不在列表内的情况
////                if (null == upgFlts) {
////                    upgFlts = new UpgFlt();
////                    upgFlts.setFlightDate(ele.getFlightDate());
////                    upgFlts.setFlightNumber(ele.getFlightNo().substring(2));
////                    upgFlts.setAirlineCode(AIRLINE_CODE);
////                    upgFlts.setDepartureAirport(ele.getOrg());
////                    upgFlts.setArrivalAirport(ele.getDst());
////                }
//                FltPassengerRealInfo byId = fltPassengerRealInfoService.getById(ele.getPayPaxId());
////                // 获取可升舱旅客信息 getUpgPsrByFltInfo-->
////                Psr psrInfo = getUpgPsrByFltInfo(upgFlts.getAirlineCode(), upgFlts.getFlightNumber(), upgFlts.getFlightSuffix(),
////                        upgFlts.getFlightDate(), upgFlts.getDepartureAirport(), upgFlts.getArrivalAirport(), null, null, byId.getTicketNumber()
////                        , byId.getSubCabin(), byId.getSeatNumber(), byId.getPnrRef());
////
////                if (StringUtils.isBlank(psrInfo.getSeat())) {
////                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
////                    i18nCodeInfo.setCode(MessageCode.UPGRADE_USER_NOT_CHECK_IN.getCode());
////                    dto.getCodes().add(i18nCodeInfo);
////                    return;
////                }
////
////                // 升舱办理 获取可升舱航班数据 getUpgFlts-->
////                UpgradeOrderOutPutBean upgradeOrder = new UpgradeOrderOutPutBean();
////
////                UpgOrderDetail orderTetail = new UpgOrderDetail();
////
////                String newSeat = aoMergeCounterUpgradeDetailDto.getNewSeat();
////                String newCabin = aoMergeCounterUpgradeDetailDto.getNewCabin();
////                boolean b = PeUpgradeUtils.seatAvailability(upgFlts.getAirlineCode() + upgFlts.getFlightNumber(), upgFlts.getFlightDate(), upgFlts.getArrivalAirport(), upgFlts.getDepartureAirport(), newCabin, newSeat);
////                if (!b) {
////                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
////                    i18nCodeInfo.setCode(MessageCode.SEAT_NO_UPGRADE.getCode());
////                    dto.getCodes().add(i18nCodeInfo);
////                    return;
////                }
////                log.info("获取新仓位和新座位是{}", "newSeat:" + newSeat + ";newCabin:" + newCabin);
////                String ticketNumber = psrInfo.getTicketNumber();
////                //2021-12-28 处理创建升舱订单的时候，通过PE接口获取到的旅客数据可能没有票号的BUG增加票号为空的判断
////                if (StringUtils.isBlank(psrInfo.getTicketNumber()) || StringUtils.equals(psrInfo.getTicketNumber(), "null")) {
////                    ticketNumber = byId.getTicketNumber();
////                }
////                upgradeOrder = createUpgradeOrder(upgFlts.getFlightNumber(), upgFlts.getFlightDate(), ele.getOrg(), ele.getDst(), psrInfo.getPsrName(),
////                        psrInfo.getPnr(), ticketNumber, psrInfo.getTourIndex(), psrInfo.getCertType(), psrInfo.getCertNo(), psrInfo.getContactInfo(),
////                        psrInfo.getPsrLevel(), psrInfo.getCabin(), psrInfo.getSeat(), newCabin, newSeat, ele.getOrderPrice(), null, null);
////                // 查询订单详情 getOrderTetail-->
////                if (null != upgradeOrder && org.apache.commons.lang.StringUtils.isNotBlank(upgradeOrder.getOrderNum())) {
////                    orderTetail = getOrderTetail(upgradeOrder.getOrderNum());
////                    log.info("==========升舱数据-订单详情==============");
////                    log.info("获取订单详情,参数详情：{}", JSON.toJSONString(orderTetail));
////                    log.info("===========================================");
////                }
//                //升舱初始化
////                aoMergeCounterUpgrade.setUpgradeStatus(UPGRADE_INIT);
////                aoMergeCounterUpgrade.setOriginalCabin(byId.getSubCabin());
////                aoMergeCounterUpgrade.setNewCabin(newCabin);
////                aoMergeCounterUpgrade.setNewSeat(newSeat);
////                aoMergeCounterUpgrade.setUpgradeOrder(upgradeOrder.getOrderNum());
////                aoMergeCounterUpgrade.setEmdNo(orderTetail.getEmdNo());
//
//// todo-houchuan 联调完毕恢复
//                aoMergeCounterUpgrade.setUpgradeStatus(UPGRADE_INIT);
//                aoMergeCounterUpgrade.setOriginalCabin(byId.getSubCabin());
//                aoMergeCounterUpgrade.setNewCabin("C");
//                aoMergeCounterUpgrade.setNewSeat("1C");
//                aoMergeCounterUpgrade.setUpgradeOrder("123456");
//                aoMergeCounterUpgrade.setEmdNo("654321");
//
//                aoMergeCounterUpgrade.setPaxType(aoMergeCounterUpgradeDetailDto.getPaxTypeMerge());
//                aoMergeCounterUpgrade.setPaxCategory(aoMergeCounterUpgradeDetailDto.getPaxCategoryMerge());
//                aoMergeCounterUpgrade.setPaxNo(AesEncryptUtil.encrypt(aoMergeCounterUpgradeDetailDto.getPaxNo()));
//                aoMergeCounterUpgrade.setPaxPhone(AesEncryptUtil.encrypt(aoMergeCounterUpgradeDetailDto.getPaxNo(),","));
//                aoMergeCounterUpgradeService.save(aoMergeCounterUpgrade);
//                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_COUNTER_UPGRADE_SEQUENCE, AoMergeCons.COUNTER_UPGRADE_ACCOUNT_ENTRY_NO_KEY);
//                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);
//            }
//            aoMergeSuborderInfoService.saveOrUpdate(mergeSuborderInfo);
//        } catch (Exception e) {
//            AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
//            i18nCodeInfo.setCode(MessageCode.UN_KNOWN.getCode());
//            dto.getCodes().add(i18nCodeInfo);
//            log.info("合并订单子订单建单失败：{}--->>{}",e.getMessage(),JSON.toJSONString(ele),e);
//        }finally {
//            latch.countDown();
//        }
//    }





    @Transactional(propagation = Propagation.REQUIRED , rollbackFor = Exception.class)
    public void subOrderSave(AoMergeSubOrderValidataDto ele, AoMergeOrderValidataDto dto,  AoMergeOrderInfo mergeOrderInfo, CountDownLatch latch){

        try {
            AoMergeSuborderInfo mergeSuborderInfo = new AoMergeSuborderInfo();
            BeanUtils.copyProperties(ele, mergeSuborderInfo);
            String subOrderNo = MergeOrderUtils.createSubOrderNo(OrderTypeEnum.findEnumByOrderTypeCode(ele.getOrderType()).getOrderIdentityCode());
            mergeSuborderInfo.setSuborderOrderNo(subOrderNo);
            mergeSuborderInfo.setAoMergeOrderInfoId(mergeOrderInfo.getId());
            LocalDate localDate = DateUtils.parseStringToLocalDate(ele.getFlightDate(), DateUtils.YYYY_MM_DD);
            mergeSuborderInfo.setFlightDate(localDate);
            LocalDateTime std = DateUtils.parseStringToLocalDateTime(ele.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            mergeSuborderInfo.setStd(std);
            mergeSuborderInfo.setPracticalOrderPrice(new BigDecimal(ele.getPracticalOrderPrice()));
            mergeSuborderInfo.setOrderPrice(new BigDecimal(ele.getOrderPrice()));
            mergeSuborderInfo.setSuborderOrderPayStatus(AoMergeCons.SUBORDER_PAY_STATUS_UNPAY);
            mergeSuborderInfo.setSuborderOrderStatus(AoMergeCons.SUBORDER_ORDER_STATUS_UNPAY);
            mergeSuborderInfo.setPermitRefund(AoMergeCons.SUBORDER_PERMIT_REFUND_NO);
            mergeSuborderInfo.setTktNo(ele.getPayPaxTktNo());
            mergeSuborderInfo.setCardType(ele.getDisCountCardType());
            mergeSuborderInfo.setOrderTypeName(AoMergeCons.getOrderTypeName(ele.getOrderType()));
            StringBuilder segment = new StringBuilder();
            SysAirportInfoEntity org = airportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, ele.getOrg())
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            segment.append(ele.getOrg());
            if(org==null){
                segment.append("null");
            }else{
                segment.append(org.getAirportName());
            }
            segment.append("-");
            segment.append(ele.getDst());
            SysAirportInfoEntity dst = airportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, ele.getDst())
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            if(dst==null){
                segment.append("null");
            }else{
                segment.append(dst.getAirportName());
            }
            mergeSuborderInfo.setSegment(segment.toString());
            mergeSuborderInfo.setPaxType(ele.getPaxTypeMerge());
            mergeSuborderInfo.setPaxCategory(ele.getPaxCategoryMerge());
            mergeSuborderInfo.setPaxNo(AesEncryptUtil.encrypt(ele.getPaxNo()));
            aoMergeSuborderInfoService.save(mergeSuborderInfo);
            if (AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS.equals(ele.getOrderType())) {
                AoMergeSuborderInfoDto.AoMergeManySeatsDetailDto aoMergeManySeatsDetailDto = ele.getAoMergeManySeatsDetailDto();
                log.info("一人多坐建单参数>>{}",JSON.toJSONString(aoMergeManySeatsDetailDto));
                AoMergeManySeats mergeManySeats = new AoMergeManySeats();
                BeanUtils.copyProperties(aoMergeManySeatsDetailDto, mergeManySeats);
                mergeManySeats.setAoMergeOrderInfoId(mergeOrderInfo.getId());
                mergeManySeats.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                mergeManySeats.setPaxNo(AesEncryptUtil.encrypt(aoMergeManySeatsDetailDto.getPaxNo()));
                mergeManySeats.setPaxPhone(AesEncryptUtil.encrypt(aoMergeManySeatsDetailDto.getPaxNo(),","));
                mergeManySeats.setPaxType(aoMergeManySeatsDetailDto.getPaxTypeMerge());
                mergeManySeats.setPaxCategory(aoMergeManySeatsDetailDto.getPaxCategoryMerge());
                aoMergeManySeatsService.save(mergeManySeats);
                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_MANYSEAT_SEQUENCE, AoMergeCons.MANY_SEATS_FINANCE_ORDER_NO_KEY);
                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);

            } else if (AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS.equals(ele.getOrderType())) {
                AoMergeSuborderInfoDto.AoMergeSceneSeatsDetailDto aoMergeSceneSeatsDetailDtos = ele.getAoMergeSceneSeatsDetailDtos();
                log.info("现场选座建单参数>>{}",JSON.toJSONString(aoMergeSceneSeatsDetailDtos));
                AoMergeSceneSeats aoMergeSceneSeats = new AoMergeSceneSeats();
                BeanUtils.copyProperties(aoMergeSceneSeatsDetailDtos, aoMergeSceneSeats);
                aoMergeSceneSeats.setAoMergeOrderInfoId(mergeOrderInfo.getId());
                aoMergeSceneSeats.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                aoMergeSceneSeats.setPaxNo(AesEncryptUtil.encrypt(aoMergeSceneSeatsDetailDtos.getPaxNo()));
                aoMergeSceneSeats.setPaxPhone(AesEncryptUtil.encrypt(aoMergeSceneSeatsDetailDtos.getPaxNo(),","));
                aoMergeSceneSeats.setPaxType(aoMergeSceneSeatsDetailDtos.getPaxTypeMerge());
                aoMergeSceneSeats.setPaxCategory(aoMergeSceneSeatsDetailDtos.getPaxCategoryMerge());
                aoMergeSceneSeatsService.save(aoMergeSceneSeats);
                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_SCENESEAT_SEQUENCE, AoMergeCons.SCEN_SEATS_FINANCE_ORDER_NO_KEY);
                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);

            } else if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
                AoMergeSuborderInfoDto.AoMergeOverweightBkgDetailDto aoMergeOverweightBkg = ele.getAoMergeOverweightBkg();
                log.info("逾重行李建单参数>>{}",JSON.toJSONString(aoMergeOverweightBkg));
                AoMergeOverweightBkg overweightBkg = new AoMergeOverweightBkg();
                BeanUtils.copyProperties(aoMergeOverweightBkg, overweightBkg);
                overweightBkg.setAoMergeOrderInfoId(mergeOrderInfo.getId());
                overweightBkg.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                overweightBkg.setPaxNo(AesEncryptUtil.encrypt(aoMergeOverweightBkg.getPaxNo()));
                overweightBkg.setPaxPhone(AesEncryptUtil.encrypt(aoMergeOverweightBkg.getPaxNo(),","));
                overweightBkg.setPaxType(aoMergeOverweightBkg.getPaxTypeMerge());
                overweightBkg.setPaxCategory(aoMergeOverweightBkg.getPaxCategoryMerge());
                aoMergeOverweightBkgService.save(overweightBkg);
                List<AoMergeOverweightBkgChargeDetails> aoMergeOverweightBkgChargeDetails = ele.getAoMergeOverweightBkgChargeDetails();
                for(AoMergeOverweightBkgChargeDetails detail : aoMergeOverweightBkgChargeDetails){
                    detail.setAoMergeOverweightBkgId(overweightBkg.getId());
                }

                aoMergeOverweightBkgChargeDetailsService.saveBatch(aoMergeOverweightBkgChargeDetails);
                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_OVERWEIGHT_SEQUENCE, AoMergeCons.OVERWEIGHT_FINANCE_ORDER_NO_KEY);
                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);
            } else if (AoMergeCons.ORDER_TYPE_CODE_VIP.equals(ele.getOrderType())) {
                List<AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto> aoMergeViproomItemSellDetailDtos = ele.getAoMergeViproomItemSellDetailDtos();
                log.info("贵宾厅建单参数>>{}",JSON.toJSONString(aoMergeViproomItemSellDetailDtos));


                for (AoMergeSuborderInfoDto.AoMergeViproomItemSellDetailDto eleInfo : aoMergeViproomItemSellDetailDtos) {
                    AoMergeViproomItemSell viproomItemSell = new AoMergeViproomItemSell();
                    BeanUtils.copyProperties(eleInfo, viproomItemSell);
                    viproomItemSell.setAoMergeOrderInfoId(mergeOrderInfo.getId());
                    viproomItemSell.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                    viproomItemSell.setPaxNo(AesEncryptUtil.encrypt(eleInfo.getPaxNo()));
                    viproomItemSell.setPaxPhone(AesEncryptUtil.encrypt(eleInfo.getPaxNo(),","));
                    viproomItemSell.setPaxType(eleInfo.getPaxTypeMerge());
                    viproomItemSell.setPaxCategory(eleInfo.getPaxCategoryMerge());
                    aoMergeViproomItemSellService.save(viproomItemSell);
                }

            } else if (AoMergeCons.ORDER_TYPE_CODE_UPGRADE.equals(ele.getOrderType())) {
                AoMergeSuborderInfoDto.AoMergeCounterUpgradeDetailDto aoMergeCounterUpgradeDetailDto = ele.getAoMergeCounterUpgradeDetailDto();
                log.info("柜台升舱建单参数>>{}",JSON.toJSONString(aoMergeCounterUpgradeDetailDto));
                AoMergeCounterUpgrade aoMergeCounterUpgrade = new AoMergeCounterUpgrade();
                BeanUtils.copyProperties(aoMergeCounterUpgradeDetailDto, aoMergeCounterUpgrade);
                aoMergeCounterUpgrade.setAoMergeOrderInfoId(mergeOrderInfo.getId());
                aoMergeCounterUpgrade.setAoMergeSuborderInfoId(mergeSuborderInfo.getId());
                // 已经有订单的判断
                //增加重复生成升舱订单的处理，有除了退款成功之外的订单不让升舱
                List<AoMergeSuborderInfo> orderList = aoMergeSuborderInfoService.lambdaQuery()
                        .eq(AoMergeSuborderInfo::getFlightDate, ele.getFlightDate())
                        .eq(AoMergeSuborderInfo::getOrg, ele.getOrg())
                        .eq(AoMergeSuborderInfo::getDst, ele.getDst())
                        .eq(AoMergeSuborderInfo::getFlightNo, ele.getFlightNo())
                        .eq(AoMergeSuborderInfo::getOrderType, AoOrderConstant.ORDER_TYPE_CODE_UPGRADE)
                        .ne(AoMergeSuborderInfo::getId,mergeSuborderInfo.getId())
                        .notIn(AoMergeSuborderInfo::getSuborderOrderStatus, new String[]{AoMergeCons.SUBORDER_ORDER_STATUS_REFUND, AoMergeCons.SUBORDER_ORDER_STATUS_CANCEL})
                        .list();
                if (null != orderList && !orderList.isEmpty()) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(MessageCode.UPGRADE_ORDER_EXSIST.getCode());
                    dto.getCodes().add(i18nCodeInfo);
                    return;
                }
                UpgFlt upgFlts = getUpgFlts(ele.getFlightNo(), AIRLINE_CODE, ele.getOrg());
                //2021-12-01 处理当前可升舱航班不在列表内的情况
                if (null == upgFlts) {
                    upgFlts = new UpgFlt();
                    upgFlts.setFlightDate(ele.getFlightDate());
                    upgFlts.setFlightNumber(ele.getFlightNo().substring(2));
                    upgFlts.setAirlineCode(AIRLINE_CODE);
                    upgFlts.setDepartureAirport(ele.getOrg());
                    upgFlts.setArrivalAirport(ele.getDst());
                }
                FltPassengerRealInfo byId = fltPassengerRealInfoService.getById(ele.getPayPaxId());
                // 获取可升舱旅客信息 getUpgPsrByFltInfo-->
                PePsr psrInfo=new PePsr();
                try {
                    psrInfo = getUpgPsrByFltInfo(upgFlts.getAirlineCode(), upgFlts.getFlightNumber(), upgFlts.getFlightSuffix(),
                            upgFlts.getFlightDate(), upgFlts.getDepartureAirport(), upgFlts.getArrivalAirport(), null, null, byId.getTicketNumber()
                            , byId.getSubCabin(), byId.getSeatNumber(), byId.getPnrRef(),ele);
                }catch (BusinessException e){
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    if (e.getParams()[0].contains("非OP")) {
                        i18nCodeInfo.setCode(MessageCode.UPGRADE_PE_TIME_ERROR.getCode());
                    } else {
                        i18nCodeInfo.setCode(MessageCode.UPGRADE_CUSTOMIZE.getCode());
                        i18nCodeInfo.setParam(new String[]{e.getParams()[0]});
                    }
                    dto.getCodes().add(i18nCodeInfo);
                    return;

                }


                if (StringUtils.isBlank(psrInfo.getSeat())) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(MessageCode.UPGRADE_USER_NOT_CHECK_IN.getCode());
                    dto.getCodes().add(i18nCodeInfo);
                    return;
                }
//                屏蔽业务端可升舱时间范围限制
//                if(StringUtils.isNotEmpty(psrInfo.getUpgStartTime()) && StringUtils.isNotEmpty(psrInfo.getUpgStopTime())){
//                    LocalDateTime now = LocalDateTime.now();
//                    LocalDateTime upStartTime = DateUtils.parseStringToLocalDateTime(psrInfo.getUpgStartTime() + ":00", DateUtils.YYYYMMDD_HH_MM_SS);
//                    LocalDateTime upgStopTime = DateUtils.parseStringToLocalDateTime(psrInfo.getUpgStopTime() + ":00", DateUtils.YYYYMMDD_HH_MM_SS);
//                    if(now.isBefore(upStartTime) || now.isAfter(upgStopTime)){
//                        throw new BusinessException(MessageCode.UPGRADE_PE_TIME_ERROR.getCode());
//                    }
//                }

                // 升舱办理 获取可升舱航班数据 getUpgFlts-->
                UpgradeOrderOutPutBean upgradeOrder = new UpgradeOrderOutPutBean();

                UpgOrderDetail orderTetail = new UpgOrderDetail();

                String newSeat = aoMergeCounterUpgradeDetailDto.getNewSeat();
                String newCabin = aoMergeCounterUpgradeDetailDto.getNewCabin();
                log.info("merge查询座位图查看可用座位--{}-{}-{}",upgFlts.getFlightDate(), upgFlts.getAirlineCode() + upgFlts.getFlightNumber(), upgFlts.getArrivalAirport());
                boolean b = PeUpgradeUtils.seatAvailability( upgFlts.getFlightDate(),upgFlts.getAirlineCode() + upgFlts.getFlightNumber(), upgFlts.getArrivalAirport(), upgFlts.getDepartureAirport(), newCabin, newSeat);
                if (!b) {
                    AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
                    i18nCodeInfo.setCode(MessageCode.SEAT_NO_UPGRADE.getCode());
                    i18nCodeInfo.setParam(new String[]{newSeat});
                    dto.getCodes().add(i18nCodeInfo);
                    return;
                }
                log.info("获取新仓位和新座位是{}", "newSeat:" + newSeat + ";newCabin:" + newCabin);
                String ticketNumber = psrInfo.getTicketNumber();
                //2021-12-28 处理创建升舱订单的时候，通过PE接口获取到的旅客数据可能没有票号的BUG增加票号为空的判断
                if (StringUtils.isBlank(psrInfo.getTicketNumber()) || StringUtils.equals(psrInfo.getTicketNumber(), "null")) {
                    ticketNumber = byId.getTicketNumber();
                }
                upgradeOrder = createUpgradeOrder(upgFlts.getFlightNumber(), upgFlts.getFlightDate(), ele.getOrg(), ele.getDst(), psrInfo.getPsrName(),
                        psrInfo.getPnr(), ticketNumber, psrInfo.getTourIndex(), psrInfo.getCertType(), psrInfo.getCertNo(), psrInfo.getContactInfo(),
                        psrInfo.getPsrLevel(), psrInfo.getCabin(), psrInfo.getSeat(), newCabin, newSeat, ele.getOrderPrice(), null, null);
                // 查询订单详情 getOrderTetail-->
                if (null != upgradeOrder && StringUtils.isNotBlank(upgradeOrder.getOrderNum())) {
                    orderTetail = PeUpgradeUtils.getOrderTetail(upgradeOrder.getOrderNum());
                    log.info("==========升舱数据-订单详情==============");
                    log.info("获取订单详情,参数详情：{}", JSON.toJSONString(orderTetail));
                    log.info("===========================================");
                }
                aoMergeCounterUpgrade.setUpgradeStatus(AoMergeCons.UPGRADE_STATUS_INIT);
                aoMergeCounterUpgrade.setOriginalCabin(byId.getSubCabin());
                aoMergeCounterUpgrade.setNewCabin(newCabin);
                aoMergeCounterUpgrade.setNewSeat(newSeat);
                aoMergeCounterUpgrade.setUpgradeOrder(upgradeOrder.getOrderNum());
                aoMergeCounterUpgrade.setEmdNo(orderTetail.getEmdNo());


                aoMergeCounterUpgrade.setPaxType(aoMergeCounterUpgradeDetailDto.getPaxTypeMerge());
                aoMergeCounterUpgrade.setPaxCategory(aoMergeCounterUpgradeDetailDto.getPaxCategoryMerge());
                aoMergeCounterUpgrade.setPaxNo(AesEncryptUtil.encrypt(aoMergeCounterUpgradeDetailDto.getPaxNo()));
                aoMergeCounterUpgrade.setPaxPhone(AesEncryptUtil.encrypt(aoMergeCounterUpgradeDetailDto.getPaxNo(),","));
                aoMergeCounterUpgradeService.save(aoMergeCounterUpgrade);
                String financeOrderNo = MergeOrderUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_COUNTER_UPGRADE_SEQUENCE, AoMergeCons.COUNTER_UPGRADE_ACCOUNT_ENTRY_NO_KEY);
                mergeSuborderInfo.setFinanceOrderNo(financeOrderNo);
            }
            aoMergeSuborderInfoService.saveOrUpdate(mergeSuborderInfo);
        } catch (Exception e) {
            AoMergeOrderValidataDto.I18nCodeInfo i18nCodeInfo = new AoMergeOrderValidataDto.I18nCodeInfo();
            i18nCodeInfo.setCode(MessageCode.UN_KNOWN.getCode());
            dto.getCodes().add(i18nCodeInfo);
            log.info("合并订单子订单建单失败：{}--->>{}",e.getMessage(),JSON.toJSONString(ele),e);
        }finally {
            latch.countDown();
        }
    }




    /**
     * 获取订单详情
     */
    public UpgOrderDetail getOrderTetail(String orderNum) throws Exception {
        return iCheckInService.getOrderTetail(orderNum);
    }

    /**
     * 生成升舱订单
     */
    public UpgradeOrderOutPutBean createUpgradeOrder(String flightNumber, String flightDate, String departureAirport, String arrivalAirport,
                                                     String psrName, String pnr, String ticketNumber, String TourIndex, String certType,
                                                     String certNo, String contactInfo, String psrLevel, String cabin, String seat,
                                                     String seniorCabin, String seniorSeat, String upPrice, String ffpAirlineCode, String ffpCardNumber) {

        String peCertType = "PSPT".equals(certType) ? "PP" : certType;
        Map<String, String> orderInfo = new HashMap<>();
        orderInfo.put("flightNumber", "3U" + flightNumber);
        orderInfo.put("flightDate", DateUtils.parseStrToStr(flightDate, DateUtils.YYYY_MM_DD, DateUtils.YYYYMMDD));
        orderInfo.put("departureAirport", departureAirport);
        orderInfo.put("arrivalAirport", arrivalAirport);
        orderInfo.put("psrName", psrName);
        orderInfo.put("pnr", pnr);
        orderInfo.put("ticketNumber", ticketNumber);
        orderInfo.put("TourIndex", TourIndex);
        orderInfo.put("certType", peCertType);
        orderInfo.put("certNo", certNo);
        orderInfo.put("contactInfo", contactInfo);
        orderInfo.put("psrLevel", psrLevel);
        orderInfo.put("cabin", cabin);
        orderInfo.put("seat", seat);
        orderInfo.put("seniorCabin", seniorCabin);
        orderInfo.put("seniorSeat", seniorSeat);
        orderInfo.put("upPrice", upPrice);
        orderInfo.put("currencyCode", "CNY");
        orderInfo.put("ffpAirlineCode", ffpAirlineCode);
        orderInfo.put("ffpCardNumber", ffpCardNumber);
        log.info("==================创建升舱订单传入参数：====================="
                + "flightNumber:3U" + flightNumber + "; "
                + "flightDate:" + DateUtils.parseStrToStr(flightDate, DateUtils.YYYY_MM_DD, DateUtils.YYYYMMDD) + ";"
                + "departureAirport:" + departureAirport + ";"
                + "arrivalAirport:" + arrivalAirport + ";"
                + "departureAirport:" + departureAirport + ";"
                + "psrName:" + psrName + ";"
                + "pnr:" + pnr + ";"
                + "ticketNumber:" + ticketNumber + ";"
                + "TourIndex:" + TourIndex + ";"
                + "certType:" + peCertType + ";"
                + "certNo:" + certNo + ";"
                + "contactInfo:" + contactInfo + ";"
                + "psrLevel:" + psrLevel + ";"
                + "cabin:" + cabin + ";"
                + "seat:" + seat + ";"
                + "seniorCabin:" + seniorCabin + ";"
                + "seniorSeat:" + seniorSeat + ";"
                + "upPrice:" + upPrice + ";"
                + "currencyCode:CNY;"
                + "ffpAirlineCode:" + ffpAirlineCode + ";"
                + "ffpCardNumber:" + ffpCardNumber + ";"
                + "=================================================="
        );
        UpgradeOrderOutPutBean upgradeOrder = iCheckInService.createUpgradeOrder(orderInfo);
        log.info("创建柜台升舱订单返回结果：{}", JSON.toJSONString(upgradeOrder));
        return upgradeOrder;
    }

    private UpgFlt getUpgFlts(String flightNo, String airLine, String org) {
        UpgFltOutBean upgFlts = iCheckInService.getUpgFlts(airLine);
        List<UpgFlt> upgFltsList = upgFlts.getUpgFlts();

        UpgFlt upgFlt = upgFltsList.stream()
                .filter(a -> StringUtils.equals(a.getAirlineCode() + a.getFlightNumber(), flightNo) && StringUtils.equals(a.getDepartureAirport(), org))
                .findAny().orElse(null);
        return upgFlt;
    }

    /**
     * 查询当前客户是否在可升舱用户列表内
     */
    private PePsr getUpgPsrByFltInfo(String airlineCode, String flightNumber, String flightSuffix, String flightDate,
                                   String departureAirport, String arrivalAirport, String etNumberPrefix, String isSupportNoPNR, String tktNo,  String subCabinCode, String seatNumber, String pnr,AoMergeSubOrderValidataDto dto) {

        // 根据航信邮件回复内容解释：2021-08-26
        // 可能是因为旅客的出票时间原因，
        // 已经统计完毕可升舱的旅客
        // 这里不做验证旅客是否在升舱列表内
        FltPassengerRealInfo passengerRealInfo = fltPassengerRealInfoService.getOne(Wrappers.<FltPassengerRealInfo>lambdaQuery()
                .eq(FltPassengerRealInfo::getTicketNumber, tktNo).eq(FltPassengerRealInfo::getIsCancel, "N")
                .eq(FltPassengerRealInfo::getOrg,dto.getOrg())
                .eq(FltPassengerRealInfo::getFlightNumber,dto.getFlightNo())
                .eq(FltPassengerRealInfo::getFlightDate,DateUtils.parseStringToLocalDate(dto.getFlightDate(),DateUtils.YYYY_MM_DD))
        );

        log.info("当前旅客不在可升舱列表内！");
        UpgFltInfoOutBean psrFltInfoByCert = null;
        // 根据旅客证件号提取行程及升舱信息 getPsrFltInfoByCert-->
        try {
            if (null != passengerRealInfo && org.apache.commons.lang.StringUtils.isNotBlank(passengerRealInfo.getIdType()) && org.apache.commons.lang.StringUtils.isNotBlank(passengerRealInfo.getIdNumber())) {
                psrFltInfoByCert = getPsrFltInfoByCert(passengerRealInfo.getIdType(),
                        AesEncryptUtil.decryption(passengerRealInfo.getIdNumber()), airlineCode,
                        flightNumber, flightSuffix, flightDate, null,
                        departureAirport, tktNo);
            }
        } catch (Exception e) {
            log.error("获取当前可升舱旅客信息错误，根据行程提取当前旅客信息失败！>>{}", e.getMessage(),e);
            throw new BusinessException(MessageCode.UPGRADE_CUSTOMIZE.getCode(),new String[]{e.getMessage()});
        }
        PePsr psrInfo = new PePsr();
        if (null != psrFltInfoByCert) {
            List<UpgFltInfo> upgFltInfos = psrFltInfoByCert.getUpgFltInfos();
            if (null != upgFltInfos && !upgFltInfos.isEmpty()) {
                UpgFltInfo upgFltInfo = upgFltInfos.stream().filter(psr -> psr.getTicketNumber().equals(tktNo)).findAny().orElse(null);
                if (null != upgFltInfo) {
                    log.info("柜台升舱基于旅客信息查询到可升舱旅客信息：{}", JSON.toJSONString(upgFltInfo));
                    psrInfo.setPnr(upgFltInfo.getPnr());
                    psrInfo.setSeat(upgFltInfo.getSeat());
                    psrInfo.setCabin(upgFltInfo.getCabin());
                    psrInfo.setTicketNumber(upgFltInfo.getTicketNumber());
                    psrInfo.setCertNo(upgFltInfo.getCertNo());
                    psrInfo.setCertType(upgFltInfo.getCertType());
                    psrInfo.setContactInfo(upgFltInfo.getContactInfo());
                    psrInfo.setPsrLevel(upgFltInfo.getPsrLevel());
                    psrInfo.setPsrName(upgFltInfo.getPsrName());
                    psrInfo.setTourIndex(upgFltInfo.getTourIndex());
                    psrInfo.setUpgStartTime(upgFltInfo.getUpgStartTime());
                    psrInfo.setUpgStopTime(upgFltInfo.getUpgStopTime());
                }
            }
        } else {
            psrInfo.setTicketNumber(tktNo);
            psrInfo.setCabin(subCabinCode);
            psrInfo.setSeat(seatNumber);
            psrInfo.setPnr(pnr);
        }


        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getSeat()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getSeat(), "null")) {
            psrInfo.setSeat(seatNumber);
        }
        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getCabin()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getCabin(), "null")) {
            psrInfo.setCabin(subCabinCode);
        }
        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getPsrName()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getPsrName(), "null")) {
            if (null != passengerRealInfo) {
                psrInfo.setPsrName(passengerRealInfo.getPassengerNameEn());
            }
        }
        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getCertNo()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getCertNo(), "null")) {
            if (null != passengerRealInfo) {
                psrInfo.setCertNo(AesEncryptUtil.decryption(passengerRealInfo.getIdNumber()));
            }
        }
        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getCertType()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getCertType(), "null")) {
            if (null != passengerRealInfo) {
                psrInfo.setCertType(passengerRealInfo.getIdType());
            }
        }
        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getPnr()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getPnr(), "null")) {
            if (null != passengerRealInfo) {
                psrInfo.setPnr(passengerRealInfo.getPnrRef());
            }
        }
        //2022-04-02 根据反馈需要默认TourIndex为 1
        //以下是反馈结果：TourIndex含义是ET客票对应的航段序号。升舱只支持单人单段，所以固定传1即可。
        if (org.apache.commons.lang.StringUtils.isBlank(psrInfo.getTourIndex()) || org.apache.commons.lang.StringUtils.equals(psrInfo.getTourIndex(), "null")) {
            psrInfo.setTourIndex("1");
        }
        return psrInfo;
    }

    /**
     * 获取可升舱座位
     */
    private UpgFltInfoOutBean getPsrFltInfoByCert(String certType, String certNo, String airlineCode, String flightNumber,
                                                  String flightSuffix, String flightDate, String psrLevel, String departureAirport,
                                                  String tktNo) throws Exception {
        Map<String, String> psrFltInputMap = new HashMap<>();
        //证件类型
        psrFltInputMap.put("certType", certType);
        //证件号
        psrFltInputMap.put("certNo", certNo);
        //承运方航空公司二字码
        psrFltInputMap.put("airlineCode", airlineCode);
        //承运方航空公司航班号
        psrFltInputMap.put("flightNumber", flightNumber);
        //航班后缀
        psrFltInputMap.put("flightSuffix", flightSuffix);
        //航班日期
        psrFltInputMap.put("flightDate", flightDate);
        //2022年8月31日-->PE更新接口删除常客级别字段
        //常客级别
//        psrFltInputMap.put("psrLevel", psrLevel);
        //币种
        psrFltInputMap.put("currency", "CNY");
        //始发站
        psrFltInputMap.put("departureAirport", departureAirport);
        UpgFltInfoOutBean psrFltInfoByCert = iCheckInService.getPsrFltInfoByCert(psrFltInputMap);
        return psrFltInfoByCert;
    }

    /**
     * 获取舱位图，解析可升舱座位号
     */
    private Map<String, Object> querySeatChart(String flightDate, String flightNo, String toCity, String fromCity) {
        SeatChartQueryBean seatChartQueryBean = new SeatChartQueryBean();
        Map<String, Object> resultMap = new HashMap<>();
        seatChartQueryBean.setFlightDate(flightDate);
        seatChartQueryBean.setFlightNo(flightNo);
        seatChartQueryBean.setToCity(toCity);
        seatChartQueryBean.setFromCity(fromCity);
        SeatChartResultBean seatChartResultBean = iCheckInService.querySeatChart(seatChartQueryBean);
        log.info("==========升舱数据-舱位图==============");
        log.info("获取舱位图,参数详情：{}",
                "SeatMap:" + seatChartResultBean.getSeatMap() + ";" +
                        "SeatsMapList:" + seatChartResultBean.getSeatsMap() + ";" +
                        "PlaneType:" + seatChartResultBean.getPlaneType() + ";" +
                        "PlaneClass:" + seatChartResultBean.getPlaneClass() + ";" +
                        "originalSeatMap:" + seatChartResultBean.getOriginalSeatMap() + ";" +
                        "flightLayout:" + seatChartResultBean.getFlightLayout() + ";"
        );
        log.info("===========================================");
        String[] seatList = seatChartResultBean.getSeatMap().split("%");
        String seatCabin = seatList[0];
        String[] splitLineList = seatCabin.split("\\\\n");
        for (int i = 1; i < splitLineList.length; i++) {
            if (splitLineList[i].contains("*")) {
                char c = splitLineList[i].charAt(1);
                String s1 = splitLineList[i].replaceAll("\\d+", "")
                        .replaceAll("=", "")
                        .replaceAll(" ", "");
                int temp = 0;
                for (int j = 0; j < s1.length(); j++) {
                    if (org.apache.commons.lang.StringUtils.equals(String.valueOf(s1.charAt(j)), "*")) {
                        break;
                    }
                    temp++;
                }
                String s2 = splitLineList[0].replaceAll(" ", "");
                String s3 = s2.substring(1, s2.length() - 1);
                char s4 = s3.charAt(temp);
                resultMap.put("newSeat", String.valueOf(c) + String.valueOf(s4));
                resultMap.put("newCabin", String.valueOf(splitLineList[0].charAt(0)));
                return resultMap;
            }
        }
        return resultMap;
    }

}
