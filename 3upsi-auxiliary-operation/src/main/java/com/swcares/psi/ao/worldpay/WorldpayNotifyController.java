package com.swcares.psi.ao.worldpay;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.swcares.psi.ao.common.service.impl.PayNotifyRedisLockService;
import com.swcares.psi.ao.config.LogFactory;
import com.swcares.psi.aoMergeOrder.controller.MergeOrderNotifyController;
import com.swcares.psi.aoMergeOrder.service.impl.AoMegerOrderNotifyLockService;
import com.swcares.psi.common.worldpay.WorldpayUtil;
import com.swcares.psi.common.worldpay.vo.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.worldpay.WorldpayNotifyController <br>
 * Description：WorldPay notification回调通知接收控制器<br>
 * Copyright © 2023/9/18 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * @date 2023/9/18 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/wp/notify")
public class WorldpayNotifyController {

    private Logger log = LogFactory.WORLDPAY_LOGS;

    @Autowired
    private WorldpayUtil worldpayUtil;
    @Autowired
    private PayNotifyRedisLockService commonService;
    @Autowired
    AoMegerOrderNotifyLockService aoMegerOrderNotifyLockService;

    @PostMapping(value = "", headers = "content-type=text/xml")
    public String wpNotify(@RequestBody String data) {
        WorldPayBaseVo notification= worldpayUtil.notification(data);
        log.info("WorldPay Notify转换:{}",JSON.toJSONString(notification));
        Boolean isMergeOrder=false;
        if (notification != null) {
            switch (notification.getNotifyType()) {
                case AUTHORISED:
                    isMergeOrder=commonService.worldpayNotifyUrl((WorldpayAuthorisedVo) notification);
                    break;
                case CANCELLED:
                    isMergeOrder=commonService.worldpayCancelledNotify((WorldpayCancelledVo) notification);
                    break;
                case CAPTURED:
                    break;
                case REFUNDED:
                    isMergeOrder=commonService.worldpayRefundNotifyUrl((WorldpayRefundedVo) notification);
                    break;
                case SETTLED:
                    break;
                default:
                    break;
            }
            if(isMergeOrder){
                switch (notification.getNotifyType()) {
                    case AUTHORISED:
                        aoMegerOrderNotifyLockService.worldpayNotifyUrl((WorldpayAuthorisedVo) notification);
                        break;
                    case CANCELLED:
                        break;
                    case CAPTURED:
                        aoMegerOrderNotifyLockService.worldpayCapturedNotifyUrl((WorldpayCapturedVo) notification);
                        break;
                    case REFUNDED:
                        aoMegerOrderNotifyLockService.worldpayRefundNotifyUrl((WorldpayRefundedVo) notification);
                        break;
                    case SETTLED:
                        break;
                    default:
                        break;
                }
            }

            return "[OK]";
        } else {
            return "no";
        }
    }

    @Deprecated //TODO：测试添加待移除
    @PostMapping("/cm")
    public void orderCreateMofify(@RequestBody String data) {
        JSONObject json = JSON.parseObject(data);
        String method = json.getString("method");
        JSONObject param = json.getJSONObject("param");
        switch (method) {
            case "create": {
                worldpayUtil.orderCreateHosted(param.getString("orderNo"), param.getString("currencyCode"), param.getString("amount"), param.getString("paymentMethod"));
            }break;
            case "cancel": {
                worldpayUtil.orderCancel(param.getString("orderNo"));
            }break;
            case "refund": {
                worldpayUtil.orderRefund(param.getString("orderNo"), param.getString("currencyCode"), param.getString("amount"), param.getString("callbackData"));
            }break;
            case "capture": {
                worldpayUtil.orderCapture(param.getString("orderNo"), param.getString("currencyCode"), param.getString("amount"), param.getString("callbackData"));
            }break;
        }
    }

}
