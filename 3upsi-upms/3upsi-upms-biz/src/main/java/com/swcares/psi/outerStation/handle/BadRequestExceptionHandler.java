package com.swcares.psi.outerStation.handle;

import com.swcares.psi.admin.api.exception.HRSException;
import com.swcares.psi.admin.api.vo.HumanResourceSyncVo;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.RenderResult;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @Classname Hander
 * @Description TODO
 * @Date 2022/4/22 11:44
 * @Created by HuangXin
 */
@Slf4j
@RestControllerAdvice
public class BadRequestExceptionHandler {

    /**
     *  校验错误拦截处理
     *
     * @return 错误信息
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public RenderResult<String> validationBodyException(MethodArgumentNotValidException exception){
        BindingResult result = exception.getBindingResult();
        List<String> message = new ArrayList<>();
        /**
         *可解开
         **/
          if (result.hasErrors()) {
              List<ObjectError> errors = result.getAllErrors();
              errors.forEach(p ->{
                  FieldError fieldError = (FieldError) p;
                  message.add(fieldError.getDefaultMessage());
              });
          }
        return new RenderResult("0", StringUtils.join(message, ","), null);
    }

    /**
     *  人力资源同步接口产生异常封装返回
     *
     * @param request
     * @param e
     * @return
     */
    @ExceptionHandler(HRSException.class)
    public Object exception(HttpServletRequest request, HRSException e) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        log.error("全局异常信息，url:{}, userNo:{}, ex={}", request.getServletPath(), authentication != null ? authentication.getPrincipal() : null, e.getMessage(), e);

        return HumanResourceSyncVo.faild(e.getBimRequestId(), e.getMessage());
    }

}
