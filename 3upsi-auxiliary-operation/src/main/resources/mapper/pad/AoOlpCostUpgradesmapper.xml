<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.pad.mapper.AoOlpCostUpgradesMapper">
    <sql id="sql">
        select
        ougo.id,
        aoin.ORDER_NO ,
        pay.PAY_ORDER_NO,
        date_format(aoin.ORDER_DATE, '%Y-%m-%d') as ORDER_DATE,
        aoin.ORDER_TYPE ,
        case ougo.`status`
        when '0' then '未支付'
        when '1' then '支付失败'
        when '2' then '支付完成'
        when '3' then '发起退款'
        when '4' then '退款中'
        when '5' then '退款成功'
        when '6' then '退款失败'
        when '7' then '取消'
        end as status,
        ougo.PRICE,
        pay.CURRENCY_SPECIES,
        aoin.SIGNATURE_URL as signatureUrl,
        CONCAT(pay.CURRENCY_SPECIES_CODE,'(', pay.CURRENCY_SPECIES,')')  AS currencySpeciesName,
        aoin.USER_NO ,
        case
        when ougo.INVOICE is null then '未开票'
        else '已开票'
        end as invoice,
        ougo.PAX_NAME,
        aoin.FLIGHT_NO,
        case pay.PAY_TYPE
        when '0' then '现金'
        when '1' then '微信'
        when '2' then '支付宝'
        end as payType,
        date_format (pay.PAY_TIME,'%Y-%m-%d %H:%i:%s') as payTime,
        if(ougo.REVIEW_USER  is null,null,'1') as reviewStatus,
        CONCAT(get_city_name(aoin.ORG),aoin.ORG,"-",get_city_name(aoin.DST),aoin.DST) AS segment,
        date_format (aoin.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        ougo.OLD_SEAT_NUMBER as oldSeatNumber,
        ougo.CONTROL_NUMBER as controlNumber,
        aoin.id as orderId,
        aoin.ORG as org,
        aoin.DST as dst,
        sti.IS_CANCEL AS cancelStimulate,
        date_format (ougo.REFUND_END_TIME,'%Y-%m-%d %H:%i:%s') as refundEndTime
        from ao_order_info_new aoin
        left join AO_OLP_COST_UPGRADES ougo on ougo.AO_ORDER_ID=aoin.id
        left join ao_pay_order_info pay on pay.AO_ORDER_ID =aoin.id
        LEFT JOIN AO_STIMULATE_ORDER_INFO sti  on sti.ORDER_ID=aoin.id and sti.STATUS ='1'

    </sql>
    <sql id="whereSql">
        <where>
            <if test="dto.orderStartDate !=null and dto.orderStartDate !=''">
                aoin.ORDER_DATE &gt;= date_format(#{dto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="dto.orderEndDate !=null and dto.orderEndDate !=''">
                and aoin.ORDER_DATE &lt;= date_format(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>
            <if test="dto.orderNo !=null and dto.orderNo!=''">
                and aoin.ORDER_NO = #{dto.orderNo}
            </if>

            <if test="dto.invoice !=null and dto.invoice !=''">
                and 1=1
                <if test=" dto.invoice == 1">
                    and ougo.INVOICE is not null
                </if>
                <if test=" dto.invoice == 0">
                    and ougo.INVOICE is null
                </if>
            </if>
            <if test="dto.flightNo !=null and dto.flightNo !=''">
                and aoin.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.org !=null and dto.org !=''">
                and aoin.ORG = #{dto.org}
            </if>
            <if test="dto.dst !=null and dto.dst !=''">
                and aoin.DST = #{dto.dst}
            </if>
            <if test="dto.paxName !=null and dto.paxName !=''">
                and ougo.PAX_NAME like CONCAT('%',#{dto.paxName},'%')
            </if>
            <if test="dto.paxNo !=null and dto.paxNo !=''">
                and ougo.ID_NO =#{dto.paxNo}

            </if>
            <if test="dto.userNo !=null and dto.userNo !=''">
                and aoin.USER_NO like CONCAT('%',#{dto.userNo},'%')

            </if>

        </where>
    </sql>
    <select id="getOrderPage" resultType="com.swcares.psi.ao.pad.vo.AoOlpCostUpgradesOrderVo"
            parameterType="com.swcares.psi.ao.pad.dto.AoOlpCostUpgradesOrderDto">
        <include refid="sql"></include>
        <include refid="whereSql"></include>

    </select>
    <select id="getOrderList" resultType="com.swcares.psi.ao.pad.vo.AoOlpCostUpgradesOrderVo"
            parameterType="com.swcares.psi.ao.pad.dto.AoOlpCostUpgradesOrderDto">
        <include refid="sql"></include>
        <include refid="whereSql"></include>
    </select>
    <sql id="accountsReportSql">
        select (<include refid="com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper.getPaxHedCategories"></include>) as paxTypeDesc,
        pax.FQTV_NUMBER as ffpNumber,
        AOCU.PAX_NAME,
        date_format(apoi.PAY_TIME, '%Y-%m-%d %H:%i:%S') as payTime,
        aoi.ORDER_NO,
        apoi.PAY_ORDER_NO as payNo,
        AOCU.TURN_NUMBER,
        AOCU.TURN_NUMBER as turnNumber,
        aoi.FLIGHT_TYPE,
        case
        when AOCU.TURN_NUMBER is null and apoi.PAY_TYPE != '0' then '-'
        when AOCU.TURN_NUMBER is null and apoi.PAY_TYPE = '0' then '未缴纳'
        else'已缴纳'
        end as turn,
        apoi.CURRENCY_SPECIES,
        case apoi.CURRENCY_SPECIES_CODE
        when 'CNY' THEN CONCAT(apoi.CURRENCY_SPECIES_CODE,'(人民币)')
        ELSE
        (SELECT CONCAT(apoi.CURRENCY_SPECIES_CODE,'(', DATA_VALUE,')') FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=apoi.CURRENCY_SPECIES_CODE limit 1)
        end AS currencySpeciesName,
        AOCU.CONTROL_NUMBER,
#         AOCU.FINANCE_ORDER_NO,
        concat('JSC', if(AOCU.TKT_TYPE  is null,right(pax.TICKET_NUMBER,7),AOCU.TKT_TYPE)) as FINANCE_ORDER_NO,
        aoi.FLIGHT_NO,
        date_format (aoi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        CONCAT(po.AIRPORT_NAME,'-',pd.AIRPORT_NAME) as airLine,
        AOCU.TKT_NO,
        AOCU.OLD_SEAT_NUMBER,
        AOCU.NEW_SEAT_NUMBER,
        AOCU.NEW_SHIPPING_SPACE as newShippingSpace,
        AOCU.OLD_SHIPPING_SPACE as oldShippingSpace,
        case apoi.PAY_TYPE
        when '0' then '现金'
        when '1' then '微信'
        when '2' then '支付宝'
        end as payType,
        AOCU.PRICE,
        aoi.SEGMENT_TYPE,
        case AOCU.STATUS
        when '2' then '支付完成'
        when '5' then '退款成功'
        end as status,
        aoi.USER_NAME,
        if(AOCU.REVIEW_USER  is null,'未复核','已复核') as review,
        AOCU.REVIEW_USER,
        ifnull(AOCU.SALES_PERSONNEL,'-') as salesPersonnel,
        AOCU.BUSINESS_CLASS_STEWARD,
        (
            select  CONCAT(b.TOFNAME,'/',a.TOFNAME) from DEPARTMENT  a
            left join DEPARTMENT b on a.TOPID = b. TOID
            where a.id=aoi.DEPARTMENT_ID
        )  as deptNameLast,
        AOCU.SUPERVISION_PERSONNEL,
        CASE pax.LOYAL_LEVEL WHEN 'C' THEN '普卡' WHEN 'S' THEN '银卡' WHEN 'G' THEN '金卡' WHEN 'P' THEN '白金卡'
                              WHEN 'V' THEN '贵宾卡' WHEN 'J' THEN '集团客户' WHEN 'T' THEN '终身白金卡' END AS ffpType
        from AO_ORDER_INFO_NEW aoi
        left join AO_PAY_ORDER_INFO apoi on aoi.id=apoi.AO_ORDER_ID
        LEFT JOIN AO_OLP_COST_UPGRADES AOCU  ON AOCU.AO_ORDER_ID=aoi.id
        left join sys_airport_info po on po.code = aoi.org
        left join sys_airport_info pd on pd.code = aoi.dst
        left join flt_passenger_real_info pax on pax.id=AOCU.PAX_ID
    </sql>
    <sql id="accountsReportWhereSql">
        <where>
             AOCU.STATUS in ('2','5')
            <if test="dto.flightNo !=null and dto.flightNo !=''">
                and aoi.FLIGHT_NO = #{dto.flightNo}
            </if>

            <if test="dto.flightStartDate !=null and dto.flightStartDate !=''">
                AND aoi.FLIGHT_DATE &gt;= date_format(#{dto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="dto.flightEndDate !=null and dto.flightEndDate !=''">
                and aoi.FLIGHT_DATE &lt;= date_format(#{dto.flightEndDate},'%Y-%m-%d')
            </if>

            <if test="dto.org !=null and dto.org !=''">
                and aoi.ORG = #{dto.org}
            </if>
            <if test="dto.dst !=null and dto.dst !=''">
                and aoi.DST = #{dto.dst}
            </if>
            <if test="dto.flightType !=null and dto.flightType !=''">
                and aoi.FLIGHT_TYPE = #{dto.flightType}
            </if>

            <if test="dto.segmentType !=null and dto.segmentType !=''">
                and aoi.SEGMENT_TYPE = #{dto.segmentType}
            </if>

            <if test="dto.tktNo !=null and dto.tktNo !=''">
                and AOCU.TKT_NO like  CONCAT('%',#{dto.tktNo}, '%')
            </if>
            <if test="dto.payStatus !=null and dto.payStatus !=''">
                and apoi.PAY_STATUS = #{dto.payStatus}
            </if>

            <if test="dto.payType !=null and dto.payType !=''">
                and apoi.PAY_TYPE = #{dto.payType}
            </if>


            <if test="dto.orderNo !=null and dto.orderNo !=''">
                and aoi.ORDER_NO like  CONCAT('%',#{dto.orderNo}, '%')
            </if>

            <if test="dto.turnNumber !=null and dto.turnNumber !=''">
                and AOCU.TURN_NUMBER = #{dto.turnNumber}
            </if>

            <if test="dto.turnStatus !=null and dto.turnStatus !=''">
                <if test="dto.turnStatus == 1 ">
                    and AOCU.TURN_NUMBER is not null
                </if>
                <if test="dto.turnStatus == 0 ">
                    and AOCU.TURN_NUMBER is  null and apoi.PAY_TYPE = '0'
                </if>
            </if>

        </where>
    </sql>
    <sql id="accountsOrder">
        order by aoi.FLIGHT_DATE desc ,aoi.FLIGHT_NO desc
    </sql>
    <select id="getAccountsPage" resultType="com.swcares.psi.ao.pad.vo.AccountsReportVo"
            parameterType="com.swcares.psi.ao.pad.dto.AccountsDto">
        <include refid="accountsReportSql"></include>
        <include refid="accountsReportWhereSql"></include>
        <include refid="accountsOrder"></include>
    </select>
    <select id="getAccountsList" resultType="com.swcares.psi.ao.pad.vo.AccountsReportVo"
            parameterType="com.swcares.psi.ao.pad.dto.AccountsDto">
        <include refid="accountsReportSql"></include>
        <include refid="accountsReportWhereSql"></include>
        <include refid="accountsOrder"></include>
    </select>


    <sql id="financialSql">
        select
            'JSC' as financialNoPrefix,
            if(AOCU.TKT_TYPE  is null,right(pasg.TICKET_NUMBER,7),AOCU.TKT_TYPE) as tktType,
            date_format (aoi.ORDER_DATE,'%Y-%m-%d') as tktDate,
            left(pasg.TICKET_NUMBER,3) as associated,
            right(pasg.TICKET_NUMBER,7) as associatedNo,
            substr(pasg.TICKET_NUMBER,4,3) as associatedType,
            aoi.ORG,
            aoi.DST,
            aoi.FLIGHT_NO,
            date_format (aoi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
            AOCU.PRICE
        from AO_OLP_COST_UPGRADES AOCU
        LEFT JOIN AO_ORDER_INFO_NEW aoi   ON AOCU.AO_ORDER_ID=aoi.id
        left join AO_PAY_ORDER_INFO apoi on aoi.PAY_ORDER_ID=apoi.id
        left join flt_passenger_real_info pasg on pasg.id=AOCU.PAX_ID
    </sql>
    <sql id="financialWhereSql">
        <where>
            AOCU.STATUS in ('2','5')
            and aoi.segment_type='国内' and apoi.CURRENCY_SPECIES_CODE='CNY'
            and aoi.ORG NOT IN (SELECT DATA_CODE FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0')
            and aoi.DST NOT IN (SELECT DATA_CODE FROM SYS_DICT WHERE DATA_TYPE_CODE='GAT' AND DATA_STATUS='0')
            <if test="dto.flightNo !=null and dto.flightNo !=''">
                and aoi.FLIGHT_NO like  CONCAT('%',#{dto.flightNo}, '%')
            </if>
            <if test="dto.flightStartDate !=null and dto.flightStartDate !=''">
                AND aoi.FLIGHT_DATE &gt;= date_format(#{dto.flightStartDate},'%Y-%m-%d')
            </if>
            <if test="dto.flightEndDate !=null and dto.flightEndDate !=''">
                and aoi.FLIGHT_DATE &lt;= date_format(#{dto.flightEndDate},'%Y-%m-%d')
            </if>
            <if test="dto.org !=null and dto.org !=''">
                and aoi.ORG = #{dto.org}
            </if>
            <if test="dto.dst !=null and dto.dst !=''">
                and aoi.DST = #{dto.dst}
            </if>
            <if test="dto.associated !=null and dto.associated !=''">
                and AOCU.TKT_NO like  CONCAT(#{dto.associated}, '%')
            </if>
            <if test="dto.tktType !=null and dto.tktType !=''">
                and AOCU.TKT_TYPE like  CONCAT('%',#{dto.tktType})
            </if>
        </where>
    </sql>
    <sql id="financialOrder">
            order by aoi.FLIGHT_DATE desc ,aoi.FLIGHT_NO desc
    </sql>




    <select id="getFinancialPage" resultType="com.swcares.psi.ao.pad.vo.FinancialReportVo"
            parameterType="com.swcares.psi.ao.pad.dto.FinancialDto">
        <include refid="financialSql"></include>
        <include refid="financialWhereSql"></include>
        <include refid="financialOrder"></include>
    </select>
    <select id="getFinancialList" resultType="com.swcares.psi.ao.pad.vo.FinancialReportVo"
            parameterType="com.swcares.psi.ao.pad.dto.FinancialDto">
        <include refid="financialSql"></include>
        <include refid="financialWhereSql"></include>
        <include refid="financialOrder"></include>
    </select>


    <sql id="padLoadOrder">
        select
        pax.id as id,
        ord.id  as orderId,
        pax.PAX_ID as paxId,
        pax.PAX_NAME as paxName,
        pax.ID_NO as idNo,
        pax.ID_TYPE as idType,
        pax.OLD_SEAT_NUMBER as oldSeatNumber,
        pax.NEW_SEAT_NUMBER as newSeatNumber,
        pax.REMARK as remark,
        pax.PRICE as price,
        pax.TKT_NO as tktNo,
        pax.INVOICE as invoice,
        pax.STATUS as paxStatus,
        case   pax.STATUS
        when '0' then '未支付'
        when '1' then '支付超时失败'
        when '2' then '支付完成'
        when '3' then '发起退款'
        when '4' then '退款中'
        when '5' then '退款成功'
        when '6' then '退款失败'
        end as paxStatusName,
        pay.CURRENCY_SPECIES as CURRENCY_SPECIES,
        pay.CURRENCY_SPECIES_CODE as CURRENCY_SPECIES_CODE,
        pay.PAY_TYPE as payType,
        pay.PRICE as totalPrice,
        pay.PAY_STATUS as orderPayStatus,
        case   pay.PAY_STATUS
        when '0' then '未支付'
        when '1' then '已支付'
        when '2' then '已部分退款'
        when '3' then '全部退款'
        end as orderPayStatusName,
        ord.ORDER_TYPE as orderType,
        ord.ORDER_TYPE_CODE as orderTypeCode,
        ord.FLIGHT_NO as flightNo,
        date_format(ord.FLIGHT_DATE,'%Y-%m-%d')  as flightDate,
        ord.PAX_NO as paxNo,
        ord.PAX_NAME as orderPaxName,
        ord.TKT_NO as orderTktNo,
        ord.ORDER_NO as orderNo,
        ord.ORDER_STATUS as orderStatus,
        case   ord.ORDER_STATUS
        when '0' then '未支付'
        when '1' then '支付超时失败'
        when '2' then '支付完成'
        when '3' then '部分退款失成功'
        when '4' then '全部退款成功'
        when '5' then '取消'
        end as orderStatusName,
        ord.ORG as org,
        ord.DST as dst,
        ord.SEGMENT_TYPE as segmentType,
        ord.FLIGHT_TYPE as flightType,
        ord.PAX_ID as orderPaxId,
        date_format(ord.ORDER_DATE,'%Y-%m-%d %H:%i:%S') as orderDate,
        date_format(pay.PAY_TIME,'%Y-%m-%d %H:%i:%S') as payTime,
        ord.USER_NO as userNo,
        ord.USER_NAME as userName,
        pax.CONTROL_NUMBER as controlNumber,
        pax.CONTROL_FLIE_PATH as controlFliePath,
        pax.NEW_SHIPPING_SPACE as newShippingSpace,
        pax.OLD_SHIPPING_SPACE as oldShippingSpace,
        pax.PAD_ORDER_PAX_ID as padOrderPaxId,
        pax.REVIEW_USER_NO as reviewUserNo,
        pax.REVIEW_USER as reviewUser,
        pax.BUSINESS_CLASS_STEWARD as businessClassSteward,
        pax.SALES_PERSONNEL as salesPersonnel,
        pax.SUPERVISION_PERSONNEL as supervisionPersonnel,
        date_format(pax.REVIEW_TIME,'%Y-%m-%d %H:%i:%S') as reviewTime,
        ord.SIGNATURE_URL as signatureUrl,
        pay.QRCODE_PATH as qrCode
        from AO_ORDER_INFO_NEW ord
        left join AO_PAY_ORDER_INFO pay on pay.AO_ORDER_ID=ord.id
        left join AO_OLP_COST_UPGRADES pax on pax.AO_ORDER_ID=ord.id
    </sql>
    <select id="getOlpCostUpgradeOrder" resultType="com.swcares.psi.base.data.api.vo.OlpCostUpgradeDownInfoVo">
        <include refid="padLoadOrder"></include>

        <where>
            <if test="org != null  and org != ''">
                ord.ORG=#{org}
            </if>
            <if test="dst != null  and dst != ''">
                and ord.DST=#{dst}
            </if>
            <if test="flightNo != null  and flightNo != ''">
                and ord.FLIGHT_NO=#{flightNo}
            </if>
            <if test="flightDate != null  and flightDate != ''">
                and ord.FLIGHT_DATE=date_format(#{flightDate},'%Y-%m-%d')
            </if>

            <if test="padPaxId != null and padPaxId.size()>0">
                AND pax.PAD_ORDER_PAX_ID in
                <foreach collection="padPaxId" item="ele" separator="," open="(" close=")">
                    #{ele}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getPadOnTurnOrderInfo" resultType="com.swcares.psi.base.data.api.vo.OlpCostUpgradeDownInfoVo">
        <include refid="padLoadOrder"></include>
        <where>
            pay.PAY_TYPE='0' and pay.PAY_STATUS='1' and ord.ORDER_STATUS='2'
            and pax.TURN_NUMBER is null and pay.CURRENCY_SPECIES='人民币'
            and ord.USER_NO=#{userNo}
            <choose>
                <when test="segmentType == '国内'.toString()">
                    and ord.SEGMENT_TYPE='国内'
                </when>
                <otherwise>
                    and ord.SEGMENT_TYPE in ('国际', '地区')
                </otherwise>
            </choose>
            and not  EXISTS (
                select 1 from ao_temp_cash_return temp where temp.ORDER_NO=pax.ID and TURN_STATUS='2' and ORDER_TYPE='6'
                )

        </where>
    </select>

    <select id="getLpCostUpgradeOrderById" resultType="com.swcares.psi.ao.pad.vo.AccountsReportVo">
        select
        aoi.ORDER_NO,
        apoi.PAY_ORDER_NO,
        AOCU.TURN_NUMBER,
        if(AOCU.TURN_NUMBER is null,'未缴纳','已缴纳') as turnNumber,
        AOCU.CONTROL_NUMBER,
#         AOCU.FINANCE_ORDER_NO,
        concat('JSC', if(AOCU.TKT_TYPE  is null,right(psg.TICKET_NUMBER,7),AOCU.TKT_TYPE)) as FINANCE_ORDER_NO,
        aoi.FLIGHT_NO,
        date_format (aoi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
        CONCAT(po.AIRPORT_NAME,'-',pd.AIRPORT_NAME) as airLine,
        AOCU.TKT_NO,
        AOCU.OLD_SHIPPING_SPACE,
        AOCU.NEW_SHIPPING_SPACE,
        aoi.FLIGHT_TYPE,
        case apoi.PAY_TYPE
        when '0' then '现金'
        when '1' then '微信'
        when '2' then '支付宝'
        end as payType,
        apoi.CURRENCY_SPECIES,
        AOCU.PRICE,
        aoi.SEGMENT_TYPE,
        case apr.PAY_TYPE
        when '2' then '支付完成'
        when '5' then '退款成功'
        when '6' then '退款失败'
        end as status,
        aoi.USER_NAME,
        if(AOCU.REVIEW_USER  is null,'未复核','已复核') as review,
        AOCU.REVIEW_USER,
        AOCU.SALES_PERSONNEL,
        AOCU.BUSINESS_CLASS_STEWARD,
        AOCU.SUPERVISION_PERSONNEL
        from AO_ORDER_INFO_NEW aoi
        left join AO_PAY_ORDER_INFO apoi on aoi.id=apoi.AO_ORDER_ID
        LEFT JOIN AO_OLP_COST_UPGRADES AOCU  ON AOCU.AO_ORDER_ID=aoi.id
        left join sys_airport_info po on po.code = aoi.org
        left join sys_airport_info pd on pd.code = aoi.dst
        LEFT JOIN ao_pay_record apr on apr.ORDER_ID=aoi.ID and AOCU.id=apr.ORDER_PRODUCT_ID
        left join flt_passenger_real_info psg on psg.id=AOCU.PAX_ID
        where AOCU.id=#{id}
    </select>



    <select id="olpOrderExistValidate" resultType="java.lang.String">
        select
            ORDER_NO
        from
            AO_ORDER_INFO_NEW
        where
            id = (
            select
                distinct(AO_ORDER_ID)
            from
                AO_OLP_COST_UPGRADES
            where
                TEMP_ORDER_NO is not null
                and TEMP_ORDER_NO =
                #{tempOrderNo}
            limit 1
        )
    </select>


    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo">
        select t.* from (
        SELECT
        sum(CASE WHEN (rs.chargeType = '0' ) THEN rs.ORDER_PRICE ELSE 0 end) AS cashMoney,
        sum(CASE WHEN rs.chargeType = '1' THEN rs.ORDER_PRICE ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN rs.chargeType = '2'  THEN rs.ORDER_PRICE ELSE 0 end ) AS alipayMoney,
        '0' AS bankCardMoney,
        sum(CASE WHEN rs.chargeType = '5' THEN rs.ORDER_PRICE ELSE 0 end) AS worldPay,
        sum(rs.ORDER_PRICE  ) AS allMoney,
        case  rs.CURRENCY_SPECIES
        when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
        else   concat(rs.CURRENCY_SPECIES,(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1))
        end as currencySpecies
        from(
            select
        apoi.PAY_TYPE as  chargeType,
        AOCU.PRICE as  ORDER_PRICE,
        apoi.CURRENCY_SPECIES_CODE as CURRENCY_SPECIES
        from AO_ORDER_INFO_NEW aoi
        left join AO_PAY_ORDER_INFO apoi on aoi.id=apoi.AO_ORDER_ID
        LEFT JOIN AO_OLP_COST_UPGRADES AOCU  ON AOCU.AO_ORDER_ID=aoi.id
            <include refid="accountsReportWhereSql"></include>
            <include refid="accountsOrder"></include>
        ) rs
        group by rs.CURRENCY_SPECIES
        ) t where t.allMoney > 0
    </select>
</mapper>
