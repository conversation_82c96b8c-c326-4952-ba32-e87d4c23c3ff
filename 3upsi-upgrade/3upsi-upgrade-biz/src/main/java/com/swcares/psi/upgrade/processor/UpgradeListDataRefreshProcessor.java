package com.swcares.psi.upgrade.processor;

import com.alibaba.fastjson.JSON;
import com.swcares.psi.base.data.api.cmdReturnBean.AvCmdReturnBean;
import com.swcares.psi.base.data.api.cmdReturnBean.CmdCons;
import com.swcares.psi.base.data.api.cmdReturnBean.CmdUtils;
import com.swcares.psi.base.data.api.cmdReturnBean.DetrCmdReturnBean;
import com.swcares.psi.base.data.api.dto.UpgradePassengerDto;
import com.swcares.psi.base.data.api.entity.SysAirLineInfoEntity;
import com.swcares.psi.base.data.api.entity.SysY100Price;
import com.swcares.psi.base.data.api.vo.UpgradeFlightInfoVo;
import com.swcares.psi.base.data.api.vo.UpgradePassengerVo;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.FltPassengerRealInfoService;
import com.swcares.psi.base.data.service.ISysDictService;
import com.swcares.psi.base.data.service.SysAirLineInfoService;
import com.swcares.psi.base.data.service.SysY100PriceService;
import com.swcares.psi.common.core.rpc.RpcUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.ceUtils.OrBean;
import com.swcares.psi.common.utils.ceUtils.RoInterfaceUtil;
import com.swcares.psi.upgrade.cons.UpgradeConstant;
import com.swcares.psi.upgrade.dto.FlightInfoDto;
import com.swcares.psi.upgrade.dto.PaxInfoDto;
import com.swcares.psi.upgrade.dto.UpgradeLegQueryDto;
import com.swcares.psi.upgrade.entity.FlightUpgradeLog;
import com.swcares.psi.upgrade.entity.UpgradeFlightPlan;
import com.swcares.psi.upgrade.entity.UpgradeLfBlack;
import com.swcares.psi.upgrade.entity.UpgradePlan;
import com.swcares.psi.upgrade.entity.UpgradeRuleCard;
import com.swcares.psi.upgrade.entity.UpgradeRuleDiscount;
import com.swcares.psi.upgrade.entity.UpgradeRuleTime;
import com.swcares.psi.upgrade.enums.UpgradeEnum;
import com.swcares.psi.upgrade.exception.UpgrardeException;
import com.swcares.psi.upgrade.mapper.FlightUpgradeLogMapper;
import com.swcares.psi.upgrade.mapper.UpgradePlanMapper;
import com.swcares.psi.upgrade.service.UpgradeFlightPlanService;
import com.swcares.psi.upgrade.service.UpgradeLfBlackService;
import com.swcares.psi.upgrade.service.UpgradePlanService;
import com.swcares.psi.upgrade.service.UpgradeRuleCardService;
import com.swcares.psi.upgrade.service.UpgradeRuleDiscountService;
import com.swcares.psi.upgrade.service.UpgradeRuleLegService;
import com.swcares.psi.upgrade.service.UpgradeVerifyService;
import com.swcares.psi.upgrade.task.UpgradeExecute;
import com.swcares.psi.upgrade.vo.UpgradeLegVo;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/26 10:33
 */
@Slf4j
@Component
@RefreshScope
public class UpgradeListDataRefreshProcessor {
    @Autowired
    FltFlightRealInfoService fltFlightRealInfoService;

    @Autowired
    SysAirLineInfoService sysAirLineInfoService;//航线
    @Autowired
    SysY100PriceService sysY100PriceService;//运价
    @Autowired
    UpgradeFlightPlanService upgradeFlightPlanService;//计划升舱名单管理  航班

    @Autowired
    FltPassengerRealInfoService fltPassengerRealInfoService;
    @Autowired
    UpgradePlanService upgradePlanService;//计划升舱名单管理 旅客
    @Autowired
    UpgradePlanMapper planMapper;//计划升舱名单管理 旅客
    @Autowired
    UpgradeRuleCardService upgradeRuleCardService;//升舱旅客卡级别排序规则
    @Autowired
    UpgradeVerifyService upgradeVerifyService;  //升舱旅客验证
    @Autowired
    RpcUtil rpcUtil;
    @Autowired
    UpgradeExecute upgradeExecute;//升舱处理
    @Autowired
    FlightUpgradeLogMapper flightUpgradeLogMapper;
    @Autowired
    UpgradeRuleLegService upgradeRuleLegService;//允许升舱航段设置

    @Autowired
    UpgradeRuleDiscountService upgradeRuleDiscountService;//卡级别折扣设置
    @Autowired
    ISysDictService sysDictService;//数据字典

    @Autowired
    Redisson redisson;

    @Autowired
    UpgradeLfBlackService upgradeLfBlackService;//协议黑名单
    @Value("${system.cmd.upgrade.white.flightNo}")
    private String flightNo;
    @Value("${system.cmd.upgrade.white.segment}")
    private String segment;


    @Async("upgradeOrder")
    public void order(UpgradeFlightInfoVo ele, Map<String, Object> cancel, UpgradeRuleTime upgradeRuleTime, CountDownLatch latch) {
        String org = ele.getOrg();
        String dst = ele.getDst();
        boolean pass=true;
        if(ObjectUtils.isNotEmpty(segment) && ObjectUtils.isNotEmpty(flightNo)){
            if (!segment.toUpperCase().contains(org.toUpperCase() + "-" + dst.toUpperCase())) {
                pass = false;
            }
            if (!flightNo.toUpperCase().contains(ele.getFlightNumber())) {
                pass = false;
            }
        }else if(ObjectUtils.isNotEmpty(segment) && (!segment.toUpperCase().contains(org.toUpperCase() + "-" + dst.toUpperCase()))){
            pass = false;
        }else if(ObjectUtils.isNotEmpty(flightNo)&& (!flightNo.toUpperCase().contains(ele.getFlightNumber()))){
            pass = false;
        }
        if(!pass){
            log.info("升舱排序白名单拦截--{}>>{}",ele.getFlightNumber(),org+"-"+dst);
            latch.countDown();
            return;
        }
        log.info("执行升舱排序--{}>>{}",ele.getFlightNumber(),org+"-"+dst);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String format = ele.getFlightDate().format(fmt);
        try {
            //查询AC段运价
            if (ele.getIsLong()) {
                SysAirLineInfoEntity one = sysAirLineInfoService.lambdaQuery().eq(SysAirLineInfoEntity::getDst, ele.getDst())
                        .eq(SysAirLineInfoEntity::getOrg, ele.getOrg())
                        .eq(SysAirLineInfoEntity::getIsUse, "0")
                        .one();
                if (one == null) {
                    ele.setPrice(0);
                } else {
                    SysY100Price y100Price = sysY100PriceService.lambdaQuery().eq(SysY100Price::getAirLineId, one.getId())
                            .one();
                    if (y100Price == null) {
                        ele.setPrice(0);
                    } else {
                        ele.setPrice(y100Price.getPrice());
                    }
                }
            }



            //航段允许升舱舱位
            UpgradeLegQueryDto dto = new UpgradeLegQueryDto();
            dto.setStatus("1");
            dto.setOrgCityAirp(ele.getOrg());
            dto.setDstCityAirp(ele.getDst());
            List<UpgradeLegVo> upgradeRuleLegList = upgradeRuleLegService.getList(dto);

            //Y舱及替代舱位允许升舱折扣
            List<UpgradeRuleDiscount> cardTypeSeat = upgradeRuleDiscountService.lambdaQuery().list();
            //查询所有升舱卡级别包含的舱位
            Map<String, List<Space_Discount>> cabinMap = new HashMap<>();
            cardTypeSeat.stream().forEach(e -> {
                String[] cardType = e.getCardType().split(",");
                String[] space = StringUtils.isEmpty(e.getSpace())?new String[]{}:e.getSpace().split(",");
                for (String ct : cardType) {
                    if (cabinMap.get(ct) == null) {
                        List<Space_Discount> list =new ArrayList<>();
                        cabinMap.put(ct, list);
                    }
                    for (String sp : space) {
                        Space_Discount sd= new Space_Discount();
                        sd.setDiscount(e.getDiscount());
                        sd.setSpace(sp);
                        cabinMap.get(ct).add(sd);
                    }
                }

            });

            //查询出所有的可升舱旅客
            UpgradePassengerDto dtos = new UpgradePassengerDto();
            dtos.setOrg(ele.getOrg());
            dtos.setDst(ele.getDst());
            String flightDateStr = ele.getFlightDate().format(fmt);
            dtos.setFlightDate(flightDateStr);
            dtos.setFlightNo(ele.getFlightNumber());
            List<UpgradePassengerVo> fltPassenger = fltPassengerRealInfoService.getUpgradePassengerList(dtos);


            //特殊航班判断
            LocalDateTime std = ele.getStd();
            Integer hours = new Integer(ele.getStd().getHour());

            boolean tenBefore = true;
            if (hours == 10) {
                if (std.getMinute() > 0 || std.getSecond() > 0) {
                    tenBefore = false;
                }
            } else if (hours > 10) {
                tenBefore = false;
            }
            ele.setSpecial(tenBefore);
            //判断航班是否已在升舱航班列表中
            UpgradeFlightPlan one = upgradeFlightPlanService.lambdaQuery()
                    .eq(UpgradeFlightPlan::getOrg, ele.getOrg())
                    .eq(UpgradeFlightPlan::getDst, ele.getDst())
                    .eq(UpgradeFlightPlan::getFlightDate, ele.getFlightDate())
                    .eq(UpgradeFlightPlan::getFlightNo, ele.getFlightNumber())
                    .one();

            if (one == null) {
                //第一次新增的时候 黑名单或者取消航班  不做处理
                if (ele.getBlack() || "C ".equals(ele.getFlightState())) {
                    log.info("--------黑名单或者取消航班");
                    return;
                }
                LocalDateTime currentTime = LocalDateTime.now();
                //航班计划起飞时间在10点之前
                if (tenBefore) {
                    ele.setSpecial(true);
                    int specialEntryListHour = upgradeRuleTime.getSpecialEntryListHour(); //提前一天几点进入航班列表配置
                    int specialEntryListMinute = upgradeRuleTime.getSpecialEntryListMinute(); //提前一天几点进入航班列表配置
                    DateTimeFormatter ymddf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    //计划起飞时间前一天指定时间进入航班列表
                    LocalDateTime parse = LocalDateTime.parse(ymddf.format(std) + " " + (specialEntryListHour < 10 ? "0" : "") + specialEntryListHour +":"+(specialEntryListMinute < 10 ? "0" : "") + specialEntryListMinute+ ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));//计划起飞时间
                    LocalDateTime localDateTime = parse.minusDays(1);//進入排序航班列表时间
                    String s = DateUtils.parseLocalDateTimeToString(localDateTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    String now = DateUtils.parseLocalDateTimeToString(currentTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    String stds = DateUtils.parseLocalDateTimeToString(std, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    log.info("特殊航班{}计划起飞时间{}当前时间{}设置的进名单时间{}",ele.getFlightNumber(),stds,now,s);
                    //当前时间是否已经过了航班计划起飞时间的前一天晚上10点
                    if (currentTime.isAfter(localDateTime)) {
                        savePlan(upgradeRuleLegList, ele, fltPassenger, upgradeRuleTime , cabinMap);
                    }

                } else {//航班计划起飞时间在10点之后
                    ele.setSpecial(false);
                    //计划起飞时间多少小时进入航班列表
                    int normalEntryFlightListHours = upgradeRuleTime.getNormalEntryListHours();
                    LocalDateTime localDateTime = std.minusHours(normalEntryFlightListHours);
                    String s = DateUtils.parseLocalDateTimeToString(localDateTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    String now = DateUtils.parseLocalDateTimeToString(currentTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    String stds = DateUtils.parseLocalDateTimeToString(std, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    log.info("普通航班{}计划起飞时间{}当前时间{}设置的进名单时间{}",ele.getFlightNumber(),stds,now,s);
                    if (currentTime.isAfter(localDateTime)) {
                        savePlan(upgradeRuleLegList, ele, fltPassenger, upgradeRuleTime, cabinMap);
                    }
                }
            } else
            {
                one.setY100Price(ele.getPrice());
                one.setStd(ele.getStd());
                one.setDepartureTime(ele.getDepartureTime());
                //航班已取消
                if (cancel.get(format + ele.getFlightNumber()) != null) {
                    one.setFlightCancel(UpgradeEnum.YES);
                    one.setStatus(UpgradeEnum.UP_FLIGHT_NOT);
                    one.setAuthUpgrade(UpgradeEnum.NO);
                    one.setExplainInfo("航班取消");
                    upgradeFlightPlanService.saveOrUpdate(one);
                    return;
                }
                //航班在黑名单
                if (ele.getBlack()) {
                    one.setAuthUpgrade(UpgradeEnum.NO);
                    one.setStatus(UpgradeEnum.UP_FLIGHT_NOT);
                    one.setBlack(UpgradeEnum.YES);
                    one.setExplainInfo("黑名单航班");
                    upgradeFlightPlanService.saveOrUpdate(one);
                    return;
                }

                List<UpgradePlan> list = upgradePlanService.lambdaQuery()
                        .eq(UpgradePlan::getOrgVc, ele.getOrg())
                        .eq(UpgradePlan::getDstVc, ele.getDst())
                        .eq(UpgradePlan::getFlightDate, ele.getFlightDate())
                        .eq(UpgradePlan::getFlightNo, ele.getFlightNumber())
                        .list();
                List<UpgradePassengerVo> newPlanP = new ArrayList<>();
                DateTimeFormatter tempfmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                //修改plan  只对升舱旅客操作
                for (UpgradePassengerVo pele : fltPassenger) {
                    log.info("抓取旅客{}处理>>{}",pele.getPaxName(),JSON.toJSONString(pele));
                    String localTimePassenger = tempfmt.format(pele.getFlightDate());
                    boolean newPlan = true;//新的升舱旅客标识
                   olp : for (UpgradePlan oldPlan : list) {
                        String localTimeOldPlanr = tempfmt.format(oldPlan.getFlightDate());
                        RLock lock = null;
                        try {
                            int t=0;
                            //旅客升舱加锁
                            lock = redisson.getLock(UpgradeConstant.UPGRADE_PAX + oldPlan.getId());
                            while (!lock.tryLock()) {
                                if(t>5){
                                    continue olp;
                                }
                                Thread.sleep(330);
                                t++;
                            }

                        //已存在的升舱计划状态确认
                        if (oldPlan.getOrgVc().equals(pele.getOrgVc())
                                && oldPlan.getDstVc().equals(pele.getDstVc())
                                && oldPlan.getFlightNo().equals(pele.getFlightNo())
                                && localTimeOldPlanr.equals(localTimePassenger)
                                && oldPlan.getPaxId().equals(pele.getPaxId())
                        ) {
                            dtos.setPaxId(oldPlan.getPaxId());
                            List<UpgradePassengerVo> peleDB = fltPassengerRealInfoService.getUpgradePassengerList(dtos);
                            if(peleDB.size()<1){
                                log.info("旅客{}-{}数据不存在,不更新升舱旅客属性",oldPlan.getPaxName(),oldPlan.getTktNo());
                                break;
                            }
                            UpgradePassengerVo  cpele=peleDB.get(0);
                            newPlan=false;
                            if(UpgradeEnum.UPDATE_STATE_SUCCESS.equals(oldPlan.getUpState())){
                                log.info("旅客{}-{}已生舱,不更新升舱旅客属性",oldPlan.getPaxName(),oldPlan.getTktNo());
                                break;
                            }else if(UpgradeEnum.UPDATE_STATE_CANCEL.equals(oldPlan.getUpState()) && "1".equals(oldPlan.getCancelStyle())){//手动取消
                                log.info("旅客{}-{}已人工取消,不更新升舱旅客属性",oldPlan.getPaxName(),oldPlan.getTktNo());
                                break;
                            }


                            updatePlanPaxValidate(oldPlan, upgradeRuleLegList, cpele, ele, cabinMap);
                            upgradePlanService.saveOrUpdate(oldPlan);
                            log.info("符合升舱旅客{}入库更新,航班信息{}-{}>>{}",oldPlan.getPaxName(),oldPlan.getFlightNo(),DateUtils.parseLocalDateToString(oldPlan.getFlightDate(),DateUtils.YYYY_MM_DD), JSON.toJSONString(oldPlan));
                            break;
                        }
                        } catch (Exception e){
                            log.error("修改升舱旅客状态异常-{}-{}-{}",oldPlan.getId(),oldPlan.getPaxName(),e.getMessage(),e);
                            continue olp;
                        }finally {
                            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                                lock.unlock();
                            }
                        }
                    }
                    if (newPlan) {
                        newPlanP.add(pele);
                    }
                }

                List<UpgradeRuleCard> listUpgradeRuleCard = upgradeRuleCardService.lambdaQuery().list();
                for (UpgradePassengerVo pele : newPlanP) {
                    for (UpgradeRuleCard ruleCard : listUpgradeRuleCard) {
                        if (StringUtils.isNotEmpty(pele.getCardType()) && pele.getCardType().equals(ruleCard.getCardType())) {
                            pele.setUpgradeRuleOrder(ruleCard.getCardOrder());
                            pele.setRuleRode(ruleCard.getRuleCode());
                            break;
                        }
                    }
                }
                //新增的升舱计划
                for (UpgradePassengerVo pp : newPlanP) {
                    savePlanP(upgradeRuleLegList, pp, ele, upgradeRuleTime, cabinMap);
                }
//                updateFilghtStatus(one,tempClientId);
                //更新航班状态
                upgradeFlightPlanService.saveOrUpdate(one);
            }

        } catch (Exception e) {
            log.error("升舱排序报错", e.getMessage(), e);
        } finally {
            latch.countDown();
        }
    }

    private void savePlan(List<UpgradeLegVo> upgradeRuleLegList, UpgradeFlightInfoVo ele, List<UpgradePassengerVo> fltPassenger, UpgradeRuleTime upgradeRuleTime, Map<String, List<Space_Discount>> cabinMap) {
        if(UpgradeEnum.NO.equals( (ele.getFlightState()==null || !ele.getFlightState().equals("C"))?UpgradeEnum.YES: UpgradeEnum.NO)){
            log.info("大客户升舱航班-取消-不新增-{}-}{}-{}-{}",ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
            return;
        }

        if(ele.getBlack()){
            log.info("大客户升舱航班-黑名单-不新增-{}-}{}-{}-{}",ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
            return;
        }

        UpgradeFlightPlan flightPlan = new UpgradeFlightPlan();
        flightPlan.setStatus(UpgradeEnum.UP_FLIGHT_STATE_WAIT);
        flightPlan.setFlightNo(ele.getFlightNumber());
        flightPlan.setFlightDate(ele.getFlightDate());
        flightPlan.setOrg(ele.getOrg());
        flightPlan.setDst(ele.getDst());
        flightPlan.setY100Price(ele.getPrice());
        flightPlan.setIsAc(ele.getIsLong() ? UpgradeEnum.YES : UpgradeEnum.NO);
        flightPlan.setAuthUpgrade(UpgradeEnum.YES);
        flightPlan.setStd(ele.getStd());
        flightPlan.setDepartureTime(ele.getDepartureTime());
        flightPlan.setCreateTime(LocalDateTime.now());
        flightPlan.setMixed(UpgradeEnum.NO);
        flightPlan.setSpecial(UpgradeEnum.NO);
        flightPlan.setCarryChild(UpgradeEnum.NO);
        flightPlan.setGp(UpgradeEnum.NO);
        flightPlan.setBlack(UpgradeEnum.NO);
        flightPlan.setFlightCancel(UpgradeEnum.NO);
        flightPlan.setStdBeforeTen(ele.getSpecial() ? UpgradeEnum.YES : UpgradeEnum.NO);
        List<UpgradeRuleCard> list = upgradeRuleCardService.lambdaQuery().list();

        List<UpgradePassengerVo> newPlan = new ArrayList<>();
        for (UpgradePassengerVo pele : fltPassenger) {
            for (UpgradeRuleCard ruleCard : list) {
                if (StringUtils.isNotEmpty(pele.getCardType()) && pele.getCardType().equals(ruleCard.getCardType())) {
                    pele.setUpgradeRuleOrder(ruleCard.getCardOrder());
                    pele.setRuleRode(ruleCard.getRuleCode());
                    break;
                }
            }
            if(StringUtils.isEmpty(pele.getRuleRode())){
                continue;
            }
            newPlan.add(pele);
        }
        //验证航班状态
//        boolean b = updateFilghtStatus(flightPlan, cmdClientId);
//        if(!b){
//            return;
//        }
        if (newPlan.size() > 0 && !(UpgradeEnum.STOP_FLIGHT_UPGRADE_P.equals(flightPlan.getStopUpgrade()))) {
            upgradeFlightPlanService.save(flightPlan);
            for (UpgradePassengerVo pele : newPlan) {
                savePlanP(upgradeRuleLegList, pele, ele, upgradeRuleTime, cabinMap);
            }
        }
    }

    /**
     * 保存升舱旅客计划
     *
     * @param pele
     * @param ele
     */
    private void savePlanP(List<UpgradeLegVo> upgradeRuleLegList, UpgradePassengerVo pele, UpgradeFlightInfoVo ele, UpgradeRuleTime upgradeRuleTime,Map<String, List<Space_Discount>> cabinMap) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime std = ele.getStd();
        UpgradePlan upgradePlan = new UpgradePlan();
        //判断是否能进旅客升舱排序
        if (ele.getSpecial()) {
            LocalDate yesterDayDate = std.minusDays(1).toLocalDate();
            int specialEntryListHour = upgradeRuleTime.getSpecialEntryListHour();
            int specialEntryListMinute = upgradeRuleTime.getSpecialEntryListMinute();
            LocalDateTime specialEntryListTime = yesterDayDate.atTime(specialEntryListHour, specialEntryListMinute);
            if (now.isBefore(specialEntryListTime)) {
                log.info("大客户升舱旅客-未到入库时间-不新增{}-{}-{}-{}-{}",pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                return;
            }
        } else {
            int normalEntryListHours = upgradeRuleTime.getNormalEntryListHours();
            if (now.isBefore(std.minusHours(normalEntryListHours))) {
                log.info("大客户升舱旅客-未到入库时间-不新增{}-{}-{}-{}-{}",pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                return;
            }
        }


        //取消旅客
        if ("Y".equals(pele.getIsCancel())) {
            log.info("大客户升舱旅客-旅客取消-不新增--{}-{}-{}-{}-{}",pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
            return;
        }

        upgradePlan.setCardType(pele.getCardType());
        upgradePlan.setPsgTktTime(pele.getPrintTicketTime());
        upgradePlan.setPaxId(pele.getPaxId());
        upgradePlan.setFlightNo(pele.getFlightNo());
        upgradePlan.setFlightDate(pele.getFlightDate());
        upgradePlan.setOrgVc(pele.getOrgVc());
        upgradePlan.setDstVc(pele.getDstVc());
        upgradePlan.setPaxName(pele.getPaxName());
        upgradePlan.setPaxFoid(pele.getPaxFoid());
        upgradePlan.setTktNo(pele.getTktNo());
        upgradePlan.setPsgPnr(pele.getPnr());
        upgradePlan.setLfNum(pele.getLfNumber());
        upgradePlan.setSubCabin(pele.getSubCabin());
        upgradePlan.setUpState(UpgradeEnum.UPDATE_STATE_TOUP);
        upgradePlan.setUpStyle(UpgradeEnum.UPDATE_ORDER);
        upgradePlan.setPause(UpgradeEnum.NOT_PAUSE);
        upgradePlan.setUpOrder(pele.getUpgradeRuleOrder());
        upgradePlan.setRuleCode(pele.getRuleRode());
        upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_YES);
        upgradePlan.setCheckStatus(pele.getCheckStatus());
        upgradePlan.setFfpMile(pele.getPsgMileage().intValue());
        upgradePlan.setCreateTime(LocalDateTime.now());
        upgradePlan.setTktPrice(pele.getTktPrice());
        upgradePlan = savePlanPaxValidate(upgradePlan, upgradeRuleLegList, pele, ele, cabinMap);
        if (upgradePlan != null) {
            upgradePlanService.save(upgradePlan);
            log.info("符合升舱旅客{}入库,航班信息{}-{}>>{}", upgradePlan.getPaxName(), upgradePlan.getFlightNo(), DateUtils.parseLocalDateToString(upgradePlan.getFlightDate(), DateUtils.YYYY_MM_DD),JSON.toJSONString(upgradePlan));
        }
    }


    private UpgradePlan savePlanPaxValidate( UpgradePlan upgradePlan,List<UpgradeLegVo> upgradeRuleLegList, UpgradePassengerVo pele, UpgradeFlightInfoVo ele,Map<String, List<Space_Discount>> cabinMap){
        Iterator<Map.Entry<String, List<Space_Discount>>> iterator = cabinMap.entrySet().iterator();
        Space_Discount spaceDiscount=null;
        boolean cradOk=false;
        lab:while (iterator.hasNext()) {
            Map.Entry<String, List<Space_Discount>> next = iterator.next();
            String key = next.getKey();
            List<Space_Discount> value = next.getValue();
            if (StringUtils.isNotEmpty(pele.getCardType()) && pele.getCardType().equals(key)) {
                cradOk=true;
                for (Space_Discount sp : value) {
                    if (sp.getSpace().equals(pele.getSubCabin())) {
                        spaceDiscount = sp;
                        break lab;
                    }
                }
            }
        }
        if( !cradOk){
            log.info("大客户升舱旅客卡级别不符--不新增-{}-{}-{}-{}--{}--{}--{}",pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst(),pele.getCardType(),pele.getSubCabin());
            return null;
        }
        //允许升舱航段 旅服自己维护的可升舱条件
        boolean up = false;
        for (UpgradeLegVo ruleLeg : upgradeRuleLegList) {
            if (ruleLeg.getOrgCityAirp().equals(pele.getOrgVc()) &&
                    ruleLeg.getDstCityAirp().equals(pele.getDstVc()) &&
                    ruleLeg.getSpace().contains(pele.getSubCabin())
            ) {
                up = true;
                break;
            }
        }
        //折扣价只对没有在旅服自己维护的可升舱条件中有效
        if (!up) {
           // if(ObjectUtils.isNotEmpty(pele.getOriginalTicketNumber()) || ObjectUtils.isEmpty(pele.getTktPrice())){
            if(ObjectUtils.isEmpty(pele.getTktPrice())){
                String clientId = CmdUtils.getCmdSource(CmdCons.UPGRADES);
                if (StringUtils.isNotEmpty(clientId)) {
                    try {
                        DetrCmdReturnBean detrCmdInfo = CmdUtils.getDetrCmdInfo(CmdCons.UPGRADES, upgradePlan.getTktNo(), clientId);
                        if (detrCmdInfo != null) {
                            BigDecimal decimal = new BigDecimal(detrCmdInfo.getPrice());
                            pele.setTktPrice(decimal);
                            upgradePlan.setTktPrice(decimal);
                        }else{
                            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                            log.info("新增黑屏获取旅客票价失败{}-{},旅客票价未知{}-{}-{}-{}-{}，不进升舱名单",CmdCons.UPGRADES, clientId,pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                            return upgradePlan;
                        }
                    } catch (Exception e) {
                        upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                        upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                        upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                        log.info("新增黑屏获取旅客票价异常{}-{},旅客票价未知{}-{}-{}-{}-{}，不进升舱名单",CmdCons.UPGRADES, clientId,pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                        return upgradePlan;
                    } finally {
                        boolean b = CmdUtils.releaseCmdSource(CmdCons.UPGRADES, clientId);
                        if(!b){
                            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                            log.info("资源释放失败{}-{},旅客票价未知{}-{}-{}-{}-{}，不进升舱名单",CmdCons.UPGRADES, clientId,pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                            return upgradePlan;
                        }
                    }
                }else{
                    upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                    upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                    log.info("新增黑屏获取旅客票价失败-clientId-{}-{}",pele.getPaxName(),pele.getPaxId());
                    return upgradePlan;
                }
            }
            if (spaceDiscount !=null && !verifyTicketPrice(pele, ele.getPrice(),spaceDiscount.getDiscount())) {  //折扣价
                log.info("票面金额小于最低折扣,旅客ID:{}", pele.getPaxId());
                upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                return upgradePlan;
            }
        }
        if(ObjectUtils.isEmpty(spaceDiscount)){
            log.info("舱位不匹配,旅客ID:{}", pele.getPaxId());
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_SPACE_TYPE);
            return upgradePlan;
        }


        //旅客黑名单
        if (StringUtils.isNotEmpty(pele.getBlStatus())) {//黑名单旅客
            LocalDateTime now = LocalDateTime.now();
            //状态：1永久，2临时
            if (pele.getBlStatus().equals("1")) {
                upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                return upgradePlan;
            } else if (pele.getBlStatus().equals("2")) {
                if (now.isAfter(pele.getBlStartDt()) && pele.getBlEndDt().isAfter(now)) {
                    upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                    upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                    upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                    return upgradePlan;
                }
            }
        }


        List<UpgradeLfBlack> lfBlackList = upgradeLfBlackService.lambdaQuery()
                .eq(UpgradeLfBlack::getStatus, "1")
                .list();
        //旅客协议黑名单
        for (UpgradeLfBlack elelf:lfBlackList){
            if(StringUtils.isNotEmpty(pele.getLfNumber()) && pele.getLfNumber().contains(elelf.getLfNum())){
                upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                return upgradePlan;
            }
        }
        //是否携带儿童
        PaxInfoDto paxInfoDto = new PaxInfoDto();
        paxInfoDto.setFlightNo(pele.getFlightNo());
        paxInfoDto.setLclDptDate(pele.getFlightDate());
        paxInfoDto.setTktNo(pele.getTktNo());
        if (upgradeVerifyService.verifyCarryChild(paxInfoDto)) {
            log.info("携带儿童,不做升舱,旅客ID:{}", pele.getPaxId());
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_CHILDREN);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setIsChild(UpgradeEnum.YES);
            return upgradePlan;
        }

        //值机旅客
        if("AC".equals(pele.getCheckStatus())){
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setCheckStatus(pele.getCheckStatus());
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_AC);
            return upgradePlan;
        }
        if ("1".equals(pele.getIsGP())) {//是否GP
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_YES);
            upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_GP);
            upgradePlan.setUpState(UpgradeEnum.UPDATE_STATE_TOUP);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_GP);
            return upgradePlan;
        }

        if (pele.getSpecialService() != null) {//是否特服
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_YES);
            upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_SPECIAL);
            upgradePlan.setUpState(UpgradeEnum.UPDATE_STATE_TOUP);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_SPECIAL);
            return upgradePlan;
        }

        return upgradePlan;
    }


    private void updatePlanPaxValidate( UpgradePlan upgradePlan,List<UpgradeLegVo> upgradeRuleLegList, UpgradePassengerVo pele, UpgradeFlightInfoVo ele,Map<String, List<Space_Discount>> cabinMap){
        Iterator<Map.Entry<String, List<Space_Discount>>> iterator = cabinMap.entrySet().iterator();
        Space_Discount spaceDiscount=null;
        boolean cradOk=false;
        lab:while (iterator.hasNext()) {
            Map.Entry<String, List<Space_Discount>> next = iterator.next();
            String key = next.getKey();
            List<Space_Discount> value = next.getValue();
            if (StringUtils.isNotEmpty(pele.getCardType()) && pele.getCardType().equals(key)) {
                cradOk=true;
                for (Space_Discount sp : value) {
                    if (sp.getSpace().equals(pele.getSubCabin())) {
                        spaceDiscount = sp;
                        break lab;
                    }
                }
            }
        }

        upgradePlan.setSubCabin(pele.getSubCabin());
        upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_YES);
        upgradePlan.setUpState(UpgradeEnum.UPDATE_STATE_TOUP);
        upgradePlan.setPaxType(null);
        upgradePlan.setUpHistoryReason(null);
        //一旦手动填入新PNR  不在更新旧pnr
        if(StringUtils.isEmpty(upgradePlan.getNewPnr())){
            upgradePlan.setPsgPnr(pele.getPnr());
        }


        //取消旅客
        if ("Y".equals(pele.getIsCancel())) {
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setCancelStyle("0");
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_PAX_CANCEL);
            log.info("符合升舱旅客更新{}已取消",pele.getPaxName());
            return;
        }
        //允许升舱航段 旅服自己维护的可升舱条件
        boolean up = false;
        for (UpgradeLegVo ruleLeg : upgradeRuleLegList) {
            if (ruleLeg.getOrgCityAirp().equals(pele.getOrgVc()) &&
                    ruleLeg.getDstCityAirp().equals(pele.getDstVc()) &&
                    ruleLeg.getSpace().contains(pele.getSubCabin())
            ) {
                up = true;
                break;
            }
        }

        //折扣价只对没有在旅服自己维护的可升舱条件中有效
        if (!up) {
            //if(ObjectUtils.isNotEmpty(pele.getOriginalTicketNumber()) || ObjectUtils.isEmpty(pele.getTktPrice())){
            if(ObjectUtils.isEmpty(pele.getTktPrice())){
                String clientId = CmdUtils.getCmdSource(CmdCons.UPGRADES);
                if (StringUtils.isNotEmpty(clientId)) {
                    try {
                        DetrCmdReturnBean detrCmdInfo = CmdUtils.getDetrCmdInfo(CmdCons.UPGRADES, upgradePlan.getTktNo(), clientId);
                        if (detrCmdInfo != null) {
                            BigDecimal decimal = new BigDecimal(detrCmdInfo.getPrice());
                            pele.setTktPrice(decimal);
                            upgradePlan.setTktPrice(decimal);
                        }else{
                            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                            log.info("新增黑屏获取旅客票价失败{}-{},旅客票价未知{}-{}-{}-{}-{}，不做修改",CmdCons.UPGRADES, clientId,pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                            return ;
                        }
                    } catch (Exception e) {
                        upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                        upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                        upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                        log.info("新增黑屏获取旅客票价异常{}-{},旅客票价未知{}-{}-{}-{}-{}，不做修改",CmdCons.UPGRADES, clientId,pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                        return ;
                    } finally {
                        boolean b = CmdUtils.releaseCmdSource(CmdCons.UPGRADES, clientId);
                        if(!b){
                            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                            log.info("资源释放失败{}-{},旅客票价未知{}-{}-{}-{}-{}，不做修改",CmdCons.UPGRADES, clientId,pele.getPaxName(),ele.getFlightNumber(),DateUtils.parseLocalDateToString(ele.getFlightDate(),DateUtils.YYYY_MM_DD),ele.getOrg(),ele.getDst());
                            return ;
                        }
                    }
                }else{
                    upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                    upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                    log.info("修改黑屏获取旅客票价失败-clientId-{}-{}",pele.getPaxName(),pele.getPaxId());
                    return ;
                }
            }
            //折扣价
            if (spaceDiscount !=null && !verifyTicketPrice(pele, ele.getPrice(),spaceDiscount.getDiscount())) {
                upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                log.info("符合升舱旅客更新{}票面金额小于最低折扣",pele.getPaxName());
                return;
            }
        }




        //卡级别不符合
        if(!cradOk){
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_CRAD_TYPE);
            log.info("符合升舱旅客更新{}卡级别不符合{}",pele.getPaxName(),pele.getCardType());
            return;
        }
        //舱位不符合
        if(ObjectUtils.isEmpty(spaceDiscount)){
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_SPACE_TYPE);
            log.info("符合升舱旅客更新{}舱位不符合{}",pele.getPaxName(),pele.getSubCabin());
            return;
        }



        //旅客黑名单
        if (StringUtils.isNotEmpty(pele.getBlStatus())) {//黑名单旅客
            LocalDateTime now = LocalDateTime.now();
            //状态：1永久，2临时
            if (pele.getBlStatus().equals("1")) {
                upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                log.info("符合升舱旅客更新{}黑名单",pele.getPaxName());
                return;
            } else if (pele.getBlStatus().equals("2")) {
                if (now.isAfter(pele.getBlStartDt()) && pele.getBlEndDt().isAfter(now)) {
                    upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                    upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                    upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                    log.info("符合升舱旅客更新{}黑名单",pele.getPaxName());
                    return;
                }
            }
        }

        List<UpgradeLfBlack> lfBlackList = upgradeLfBlackService.lambdaQuery()
                .eq(UpgradeLfBlack::getStatus, "1")
                .list();
        //旅客协议黑名单
        for (UpgradeLfBlack elelf:lfBlackList){
            if(StringUtils.isNotEmpty(pele.getLfNumber()) && pele.getLfNumber().contains(elelf.getLfNum().toUpperCase())){
                upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
                upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                log.info("符合升舱旅客更新{}协议黑名单",pele.getPaxName());
                return;
            }
        }

        //是否携带儿童
        PaxInfoDto paxInfoDto = new PaxInfoDto();
        paxInfoDto.setFlightNo(pele.getFlightNo());
        paxInfoDto.setLclDptDate(pele.getFlightDate());
        paxInfoDto.setTktNo(pele.getTktNo());
        if (upgradeVerifyService.verifyCarryChild(paxInfoDto)) {
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_CHILDREN);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setIsChild(UpgradeEnum.YES);
            log.info("符合升舱旅客更新{}携带儿童",pele.getPaxName());
            return;
        }
        //值机旅客
        if("AC".equals(pele.getCheckStatus())){
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
            upgradePlan.setUpState(UpgradeEnum.NULL_VALUE);
            upgradePlan.setCheckStatus(pele.getCheckStatus());
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_AC);
            log.info("符合升舱旅客更新{}已值机",pele.getPaxName());
            return;
        }
        //是否GP
        if ("1".equals(pele.getIsGP())) {
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_YES);
            upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_GP);
            upgradePlan.setUpState(UpgradeEnum.UPDATE_STATE_TOUP);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_GP);
            log.info("符合升舱旅客更新{}GP旅客",pele.getPaxName());
            return;
        }

        //是否特服
        if (pele.getSpecialService() != null) {
            upgradePlan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_YES);
            upgradePlan.setPaxType(UpgradeEnum.PAX_TYPE_SPECIAL);
            upgradePlan.setUpState(UpgradeEnum.UPDATE_STATE_TOUP);
            upgradePlan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_SPECIAL);
            log.info("符合升舱旅客更新{}特服旅客",pele.getPaxName());
            return;
        }

    }



    /**
     * 执行航班升舱
     */
    @Async("upgradeExecutor")
    public void upgardeFlight(UpgradeFlightPlan upFlightPlan, UpgradeRuleTime upgradeRuleTime, CountDownLatch latch) {
        try {
            String org = upFlightPlan.getOrg();
            String dst = upFlightPlan.getDst();
            boolean pass = true;
            if (ObjectUtils.isNotEmpty(segment) && ObjectUtils.isNotEmpty(flightNo)) {
                if (!segment.toUpperCase().contains(org.toUpperCase() + "-" + dst.toUpperCase())) {
                    pass = false;
                }
                if (!flightNo.toUpperCase().contains(upFlightPlan.getFlightNo())) {
                    pass = false;
                }
            } else if (ObjectUtils.isNotEmpty(segment) && (!segment.toUpperCase().contains(org.toUpperCase() + "-" + dst.toUpperCase()))) {
                pass = false;
            } else if (ObjectUtils.isNotEmpty(flightNo) && (!flightNo.toUpperCase().contains(upFlightPlan.getFlightNo()))) {
                pass = false;
            }
            if (!pass) {
                log.info("执行升舱白名单拦截--{}>>{}", upFlightPlan.getFlightNo(), org + "-" + dst);
                return;
            }
            log.info("执行升舱--{}>>{}", upFlightPlan.getFlightNo(), org + "-" + dst);
            //是否可以执行升舱 时间到了
            FlightInfoDto dto = new FlightInfoDto();
            dto.setFlightNo(upFlightPlan.getFlightNo());
            dto.setDstCityAirp(upFlightPlan.getDst());
            dto.setOrgCityAirp(upFlightPlan.getOrg());
            dto.setLclDptDate(upFlightPlan.getFlightDate().atStartOfDay());
            FlightUpgradeLog lastUpgradeLog = flightUpgradeLogMapper.findLastUpgradeLog(dto);
            boolean stdBeforeTen = UpgradeEnum.YES.equals(upFlightPlan.getStdBeforeTen()) ? true : false;
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime std = upFlightPlan.getDepartureTime();
            int newUpgradeNum = 0;
            String nowStr = DateUtils.parseLocalDateTimeToString(now, DateUtils.YYYY_MM_DD_HH_MM_SS);
            String stds = DateUtils.parseLocalDateTimeToString(std, DateUtils.YYYY_MM_DD_HH_MM_SS);
            String flightDateStr = DateUtils.parseLocalDateToString(upFlightPlan.getFlightDate(), DateUtils.YYYY_MM_DD);
            //过了计划起飞时间
            if (now.isAfter(std)) {
                log.info("特殊航班{}-计划起飞时间{}-当前时间{}已过计划起飞时间不做升舱", upFlightPlan.getFlightNo(), stds, nowStr);
                return;
            }
            if (lastUpgradeLog != null) {
                int flightUpgradeNum = lastUpgradeLog.getFlightUpgradeNum();
                int minutes = 0;
                if (stdBeforeTen) {
                    switch (flightUpgradeNum) {
                        case 1:
                            minutes = upgradeRuleTime.getSpecialSecondUpgradeMinutes();
                            newUpgradeNum = 2;
                            break;
                        case 2:
                            minutes = upgradeRuleTime.getSpecialThirdUpgradeMinutes();
                            newUpgradeNum = 3;
                            break;
                        case 3:
                            minutes = upgradeRuleTime.getSpecialFourthUpgradeMinutes();
                            newUpgradeNum = 4;
                            break;
                        default:
                            return;
                    }
                    LocalDateTime dateTime = std.minusMinutes(minutes);
                    if (now.isBefore(dateTime)) {
                        log.info("特殊航班{}-当前时间{}第{}次升舱时间{}未到不做升舱", upFlightPlan.getFlightNo(), nowStr, newUpgradeNum, DateUtils.parseLocalDateTimeToString(dateTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
                        return;
                    }

                } else {
                    switch (flightUpgradeNum) {
                        case 1:
                            minutes = upgradeRuleTime.getNormalSecondUpgradeMinutes();
                            newUpgradeNum = 2;
                            break;
                        case 2:
                            minutes = upgradeRuleTime.getNormalThirdUpgradeMinutes();
                            newUpgradeNum = 3;
                            break;
                        default:
                            return;
                    }
                    LocalDateTime dateTime = std.minusMinutes(minutes);
                    if (now.isBefore(dateTime)) {
                        log.info("普通航班{}-当前时间{}第{}次升舱时间{}未到不做升舱", upFlightPlan.getFlightNo(), nowStr, newUpgradeNum, DateUtils.parseLocalDateTimeToString(dateTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
                        return;
                    }
                }

            } else {
                if (stdBeforeTen) {
                    int hour = upgradeRuleTime.getSpecialFirstUpgradeHour();
                    int minute = upgradeRuleTime.getSpecialFirstUpgradeMinute();
                    //起飞时间前一天指定时间执行首次升舱
                    LocalDateTime parse = LocalDateTime.parse(DateUtils.parseLocalDateTimeToString(std, DateUtils.YYYY_MM_DD) + " " + (hour < 10 ? "0" : "") + hour + ":" + (minute < 10 ? "0" : "") + minute + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime firstUpgrade = parse.minusDays(1);//行首次升舱时间
                    String firstUpgradeDateTime = DateUtils.parseLocalDateTimeToString(firstUpgrade, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    LocalDateTime twoUpgrade = std.minusHours(upgradeRuleTime.getSpecialSecondUpgradeMinutes());
                    String twoUpgradeDateTime = DateUtils.parseLocalDateTimeToString(twoUpgrade, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    log.info("特殊航班{}-起飞时间{}-首次升舱时间{}-当前时间{}-设置的第二次升舱时间{}", upFlightPlan.getFlightNo(), stds, firstUpgradeDateTime, nowStr, twoUpgradeDateTime);

                    //未到首次升舱时间
                    if (now.isBefore(firstUpgrade)) {//时间未到不做升舱
                        log.info("特殊航班{}-当前时间{}首次升舱时间未到不做升舱", upFlightPlan.getFlightNo(), nowStr);
                        return;
                    }
                    newUpgradeNum = 1;
                } else {
                    int minutes = upgradeRuleTime.getNormalFirstUpgradeMinutes();
                    LocalDateTime firstUpgrade = std.minusMinutes(minutes);
                    String firstUpgradeDateTime = DateUtils.parseLocalDateTimeToString(firstUpgrade, DateUtils.YYYY_MM_DD_HH_MM_SS);
                    //未到首次升舱时间
                    if (now.isBefore(firstUpgrade)) {
                        log.info("普通航班{}-当前时间{}首次升舱时间{}未到不做升舱", upFlightPlan.getFlightNo(), nowStr, firstUpgradeDateTime);
                        return;
                    }
                    newUpgradeNum = 1;
                }
            }
            FlightUpgradeLog uPlog = new FlightUpgradeLog();
            uPlog.setCreateTime(now);
            uPlog.setFlightNo(upFlightPlan.getFlightNo());
            uPlog.setFlightDate(upFlightPlan.getFlightDate());
            uPlog.setDstVc(upFlightPlan.getDst());
            uPlog.setOrgVc(upFlightPlan.getOrg());
            uPlog.setFlightUpgradeNum(newUpgradeNum);
            flightUpgradeLogMapper.insert(uPlog);

            upFlightPlan.setLastUpgradeLog(uPlog.getId());

            //开始升舱
            upFlightPlan.setStatus(UpgradeEnum.UP_FLIGHT_STATE_START);
            upgradeFlightPlanService.saveOrUpdate(upFlightPlan);
            log.info("航班{}-{},开始第{}次升舱",flightDateStr, upFlightPlan.getFlightNo(), newUpgradeNum);
            List<UpgradePlan> upgradeplan = planMapper.executeUpgradePlanList(DateUtils.formatLocalDate(upFlightPlan.getFlightDate(), DateUtils.YYYY_MM_DD),
                    upFlightPlan.getFlightNo(),
                    upFlightPlan.getOrg(),
                    upFlightPlan.getDst());
            if (upgradeplan.size() < 1) {
                log.info("航班{}-{}第{}次升舱,没有可自动升舱旅客", upFlightPlan.getFlightDate(), upFlightPlan.getFlightNo(),newUpgradeNum);
                upgradeFlightPlanService.lambdaUpdate()
                        .set(UpgradeFlightPlan::getAuthUpgrade,  UpgradeEnum.YES)
                        .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_STATE_END)
                        .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                        .update();
                return;
            }
            int first = 0;


            String endDate = DateUtils.parseLocalDateTimeToString(now.plusDays(1L), DateUtils.YYYY_MM_DD_HH_MM_SS);
            List<UpgradeFlightInfoVo> upgradeFightInfo = fltFlightRealInfoService.getUpgradeFightInfo(nowStr, endDate, flightDateStr, upFlightPlan.getFlightNo(), upFlightPlan.getOrg(), upFlightPlan.getDst());
            if (upgradeFightInfo.size() < 1) {
                log.info("航班第{}次升舱航班状态校验未查到航班信息{}-{}", newUpgradeNum,upFlightPlan.getFlightDate(), upFlightPlan.getFlightNo());
                upgradeFlightPlanService.lambdaUpdate()
                        .set(UpgradeFlightPlan::getAuthUpgrade,  UpgradeEnum.YES)
                        .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_STATE_END)
                        .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                        .update();
                return;
            }
            boolean flightCancel = false;
            boolean flightBlack = false;
            for (UpgradeFlightInfoVo ele : upgradeFightInfo) {
                if ("C".equals(ele.getFlightState())) {
                    flightCancel = true;
                }
                if (ele.getBlack()) {
                    flightBlack = true;
                }
            }

            //航班已取消
            if (flightCancel) {
                log.info("航班状态校验航班已取消{}-{}", upFlightPlan.getFlightDate(), upFlightPlan.getFlightNo());
                upgradeFlightPlanService.lambdaUpdate()
                        .set(UpgradeFlightPlan::getAuthUpgrade, UpgradeEnum.NO)
                        .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_NOT)
                        .set(UpgradeFlightPlan::getFlightCancel, UpgradeEnum.YES)
                        .set(UpgradeFlightPlan::getExplainInfo, "航班取消")
                        .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                        .update();
                return;
            }
            //航班在黑名单
            if (flightBlack) {
                log.info("航班状态校验航班已是黑名单航班{}-{}", upFlightPlan.getFlightDate(), upFlightPlan.getFlightNo());
                upgradeFlightPlanService.lambdaUpdate()
                        .set(UpgradeFlightPlan::getAuthUpgrade, UpgradeEnum.NO)
                        .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_NOT)
                        .set(UpgradeFlightPlan::getFlightCancel, UpgradeEnum.YES)
                        .set(UpgradeFlightPlan::getExplainInfo, "黑名单航班")
                        .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                        .update();
                return;
            }

            String fltDateStr = DateUtils.parseLocalDateToString(upFlightPlan.getFlightDate(), DateUtils.YYYY_MM_DD);

            List<UpgradeLfBlack> lfBlackList = upgradeLfBlackService.lambdaQuery()
                    .eq(UpgradeLfBlack::getStatus, "1")
                    .list();

           planLab: for (UpgradePlan plan : upgradeplan) {
               if (first > 0) {
                   UpgradeFlightPlan one = upgradeFlightPlanService.lambdaQuery()
                           .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                           .eq(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_STATE_START)
                           .eq(UpgradeFlightPlan::getAuthUpgrade, UpgradeEnum.YES)
                           .one();
                   if (one == null) {
                       log.info("-----" + DateUtils.parseLocalDateToString(plan.getFlightDate(), DateUtils.YYYY_MM_DD) + " " + upFlightPlan.getFlightNo() + " 不允许自动升舱");
                       return;
                   }
               }

               String clientId = CmdUtils.getCmdSource(CmdCons.UPGRADES);
               boolean b1 = updateFilghtStatus(upFlightPlan, clientId,plan);
               if(!b1){
                   boolean b = CmdUtils.releaseCmdSource(CmdCons.UPGRADES, clientId);
                   if(!b){
                       log.info("资源释放失败{}-{}>>{}-{}",CmdCons.UPGRADES, clientId,upFlightPlan.getFlightNo(),fltDateStr);
                   }
                   upgradePlanService.updateById(plan);
                   upgradeFlightPlanService.updateById(upFlightPlan);
                   return;
               }
               boolean releas = CmdUtils.releaseCmdSource(CmdCons.UPGRADES, clientId);
               if(!releas){
                   log.info("资源释放失败{}-{}>>{}-{}",CmdCons.UPGRADES, clientId,upFlightPlan.getFlightNo(),fltDateStr);
                   upFlightPlan.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                   upFlightPlan.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
                   upFlightPlan.setExplainInfo("资源释放失败"+clientId);
                   plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                   plan.setUpHistoryReason("未知错误");
                   plan.setUpStateReason("未知错误");
                   upgradePlanService.updateById(plan);
                   upgradeFlightPlanService.updateById(upFlightPlan);
                   return;
               }

                //查询出可升舱旅客
                UpgradePassengerDto dtos = new UpgradePassengerDto();
                dtos.setOrg(plan.getOrgVc());
                dtos.setDst(plan.getDstVc());
                dtos.setFlightDate(flightDateStr);
                dtos.setFlightNo(plan.getFlightNo());
                dtos.setPaxId(plan.getPaxId());
                List<UpgradePassengerVo> fltPassenger = fltPassengerRealInfoService.getUpgradePassengerList(dtos);
                if(fltPassenger.size()<0){
                    log.info("旅客{}-{}-{}-{}-{}数据不存在,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                    continue;
                }
                UpgradePassengerVo passengerVo=fltPassenger.get(0);
                //取消旅客
                if ("Y".equals(passengerVo.getIsCancel())) {
                    plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    plan.setUpState(UpgradeEnum.NULL_VALUE);
                    plan.setCancelStyle("0");
                    plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_PAX_CANCEL);
                    upgradePlanService.updateById(plan);
                    log.info("旅客{}-{}-{}-{}-{}已取消,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                    continue;
                }



                //票价不符合旅客
                //航段允许升舱舱位
                UpgradeLegQueryDto legQueryDto = new UpgradeLegQueryDto();
                legQueryDto.setStatus("1");
                legQueryDto.setOrgCityAirp(plan.getOrgVc());
                legQueryDto.setDstCityAirp(plan.getDstVc());
                List<UpgradeLegVo> upgradeRuleLegList = upgradeRuleLegService.getList(legQueryDto);


                //允许升舱航段 旅服自己维护的可升舱条件
                boolean up = false;
                for (UpgradeLegVo ruleLeg : upgradeRuleLegList) {
                    if (ruleLeg.getOrgCityAirp().equals(passengerVo.getOrgVc()) &&
                            ruleLeg.getDstCityAirp().equals(passengerVo.getDstVc()) &&
                            ruleLeg.getSpace().contains(passengerVo.getSubCabin())
                    ) {
                        up = true;
                        break;
                    }
                }
                //Y舱及替代舱位允许升舱折扣
                List<UpgradeRuleDiscount> cardTypeSeat = upgradeRuleDiscountService.lambdaQuery().list();
                //查询所有升舱卡级别包含的舱位
                Map<String, List<Space_Discount>> cabinMap = new HashMap<>();
                cardTypeSeat.stream().forEach(e -> {
                    String[] cardType = e.getCardType().split(",");
                    String[] space = StringUtils.isEmpty(e.getSpace()) ? new String[]{} : e.getSpace().split(",");
                    for (String ct : cardType) {
                        if (cabinMap.get(ct) == null) {
                            List<Space_Discount> list = new ArrayList<>();
                            cabinMap.put(ct, list);
                        }
                        for (String sp : space) {
                            Space_Discount sd = new Space_Discount();
                            sd.setDiscount(e.getDiscount());
                            sd.setSpace(sp);
                            cabinMap.get(ct).add(sd);
                        }
                    }

                });
                Iterator<Map.Entry<String, List<Space_Discount>>> iterator = cabinMap.entrySet().iterator();
                Space_Discount spaceDiscount=null;
                boolean cradOk=false;
                lab:while (iterator.hasNext()) {
                    Map.Entry<String, List<Space_Discount>> next = iterator.next();
                    String key = next.getKey();
                    List<Space_Discount> value = next.getValue();
                    if (StringUtils.isNotEmpty(passengerVo.getCardType()) && passengerVo.getCardType().equals(key)) {
                        cradOk=true;
                        for (Space_Discount sp : value) {
                            if (sp.getSpace().equals(passengerVo.getSubCabin())) {
                                spaceDiscount = sp;
                                break lab;
                            }
                        }
                    }
                }
               //卡级别不符合旅客
                if(!cradOk){
                    plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    plan.setUpState(UpgradeEnum.NULL_VALUE);
                    plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_CRAD_TYPE);
                    upgradePlanService.updateById(plan);
                    log.info("旅客{}-{}-{}-{}-{}卡级别不符合,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                    continue;
                }
               //舱位不符合旅客
               if(ObjectUtils.isEmpty(spaceDiscount)){
                   plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                   plan.setUpState(UpgradeEnum.NULL_VALUE);
                   plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_SPACE_TYPE);
                   upgradePlanService.updateById(plan);
                   log.info("旅客{}-{}-{}-{}-{}舱位不匹配,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                   continue;
               }
                //折扣价只对没有在旅服自己维护的可升舱条件中有效
                if (!up) {
                    if(ObjectUtils.isEmpty(passengerVo.getTktPrice())){
                         clientId = CmdUtils.getCmdSource(CmdCons.UPGRADES);
                        if (StringUtils.isNotEmpty(clientId)) {
                            try {
                                DetrCmdReturnBean detrCmdInfo = CmdUtils.getDetrCmdInfo(CmdCons.UPGRADES, plan.getTktNo(), clientId);
                                if (detrCmdInfo != null) {
                                    BigDecimal decimal = new BigDecimal(detrCmdInfo.getPrice());
                                    passengerVo.setTktPrice(decimal);
                                    plan.setTktPrice(decimal);
                                }else{
                                    log.info("旅客{}-{}-{}-{}-{}携带clientId-{}执行detr指令获取票价失败,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst(),clientId);
                                    continue;
                                }
                            } catch (Exception e) {
                                log.info("旅客{}-{}-{}-{}-{}携带clientId-{}执行detr指令获取票价异常,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst(),clientId);
                                continue ;
                            } finally {
                                boolean b = CmdUtils.releaseCmdSource(CmdCons.UPGRADES, clientId);
                                if(!b){
                                    log.info("旅客{}-{}-{}-{}-{}归还资源clientId-{}失败,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst(),clientId);
                                    continue ;
                                }
                            }
                        }else{
                            log.info("旅客{}-{}-{}-{}-{}查询黑屏票价获取资源失败,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                            continue ;
                        }
                    }

                     if (spaceDiscount!=null &&!verifyTicketPrice(passengerVo, upFlightPlan.getY100Price(),spaceDiscount.getDiscount())) {  //折扣价
                        plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                        plan.setUpState(UpgradeEnum.NULL_VALUE);
                        plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_TKT_PRICE);
                        log.info("旅客{}-{}-{}-{}-{}票面金额小于最低折扣,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                        continue;
                    }
                }

                //旅客协议黑名单
                for (UpgradeLfBlack elelf:lfBlackList){
                    if(StringUtils.isNotEmpty(passengerVo.getLfNumber()) && passengerVo.getLfNumber().contains(elelf.getLfNum().toUpperCase())){
                        plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                        plan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                        plan.setUpState(UpgradeEnum.NULL_VALUE);
                        plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                        upgradePlanService.updateById(plan);
                        log.info("旅客{}-{}-{}-{}-{}协议黑名单,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                        continue planLab;
                    }
                }


                //旅客黑名单
                if (StringUtils.isNotEmpty(passengerVo.getBlStatus())) {//黑名单旅客
                    //状态：1永久，2临时
                    if (passengerVo.getBlStatus().equals("1")) {
                        plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                        plan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                        plan.setUpState(UpgradeEnum.NULL_VALUE);
                        plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                        upgradePlanService.updateById(plan);
                        log.info("旅客{}-{}-{}-{}-{}黑名单,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                        continue ;
                    } else if (passengerVo.getBlStatus().equals("2")) {
                        if (now.isAfter(passengerVo.getBlStartDt()) && passengerVo.getBlEndDt().isAfter(now)) {
                            plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                            plan.setPaxType(UpgradeEnum.PAX_TYPE_BLACK);
                            plan.setUpState(UpgradeEnum.NULL_VALUE);
                            plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_BLACK);
                            upgradePlanService.updateById(plan);
                            log.info("旅客{}-{}-{}-{}-{}黑名单,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                            continue ;
                        }
                    }
                }
               //是否携带儿童
               PaxInfoDto paxInfoDto = new PaxInfoDto();
               paxInfoDto.setFlightNo(passengerVo.getFlightNo());
               paxInfoDto.setLclDptDate(passengerVo.getFlightDate());
               paxInfoDto.setTktNo(passengerVo.getTktNo());
               if (upgradeVerifyService.verifyCarryChild(paxInfoDto)) {
                   plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                   plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_CHILDREN);
                   plan.setUpState(UpgradeEnum.NULL_VALUE);
                   plan.setIsChild(UpgradeEnum.YES);
                   upgradePlanService.updateById(plan);
                   log.info("旅客{}-{}-{}-{}-{}携带儿童,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                   continue ;
               }
                //值机旅客
                if("AC".equals(passengerVo.getCheckStatus())){
                    plan.setCanUpdateBool(UpgradeEnum.CAN_UPDATE_NO);
                    plan.setUpState(UpgradeEnum.NULL_VALUE);
                    plan.setCheckStatus(passengerVo.getCheckStatus());
                    plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_AC);
                    upgradePlanService.updateById(plan);
                    log.info("旅客{}-{}-{}-{}-{}已值机,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                    continue;
                }

                first++;
                //GP旅客
                if (UpgradeEnum.STOP_FLIGHT_UPGRADE_GP.equals(plan.getPaxType())) {
                    upgradeFlightPlanService.lambdaUpdate()
                            .set(UpgradeFlightPlan::getAuthUpgrade, UpgradeEnum.NO)
                            .set(UpgradeFlightPlan::getStopUpgrade, UpgradeEnum.STOP_FLIGHT_UPGRADE_GP)
                            .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_STATE_SUSPEND)
                            .set(UpgradeFlightPlan::getExplainInfo, UpgradeEnum.NOT_UPGRADE_REASON_GP)
                            .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                            .update();
                    upgradePlanService.lambdaUpdate().eq(UpgradePlan::getId, plan.getId())
                            .set(UpgradePlan::getCanUpdateBool, UpgradeEnum.CAN_UPDATE_YES)
                            .set(UpgradePlan::getPause, UpgradeEnum.PAUSE)
                            .set(UpgradePlan::getPaxType, UpgradeEnum.PAX_TYPE_GP)
                            .set(UpgradePlan::getUpHistoryReason, UpgradeEnum.NOT_UPGRADE_REASON_GP)
                            .update();

                    log.info("旅客{}-{}-{}-{}-{}是GP旅客,不执行升舱",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                    continue;
                }
                //特服旅客
                if (UpgradeEnum.PAX_TYPE_SPECIAL.equals(plan.getPaxType())) {
                    upgradeFlightPlanService.lambdaUpdate()
                            .set(UpgradeFlightPlan::getAuthUpgrade, UpgradeEnum.NO)
                            .set(UpgradeFlightPlan::getStopUpgrade, UpgradeEnum.STOP_FLIGHT_UPGRADE_SPECIAL)
                            .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_STATE_SUSPEND)
                            .set(UpgradeFlightPlan::getExplainInfo, UpgradeEnum.NOT_UPGRADE_REASON_SPECIAL)
                            .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                            .update();

                    upgradePlanService.lambdaUpdate().eq(UpgradePlan::getId, plan.getId())
                            .set(UpgradePlan::getCanUpdateBool, UpgradeEnum.CAN_UPDATE_YES)
                            .set(UpgradePlan::getPause, UpgradeEnum.PAUSE)
                            .set(UpgradePlan::getPaxType, UpgradeEnum.PAX_TYPE_SPECIAL)
                            .set(UpgradePlan::getUpHistoryReason, UpgradeEnum.NOT_UPGRADE_REASON_SPECIAL)
                            .update();
                    log.info("旅客{}-{}-{}-{}-{}是特服旅客,不执行升舱 ",plan.getPaxName(),upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst());
                    return;
                }

                RLock lock = null;
                try {
                    clientId = CmdUtils.getCmdSource(CmdCons.UPGRADES);
                    if (StringUtils.isEmpty(clientId)) {
                        upgradeFlightPlanService.lambdaUpdate()
                                .set(UpgradeFlightPlan::getStatus, UpgradeEnum.UP_FLIGHT_STATE_END)
                                .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                                .update();
                        log.info("航班{}-{}-{}-{}第{}次升舱旅客{}资源获取失败", upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst(),newUpgradeNum,plan.getPaxName());
                        return;
                    }

                    //旅客升舱加锁
                    lock = redisson.getLock(UpgradeConstant.UPGRADE_PAX + plan.getId());
                    if (!lock.tryLock()) {
                        log.info("旅客{}-{},已在其他线程升舱",plan.getPaxName(),plan.getId());
                        break;
                    }
                    log.info("航班{}-{}-{}-{}旅客{}携带clientId-{}执行升舱", upFlightPlan.getFlightNo(), flightDateStr, upFlightPlan.getOrg(), upFlightPlan.getDst(),plan.getPaxName(),clientId);
                    upgradeExecute.up(upFlightPlan, plan, "P", CmdCons.UPGRADES,clientId);
                } catch (UpgrardeException e) {
                    log.info("-----{}-{}升舱失败:-{}" ,plan.getPaxName(), clientId,plan.getUpHistoryReason(), e.getMessage(), e);
                    plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                    plan.setUpStateReason(plan.getUpHistoryReason());
                    upFlightPlan.setAuthUpgrade(UpgradeEnum.NO);
                    upFlightPlan.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
                    upFlightPlan.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                    upFlightPlan.setExplainInfo(UpgradeEnum.NOT_UPGRADE_REASON_SYSTEM);
                } catch (Exception e) {
                    log.info("-----{}-{}升舱失败:未知错误{}",plan.getPaxName(), clientId, e.getMessage(), e);
                    plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                    plan.setUpHistoryReason("未知错误");
                    plan.setUpStateReason("未知错误");
                    upFlightPlan.setAuthUpgrade(UpgradeEnum.NO);
                    upFlightPlan.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
                    upFlightPlan.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                    upFlightPlan.setExplainInfo(UpgradeEnum.NOT_UPGRADE_REASON_SYSTEM);
                } finally {
                    //立刻归还资源
                    boolean b = CmdUtils.releaseCmdSource(CmdCons.UPGRADES, clientId);
                    if(!b){
                        log.info("资源释放失败{}-{}-{}-{}",plan.getPaxName(),plan.getId(),CmdCons.UPGRADES,clientId);
                    }
                    if(UpgradeEnum.UPDATE_STATE_SUCCESS.equals(plan.getUpState())){
                        log.info("{}-{}-自动升舱成功",plan.getPaxName(),clientId);
                        plan.setUpdateTime(LocalDateTime.now());
                    }
                    upgradeFlightPlanService.updateById(upFlightPlan);
                    upgradePlanService.saveOrUpdate(plan);
                    if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
            upgradeFlightPlanService.lambdaUpdate()
                    .set(UpgradeFlightPlan::getStatus,  UpgradeEnum.UP_FLIGHT_STATE_END )
                    .eq(UpgradeFlightPlan::getId, upFlightPlan.getId())
                    .update();
        } catch (Exception e) {
            log.error("大客服升舱异常", e.getMessage(), e);
        } finally {
            latch.countDown();
        }
    }

    private boolean updateFilghtStatus(UpgradeFlightPlan ele,String tempClientId,UpgradePlan plan){
            log.info("{}携带{}判断航班座位情况",ele.getFlightNo(),tempClientId);
            DateTimeFormatter fmtss = DateTimeFormatter.ofPattern("ddMMM", Locale.ENGLISH);
            LocalDateTime flightDate = ele.getFlightDate().atStartOfDay();
            String cmdFlightDate = flightDate.format(fmtss);
        try {
            AvCmdReturnBean avCmdReturnBean = CmdUtils.getAvFligtSeatNumInfo(CmdCons.UPGRADES, ele.getFlightNo(), ele.getOrg(), ele.getDst(), cmdFlightDate, tempClientId);
            if (StringUtils.isEmpty(avCmdReturnBean.getSourceStr())) {
                log.info("{}-{}-{}-{} AV指令调用失败", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
                ele.setExplainInfo("Av指令调用失败");
                plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                plan.setUpHistoryReason("未知错误");
                plan.setUpStateReason("未知错误");
                return false;
            }

            if (avCmdReturnBean.getSeats() == null || avCmdReturnBean.getSeats().size()==0) {
                ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
                ele.setExplainInfo("Av指令调用失败");
                log.info("{}-{}-{}-{} AV指令调用失败", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                plan.setUpHistoryReason("未知错误");
                plan.setUpStateReason("未知错误");
                return false;
            }
            //不存在P舱不升舱
            if(ObjectUtils.isEmpty(avCmdReturnBean.getSeats().get("P"))){
                log.info("{}-{}-{}-{} AV指令显示P舱不存在, 停止升舱", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                return false;
            }
            //【C】：调用AV指令，判断P舱无座
            if (avCmdReturnBean.getSeats().get("P") < 1) {
                //【C】：调用AV指令，判断P舱无座，但C舱有座而暂停升舱
                if (ObjectUtils.isNotEmpty(avCmdReturnBean.getSeats().get("C")) && avCmdReturnBean.getSeats().get("C") > 0) {
                    ele.setAuthUpgrade(UpgradeEnum.NO);
                    ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_C);
                    ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                    ele.setExplainInfo(UpgradeEnum.NOT_UPGRADE_REASON_C);
                    log.info("{}-{}-{}-{} P舱无座，但C舱有座 停止升舱", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                    plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                    plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_C);
                    plan.setUpStateReason(UpgradeEnum.NOT_UPGRADE_REASON_C);
                    return false;
                }else{
                    String fltDateStr = DateUtils.parseLocalDateToString(ele.getFlightDate(), DateUtils.YYYY_MM_DD);
                    OrBean bean =  RoInterfaceUtil.handelRoInterFace(ele.getFlightNo(), fltDateStr, ele.getOrg(), ele.getDst());
                    if (!"1".equals(bean.getCode())) {
                        ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                        ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
                        log.info("{}-{}-{}-{} Ro接口调用失败", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                        plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                        plan.setUpHistoryReason("未知错误");
                        plan.setUpStateReason("未知错误");
                        return false;
                    }
                    String yns = bean.getSeatList().get("Y");
                    String cns = bean.getSeatList().get("C");
                    Integer cNumber = new Integer(cns==null?"0":cns);
                    Integer yNumber = new Integer(yns==null?"0":yns);
                    if(cNumber==0){
                        ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                        ele.setFirstClassNotExist(UpgradeEnum.YES);
                        ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_P);
                        log.info("{}-{}-{}-{} P舱C舱都无座", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                        plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                        plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_P);
                        plan.setUpStateReason(UpgradeEnum.NOT_UPGRADE_REASON_P);
                        return false;
                    }else if(cNumber>0 && yNumber<0){
                        ele.setAuthUpgrade(UpgradeEnum.NO);
                        ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_MIXED);
                        ele.setMixed(UpgradeEnum.YES);
                        ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                        ele.setExplainInfo(UpgradeEnum.NOT_UPGRADE_REASON_ISMIX);
                        log.info("{}-{}-{}-{} 舱航班，暂停升舱", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                        plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                        plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_ISMIX);
                        plan.setUpStateReason(UpgradeEnum.NOT_UPGRADE_REASON_ISMIX);
                        return false;
                    }
                }
                ele.setAuthUpgrade(UpgradeEnum.NO);
                ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_P);
                ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
                ele.setExplainInfo(UpgradeEnum.STOP_FLIGHT_UPGRADE_P);
                log.info("{}-{}-{}-{} P舱无座【满】停止升舱", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId);
                plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
                plan.setUpHistoryReason(UpgradeEnum.NOT_UPGRADE_REASON_P);
                plan.setUpStateReason(UpgradeEnum.NOT_UPGRADE_REASON_P);
                return false;

            }
        } catch (Exception e) {
            log.info("{}-{}-{}-{} 航班状态更新任务执行发生错误{}", ele.getFlightNo(), cmdFlightDate,plan.getPaxName(),tempClientId,e.getMessage(),e);
            ele.setStatus(UpgradeEnum.UP_FLIGHT_STATE_SUSPEND);
            ele.setStopUpgrade(UpgradeEnum.STOP_FLIGHT_UPGRADE_SYSTEAM);
            plan.setUpState(UpgradeEnum.UPDATE_STATE_FAIL);
            plan.setUpHistoryReason("未知错误");
            plan.setUpStateReason("未知错误");
            return false;
        }
        return true;
    }



    private boolean verifyTicketPrice(UpgradePassengerVo pele, int y100Price,Double discount) {
        //-1表示不计算折扣 Y舱全部折扣
        if(new BigDecimal(discount.toString()).compareTo(new BigDecimal("-1"))==0){
            return true;
        } else if(new BigDecimal(discount.toString()).compareTo(new BigDecimal("11"))==0){//11表示全价
            return (new BigDecimal(y100Price).compareTo(pele.getTktPrice())==0);
        }
        //折扣价判断  按全价10折 换算
        BigDecimal discountBigDecimal = new BigDecimal(discount).multiply(new BigDecimal("0.1"));
        //获取旅客票面价
        BigDecimal seatBigDecimal = pele.getTktPrice();
        //运价
        BigDecimal Y100BigDecimal = new BigDecimal(y100Price);

        BigDecimal multiply = discountBigDecimal.multiply(Y100BigDecimal);
        if (seatBigDecimal.compareTo(multiply) < 0) {
            return false;
        }
        return true;
    }

    private class Space_Discount{
        private String space;
        private Double discount;

        public String getSpace() {
            return space;
        }

        public void setSpace(String space) {
            this.space = space;
        }

        public Double getDiscount() {
            return discount;
        }

        public void setDiscount(Double discount) {
            this.discount = discount;
        }
    }


}
