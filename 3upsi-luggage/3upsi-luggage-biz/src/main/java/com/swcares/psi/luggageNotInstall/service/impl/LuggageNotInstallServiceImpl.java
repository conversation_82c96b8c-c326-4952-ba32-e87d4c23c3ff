package com.swcares.psi.luggageNotInstall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.base.data.api.entity.FltFlightRealInfo;
import com.swcares.psi.base.data.service.CityCodeService;
import com.swcares.psi.base.data.service.FltFlightRealInfoService;
import com.swcares.psi.base.data.service.IEmployeeService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.luggageNotInstall.dto.LuggageNotInstallDto;
import com.swcares.psi.luggageNotInstall.dto.LuggageNotInstallPageDto;
import com.swcares.psi.luggageNotInstall.entity.LuggageNotInstall;
import com.swcares.psi.luggageNotInstall.entity.LuggageNotInstallClaim;
import com.swcares.psi.luggageNotInstall.entity.LuggageNotInstallEmployee;
import com.swcares.psi.luggageNotInstall.mapper.LuggageNotInstallMapper;
import com.swcares.psi.luggageNotInstall.service.LuggageNotInstallClaimService;
import com.swcares.psi.luggageNotInstall.service.LuggageNotInstallEmployeeService;
import com.swcares.psi.luggageNotInstall.service.LuggageNotInstallService;
import com.swcares.psi.luggageNotInstall.vo.LuggageNotInstallDetailInfoVo;
import com.swcares.psi.luggageNotInstall.vo.LuggageNotInstallPageVo;
import com.swcares.psi.message.api.form.MessageSendForm;
import com.swcares.psi.message.service.MessageService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created on  2022/3/23
 * Title:       [未装机行李管理]
 * Description: [未装机行李管理]
 *
 * @author: like
 * @version: 1.0
 */
@Service
public class LuggageNotInstallServiceImpl extends ServiceImpl<LuggageNotInstallMapper, LuggageNotInstall> implements LuggageNotInstallService {

    @Autowired
    private IEmployeeService employeeService;
    @Autowired
    private LuggageNotInstallEmployeeService luggageNotInstallEmployeeService;
    @Autowired
    private LuggageNotInstallClaimService luggageNotInstallClaimService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;
    @Autowired
    private CityCodeService cityCodeService;


    private final static String LUGGAGE_NOT_INSTALL_URL = "/banksearchSystem/noInstallBaggageManagement";


    @Override
    public Boolean saveLuggageNotInstall(LuggageNotInstallDto luggageNotInstallDto) {
        boolean result = false;
        String createUser = String.valueOf(AuthenticationUtil.getAuthentication().getPrincipal());
        String createTerminal = employeeService.getCity3CodeByTuNo(createUser);
        luggageNotInstallDto.setCreateUser(createUser);
        luggageNotInstallDto.setCreateTerminal(createTerminal);
        luggageNotInstallDto.setCreateTime(DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        luggageNotInstallDto.setIsDel(LuggageNotInstall.IS_DEL_NO);
        result = this.save(luggageNotInstallDto);
        //存入中间表
        List<LuggageNotInstallEmployee> employeeList = new ArrayList<>();
        if (StringUtils.isNotBlank(luggageNotInstallDto.getNoticeUser())) {
            Arrays.stream(luggageNotInstallDto.getNoticeUser().split(",")).forEach(noticeUserId -> {
                LuggageNotInstallEmployee luggageNotInstallEmployee = new LuggageNotInstallEmployee();
                luggageNotInstallEmployee.setLuggageNotInstallId(luggageNotInstallDto.getId());
                luggageNotInstallEmployee.setEmployeeId(noticeUserId);
                employeeList.add(luggageNotInstallEmployee);
            });
            result = luggageNotInstallEmployeeService.saveEmployeeList(employeeList);
        }
        if (luggageNotInstallDto.getPkgHandle().equals("速运") || luggageNotInstallDto.getPkgHandle().equals("入库")) {
            //获取航班信息
            FltFlightRealInfo flightRealInfo = fltFlightRealInfoService.getOne(Wrappers.<FltFlightRealInfo>lambdaQuery()
                    .eq(FltFlightRealInfo::getFlightNumber, luggageNotInstallDto.getFlightNo())
                    .eq(FltFlightRealInfo::getFlightDate, luggageNotInstallDto.getFlightDate())
                    .eq(FltFlightRealInfo::getDst, luggageNotInstallDto.getDst()));

            String flightSegment = "";
            String org = "";
            if (null != flightRealInfo) {
                String orgName = cityCodeService.findCityNameBy3Code(flightRealInfo.getOrg()).getCityChName();
                String dstName = cityCodeService.findCityNameBy3Code(flightRealInfo.getDst()).getCityChName();
                flightSegment = orgName + flightRealInfo.getOrg() + "-" + dstName + flightRealInfo.getDst();
                org = orgName + flightRealInfo.getOrg();
            }
            StringBuilder msgContent = new StringBuilder();
            //发送站内信
            if (luggageNotInstallDto.getPkgHandle().equals("速运")) {
                msgContent.append("<h3>航班号为：").append(luggageNotInstallDto.getFlightNo()).append(",").append(flightSegment).append(",")
                        .append(luggageNotInstallDto.getFlightDate()).append("的航班因(").append(luggageNotInstallDto.getReason()).append(")行李未同机到达。</h3>")
                        .append("<p>所定速运日期为：").append(luggageNotInstallDto.getExpressDate()).append("; </p>")
                        .append("<p>速运航班：").append(luggageNotInstallDto.getExpressFlight()).append("; </p>")
                        .append("<p>行李牌号：").append(luggageNotInstallDto.getPkgNo()).append(";  </p>")
                        .append("<p>行李类型：").append(luggageNotInstallDto.getPkgType()).append(";  </p>")
                        .append("<p>旅客姓名：").append(luggageNotInstallDto.getPaxName()).append(";</p>");
            }
            if (luggageNotInstallDto.getPkgHandle().equals("入库")) {
                msgContent.append("<h3>航班号为：").append(luggageNotInstallDto.getFlightNo()).append(",").append(flightSegment).append(",")
                        .append(luggageNotInstallDto.getFlightDate()).append("的航班因(").append(luggageNotInstallDto.getReason()).append(")行李未同机到达。</h3>")
                        .append("<p>已在：").append(org).append("入库。</p>")
                        .append("<p>行李牌号：").append(luggageNotInstallDto.getPkgNo()).append(";  </p>")
                        .append("<p>行李类型：").append(luggageNotInstallDto.getPkgType()).append(";  </p>")
                        .append("<p>旅客姓名：").append(luggageNotInstallDto.getPaxName()).append(";</p>");
            }
            MessageSendForm messageSendForm = new MessageSendForm();
            messageSendForm.setMsgType(2);
            messageSendForm.setMsgTypeName("未装机行李");
            messageSendForm.setMsgTitle("未装机行李处理");
            messageSendForm.setMsgContent(msgContent.toString());
            messageSendForm.setMsgDate(new Date());
            messageSendForm.setFlightDate(luggageNotInstallDto.getFlightDate());
            messageSendForm.setFlightNo(luggageNotInstallDto.getFlightNo());
            messageSendForm.setMsgUser("-1");
            messageSendForm.setMsgChildType("1");
            messageSendForm.setMsgChildTypeName("未装机行李处理");
            messageSendForm.setPcUrl(LUGGAGE_NOT_INSTALL_URL);
            messageSendForm.setMobileUrl(LUGGAGE_NOT_INSTALL_URL);
            messageSendForm.setIsAudit(0);
            if (StringUtils.isBlank(luggageNotInstallDto.getNoticeUser())) {
                List<Map<String, Object>> terminalUserList = getTerminalUserList();
                String[] userList = terminalUserList.stream().map(user -> String.valueOf(user.get("id"))).toArray(String[]::new);
                messageSendForm.setMsgReplyUser(userList);
            } else {
                messageSendForm.setMsgReplyUser(luggageNotInstallDto.getNoticeUser().split(","));
            }
            //开启单线程保存站内信
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        messageService.sendMsgToUser(messageSendForm);
                    } catch (Exception e) {
                        log.error("保存未装机行李站内信失败！", e);
                    }
                }
            }).start();
        }
        return result;
    }

    @Override
    public Boolean updateLuggageNotInstall(LuggageNotInstallDto luggageNotInstallDto) {
        String updateUser = String.valueOf(AuthenticationUtil.getAuthentication().getPrincipal());
        luggageNotInstallDto.setUpdateUser(updateUser);
        luggageNotInstallDto.setUpdateDate(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        return this.updateById(luggageNotInstallDto);
    }

    @Override
    public List<LuggageNotInstallPageVo> getLuggageNotInstallExport(LuggageNotInstallPageDto param) {
        if (StringUtils.isNotBlank(param.getPkgStatus())) {
            param.setPkgStatusList(Arrays.asList(param.getPkgStatus().split(",")));
        }
        return baseMapper.getLuggageNotInstallList(param);
    }

    @Override
    public Boolean deleteLuggageNotInstall(String id) {
        List<LuggageNotInstall> delParam = new ArrayList<>();
        Arrays.stream(id.split(",")).forEach(a -> {
            LuggageNotInstall luggageNotInstall = new LuggageNotInstall();
            luggageNotInstall.setId(a);
            luggageNotInstall.setIsDel(LuggageNotInstall.IS_DEL_YES);
            delParam.add(luggageNotInstall);
        });
        return updateBatchById(delParam);
    }

    @Override
    @EncryptMethod
    public Boolean luggageNotInstallClaim(LuggageNotInstallClaim param) {
        LuggageNotInstallClaim luggageNotInstallClaim = luggageNotInstallClaimService.getOne(Wrappers.<LuggageNotInstallClaim>lambdaQuery()
                .eq(LuggageNotInstallClaim::getLuggageNotInstallId, param.getLuggageNotInstallId()));
        if (null != luggageNotInstallClaim) {
            throw new BusinessException(MessageCode.LUGGAGE_NOT_INSTALL_CLAIM_EXSIST.getCode());
        }
        String createUser = String.valueOf(AuthenticationUtil.getAuthentication().getPrincipal());
        param.setClaimUser(createUser);
        param.setClaimTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        return luggageNotInstallClaimService.save(param);
    }

    @Override
    public LuggageNotInstallDetailInfoVo getLuggageNotInstallInfo(String id) {
        return baseMapper.getLuggageNotInstallInfo(id);
    }

    @Override
    public List<Map<String, Object>> getTerminalUserList() {
        String currentUser = String.valueOf(AuthenticationUtil.getAuthentication().getPrincipal());
        String cityCode = employeeService.getCity3CodeByTuNo(currentUser);
        return employeeService.getTerminalUserList(cityCode);
    }

    @Override
    public IPage<LuggageNotInstallPageVo> getLuggageNotInstallPage(LuggageNotInstallPageDto param) {
        PsiPage<LuggageNotInstallPageVo> page = new PsiPage<>(param);
        if (StringUtils.isNotBlank(param.getPkgStatus())) {
            param.setPkgStatusList(Arrays.asList(param.getPkgStatus().split(",")));
        }
        return baseMapper.getLuggageNotInstallPage(page, param);
    }


}
