package com.swcares.psi.upgrade.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：UpgradePlan <br>
 * Package：com.swcares.psi.upgrade.entity <br>
 * Description：计划升舱表实体类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021/5/8 14:58 <br>
 * @version v1.0
 */
@Data
@TableName("FFP_UPGRADE_PAX_PLAN")
public class UpgradePlan implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @TableField("PAX_ID")
    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    @TableField("FLIGHT_NO")
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    //todo-add
    @TableField("CARD_TYPE")
    @ApiModelProperty(value = "卡级别")
    private String cardType;

    @TableField("FLIGHT_DATE")
    @ApiModelProperty(value = "航班日期")
    private LocalDate flightDate;

    @TableField("ORG_VC")
    @ApiModelProperty(value = "出发地")
    private String orgVc;

    @TableField("DST_VC")
    @ApiModelProperty(value = "到达地")
    private String dstVc;

    @TableField("PAX_NAME")
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @TableField("PAX_FOID")
    @ApiModelProperty(value = "旅客证件号")
    private String paxFoid;

    @TableField("TKT_NO")
    @ApiModelProperty(value = "票号")
    private String tktNo;



    @TableField("TKT_PRICE")
    @ApiModelProperty(value = "票面价")
    private BigDecimal tktPrice;

    @TableField("CAN_UPDATE_BOOL")
    @ApiModelProperty(value = "是否允许升舱 1允许 0不允许")
    private String canUpdateBool;





    @TableField("REASION_VC")
    @ApiModelProperty(value = "升舱原因，手动添加时，保存用户填写的升舱原因")
    private String reasionVc;

    @TableField("LF_PAX_BOOL")
    @ApiModelProperty(value = "是否为LF旅客 0不是 1是")
    private String lfPaxBool;

//    @TableField("FRI_ID")
//    @ApiModelProperty(value = "升舱航班ID（FFP_UPGRADE_FLIGHT_PLAN）")
//    private String friId;
//
//    @TableField("UP_CARD_ID")
//    @ApiModelProperty(value = "升舱卡对应旅客ID（P5022_UPGRADE_CARD_PSG）")
//    private String upCardId;

//    //TODO 什么产品？？
//    @TableField("PRODUCT_ID")
//    @ApiModelProperty(value = "升舱产品ID")
//    private String productId;

//    //TODO 超级名单？？
//    @TableField("SUPER_ID")
//    @ApiModelProperty(value = "超级名单ID")
//    private String superId;

//    //TODO 如果尝试过多次升舱，只记录最后一次的ID？或者是log中记录了plan的id
//    @TableField("UP_RST_ID")
//    @ApiModelProperty(value = "升舱结果ID（P5043_UPGRADE_LOG）")
//    private String upRstId;

    //TODO 之前金卡翡翠卡贵宾卡可立即升舱，升舱产品里有UP_STYLE？？？
    @TableField("UP_STYLE")
    @ApiModelProperty(value = "升舱时机：0立即、1排序")
    private String upStyle;

    @TableField("UP_STATE")
    @ApiModelProperty(value = "升舱状态：0待升、1成功、2失败 3取消升舱 -不予升舱")
    private String upState;

    @ApiModelProperty(value = "暂停升舱：0不暂停、1暂停")
    @TableField("PAUSE")
    private String pause;

    @TableField("CHECK_STATUS")
    @ApiModelProperty(value = "值机状态,AC 标识已值机")
    private String checkStatus;

//    //TODO 升舱产品里的权重？？
//    @TableField("WEIGHT")
//    @ApiModelProperty(value = "升舱权重")
//    private BigDecimal weight;

    @TableField("PSG_TKT_TIME")
    @ApiModelProperty(value = "旅客订票时间")
    private LocalDateTime psgTktTime;



//    @TableField("PSG_TAKE_TIME")
//    @ApiModelProperty(value = "旅客乘机次数")
//    private BigDecimal psgTakeTime;

    @TableField("UP_ORDER")
    @ApiModelProperty(value = "升舱序号")
    private int upOrder;

    //TODO 现在不能手动添加了？
    @TableField("ADD_STYLE")
    @ApiModelProperty(value = "添加方式：0自动、1手动")
    private String addStyle;

    //TODO 自动取消是怎么操作？
    @TableField("CANCEL_STYLE")
    @ApiModelProperty(value = "取消方式：0自动、1手动")
    private String cancelStyle;



//    @TableField("IS_MIX")
//    @ApiModelProperty(value = "是否混舱 Y 是  N否")
//    private String isMix;



    @TableField("CREATE_USER_ID")
    @ApiModelProperty(value = "创建者id。null表示系统创建")
    private String createUserId;

    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    //TODO 这个设计不能记录所有修改历史，是否满足需求？
    @TableField("UPDATE_USER_ID")
    @ApiModelProperty(value = "最后 一次修改者ID")
    private String updateUserId;

    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "最后一次修改时间 当升舱时间用")
    private LocalDateTime updateTime;

    @TableField("UP_STATE_REASON")//字段长点
    @ApiModelProperty(value = "升舱状态产生的原因，调用升舱接口后会将调用结果写入到这里")
    private String upStateReason;

    @TableField("STD")
    @ApiModelProperty(value = "航班计划起飞时间")
    private LocalDateTime std;

    //TODO 没有调序功能了？
//    @TableField("ORDER_USER_ID")
//    @ApiModelProperty(value = "调序人ID")
//    private String orderUserId;

    @TableField("CANCEL_USER_ID")
    @ApiModelProperty(value = "取消人工号")
    private String cancelUserId;

    @TableField("UPGRADE_USER_ID")
    @ApiModelProperty(value = "立即升舱操作人工号")
    private String upgradeUserId;

    @TableField("RULE_CODE")
    @ApiModelProperty(value = "规则编码")
    private String ruleCode;


    @TableField("NEW_PNR")
    @ApiModelProperty(value = "生成的新编码")
    private String newPnr;

    @TableField("IS_CHILD")
    @ApiModelProperty(value = "是否携带儿童,Y:是，N：否")
    private String isChild;

//    //TODO 新增字段
//    @TableField("IS_BABY")
//    @ApiModelProperty(value = "是否携带婴儿，Y:是，N：否")
//    private String isBaby;

    @TableField(value = "PAX_TYPE",updateStrategy= FieldStrategy.IGNORED, jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value = "旅客类型，GP:GP旅客 BLACK:黑名单旅客 SPECIAL:特服")
    private String paxType;

//    /**
//     * F舱剩余座位数
//     */
//    @TableField("SEAT_NUM")
//    @ApiModelProperty(value = "")
//    private BigDecimal seatNum;



    //TODO 这个和前面的升舱原因，优先展示谁？
    @TableField(value = "UP_HISTORY_REASON",updateStrategy= FieldStrategy.IGNORED, jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value = "升舱状态产生的历史原因，每次校验名单都会把相应的不能升舱原因记录到这里")
    private String upHistoryReason;

    @TableField("LF_NUM")
    @ApiModelProperty(value = "lf旅客协议号")
    private String lfNum;




    //----------新增字段

    @TableField("PSG_PNR")
    @ApiModelProperty(value = "原PNR")
    private String psgPnr;

    @ApiModelProperty(value = "PS PNR")
    @TableField("PSG_PS_PNR")
    private String psgPsPnr;

    @TableField("SUB_CABIN")
    @ApiModelProperty(value = "子舱位")
    private String subCabin;

    @TableField("UP_NEW_TKT_NO")
    @ApiModelProperty(value = "升舱新票号")
    private String upNewTktNo;

    @TableField("FFP_MILE")
    @ApiModelProperty(value = "常客飞行里程")
    private Integer ffpMile;
}
