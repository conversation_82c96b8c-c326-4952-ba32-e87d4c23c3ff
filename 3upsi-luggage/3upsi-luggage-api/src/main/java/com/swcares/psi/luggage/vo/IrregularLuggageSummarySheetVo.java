package com.swcares.psi.luggage.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * Title：IrregularLuggageSummarySheetVo
 * Package：com.swcares.psi.report.compensate.irregularluggage.vo
 * Description：异常行李合计报表Vo
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * date 2020-10-12
 * @version v1.0
 */
@Data
public class IrregularLuggageSummarySheetVo {

    @ExcelProperty("日期")
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ExcelProperty("破损补偿")
    @ApiModelProperty(value = "破损补偿")
    private String damageCompensateCount;

    @ExcelProperty("箱包补偿")
    @ApiModelProperty(value = "箱包补偿")
    private String baggageCompensateCount;

    @ExcelProperty("少收补偿")
    @ApiModelProperty(value = "少收补偿")
    private String lessCompensateCount;

    //    @ExcelProperty("多收补偿")
//    @ApiModelProperty(value = "多收补偿")
//    private long moreCompensateCount;
    @ExcelProperty("大面积延误事故单补偿")
    @ApiModelProperty(value = "大面积延误事故单补偿")
    private String batchCompensateCount;

    @ExcelProperty("内件缺失补偿")
    @ApiModelProperty(value = "内件缺失补偿")
    private String missCompensateCount;

    @ExcelProperty("合计")
    @ApiModelProperty(value = "合计补偿")
    private String totalCount;

    @ExcelProperty("A319")
    @ApiModelProperty(value = "A319机型合计")
    private String flightModelA319;

    @ExcelProperty("A320")
    @ApiModelProperty(value = "A320机型合计")
    private String flightModelA320;

    @ExcelProperty("A321")
    @ApiModelProperty(value = "A321机型合计")
    private String flightModelA321;

    @ExcelProperty("A330")
    @ApiModelProperty(value = "A330机型合计")
    private String flightModelA330;

    @ExcelProperty("A350")
    @ApiModelProperty(value = "A350机型合计")
    private String flightModelA350;
}
