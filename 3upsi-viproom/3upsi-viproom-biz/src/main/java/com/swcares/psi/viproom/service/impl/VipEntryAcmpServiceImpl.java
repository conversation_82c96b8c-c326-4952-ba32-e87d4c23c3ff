package com.swcares.psi.viproom.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swacares.psi.viproom.dto.VipEntryAcmpDto;
import com.swacares.psi.viproom.entity.VipEntryStatus;
import com.swacares.psi.viproom.entity.VipOperateLog;
import com.swacares.psi.viproom.entity.VipRoomInfo;
import com.swacares.psi.viproom.enums.VipEntryTypeEnum;
import com.swacares.psi.viproom.vo.VipEntryInfoVo;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.viproom.mapper.VipEntryAcmpMapper;
import com.swcares.psi.viproom.mapper.VipEntryStatusMapper;
import com.swcares.psi.viproom.mapper.VipRoomInfoMapper;
import com.swcares.psi.viproom.service.VipEntryAcmpService;
import com.swcares.psi.viproom.service.VipEntryStatusService;
import com.swcares.psi.viproom.service.VipOperateLogService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 贵宾随行旅客信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Transactional(rollbackFor = Throwable.class)
@Service
@Slf4j
public class VipEntryAcmpServiceImpl extends ServiceImpl<VipEntryAcmpMapper, VipEntryStatus> implements VipEntryAcmpService {

    @Resource
    VipRoomInfoMapper vipRoomInfoMapper;

    @Resource
    VipEntryStatusMapper vipEntryStatusMapper;

    @Resource
    VipEntryAcmpMapper vipEntryAcmpMapper;

    @Resource
    VipOperateLogService vipOperateLogService;

    @Resource
    VipEntryStatusService vipEntryStatusService;

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public void addVipEntryAcmp(List<VipEntryInfoVo> vipAcmpList, String vipId, String userId, String vipRoomId, boolean saveOptLog) {
        //过滤已是迎接状态的旅客
        filterGreetedFllower(vipAcmpList);

        List<VipEntryStatus> vipEntryList = new ArrayList<>();
        //设置随行对象参数
        for (VipEntryInfoVo v : vipAcmpList) {
            v.setParentId(vipId);
            if (StringUtils.isEmpty(v.getStatus())) {
                v.setStatus(VipEntryTypeEnum.ENTRY_VIP.getKey());
                v.setEntryTime(DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            } else {
                if(VipEntryTypeEnum.ENTRY_VIP.getKey().equals(v.getStatus())){
                    v.setEntryTime(DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                }
                v.setStatus(v.getStatus());
            }
            v.setUserId(userId);
            v.setVipRoomId(vipRoomId);
            vipEntryList.add(this.exchangeVinfoToVipEntry(v, false));
        }

        try {
            // 检查该旅客是否有历史迎接记录,更新之前历史数据状态history字段。
            this.update(new LambdaUpdateWrapper<VipEntryStatus>().set(VipEntryStatus::getHistory, "1").in(VipEntryStatus::getPaxId,
                    vipEntryList.stream().map(VipEntryStatus::getPaxId).collect(Collectors.toList())));
            // 贵宾旅客备注信息添加
            VipEntryStatus vipEntryStatus = vipEntryStatusService.getById(vipId);
            String memo = vipEntryStatus.getMemo();
            StringBuffer tmpBuffer = new StringBuffer(" 随行人员:");
            vipEntryList.stream().forEach(e -> {
                tmpBuffer.append(e.getPaxName() + ",");
            });
            tmpBuffer.delete(tmpBuffer.length()-1, tmpBuffer.length());
            tmpBuffer.append(";");
            if (StringUtils.isNotBlank(memo)) {
                memo += tmpBuffer;
            } else {
                memo = tmpBuffer.toString();
            }
            vipEntryStatus.setMemo(memo);
            vipEntryStatusService.updateById(vipEntryStatus);
            // 添加随行
            vipEntryList.stream().forEach(e -> {
                if (StringUtils.isNotBlank(e.getMemo())) {
                    e.setMemo(e.getMemo() + " 主宾" + vipEntryStatus.getPaxName() + "的随行");
                } else {
                    e.setMemo(" 主宾" + vipEntryStatus.getPaxName() + "的随行");
                }
            });
            vipEntryStatusService.saveBatch(vipEntryList);

            vipAcmpList.forEach(acmp -> acmp.setId(vipEntryList.stream().filter(ves -> ves.getPaxId()==acmp.getPaxId()).findFirst().get().getId()));
            if (saveOptLog) {
                addVIPOperateLog(vipAcmpList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("批量添加贵宾随行人员信息异常：", e);
            throw new BusinessException(MessageCode.FAIL.getCode());
        }
    }

    /**
     * 添加随从过滤是迎接状态的旅客
     * @param vipAcmpList
     */
    private void filterGreetedFllower(List<VipEntryInfoVo> vipAcmpList) {
        VipEntryStatus greeted = vipEntryStatusMapper.selectOne(
                Wrappers.<VipEntryStatus>lambdaQuery()
                        .eq(VipEntryStatus::getStatus, "1")
                        .eq(VipEntryStatus::getHistory, "0")
                        .in(VipEntryStatus::getPaxId, vipAcmpList.stream().map(VipEntryInfoVo::getPaxId).collect(Collectors.toList()))
        );

        if (greeted != null) {
            VipRoomInfo vipRoomInfo = vipRoomInfoMapper.selectById(greeted.getVipRoomId());
            throw new BusinessException(MessageCode.VIP_NEW_ENTRY_TIP.getCode(), new String[]{greeted.getPaxName(), vipRoomInfo.getRoomName()});
        }
    }

    @Override
    public IPage<VipEntryInfoVo> getChioceVipAcmp(VipEntryAcmpDto vei, PsiPage<VipEntryAcmpDto> psiPage, String vipRoomId) {
        String flightNo = vei.getFlightNo();
        String org = vei.getOrgCityAirp();
        String paxName = vei.getPaxName();
        String lclDptDate = vei.getFlightDate();
        // 判断查询条件是否为空
        if (StringUtils.isEmpty(vei.getFlightNo()) || StringUtils.isEmpty(vei.getOrgCityAirp()) || StringUtils.isEmpty(vei.getLclDptDate()) || StringUtils.isEmpty(vei.getPaxId())) {
            log.info("贵宾室随行人员添加查询：flightNo:" + flightNo + ",org:" + org
                    + ",lclDptDate:" + lclDptDate + "paxName:" + paxName);
            throw new BusinessException(MessageCode.VIP_PARAM_IS_EMPTY.getCode());
        }
        return vipEntryAcmpMapper.getPaxInfo(vei, psiPage, vipRoomId);
    }


    @Override
    public String deleteVipEntryAcmp(String isc) {
        return null;
    }

    @Override
    public String getVipIdsByPaxIdS(List<String> ids) {
        return vipEntryAcmpMapper.getVipIdsByPaxIdS(ids);
    }

    public void addVes(VipEntryInfoVo vei, String userId) {
        VipEntryStatus ves = new VipEntryStatus();
        ves.setId(vei.getId());
        ves.setDst(vei.getDst());
        ves.setEntryTime(DateUtils.parseStringToLocalDateTime(vei.getEntryTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        ves.setFlightNo(vei.getFlightNo());
        ves.setFlightStatus(vei.getFlightStatus());
        ves.setFlightDate(DateUtils.parseStringToLocalDate(vei.getFlightDate(), DateUtils.YYYY_MM_DD));
        ves.setLeaveTime(DateUtils.parseStringToLocalDateTime(vei.getLeaveTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        ves.setMemo(vei.getMemo());
        ves.setBordNo(vei.getBordNo());
        ves.setTktNo(vei.getTktNo());
        ves.setPnr(vei.getPnrRef());
        ves.setOrg(vei.getOrg());
        ves.setPaxId(vei.getPaxId());
        ves.setPaxName(vei.getPaxName());
        ves.setPaxTypeCode(StringUtils.isNotEmpty(vei.getPaxTypeCode()) ? "" : vei.getPaxTypeCode()
                .replaceAll("/(^[,]*)|([,]*$)/g", ""));
        ves.setStatus(vei.getStatus());
        ves.setStd(DateUtils.parseStringToLocalDateTime(vei.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        ves.setUpdateTime(DateUtils.parseStringToLocalDateTime(vei.getUpdateTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        ves.setUpdateUser(vei.getUpdateUser());
        ves.setEmpId(vei.getEmpId());
        ves.setFlightType(vei.getFlightType());
        vipEntryStatusMapper.insert(ves);
    }


    @Override
    public boolean revokeAcmpInfo(List<String> acmpIds) {
        //记录操作日志信息
        List<VipEntryInfoVo> vess = vipEntryAcmpMapper.getAcmpByIds(acmpIds);
        for (VipEntryInfoVo ves : vess) {
            ves.setStatus(VipEntryTypeEnum.REVOKE_VIP.getKey());
            this.addVIPOperateLog(Arrays.asList(ves));
        }
        return vipEntryAcmpMapper.revokeAcmpInfo(acmpIds, AuthenticationUtil.getUserNo());
    }

    @Override
    public boolean seeoffAcmpInfo(List<String> acmpIds) {
        //记录操作日志信息
        List<VipEntryInfoVo> vess = vipEntryAcmpMapper.getAcmpByIds(acmpIds);
        for (VipEntryInfoVo ves : vess) {
            ves.setStatus(VipEntryTypeEnum.SEE_OFF_VIP.getKey());
            this.addVIPOperateLog(Arrays.asList(ves));
        }

        List<VipEntryStatus> list = this.lambdaQuery().in(VipEntryStatus::getId,acmpIds).list();
        LocalDateTime now = LocalDateTime.now();
        for(VipEntryStatus ele : list){
            if ("1".equals(ele.getUseStatus())) {
                LocalDateTime startUseTime = ele.getStartUseTime();
                long between = ChronoUnit.SECONDS.between(startUseTime, now);
                Integer integer = new Integer(between + "");
                ele.setUseTime((ele.getUseTime() == null ? 0 : ele.getUseTime()) + integer);
            }
            ele.setUpdateUser(AuthenticationUtil.getUserNo());
            ele.setUpdateTime(now);
            ele.setStatus(VipEntryTypeEnum.SEE_OFF_VIP.getKey());
            ele.setLeaveTime(now);
        }
       return this.saveOrUpdateBatch(list);
    }

    /**
     * 记录操作日志
     *
     * @param list
     */
    private void addVIPOperateLog(List<VipEntryInfoVo> list) {
        vipOperateLogService.addVIPOperateLog(list.stream().map(ele -> this.toOperationLog(ele, true)).collect(Collectors.toList()));
    }

    private VipEntryStatus exchangeVinfoToVipEntry(VipEntryInfoVo vei, boolean isUpdate) {
        VipEntryStatus ves = new VipEntryStatus();
        ves.setDst(vei.getDst());
        if (StringUtils.isNotEmpty(vei.getEntryTime())) {
            ves.setEntryTime(DateUtils.parseStringToLocalDateTime(vei.getEntryTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        if (StringUtils.isNotEmpty(vei.getStd())) {
            ves.setStd(DateUtils.parseStringToLocalDateTime(vei.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }

        ves.setFlightNo(vei.getFlightNo());
        ves.setFlightStatus(vei.getFlightStatus());
        if (StringUtils.isNotEmpty(vei.getFlightDate())) {
            ves.setFlightDate(DateUtils.parseStringToLocalDate(vei.getFlightDate(), DateUtils.YYYY_MM_DD));
        }
        if (StringUtils.isNotEmpty(vei.getLeaveTime())) {
            ves.setLeaveTime(DateUtils.parseStringToLocalDateTime(vei.getLeaveTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        ves.setMemo(vei.getMemo());
        ves.setBordNo(vei.getBordNo());
        ves.setTktNo(vei.getTktNo());
        ves.setPnr(vei.getPnrRef());
        ves.setOrg(vei.getOrg());
        ves.setPaxId(vei.getPaxId());
        ves.setPaxName(vei.getPaxName());
        ves.setPaxTypeCode(StringUtils.isEmpty(vei.getPaxTypeCode()) ? null : vei.getPaxTypeCode()
                .replaceAll("/(^[,]*)|([,]*$)/g", ""));
        ves.setStatus(vei.getStatus());
        ves.setEmpId(vei.getUserId());
        ves.setVipRoomId(vei.getVipRoomId());
        ves.setFlightType(vei.getFlightType());
        ves.setParentId(vei.getParentId());
        ves.setDataFrom(vei.getDataFrom());
        if (isUpdate) {
            ves.setId(vei.getId());
            ves.setUpdateTime(LocalDateTime.now());
            ves.setUpdateUser(vei.getUserId());
        } else {
            ves.setCreateTime(LocalDateTime.now());
            ves.setCreateUser(vei.getUserId());
            ves.setStartUseTime(LocalDateTime.now());
            ves.setUseStatus("1");
        }
        return ves;
    }

    @Override
    public VipOperateLog toOperationLog(VipEntryInfoVo vo, boolean isAccompany) {
        VipOperateLog optLog = new VipOperateLog();
        optLog.setEntryId(vo.getId());
        optLog.setOperateTime(LocalDateTime.now());
        optLog.setOperator(AuthenticationUtil.getUserNo());
        optLog.setOperatorName(AuthenticationUtil.getRealName());
        optLog.setOperationType(vo.getStatus());
        optLog.setPaxTypeCode(vo.getPaxTypeCode());
        optLog.setDataFrom(vo.getDataFrom());
        if (vo.getFlightDate() != null) {
            LocalDate flightDate = null;
            try {
                flightDate = LocalDateTime.parse(vo.getFlightDate(), dateTimeFormatter).toLocalDate();
            } catch (Exception e) {
                try {
                    flightDate = LocalDate.parse(vo.getFlightDate(), dateFormatter);
                } catch (Exception ex) {
                    log.error("贵宾厅操作记录工作记录出错！vo:", vo, ex);
                }
            }
            optLog.setFlightDate(flightDate);
        }
        optLog.setStd(vo.getStd()!=null? LocalDateTime.parse(vo.getStd(), dateTimeFormatter) : null);
        optLog.setFlightNo(vo.getFlightNo());
        optLog.setOrg(vo.getOrg());
        optLog.setDst(vo.getDst());
        optLog.setFlightType(vo.getFlightType());
        optLog.setFlightCampType(vo.getFlightCampType());
        optLog.setTktNo(vo.getTktNo());
        optLog.setPaxName(vo.getPaxName());
        optLog.setVipRoomId(vo.getVipRoomId());
        optLog.setVipRoomName(vo.getRoomName());
        optLog.setIsAccompany(isAccompany? 1:0);

        return optLog;
    }

}
