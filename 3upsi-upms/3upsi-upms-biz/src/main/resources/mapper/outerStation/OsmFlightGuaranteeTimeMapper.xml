<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.outerStation.mapper.OsmFlightGuaranteeTimeMapper">

    <select id="flightGuaranteeTimePage" resultType="com.swcares.psi.outerStation.vo.OsmFlightGuaranteeTimeVo">
        SELECT
        OFGT.ID AS id,
        OFGT.GUARANTEE_TIME_LEVEL AS guaranteeTimeLevel,
        OFGT.GUARANTEE_TIME AS guaranteeTime,
        OFGT.FLIGHT_MODEL AS flightModel,
        OFGT.STATE AS state,
        OFGT.CREATOR AS creator,
        DATE_FORMAT(OFGT.CREATE_TIME, '%Y-%m-%d %H:%i:%s') AS createTime,
        OFGT.UPDATER AS updater,
        DATE_FORMAT(OFGT.UPDATE_TIME, '%Y-%m-%d %H:%i:%s') AS updateTime,
        GROUP_CONCAT(DISTINCT SAI.CODE) AS terminals
        FROM OSM_FLIGHT_GUARANTEE_TIME OFGT
        LEFT JOIN SYS_AIRPORT_INFO SAI ON OFGT.GUARANTEE_TIME_LEVEL = SAI.GUARANTEE_TIME_LEVEL
        <where>
            <if test="dto.terminal != null and dto.terminal != ''">
                AND OFGT.GUARANTEE_TIME_LEVEL = (SELECT GUARANTEE_TIME_LEVEL FROM SYS_AIRPORT_INFO WHERE CODE = #{dto.terminal})
            </if>
            <if test="dto.state != null and dto.state != ''">
                AND OFGT.STATE = #{dto.state}
            </if>
        </where>
        GROUP BY OFGT.GUARANTEE_TIME_LEVEL, OFGT.FLIGHT_MODEL
        ORDER BY OFGT.GUARANTEE_TIME_LEVEL ASC
    </select>
    <select id="getGuaranteeTerminal" resultType="java.lang.String">
        SELECT
            SAI.CODE
        FROM SYS_AIRPORT_INFO SAI
        WHERE
        SAI.GUARANTEE_TIME_LEVEL = #{dto.level}
    </select>
    <select id="getFlightModel" resultType="java.lang.String">
        SELECT
            DISTINCT FLIGHT_MODEL
        FROM
            FLT_FLIGHT_REAL_INFO
        WHERE
            FLIGHT_MODEL IS NOT NULL
    </select>
    <select id="getFlightGuaranteeModel" resultType="java.lang.String">
        SELECT
        OFGT.FLIGHT_MODEL AS flightModel
        FROM OSM_FLIGHT_GUARANTEE_TIME OFGT
        WHERE
        OFGT.GUARANTEE_TIME_LEVEL = #{dto.guaranteeTimeLevel}
        AND OFGT.GUARANTEE_TIME != #{dto.guaranteeTime}
    </select>

    <update id="updateflightGuarantee">
        UPDATE SYS_AIRPORT_INFO SET
        GUARANTEE_TIME_LEVEL = #{dto.level}
        WHERE CODE IN
        <foreach collection="dto.terminals" open="(" close=")" separator="," item="terminal">
                #{terminal}
        </foreach>
    </update>
</mapper>
