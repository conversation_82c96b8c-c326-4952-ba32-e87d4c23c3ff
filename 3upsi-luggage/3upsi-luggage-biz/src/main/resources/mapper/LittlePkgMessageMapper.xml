<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.luggage.mapper.LittlePkgMessageMapper">
    <select id="getLittlePkgMessage" resultType="com.swcares.psi.luggage.vo.LittlePkgMessageVo">
      SELECT PKG.ACCIDENT_ID AS accidentId,F.FLIGHT_NO AS flightNo,
      F.FLIGHT_DATE AS flightDate,PKG.CREATE_USER AS createUser,
      DPI.PAX_NAME AS paxName,PKG.REMIND_DAY AS remindDay
      FROM DP_PKG_INFO PKG
      LEFT JOIN dp_flight_info F
      ON F.FLIGHT_ID=PKG.FLIGHT_ID
      LEFT JOIN DP_PAX_INFO DPI ON
      DPI.PAX_ID=PKG.PAX_ID
      WHERE PKG.ACCIDENT_TYPE IN ('2','6') AND PKG.STATUS IN ('0','2') AND PKG.SEND_STATUS is NULL
      AND date_format(date_add(PKG.CREATE_TIME, interval PKG.REMIND_DAY day), '%Y-%m-%d %H') = date_format(
      #{currentTime}, '%Y-%m-%d %H')
      GROUP BY PKG.ACCIDENT_ID,F.FLIGHT_NO,F.FLIGHT_DATE,PKG.CREATE_USER,DPI.PAX_NAME,PKG.REMIND_DAY
    </select>
    <select id="updateLittlePkgSendStatus">
         UPDATE DP_PKG_INFO PKG 
         SET PKG.SEND_STATUS='1' 
         WHERE PKG.ACCIDENT_ID=#{accidentId}
    </select>
</mapper>
