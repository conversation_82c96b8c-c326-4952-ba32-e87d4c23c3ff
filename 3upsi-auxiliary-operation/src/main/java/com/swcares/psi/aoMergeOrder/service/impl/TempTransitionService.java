package com.swcares.psi.aoMergeOrder.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.psi.ao.cons.AoCons;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.InvoiceInfoMergeSaveDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeInvoiceInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSubOrderJoinInvoiceInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.service.AoMergeSubOrderJoinInvoiceInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeInvoiceInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PaxPayLoginInfo;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.invoice.InvoiceUtil;
import com.swcares.psi.common.invoice.v2.InvoiceParam;
import com.swcares.psi.common.invoice.v2.InvoiceUtilV2;
import com.swcares.psi.common.invoice.v2.constant.InvoiceOrderType;
import com.swcares.psi.common.invoice.v2.constant.InvoiceStatus;
import com.swcares.psi.common.invoice.v2.entity.YyInvoice;
import com.swcares.psi.common.invoice.v2.mapper.YyInvoiceMapper;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.security.util.PsiUserUtils;
import com.swcares.psi.common.utils.AesEncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * todo-houchuan 临时过渡
 */
@Slf4j
@Service
public class TempTransitionService {
    @Autowired
    IAoMergeInvoiceInfoService aoMergeInvoiceInfoService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;

    @Autowired
    private PsiUserUtils psiUserUtils;
    @Autowired
    private InvoiceUtilV2 invoiceUtilV2;
    @Autowired
    private AoMergeSubOrderJoinInvoiceInfoService aoMergeSubOrderJoinInvoiceInfoService;
    @Autowired
    YyInvoiceMapper yyInvoiceMapper;

    @Transactional(rollbackFor = Exception.class)
    public void saveInvoice(List<InvoiceInfoMergeSaveDto> dtos) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userName = authentication.getRealName();
        if(StringUtils.isEmpty(userName)||userNo.startsWith("paxuser_")){
            PaxPayLoginInfo paxPayLoginInfo = psiUserUtils.getPaxPayLoginInfo();
            if (paxPayLoginInfo == null) {
                log.info("合并订单开票登录信息异常{}-{}", userNo, userName);
            } else {
                userName = paxPayLoginInfo.getName();
            }
        }


        for (InvoiceInfoMergeSaveDto invoice : dtos) {
            Integer count = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery().in(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, invoice.getSubMergeOrderId()).count();
            if(count>0){
                throw new BusinessException(MessageCode.ORDER_INVOICE_YES.getCode());
            }


            AoMergeInvoiceInfo aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
            BeanUtils.copyProperties(invoice, aoMergeInvoiceInfo);
            aoMergeInvoiceInfo.setProductType("1");
            aoMergeInvoiceInfo.setUserNo(userNo);
            aoMergeInvoiceInfo.setUserName(userName);
            aoMergeInvoiceInfo.setInvoiceStatus(InvoiceStatus.AWAIT_INVOICE);
            String seqNo = InvoiceUtil.createInvoiceNo(AoCons.AO_INVOICE_LOCK, AoCons.AO_INVOICE_NO_KEY);
            aoMergeInvoiceInfo.setSeqNo(seqNo);
            aoMergeInvoiceInfo.setPhone(AesEncryptUtil.encrypt(invoice.getPhone()));
            aoMergeInvoiceInfo.setPurchaserPhone(AesEncryptUtil.encrypt(invoice.getPurchaserPhone()));
            aoMergeInvoiceInfo.setCreateTime(LocalDateTime.now());

            for (String subMergeId : invoice.getSubMergeOrderId()) {
                AoMergeSuborderInfo suborderInfo = aoMergeSuborderInfoService.getById(subMergeId);
                if(!AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS.equals(suborderInfo.getSuborderOrderStatus())){
                    throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_INVOICE.getCode());
                }
                if (suborderInfo != null) {
                    AoMergeSubOrderJoinInvoiceInfo joinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                    joinInvoiceInfo.setMergeOrderId(suborderInfo.getAoMergeOrderInfoId());
                    joinInvoiceInfo.setSuborderOrderId(suborderInfo.getId());
                    joinInvoiceInfo.setSeqNo(seqNo);
                    aoMergeSubOrderJoinInvoiceInfoService.save(joinInvoiceInfo);
                    suborderInfo.setInvoiceJoinId(joinInvoiceInfo.getId());
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);

                }
            }
            aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveInvoiceByC(List<InvoiceInfoMergeSaveDto> dtos) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userName = authentication.getRealName();
        if(StringUtils.isEmpty(userName)||userNo.startsWith("paxuser_")){
            PaxPayLoginInfo paxPayLoginInfo = psiUserUtils.getPaxPayLoginInfo();
            if (paxPayLoginInfo == null) {
                log.info("合并订单开票登录信息异常{}-{}", userNo, userName);
            } else {
                userName = paxPayLoginInfo.getName();
                userNo=null;
            }
        }


        for (InvoiceInfoMergeSaveDto invoice : dtos) {
            Integer count = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery().in(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, invoice.getSubMergeOrderId()).count();
            if (count > 0) {
                for (String subId : invoice.getSubMergeOrderId()) {
                    Integer subCount = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery().eq(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, subId).count();
                    if (subCount > 0) {
                        AoMergeSuborderInfo byId = aoMergeSuborderInfoService.getById(subId);
                        throw new BusinessException(MessageCode.ORDER_INVOICE_YES_BY_C.getCode(), new String[]{byId.getPaxName()});
                    }
                }
            }


            AoMergeInvoiceInfo aoMergeInvoiceInfo = new AoMergeInvoiceInfo();
            BeanUtils.copyProperties(invoice, aoMergeInvoiceInfo);
            aoMergeInvoiceInfo.setProductType("1");
            aoMergeInvoiceInfo.setUserNo(userNo);
            aoMergeInvoiceInfo.setUserName(userName);
            aoMergeInvoiceInfo.setInvoiceStatus(InvoiceStatus.AWAIT_INVOICE);
            String seqNo = InvoiceUtil.createInvoiceNo(AoCons.AO_INVOICE_LOCK, AoCons.AO_INVOICE_NO_KEY);
            aoMergeInvoiceInfo.setSeqNo(seqNo);
            aoMergeInvoiceInfo.setPhone(AesEncryptUtil.encrypt(invoice.getPhone()));
            aoMergeInvoiceInfo.setPurchaserPhone(AesEncryptUtil.encrypt(invoice.getPurchaserPhone()));
            aoMergeInvoiceInfo.setCreateTime(LocalDateTime.now());

            for (String subMergeId : invoice.getSubMergeOrderId()) {
                AoMergeSuborderInfo suborderInfo = aoMergeSuborderInfoService.getById(subMergeId);
                if(!AoMergeCons.SUBORDER_ORDER_STATUS_SUCCESS.equals(suborderInfo.getSuborderOrderStatus())){
                    throw new BusinessException(MessageCode.ORDER_STATUS_CHANGE_NO_INVOICE.getCode());
                }
                if (suborderInfo != null) {
                    AoMergeSubOrderJoinInvoiceInfo joinInvoiceInfo = new AoMergeSubOrderJoinInvoiceInfo();
                    joinInvoiceInfo.setMergeOrderId(suborderInfo.getAoMergeOrderInfoId());
                    joinInvoiceInfo.setSuborderOrderId(suborderInfo.getId());
                    joinInvoiceInfo.setSeqNo(seqNo);
                    aoMergeSubOrderJoinInvoiceInfoService.save(joinInvoiceInfo);
                    suborderInfo.setInvoiceJoinId(joinInvoiceInfo.getId());
                    aoMergeSuborderInfoService.saveOrUpdate(suborderInfo);

                }
            }
            aoMergeInvoiceInfoService.save(aoMergeInvoiceInfo);
        }
    }
}
