/**  
 * All rights Reserved, Designed By <br>
 * Title：AirplaneSeatBitmapHandler.java <br>
 * Package：com.swcares.psi.sere.analysis <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2023 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2023年4月14日 下午4:01:04 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.psi.sere.analysis;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.psi.sere.analysis.entity.Airplane;
import com.swcares.psi.sere.analysis.entity.Row;
import com.swcares.psi.sere.analysis.entity.Seat;
import com.swcares.psi.sere.analysis.entity.SeatBitmap;
import com.swcares.psi.sere.analysis.entity.Site;
import com.swcares.psi.sere.analysis.enums.SeatState;
import com.swcares.psi.sere.analysis.enums.SiteType;

import cn.hutool.core.util.NumberUtil;

/**   
 * ClassName：com.swcares.psi.sere.analysis.AirplaneSeatBitmapHandler <br>
 * Description：飞机座位图解析器 <br>
 * Copyright © 2023 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2023年4月14日 下午4:01:04 <br>
 * @version v1.0 <br>  
 */
@Component
public class AirplaneSeatBitmapHandler {

	@Autowired
	private FlightSeatLatticeBitmap latticeBitmap;

    //description[Thomas]:2.3.3-(1)(确认从32排开始预留;(2)预留范围为座位图总*标识和经济舱C标识的座位.
	public Airplane analysisSeatBitmap(String content) {
		SeatBitmap lattice = latticeBitmap.analysisSite(content);
		return siteConvertSeat(lattice);
	}
	
	private Airplane siteConvertSeat(SeatBitmap bitMap) {
	    Airplane air = new Airplane(bitMap);
        Row aisleRow = getAisleRow(bitMap);
        for (Entry<Integer, Row> entity : bitMap.getRows().entrySet()) {
            Row row = entity.getValue();
            List<Seat> seatRow = siteToSeat(row, aisleRow);
            air.addSeat(row, seatRow);
        }
        air.searchIdleSeat();
        return air;
	}
	
	private List<Seat> siteToSeat(Row row, Row aisleRow) {
	    List<Seat> seatRow = new ArrayList<>();
        boolean flag = false;
        Site rowTag = null;
        Site colTag = null;
        for (Entry<Integer, Site> ey : row.getSites().entrySet()) {
            Site site = ey.getValue();
            Site desc = site.getDesc();
            Site sequence = site.getSequence();
            if(!flag) {
                flag = desc.isEc();
                rowTag = site;
            }
            if(flag) {
                if(desc != null && isDescribeNumber(desc)) {
                    colTag = desc;
                }
                if(rowTag != null && colTag != null && sequence.isNotEmpty()) {
                    Seat seat = buildSeat(row, aisleRow, site, rowTag, colTag);
                    seatRow.add(seat);
                }
            }
        }
        return seatRow;
	}
	
	private Seat buildSeat(Row row, Row aisle, Site site, Site rowTag, Site colTag) {
	    Seat seat = new Seat();
        Site header = row.getHeader();
        Site desc = site.getDesc();
        Site sequence = site.getSequence();
        BigDecimal num = buildSeatNumber(colTag, sequence);
        seat.setColumn(num.intValue());
        seat.setRowTag(rowTag.getContent());
        seat.setSeatNumber(String.valueOf(seat.getColumn()) + seat.getRowTag());
        seat.setState(SeatState.getByCode(site.getContent()));
        if(site.getRow() == 3 || row.isLast()) {
            seat.setWindow(Boolean.TRUE);
            seat.setWing(SiteType.isWing(desc == null ? null : desc.getContent()));
        }
        Site aisleSite = aisle.getSites().get(site.getSerial());
        seat.setEmergency(SiteType.isEyExit(aisleSite.getContent()));
        seat.setDesc(desc);
        seat.setHeader(header);
        seat.setAisle(site.isAisleSeat());
        seat.setSite(site);
        return seat;
	}
	
	private BigDecimal buildSeatNumber(Site colTag, Site sequence) {
        BigDecimal num = new BigDecimal(colTag.getContent());
        BigDecimal rs = num.multiply(new BigDecimal(10));
        num = rs.add(new BigDecimal(sequence.getContent()));
        return num;
    }

    private Row getAisleRow(SeatBitmap bitMap) {
        Map<Integer, Row> aisleRows = bitMap.getAisleRows();
        for (Entry<Integer, Row> entity : aisleRows.entrySet()) {
            return entity.getValue();
        }
        return null;
    }
    
    private boolean isDescribeNumber(Site site) {
        if(site.isNotEmpty()) {
            return NumberUtil.isNumber(site.getContent());
        }
        return false;
    }
	
}
