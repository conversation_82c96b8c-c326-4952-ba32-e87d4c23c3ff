package com.swcares.psi.aoMergeOrder.mapper;

import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeQueryDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountQueryDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCardTypeDiscountList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付卡级别折扣清单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeCardTypeDiscountListMapper extends BaseMapper<AoMergeCardTypeDiscountList> {
    PsiPage<AoMergeCardTypegetListVo> getPage(PsiPage<AoMergeCardTypegetListVo> page, @Param("dto") AoMergeCardTypeQueryDto dto);
    List<AoMergeCardTypegetListVo> getList( @Param("dto") AoMergeCardTypeQueryDto dto);

}
