package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.ao.common.dto.AoOrderInfoDto;
import com.swcares.psi.ao.common.entity.AoOrderInfoEntity;
import com.swcares.psi.ao.common.mapper.AoOrderInfoMapper;
import com.swcares.psi.ao.common.service.AoOrderInfoService;
import com.swcares.psi.ao.common.vo.*;
import com.swcares.psi.ao.cons.AoCons;
import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeOrderService;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeDetailsResultVo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeDetailsVo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeDetailsVos;
import com.swcares.psi.ao.invoice.entity.AoInvoiceInfoEntity;
import com.swcares.psi.ao.invoice.service.IAoInvoiceInfoService;
import com.swcares.psi.ao.manySeats.entity.AoManySeatsEntity;
import com.swcares.psi.ao.manySeats.service.ManySeatsService;
import com.swcares.psi.ao.model.dto.AoViproomPaxInfoDto;
import com.swcares.psi.ao.model.vo.AoViproomOrderDetailVO;
import com.swcares.psi.ao.model.vo.AoViproomOrderDetailsVO;
import com.swcares.psi.ao.model.vo.AoViproomPaxInfoVO;
import com.swcares.psi.ao.overweightbkg.service.AoOverweightBkgService;
import com.swcares.psi.ao.overweightbkg.vo.AoOverweightBkgBusinessVo;
import com.swcares.psi.ao.overweightbkg.vo.AoOverweightBkgDetailVo;
import com.swcares.psi.ao.overweightbkg.vo.AoOverweightBkgDetailVos;
import com.swcares.psi.ao.sceneSeats.service.SceneSeatsService;
import com.swcares.psi.ao.sceneSeats.vo.ScenSeatsVo;
import com.swcares.psi.ao.service.AoViproomItemSellService;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.aoMergeOrder.cons.AoMergeCons;
import com.swcares.psi.aoMergeOrder.dto.AoMergeH5OrderListDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderManageDto;
import com.swcares.psi.aoMergeOrder.dto.AoOrderInfoListByCpaxDto;
import com.swcares.psi.aoMergeOrder.entity.*;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeOrderInfoMapper;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.aoMergeOrder.utils.MergeOrderUtils;
import com.swcares.psi.aoMergeOrder.vo.*;
import com.swcares.psi.base.data.api.entity.FltPassengerRealInfo;
import com.swcares.psi.base.data.api.entity.SysAirportInfoEntity;
import com.swcares.psi.base.data.api.vo.AoDataPermissionsVo;
import com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper;
import com.swcares.psi.base.data.service.AoDataPermissionsService;
import com.swcares.psi.base.data.service.SysAirportInfoService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.user.impl.PaxPayLoginInfo;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.retrievephone.PaxerPhoneUtil;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.security.util.PsiUserUtils;
import com.swcares.psi.common.utils.AesEncryptUtil;
import com.swcares.psi.common.utils.DateUtils;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 辅营合并总订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeOrderInfoServiceImpl extends ServiceImpl<AoMergeOrderInfoMapper, AoMergeOrderInfo> implements IAoMergeOrderInfoService {

    @Autowired
    SysAirportInfoService sysAirportInfoService;

    @Autowired
    private AoDataPermissionsService aoDataPermissionsService;
    @Autowired
    private IAoMergeManySeatsService aoMergeManySeatsService;
    @Autowired
    private IAoMergeOverweightBkgService aoMergeOverweightBkgService;

    @Autowired
    private AoMergeOverweightBkgChargeDetailsService aoMergeOverweightBkgChargeDetailsService;

    @Autowired
    private IAoMergeSceneSeatsService aoMergeSceneSeatsService;
    @Autowired
    private IAoMergeViproomItemSellService aoMergeViproomItemSellService;
    @Autowired
    private IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    private IAoMergeSuborderInfoService aoMergeSuborderInfoService;

    @Autowired
    private IAoMergePayOrderInfoService aoMergePayOrderInfoService;
    @Autowired
    private IAoMergeRefundOrderInfoService aoMergeRefundOrderInfoService;

    @Autowired
    private IAoMergeCashTurnListService aoMergeCashTurnListService;
    @Autowired
    private IAoMergeInvoiceInfoService aoMergeInvoiceInfoService;
    @Autowired
    private AoMergeSubOrderJoinInvoiceInfoService aoMergeSubOrderJoinInvoiceInfoService;
    @Autowired
    private PsiUserUtils psiUserUtils;
    @Autowired
    private FltPassengerRealInfoMapper fltPassengerRealInfoMapper;
    @Autowired
    private PaxerPhoneUtil paxerPhoneUtil;
    /**
     * 查询出未支付的有效的
     *
     * @return
     */
    @Override
    public List<VaidatePayOrderVo> vaidatePayOrderList() {
        LocalDateTime localDateTime = LocalDateTime.now().minusSeconds(7620L);
        String date = DateUtils.parseLocalDateTimeToString(localDateTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
        return baseMapper.vaidatePayOrderList(date);
    }


    @Override
    public List<CashTurnQueryVo> cashTurnQuery(String servicePort, List<String> mergeOrderIdList) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        SysAirportInfoEntity one = sysAirportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, servicePort)
                .one();
        if (one == null) {
            throw new BusinessException(MessageCode.NOT_FIND_AIR_LINE.getCode(), new String[]{servicePort});
        }
        return baseMapper.cashTurnQuery(userNo, servicePort, "D".equals(one.getType()) ? "D" : ("I".equals(one.getType())?"I":"R"), mergeOrderIdList);
    }

    @Override
    public PsiPage<AoMergeH5OrderListVo> h5OrderList(AoMergeH5OrderListDto dto) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String userNo = authentication.getUsername();
        String userId = authentication.getId();
        PsiPage<AoMergeH5OrderListVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        return baseMapper.h5OrderList(page, dto, userId, userNo);
    }

    @Override
    public AoMergeOrderDetailsVo orderDetails(String mergeOrderId) {
        AoMergeOrderDetailsVo orderListVo = new AoMergeOrderDetailsVo();
        AoMergeOrderInfo mergeOrderInfo = this.getById(mergeOrderId);
        orderListVo.setOrderId(mergeOrderId);
        orderListVo.setOrderNo(mergeOrderInfo.getOrderNo());
        String dateTimeToString = DateUtils.parseLocalDateTimeToString(mergeOrderInfo.getCreateDate(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        orderListVo.setCreateDate(dateTimeToString);
        orderListVo.setMergeOrderStatus(mergeOrderInfo.getMergeOrderStatus());
        orderListVo.setCreateUser(mergeOrderInfo.getCreateUser());
        orderListVo.setCreateUserDepartment(mergeOrderInfo.getCreateUserDepartment());
        orderListVo.setCreateNo(mergeOrderInfo.getCreateNo());
        AoMergePayOrderInfo payOrder = aoMergePayOrderInfoService.getById(mergeOrderInfo.getOrderPayId());
        orderListVo.setPayType(payOrder.getPayType());
        if (payOrder != null && ObjectUtils.isNotEmpty(payOrder.getPayTime())) {
            String payTime = DateUtils.parseLocalDateTimeToString(payOrder.getPayTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            orderListVo.setPayTime(payTime);
        }
        orderListVo.setPayPrice(new BigDecimal(payOrder.getPrice().toString()).divide(new BigDecimal("100")).toString());
        orderListVo.setCurrencySpecies(payOrder.getCurrencySpecies());
        orderListVo.setCurrencySpeciesName(payOrder.getCurrencySpeciesName());
        orderListVo.setCreateUserDepartment(mergeOrderInfo.getCreateUserDepartment());
        orderListVo.setCashReceiveUser(mergeOrderInfo.getCashReceiveUser());
        orderListVo.setCashReceiveUserName(mergeOrderInfo.getCashReceiveUserName());
        orderListVo.setCashReceiveUserDepartment(mergeOrderInfo.getCashReceiveUserDepartment());
        orderListVo.setTurnNumber(mergeOrderInfo.getTurnNumber());
        if(ObjectUtils.isNotEmpty(mergeOrderInfo.getTurnTime())){
        orderListVo.setTurnTime(DateUtils.parseLocalDateTimeToString(mergeOrderInfo.getTurnTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        if(StringUtils.isNotEmpty(mergeOrderInfo.getTurnNumber())){
            AoMergeCashTurnList one = aoMergeCashTurnListService.lambdaQuery()
                    .eq(AoMergeCashTurnList::getCallbackPayNo, mergeOrderInfo.getTurnNumber())
                    .one();
            orderListVo.setChargeType(one.getChargeType());
        }
        SysAirportInfoEntity airportInfoEntity = sysAirportInfoService.lambdaQuery()
                .eq(SysAirportInfoEntity::getCode, mergeOrderInfo.getServicePort())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        orderListVo.setServicePort(airportInfoEntity.getAirportName() + airportInfoEntity.getCode());

        List<AoMergeRefundOrderInfo> refundOrderList = aoMergeRefundOrderInfoService.lambdaQuery()
                .eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, mergeOrderId)
                .eq(AoMergeRefundOrderInfo::getStatus,AoMergeRefundOrderInfo.STATUS_YES)
                .list();
        if (refundOrderList.size() > 0) {
            List<AoMergeRefundOrderInfo> collect = refundOrderList.stream().filter(ele -> ele.getRefundStatus().equals(AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)).collect(Collectors.toList());

            if (collect.size() > 0) {
                String refundPrice = collect.stream().map(AoMergeRefundOrderInfo::getRefundPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal("100")).toString();
                orderListVo.setRefundPrice(refundPrice);
            } else {
                orderListVo.setRefundPrice("0");
            }
        } else {
            orderListVo.setRefundPrice("0");
        }
        String receivePrice = new BigDecimal(orderListVo.getPayPrice()).subtract(new BigDecimal(orderListVo.getRefundPrice())).toString();
        orderListVo.setReceivePrice(receivePrice);
        List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderId)
                .list();
        long count = list.stream().filter(d -> StringUtils.isEmpty(d.getInvoiceJoinId())).count();
        orderListVo.setPermitInvoice(count+"");
        Map<String, List<AoMergeSuborderInfo>> collect = list.stream().collect(Collectors.groupingBy(AoMergeSuborderInfo::getPayPaxId));

        Iterator<Map.Entry<String, List<AoMergeSuborderInfo>>> iterator = collect.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, List<AoMergeSuborderInfo>> next = iterator.next();
            List<AoMergeSuborderInfo> value = next.getValue();
            AoMergeSuborderInfo suborderInfo = value.get(0);
            AoMergeOrderDetailsVo.PaxDetails paxDetails = new AoMergeOrderDetailsVo.PaxDetails();
            paxDetails.setPaxName(suborderInfo.getPaxName());
            paxDetails.setPaxNo(AesEncryptUtil.decryption(suborderInfo.getPaxNo()));
            paxDetails.setFlightNo(suborderInfo.getFlightNo());
            paxDetails.setFlightDate(DateUtils.parseLocalDateTimeToString(suborderInfo.getStd(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            paxDetails.setPaxCategory(suborderInfo.getPaxCategory());
            paxDetails.setCardType(suborderInfo.getCardType());
            paxDetails.setPaxId(suborderInfo.getPayPaxId());
            paxDetails.setFlightType(suborderInfo.getFlightType());
            SysAirportInfoEntity org = sysAirportInfoService.lambdaQuery()
                    .eq(SysAirportInfoEntity::getCode, suborderInfo.getOrg())
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            SysAirportInfoEntity dst = sysAirportInfoService.lambdaQuery()
                    .eq(SysAirportInfoEntity::getCode, suborderInfo.getDst())
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            paxDetails.setSegment(org.getCode() + "(" + org.getAirportName() + ")-" + dst.getCode() + "(" + dst.getAirportName() + ")");
            String paxProductPrice = value.stream().map(AoMergeSuborderInfo::getPracticalOrderPrice)
                    .reduce(BigDecimal.ZERO.ZERO, BigDecimal::add).toString();
            paxDetails.setPaxProductPrice(paxProductPrice);
            paxDetails.setIdentityDiscountDetails(suborderInfo.getIdentityDiscountDetails());
            //订单集合
            for (AoMergeSuborderInfo suborder : value) {
                AoMergeOrderDetailsVo.ProductBaseVo productBaseVo=new  AoMergeOrderDetailsVo.ProductBaseVo();

                if (suborder.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS)) {
                    AoMergeManySeats one = aoMergeManySeatsService.lambdaQuery()
                            .eq(AoMergeManySeats::getAoMergeSuborderInfoId, suborder.getId())
                            .one();
                    AoMergeOrderDetailsVo.ManySeatsDetailsVo manySeatsDetailsVo = new AoMergeOrderDetailsVo.ManySeatsDetailsVo();
                    manySeatsDetailsVo.setSeatsNumber(one.getSeatsNumber());
                    productBaseVo = manySeatsDetailsVo;
                } else if ((suborder.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS))) {
                    AoMergeSceneSeats one = aoMergeSceneSeatsService.lambdaQuery()
                            .eq(AoMergeSceneSeats::getAoMergeSuborderInfoId, suborder.getId())
                            .one();
                    AoMergeOrderDetailsVo.SceneSeatsDetailsVo seatsDetailsVo = new AoMergeOrderDetailsVo.SceneSeatsDetailsVo();
                    seatsDetailsVo.setSeatsType(one.getSeatsType());
                    productBaseVo = seatsDetailsVo;
                } else if ((suborder.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG))) {
                    AoMergeOverweightBkg one = aoMergeOverweightBkgService.lambdaQuery()
                            .eq(AoMergeOverweightBkg::getAoMergeSuborderInfoId, suborder.getId())
                            .one();
                    List<AoMergeOverweightBkgChargeDetails> list1 = aoMergeOverweightBkgChargeDetailsService.lambdaQuery()
                            .eq(AoMergeOverweightBkgChargeDetails::getAoMergeOverweightBkgId, one.getId())
                            .list();
                    AoMergeOrderDetailsVo.OverweightBkgDetailsVo overweightBkgDetailsVo = new AoMergeOrderDetailsVo.OverweightBkgDetailsVo();
                    for (AoMergeOverweightBkgChargeDetails ele : list1) {
                        AoMergeOrderDetailsVo.OverweightBkgDetailsVo.AoMergeOverweightBkgChargeDetailVo aoMergeOverweightBkgChargeDetailVo = new AoMergeOrderDetailsVo.OverweightBkgDetailsVo.AoMergeOverweightBkgChargeDetailVo();
                        aoMergeOverweightBkgChargeDetailVo.setChargeItem(ele.getChargeItem());
                        aoMergeOverweightBkgChargeDetailVo.setNumber(ele.getNumber().toString());
                        aoMergeOverweightBkgChargeDetailVo.setPrice(ele.getPrice().toString());
                        aoMergeOverweightBkgChargeDetailVo.setChargeStandardExplain(ele.getChargeStandardExplain());
                        overweightBkgDetailsVo.getChargeDetails().add(aoMergeOverweightBkgChargeDetailVo);
                    }
                    productBaseVo = overweightBkgDetailsVo;

                } else if ((suborder.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_VIP))) {
                    AoMergeViproomItemSell one = aoMergeViproomItemSellService.lambdaQuery()
                            .eq(AoMergeViproomItemSell::getAoMergeSuborderInfoId, suborder.getId())
                            .one();
                    AoMergeOrderDetailsVo.ViproomItemSellDetailsVo viproomItemSellDetailsVo = new AoMergeOrderDetailsVo.ViproomItemSellDetailsVo();
                    viproomItemSellDetailsVo.setItemName(one.getItemName());
                    productBaseVo = viproomItemSellDetailsVo;
                } else if ((suborder.getOrderType().equals(AoMergeCons.ORDER_TYPE_CODE_UPGRADE))) {
                    AoMergeCounterUpgrade one = aoMergeCounterUpgradeService.lambdaQuery()
                            .eq(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, suborder.getId())
                            .one();
                    AoMergeOrderDetailsVo.CounterUpgradeDetailsVo counterUpgradeDetailsVo = new AoMergeOrderDetailsVo.CounterUpgradeDetailsVo();
                    counterUpgradeDetailsVo.setErrorInfo(one.getErrorInfo());
                    counterUpgradeDetailsVo.setNewSeat(one.getNewSeat());
                    counterUpgradeDetailsVo.setUpgradeStatus(one.getUpgradeStatus());
                    counterUpgradeDetailsVo.setApplyUpdateStatusReason(one.getApplyUpdateStatusReason());
                    counterUpgradeDetailsVo.setApplyUser(one.getApplyUser());
                    counterUpgradeDetailsVo.setApplyUserNo(one.getApplyUserNo());
                    counterUpgradeDetailsVo.setApproveUser(one.getApproveUser());
                    counterUpgradeDetailsVo.setApproveUserNo(one.getApproveUserNo());
                    if (ObjectUtils.isNotEmpty(one.getApplyDate())) {
                        counterUpgradeDetailsVo.setApplyDate(DateUtils.parseLocalDateTimeToString(one.getApplyDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                    }
                    if (ObjectUtils.isNotEmpty(one.getApproveDate())) {
                        counterUpgradeDetailsVo.setApproveDate(DateUtils.parseLocalDateTimeToString(one.getApproveDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                    }
                    productBaseVo = counterUpgradeDetailsVo;
                }
                Optional<AoMergeRefundOrderInfo> first = refundOrderList.stream().filter(reod -> reod.getAoMergeSuborderInfoId().equals(suborder.getId())).findFirst();
                if (first.isPresent()) {
                    AoMergeRefundOrderInfo aoMergeRefundOrderInfo = first.get();
                    productBaseVo.setApproveUserDepartment(aoMergeRefundOrderInfo.getApproveUserDepartment());
                    if (StringUtils.isNotEmpty(aoMergeRefundOrderInfo.getApproveUserNo())) {
                        productBaseVo.setRefundApproveUser(aoMergeRefundOrderInfo.getApproveUserNo() + "-" + aoMergeRefundOrderInfo.getApproveUserName());
                    }
                    if (ObjectUtils.isNotEmpty(aoMergeRefundOrderInfo.getRefundSendTime())) {
                        productBaseVo.setRefundEndTime(DateUtils.parseLocalDateTimeToString(aoMergeRefundOrderInfo.getRefundSendTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                    }
                    productBaseVo.setRefundSause(aoMergeRefundOrderInfo.getRefundSause());
                    productBaseVo.setRefundExplain(aoMergeRefundOrderInfo.getRefundExplain());
                    productBaseVo.setRefundStartTime(DateUtils.parseLocalDateTimeToString(aoMergeRefundOrderInfo.getCreateDate(),DateUtils.YYYY_MM_DD_HH_MM_SS));
                }
                productBaseVo.setSubOrderId(suborder.getId());
                productBaseVo.setSubOrderNo(suborder.getSuborderOrderNo());
                productBaseVo.setOrderType(suborder.getOrderType());
                productBaseVo.setProductPrice(suborder.getPracticalOrderPrice().toString());
                productBaseVo.setCurrencySpecies(payOrder.getCurrencySpecies());
                productBaseVo.setCurrencySpeciesName(payOrder.getCurrencySpeciesName());
                productBaseVo.setPayStatus(suborder.getSuborderOrderPayStatus());
                productBaseVo.setSuborderOrderStatus(suborder.getSuborderOrderStatus());

                //发票
                AoMergeSubOrderJoinInvoiceInfo mergeSubOrderJoinInvoiceInfo = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery()
                        .eq(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, suborder.getId())
                        .eq(AoMergeSubOrderJoinInvoiceInfo::getMergeOrderId, suborder.getAoMergeOrderInfoId())
                        .one();
                    productBaseVo.setHaveInvoice("0");
                if(mergeSubOrderJoinInvoiceInfo!=null){
                    productBaseVo.setHaveInvoice("1");
                    productBaseVo.setSeqNo(mergeSubOrderJoinInvoiceInfo.getSeqNo());
                    AoMergeInvoiceInfo invoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                            .eq(AoMergeInvoiceInfo::getSeqNo, mergeSubOrderJoinInvoiceInfo.getSeqNo())
                            .one();
                    productBaseVo.setInvoiceTime(invoiceInfo.getInvoiceTime());
                }

                paxDetails.getSubOrderList().add(productBaseVo);
            }
            orderListVo.getOrderPaxList().add(paxDetails);
        }
        return orderListVo;
    }

    @Override
    @EncryptMethod
    public PsiPage<AoMergeOrderManageVo> getMergeOrderInfo(AoMergeOrderManageDto dto) {
        PsiPage<AoMergeOrderManageVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        PsiPage<AoMergeOrderManageVo> mergeOrderInfo = baseMapper.getMergeOrderInfo(page, dto);
        for (AoMergeOrderManageVo mele : mergeOrderInfo.getRecords()) {
            dto.setMergeOrderId(mele.getMergeOrderId());
            List<AoMergeOrderManageVo.AoMergeSubOrderManageVo> subOrderList = getSubOrderList(dto);
            mele.setSubOrderList(subOrderList);
        }
        return mergeOrderInfo;
    }

    @Override
    public List<AoMergeOrderManageVo> getMergeOrderInfoList(AoMergeOrderManageDto dto) {
        return baseMapper.getMergeOrderInfoList(dto);
    }

    @Override
    public List<AoMergeOrderManageVo.AoMergeSubOrderManageVo> getSubOrderList(AoMergeOrderManageDto dto) {
        List<AoMergeOrderManageVo.AoMergeSubOrderManageVo> subOrderList = baseMapper.getSubOrderList(dto);
        for (AoMergeOrderManageVo.AoMergeSubOrderManageVo ele : subOrderList){
           ele.setPaxNo(AesEncryptUtil.decryption(ele.getPaxNo()));
        }
        return subOrderList;
    }

    @Override
    public PsiPage<AoOrderInfoListByCpaxVo> getMergeOrderInfoByCpax(AoOrderInfoListByCpaxDto dto) {
        PsiPage<AoOrderInfoListByCpaxVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        PaxPayLoginInfo paxPayLoginInfo = psiUserUtils.getPaxPayLoginInfo();
        String paxNo = AesEncryptUtil.encrypt(paxPayLoginInfo.getIdNo(), ",");
        PsiPage<AoOrderInfoListByCpaxVo> mergeOrderInfoByCpax = baseMapper.getMergeOrderInfoByCpax(page, dto.getInvoice(), dto.getParam(), paxNo);
        for (AoOrderInfoListByCpaxVo ele : mergeOrderInfoByCpax.getRecords()){
            if("1".equals(ele.getVersions())){
                String s = AoUtils.pointsToYuan(ele.getOrderPrice());
                ele.setOrderPrice(s);
            }
        }

        return page;
    }


    @Override
    public   AoOrderDetailsByCpaxVo orderDetailsByCpax(String orderId){

        AoOrderDetailsByCpaxVo aoOrderDetailsByCpaxVo = new AoOrderDetailsByCpaxVo();
        AoMergeOrderInfo mergeOrder = this.getById(orderId);
        aoOrderDetailsByCpaxVo.setMergeOrderId(orderId);
        aoOrderDetailsByCpaxVo.setMergeOrderNo(mergeOrder.getOrderNo());
        List<AoMergeSuborderInfo> subOrderList = aoMergeSuborderInfoService.lambdaQuery().eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, orderId)
                .list();
        AoMergePayOrderInfo pay = aoMergePayOrderInfoService.getById(mergeOrder.getOrderPayId());
        List<AoMergeRefundOrderInfo> refundOrderInfoList = aoMergeRefundOrderInfoService.lambdaQuery().eq(AoMergeRefundOrderInfo::getAoMergeOrderInfoId, orderId).list();

        switch (pay.getPayType()){
            case "0":aoOrderDetailsByCpaxVo.setPayType("微信");break;
            case "1":aoOrderDetailsByCpaxVo.setPayType("易宝");break;
            case "2":aoOrderDetailsByCpaxVo.setPayType("支付宝");break;
            case "3":aoOrderDetailsByCpaxVo.setPayType("现金");break;
            case "4":aoOrderDetailsByCpaxVo.setPayType("pos机");break;
            case "5":aoOrderDetailsByCpaxVo.setPayType("worldPay");break;
            default:break;
        }
        if(ObjectUtils.isNotEmpty(pay.getPayTime())){
            aoOrderDetailsByCpaxVo.setPayTime(DateUtils.parseLocalDateTimeToString(pay.getPayTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        aoOrderDetailsByCpaxVo.setPayPrice( AoUtils.pointsToYuan(pay.getPrice().toString()));
        if (refundOrderInfoList.size() > 0) {

            List<AoMergeRefundOrderInfo> collect = refundOrderInfoList.stream().filter(ele -> ele.getRefundStatus().equals(AoMergeCons.SUBORDER_REFUND_STATUS_SUCCESS)).collect(Collectors.toList());
            if (collect.size() > 0) {
                String refundPrice = collect.stream().map(AoMergeRefundOrderInfo::getRefundPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal("100")).toString();
                aoOrderDetailsByCpaxVo.setRefundPrice(refundPrice);
            } else {

                aoOrderDetailsByCpaxVo.setRefundPrice("0");
            }
        } else {
            aoOrderDetailsByCpaxVo.setRefundPrice("0");
        }
        String receivePrice = new BigDecimal(pay.getPrice().toString()).divide(new BigDecimal("100")).subtract(new BigDecimal(aoOrderDetailsByCpaxVo.getRefundPrice())).toString();
        aoOrderDetailsByCpaxVo.setReceivePrice(receivePrice);
        SysAirportInfoEntity servicePort = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, mergeOrder.getServicePort())
                .eq(SysAirportInfoEntity::getIsUse, "0")
                .one();
        aoOrderDetailsByCpaxVo.setServicePort(servicePort.getAirportName()+mergeOrder.getServicePort());
        aoOrderDetailsByCpaxVo.setCurrencySpecies(pay.getCurrencySpecies());
        aoOrderDetailsByCpaxVo.setCurrencySpeciesName(pay.getCurrencySpeciesName());

        Map<String, List<AoMergeSuborderInfo>> collect = subOrderList.stream().collect(Collectors.groupingBy(AoMergeSuborderInfo::getPayPaxId));
        Iterator<Map.Entry<String, List<AoMergeSuborderInfo>>> iterator = collect.entrySet().iterator();
        while (iterator.hasNext()){
            AoOrderDetailsByCpaxVo.PaxInfo paxInfo = new AoOrderDetailsByCpaxVo.PaxInfo();
            Map.Entry<String, List<AoMergeSuborderInfo>> next = iterator.next();
            List<AoMergeSuborderInfo> value = next.getValue();
            AoMergeSuborderInfo suborderInfo = value.get(0);
            paxInfo.setPaxName(suborderInfo.getPaxName());
            paxInfo.setPaxNo(AesEncryptUtil.decryption(suborderInfo.getPaxNo()));
            paxInfo.setFlightDate(DateUtils.parseLocalDateTimeToString(suborderInfo.getStd(),DateUtils.YYYY_MM_DD_HH_MM_SS));
            paxInfo.setFlightNo(suborderInfo.getFlightNo());
            SysAirportInfoEntity org = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, suborderInfo.getOrg())
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            SysAirportInfoEntity dst = sysAirportInfoService.lambdaQuery().eq(SysAirportInfoEntity::getCode, suborderInfo.getDst())
                    .eq(SysAirportInfoEntity::getIsUse, "0")
                    .one();
            paxInfo.setSegment(org.getCode()+"("+org.getAirportName()+")-"+dst.getCode()+"("+dst.getAirportName()+")");
            String paxProductPrice = value.stream().map(AoMergeSuborderInfo::getPracticalOrderPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
            paxInfo.setPaxProductPrice(paxProductPrice);

            String paxRefundPrice = value.stream().filter(ele -> ele.getSuborderOrderStatus().equals(AoMergeCons.SUBORDER_PAY_STATUS_REFUND))
                    .map(AoMergeSuborderInfo::getPracticalOrderPrice).reduce(BigDecimal.ZERO, BigDecimal::add).toString();
            paxInfo.setPaxRefundPrice(paxRefundPrice);


            List<AoMergeSubOrderJoinInvoiceInfo> mergeSubOrderJoinInvoiceInfoList = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery()
                    .eq(AoMergeSubOrderJoinInvoiceInfo::getSuborderOrderId, suborderInfo.getId())
                    .eq(AoMergeSubOrderJoinInvoiceInfo::getMergeOrderId, suborderInfo.getAoMergeOrderInfoId())
                    .list();
            paxInfo.setHaveInvoice("0");
            if(mergeSubOrderJoinInvoiceInfoList !=null && mergeSubOrderJoinInvoiceInfoList.size()>0){
               AoMergeSubOrderJoinInvoiceInfo mergeSubOrderJoinInvoiceInfo = mergeSubOrderJoinInvoiceInfoList.get(0);
                paxInfo.setHaveInvoice("1");
                paxInfo.setSeqNo(mergeSubOrderJoinInvoiceInfo.getSeqNo());
                AoMergeInvoiceInfo invoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                        .eq(AoMergeInvoiceInfo::getSeqNo, mergeSubOrderJoinInvoiceInfo.getSeqNo())
                        .one();
                paxInfo.setInvoiceTitle(invoiceInfo.getInvoiceTitle());
                paxInfo.setTaxNo(invoiceInfo.getTaxNo());

                List<String> subIdList = mergeSubOrderJoinInvoiceInfoList.stream().map(x -> x.getSuborderOrderId()).collect(Collectors.toList());
                List<AoMergeSuborderInfo> invoiceSub = aoMergeSuborderInfoService.lambdaQuery().in(AoMergeSuborderInfo::getId, subIdList)
                        .list();
                String invoicePaxName = invoiceSub.stream().map(x -> x.getPaxName()).distinct().collect(Collectors.joining(","));
                paxInfo.setInvoicePaxName(invoicePaxName);
                paxInfo.setInvoicePrice(invoiceInfo.getInvoicePrice());
                paxInfo.setInvoiceTime(DateUtils.parseLocalDateTimeToString(invoiceInfo.getCreateTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
            }


            for (AoMergeSuborderInfo ele : value){
                //一人多座
                if (AoMergeCons.ORDER_TYPE_CODE_MANY_SEATS.equals(ele.getOrderType())) {
                    AoOrderDetailsByCpaxVo.ManySeatsDetailsVo manySeatsDetailsVo = new AoOrderDetailsByCpaxVo.ManySeatsDetailsVo();
                    AoMergeManySeats one = aoMergeManySeatsService.lambdaQuery()
                            .eq(AoMergeManySeats::getAoMergeSuborderInfoId, ele.getId())
                            .one();
                    manySeatsDetailsVo.setSeatsNumber(one.getSeatsNumber());
                    manySeatsDetailsVo.setProductPrice(ele.getPracticalOrderPrice().toString());
                    manySeatsDetailsVo.setStatus(ele.getSuborderOrderStatus());
                    manySeatsDetailsVo.setSubMergeOrderId(one.getId());
                    manySeatsDetailsVo.setSubMergeOrderNo(ele.getSuborderOrderNo());
                    manySeatsDetailsVo.setOrderType(ele.getOrderType());
                    paxInfo.getProductInfoVo().add(manySeatsDetailsVo);
                }
                //现场座位销售(选座)
                if (AoMergeCons.ORDER_TYPE_CODE_SCENE_SEATS.equals(ele.getOrderType())) {
                    AoOrderDetailsByCpaxVo.SceneSeatsDetailsVo sceneSeatsDetailsVo = new AoOrderDetailsByCpaxVo.SceneSeatsDetailsVo();
                    AoMergeSceneSeats one = aoMergeSceneSeatsService.lambdaQuery().eq(AoMergeSceneSeats::getAoMergeSuborderInfoId, ele.getId()).one();
                    sceneSeatsDetailsVo.setSeatsType(one.getSeatsType());
                    sceneSeatsDetailsVo.setProductPrice(ele.getPracticalOrderPrice().toString());
                    sceneSeatsDetailsVo.setStatus(ele.getSuborderOrderStatus());
                    sceneSeatsDetailsVo.setSubMergeOrderId(one.getId());
                    sceneSeatsDetailsVo.setSubMergeOrderNo(ele.getSuborderOrderNo());
                    sceneSeatsDetailsVo.setOrderType(ele.getOrderType());
                    paxInfo.getProductInfoVo().add(sceneSeatsDetailsVo);

                }

                //逾重行李
                if (AoMergeCons.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
                    AoOrderDetailsByCpaxVo.OverweightBkgDetailsVo overweightBkgDetailsVo = new AoOrderDetailsByCpaxVo.OverweightBkgDetailsVo();
                   AoMergeOverweightBkg one = aoMergeOverweightBkgService.lambdaQuery().eq(AoMergeOverweightBkg::getAoMergeSuborderInfoId, ele.getId()).one();
                    overweightBkgDetailsVo.setSubMergeOrderId(one.getId());
                    overweightBkgDetailsVo.setSubMergeOrderNo(ele.getSuborderOrderNo());
                    overweightBkgDetailsVo.setOrderType(ele.getOrderType());
                    overweightBkgDetailsVo.setProductPrice(ele.getPracticalOrderPrice().toString());
                    overweightBkgDetailsVo.setStatus(ele.getSuborderOrderStatus());
                    List<AoMergeOverweightBkgChargeDetails> list = aoMergeOverweightBkgChargeDetailsService.lambdaQuery().eq(AoMergeOverweightBkgChargeDetails::getAoMergeOverweightBkgId, one.getId()).list();

                    for (AoMergeOverweightBkgChargeDetails dt : list) {
                        AoOrderDetailsByCpaxVo.OverweightBkgDetailsVo.AoMergeOverweightBkgChargeDetailVo chargeDetailVo = new AoOrderDetailsByCpaxVo.OverweightBkgDetailsVo.AoMergeOverweightBkgChargeDetailVo();
                        chargeDetailVo.setPrice(dt.getPrice().toString());
                        chargeDetailVo.setChargeItem(dt.getChargeItem());
                        chargeDetailVo.setNumber(dt.getNumber().toString());
                        chargeDetailVo.setChargeStandardExplain(dt.getChargeStandardExplain());
                        overweightBkgDetailsVo.getChargeDetails().add(chargeDetailVo);
                    }
                    paxInfo.getProductInfoVo().add(overweightBkgDetailsVo);
                }
                //贵宾室
                if (AoMergeCons.ORDER_TYPE_CODE_VIP.equals(ele.getOrderType())) {
                    AoOrderDetailsByCpaxVo.ViproomItemSellDetailsVo viproomItemSellDetailsVo = new AoOrderDetailsByCpaxVo.ViproomItemSellDetailsVo();
                    AoMergeViproomItemSell one = aoMergeViproomItemSellService.lambdaQuery().eq(AoMergeViproomItemSell::getAoMergeSuborderInfoId, ele.getId()).one();
                    viproomItemSellDetailsVo.setItemName(one.getItemName());
                    viproomItemSellDetailsVo.setProductPrice(ele.getPracticalOrderPrice().toString());
                    viproomItemSellDetailsVo.setStatus(ele.getSuborderOrderStatus());
                    viproomItemSellDetailsVo.setSubMergeOrderId(one.getId());
                    viproomItemSellDetailsVo.setSubMergeOrderNo(ele.getSuborderOrderNo());
                    viproomItemSellDetailsVo.setOrderType(ele.getOrderType());
                    paxInfo.getProductInfoVo().add(viproomItemSellDetailsVo);

                }
                //柜台升舱
                if (AoMergeCons.ORDER_TYPE_CODE_UPGRADE.equals(ele.getOrderType())) {
                    AoOrderDetailsByCpaxVo.CounterUpgradeDetailsVo counterUpgradeDetailsVo = new AoOrderDetailsByCpaxVo.CounterUpgradeDetailsVo();
                    AoMergeCounterUpgrade one = aoMergeCounterUpgradeService.lambdaQuery().eq(AoMergeCounterUpgrade::getAoMergeSuborderInfoId, ele.getId()).one();
                    counterUpgradeDetailsVo.setNewSeat(one.getNewSeat());
                    counterUpgradeDetailsVo.setProductPrice(ele.getPracticalOrderPrice().toString());
                    counterUpgradeDetailsVo.setStatus(ele.getSuborderOrderStatus());
                    counterUpgradeDetailsVo.setSubMergeOrderId(one.getId());
                    counterUpgradeDetailsVo.setSubMergeOrderNo(ele.getSuborderOrderNo());
                    counterUpgradeDetailsVo.setOrderType(ele.getOrderType());
                    paxInfo.getProductInfoVo().add(counterUpgradeDetailsVo);
                }
            }

            aoOrderDetailsByCpaxVo.getPaxInfo().add(paxInfo);
        }

        return aoOrderDetailsByCpaxVo;
    }

    @Override
    public List<AoMergeOrderPaxVo> orderPaxList(String mergeOrderNo) {
        LambdaQueryChainWrapper<AoMergeOrderInfo> wrapper = this.lambdaQuery();
        wrapper.eq(AoMergeOrderInfo::getOrderNo,mergeOrderNo);
        AoMergeOrderInfo mergeOrderInfo = wrapper.one();
        if (mergeOrderInfo == null) {
            AoMergePayOrderInfo one = aoMergePayOrderInfoService.getOne(Wrappers.<AoMergePayOrderInfo>lambdaQuery()
                    .eq(AoMergePayOrderInfo::getBankOrderNo, mergeOrderNo));
            if (one == null) {
                return null;
            }
            mergeOrderInfo = this.getById(one.getAoMergeOrderInfoId());
            if (mergeOrderInfo == null) {
                return null;
            }
        }

        List<AoMergeSuborderInfo> list = aoMergeSuborderInfoService.lambdaQuery()
                .eq(AoMergeSuborderInfo::getAoMergeOrderInfoId, mergeOrderInfo.getId())
                .list();
        List<String> ids = list.stream().map(AoMergeSuborderInfo::getId).collect(Collectors.toList());
        Map<String, String> paxerPhones = paxerPhoneUtil.getPaxersPhones(ids);

        List<AoMergeOrderPaxVo> paxVoList = new ArrayList<>();
        for(AoMergeSuborderInfo suborderInfo:list){
            AoMergeOrderPaxVo paxVo = new AoMergeOrderPaxVo();
            paxVo.setPassengerName(suborderInfo.getPaxName());
            paxVo.setCertificateID(AesEncryptUtil.decryption(suborderInfo.getPaxNo()));
            FltPassengerRealInfo realInfo = fltPassengerRealInfoMapper.selectById(suborderInfo.getPayPaxId());
            if(ObjectUtils.isNotEmpty(realInfo)){
                if("NI".equals(realInfo.getIdType())){
                    paxVo.setCertificateType("身份证");
                }else if("PSPT".equals(realInfo.getIdType()) || "PP".equals(realInfo.getIdType())){
                    paxVo.setCertificateType("护照");
                }else if("ID".equals(realInfo.getIdType())){
                    paxVo.setCertificateType("军官证");
                }else {
                    paxVo.setCertificateType("其他");
                }

                paxVo.setBirthDate(realInfo.getBirthDate());
            }
            paxVo.setPhoneNumber(paxerPhones.get(suborderInfo.getId()));
            paxVoList.add(paxVo);
        }
        return paxVoList;
    }
}
