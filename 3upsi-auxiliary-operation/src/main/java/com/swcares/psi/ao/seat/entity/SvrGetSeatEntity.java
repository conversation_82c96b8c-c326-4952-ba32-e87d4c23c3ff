package com.swcares.psi.ao.seat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.seat.entity.SvrGetSeat <br>
 * Description：(机组占座表)<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022-3-3  16:38:10<br>
 * @version v1.0 <br>
 */

@Data
@TableName("ao_svr_get_seat")
public class SvrGetSeatEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键Id")
    private Long sgsId;

    @TableField("APPLY_ID")
    @ApiModelProperty("ID编号")
    private Long applyId;

    @TableField("APPLY_NO")
    @ApiModelProperty("申报编号")
    private String applyNo;

    @TableField("RECORD_NO")
    @ApiModelProperty("记录编号")
    private String recordNo;

    @TableField("FLIGHT_NO")
    @ApiModelProperty("航班号")
    private String flightNo;

    @TableField("FLIGHT_DATE")
    @ApiModelProperty("航班时间")
    private Date flightDate;

    @TableField("LOCAL_FLIGHT_DATE")
    @ApiModelProperty("当地航班时间")
    private Date localFlightDate;

    @TableField("ORG_CITY_AIRP")
    @ApiModelProperty("起飞航站")
    private String OrgCityAirp;

    @TableField("DST_CITY_AIRP")
    @ApiModelProperty("到达航站")
    private String DstCityAirp;

    @TableField("DEPART_CODE")
    @ApiModelProperty("部门编码")
    private String departCode;

    @TableField("PLAN_NUM")
    @ApiModelProperty("人数")
    private Integer planNum;

    @TableField("EMP_LIST")
    @ApiModelProperty("人员名单")
    private String empList;

    @TableField("STATUS")
    @ApiModelProperty("状态")
    private String status;

    @TableField("APPLY_EMP")
    @ApiModelProperty("申报人")
    private String applyEmp;

    @TableField("APPLY_TIME")
    @ApiModelProperty("申报时间")
    private Date applyTime;

    @TableField("REMARKS")
    @ApiModelProperty("备注")
    private String remarks;

    @TableField("CREATE_ID")
    @ApiModelProperty("创建者")
    private String createId;

    @TableField("CREATE_TIME")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField("UPDATE_ID")
    @ApiModelProperty("修改者")
    private String updateId;

    @TableField("UPDATE_TIME")
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @TableField("ACTUAL_NUM")
    @ApiModelProperty("实控人数")
    private Integer actualNum;

    @TableField("RESON")
    @ApiModelProperty("原因")
    private String reson;

    @TableField("PROFIT_REMARK")
    @ApiModelProperty("收益备注")
    private String profitRemark;

    @TableField("IS_INTER_LINE")
    @ApiModelProperty("是否为第二段国内")
    private Integer isInterLine;

    @TableField("APPLY_TYPE")
    @ApiModelProperty("申请类型")
    private String applyType;

    @TableField("REPLY_CONTENT")
    @ApiModelProperty("FOC返回结果")
    private String replyContent;
}

