package com.swcares.psi.aoMergeOrder.controller;


import com.swcares.psi.aoMergeOrder.service.IAoMergePayOrderInfoService;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 辅营合并总支付订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Api(tags = "合并总支付订单")
@RestController
@RequestMapping("/api/ao/merge/payOrder")
public class AoMergePayOrderInfoController {

    @Autowired
    IAoMergePayOrderInfoService aoMergePayOrderInfoService;

    @ApiOperation(value = "获取订单支付二维码")
    @GetMapping("getShowQrCode")
    public RenderResult<String> getShowQrCode(String orderNo) {
        return RenderResult.success(aoMergePayOrderInfoService.getShowQrCode(orderNo)) ;
    }

}

