### 模块名
+ `3upsi-register` 注册中心，重写阿里的nacos 账号 nacos 密码 nacos  
+ `3upsi-auth` auth 授权服务，如果需要了解授权，请学习
+ `3upsi-gatway` 网关 动态路由转发
+ `3upsi-common` 工共模块
    + `3upsi-common-core` 核心
    + `3upsi-common-log`  日志管理
    + `3upsi-common-mybatis` mybatis
    + `3upsi-common-security` 权限认证
    + `3upsi-common-swagger` 文档
+ `3upsi-monitor` 数据监控，发送邮件,监听堆栈，ELK 链路 账号psi psi
+ `3upsi-upms` 用户模块
    +  `3upsi-upms-api` 用户远程调用接口
    +  `3upsi-upms-biz` 用户模块  账号admin 123456
### 
   +  `db` mysql 数据库配置文件
   +  `img` 系统登录介绍和使用说明



### 注意事项
+ mysql5.7+
+ jdk 1.8+
+ redis 3.2以上
+ 环境架构 采用 spring2.,2+  全家桶 配置文件和注册中心采用阿里 Nacos
+ orm框架 mybatis-plus
### 地址
 + http://localhost:9999/swagger-ui.html `文档地址`
 + http://localhost:5001  `监控` `psi: psi`
 + http://localhost:8848/nacos/ `配置文件和注册中心` ` nacos : nacos`