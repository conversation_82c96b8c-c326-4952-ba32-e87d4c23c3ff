package com.swcares.psi.ao.seat.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.seat.dto.addRemarkDto <br>
 * Description：(编辑收益备注信息接口参数)<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date  2022-3-8 16:50:59 <br>
 * @version v1.0 <br>
 */
@Data
public class SaveRemarkDto {

    @ApiModelProperty("id")
    @NotEmpty(message = "参数异常")
    private List<String> sgsIds;

    @ApiModelProperty("收益备注")
    private String profitRemark;

}

