package com.swcares.psi.aoMergeOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSalesRecordDetailDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeComparisonGrowthInteriorVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeH5OrderListVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSalesRecordVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeSellDetailsVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付订单子订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeSuborderInfoMapper extends BaseMapper<AoMergeSuborderInfo> {
    List<AoMergeComparisonGrowthInteriorVo> sellNumberByDate(@Param("begin") String begin, @Param("end") String end , @Param("orderType") String orderType , @Param("userNo") String userNo );
    Integer sellOrderNumberByDate(@Param("begin") String begin,@Param("end") String end ,@Param("userNo") String userNo );
    PsiPage<AoMergeSalesRecordVo> salesRecordByUser(PsiPage<AoMergeSalesRecordVo> page, @Param("dto") AoMergeSalesRecordDetailDto dto, @Param("userNo") String userNo);
}
