package com.swcares.psi.ground.assurance.controller;

import com.swcares.psi.common.core.util.Excel2ResponseUtils;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.ExcelExportUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import com.swcares.psi.ground.assurance.dto.DpAssuranceOrderFlightStatsReportDto;
import com.swcares.psi.ground.assurance.dto.DpAssuranceOrderStatsReportDto;
import com.swcares.psi.ground.assurance.dto.QueryDpAssuranceOrderBillFeeReportPageReqDto;
import com.swcares.psi.ground.assurance.dto.QueryDpAssuranceOrderBusinessReportDto;
import com.swcares.psi.ground.assurance.service.GroundAssuranceOrderBillService;
import com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBillFeeReportVo;
import com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBusinessReportVo;
import com.swcares.psi.ground.assurance.vo.DpAssuranceOrderBusinessSummaryVo;
import com.swcares.psi.ground.assurance.vo.DpAssuranceOrderFlightStatsReportVo;
import com.swcares.psi.ground.assurance.vo.DpAssuranceOrderStatsReportVo;
import com.swcares.psi.ground.assurance.vo.DpAssuranceOrderStatsSumReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：DpAssuranceOrderReportController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/9/15 17:13
 * @version： v1.0
 */
@RestController
@RequestMapping("/api/ground/assurance/dpOrder/report")
@Api(tags = "航延保障单服务报表相关接口")
@Slf4j
public class DpAssuranceOrderReportController {
    private GroundAssuranceOrderBillService groundAssuranceOrderBillService;

    @Autowired
    public DpAssuranceOrderReportController(GroundAssuranceOrderBillService groundAssuranceOrderBillService){
        this.groundAssuranceOrderBillService=groundAssuranceOrderBillService;
    }

    @PostMapping("/bill/feePage")
    @ApiOperation(value = "财务报表列表分页接口")
    public RenderResult<PsiPage<DpAssuranceOrderBillFeeReportVo>> queryFeeReportPage(@RequestBody @Valid QueryDpAssuranceOrderBillFeeReportPageReqDto request) {
        PsiPage<DpAssuranceOrderBillFeeReportVo> result=groundAssuranceOrderBillService.queryFeeReportPage(request);
        return RenderResult.success(result);
    }

    @PostMapping("/bill/downloadFeeReport")
    @ApiOperation(value = "财务报表列表下载接口")
    public void downloadFeeReport(@RequestBody @Valid QueryDpAssuranceOrderBillFeeReportPageReqDto reqDto, HttpServletRequest request, HttpServletResponse response) {
        List<DpAssuranceOrderBillFeeReportVo> list=groundAssuranceOrderBillService.queryFeeReport(reqDto);
        Excel2ResponseUtils.writeExcel(list, "财务明细表" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), DpAssuranceOrderBillFeeReportVo.class);
    }

    @PostMapping("/business/page")
    @ApiOperation(value = "业务报表列表分页接口")
    public RenderResult<PsiPage<DpAssuranceOrderBusinessReportVo>> queryBusinessReportPage(@RequestBody @Valid QueryDpAssuranceOrderBusinessReportDto dto) {
        PsiPage<DpAssuranceOrderBusinessReportVo> result=groundAssuranceOrderBillService.queryBusinessReport(dto);
        return RenderResult.success(result);
    }

    @PostMapping("/business/summary")
    @ApiOperation(value = "业务报表汇总接口")
    public RenderResult<DpAssuranceOrderBusinessSummaryVo> queryBusinessReportSummary(@RequestBody @Valid QueryDpAssuranceOrderBusinessReportDto dto) {
        DpAssuranceOrderBusinessSummaryVo result = groundAssuranceOrderBillService.queryBusinessReportSummary(dto);
        return RenderResult.success(result);
    }

    @PostMapping("/business/download")
    @ApiOperation(value = "业务报表下载接口")
    public void downloadBusinessReport(@RequestBody @Valid QueryDpAssuranceOrderBusinessReportDto dto, HttpServletRequest request, HttpServletResponse response) {
        List<DpAssuranceOrderBusinessReportVo> list=groundAssuranceOrderBillService.downBusinessReport(dto);
        Excel2ResponseUtils.writeExcel(list, "补偿保障明细表" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), DpAssuranceOrderBusinessReportVo.class);

    }

    @PostMapping("/flight/page")
    @ApiOperation(value = "服务保障按航班统计查询")
    public RenderResult<PsiPage<DpAssuranceOrderFlightStatsReportVo>> dpAssuranceOrderFlightStatsReportQuery(@RequestBody @Valid DpAssuranceOrderFlightStatsReportDto dto) {
        PsiPage<DpAssuranceOrderFlightStatsReportVo> psiPage = groundAssuranceOrderBillService.dpAssuranceOrderFlightStatsReportPage(dto);
        return RenderResult.success(psiPage);
    }

    @PostMapping("/flight/download")
    @ApiOperation(value = "服务保障按航班统计下载")
    public void dpAssuranceOrderFlightStatsReportExport(@RequestBody @Valid DpAssuranceOrderFlightStatsReportDto dto, HttpServletResponse response) {
        List<DpAssuranceOrderFlightStatsReportVo> list = groundAssuranceOrderBillService.dpAssuranceOrderFlightStatsReportList(dto);
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "航班保障报表";
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");
            ExcelExportUtils.export(response.getOutputStream(), DpAssuranceOrderFlightStatsReportVo.class, list, AuthenticationUtil.getUserNo(), fileName);
        } catch (Exception e) {
            log.error("服务保障按航班统计下载出错！", e);
        }
    }

    @PostMapping("/stats/page")
    @ApiOperation(value = "服务保障按航班旅客类型服务项统计查询")
    public RenderResult<PsiPage<DpAssuranceOrderStatsReportVo>> dpAssuranceOrderStatsReportQuery(@RequestBody @Valid DpAssuranceOrderStatsReportDto dto) {
        PsiPage<DpAssuranceOrderStatsReportVo> psiPage = groundAssuranceOrderBillService.dpAssuranceOrderStatsReportPage(dto);
        return RenderResult.success(psiPage);
    }

    @PostMapping("/stats/download")
    @ApiOperation(value = "服务保障按航班旅客类型服务项统计下载")
    public void dpAssuranceOrderStatsReportExport(@RequestBody @Valid DpAssuranceOrderStatsReportDto dto, HttpServletResponse response) {
        List<DpAssuranceOrderStatsReportVo> list = groundAssuranceOrderBillService.dpAssuranceOrderStatsReportList(dto);
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "保障统计报表";
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".csv");
            ExcelExportUtils.export(response.getOutputStream(), DpAssuranceOrderStatsReportVo.class, list, AuthenticationUtil.getUserNo(), fileName);
        } catch (Exception e) {
            log.error("保障统计报表下载出错！", e);
        }
    }

    @PostMapping("/stats/sum/page")
    @ApiOperation(value = "服务保障按航班旅客类型服务项统计总计查询")
    public RenderResult<DpAssuranceOrderStatsSumReportVo> dpAssuranceOrderStatsSumReportQuery(@RequestBody @Valid DpAssuranceOrderStatsReportDto dto) {
        DpAssuranceOrderStatsSumReportVo vo = groundAssuranceOrderBillService.dpAssuranceOrderStatsSumReport(dto);
        return RenderResult.success(vo);
    }

    @PostMapping("/stats/sum/download")
    @ApiOperation(value = "服务保障按航班旅客类型服务项统计总计下载")
    public void dpAssuranceOrderStatsSumReportExport(@RequestBody @Valid DpAssuranceOrderStatsReportDto dto, HttpServletResponse response) {
        DpAssuranceOrderStatsSumReportVo vo = groundAssuranceOrderBillService.dpAssuranceOrderStatsSumReport(dto);
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "保障统计汇总报表";
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".csv");
            ExcelExportUtils.export(response.getOutputStream(), DpAssuranceOrderStatsSumReportVo.ServiceType.class, vo.getServiceTypes(), AuthenticationUtil.getUserNo(), fileName);
        } catch (Exception e) {
            log.error("保障统计汇总报表下载出错！", e);
        }
    }

}
