package com.swcares.psi.ao.midplatform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 辅营中台预付费行李表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AoMidplatformOrderLuggage对象", description="辅营中台预付费行李表")
public class AoMidplatformOrderLuggage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    private String id;

    @ApiModelProperty(value = "订单ID")
    @TableField("ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "订单号")
    @TableField("ORDER_NO")
    private String orderNo;

    @ApiModelProperty(value = "EMD票号")
    @TableField("EMD_NO")
    private String emdNo;

    @ApiModelProperty(value = "行李额单位")
    @TableField("LUG_UNIT")
    private String lugUnit;

    @ApiModelProperty(value = "行李个数")
    @TableField("LUG_UNIT_COUNT")
    private String lugUnitCount;


}
