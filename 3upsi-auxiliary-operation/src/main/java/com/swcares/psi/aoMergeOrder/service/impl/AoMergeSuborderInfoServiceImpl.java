package com.swcares.psi.aoMergeOrder.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.dto.AoMergeSalesRecordDetailDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeSuborderInfoMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.aoMergeOrder.vo.*;
import com.swcares.psi.combine.user.impl.PsiUser;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.PsiPage;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 合并支付订单子订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeSuborderInfoServiceImpl extends ServiceImpl<AoMergeSuborderInfoMapper, AoMergeSuborderInfo> implements IAoMergeSuborderInfoService {


    @Override
    public AoMergeComparisonGrowthVo getComparisonGrowth() {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String currentUser = authentication.getUsername();
        LocalDate now = LocalDate.now();
        AoMergeComparisonGrowthVo comparisonGrowthVo = new AoMergeComparisonGrowthVo();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //今天销售额
        String format = now.format(formatter);
        List<AoMergeComparisonGrowthInteriorVo> toDay = baseMapper.sellNumberByDate(format, format,null, currentUser);
        Integer toDayBeforeOrder = baseMapper.sellOrderNumberByDate(format, format, currentUser);
        comparisonGrowthVo.setTodayOrderNum(toDayBeforeOrder);
        comparisonGrowthVo.setToday(toDay);
        //最近7天销售额
        LocalDate sevenDayBefore = now.plusDays(-6L);
        String sevenDayBeforeStr = sevenDayBefore.format(formatter);
        List<AoMergeComparisonGrowthInteriorVo> sevenDay = baseMapper.sellNumberByDate(sevenDayBeforeStr, format,null, currentUser);
        comparisonGrowthVo.setSevenDay(sevenDay);
        //最近七天销售订单数
        Integer sevenDayBeforeOrder = baseMapper.sellOrderNumberByDate(sevenDayBeforeStr, format, currentUser);
        comparisonGrowthVo.setSevenDayOrderNum(sevenDayBeforeOrder);
        //最近七天销售额增长比  与上一个7天对比
//        LocalDate eightDayBefore = now.plusDays(-7L);
//        String eightDayBeforeStr = eightDayBefore.format(formatter);
//        LocalDate thirteenDayBefore = now.plusDays(-13L);
//        String thirteenDayBeforeStr = thirteenDayBefore.format(formatter);
//        Integer thirteenDay = aoOrderInfoMapper.sellNumberByDate(thirteenDayBeforeStr, eightDayBeforeStr, currentUser);
//        String sevenDayGrowth = growthConversion(sevenDayBeforeOrder, thirteenDay);
//        comparisonGrowthVo.setSevenDayOrderGrowth(sevenDayGrowth);
        //最近三十天销售额
        String thirtyDayBeforeStr = now.plusDays(-29L).format(formatter);
        List<AoMergeComparisonGrowthInteriorVo> thirtyDay = baseMapper.sellNumberByDate(thirtyDayBeforeStr, format,null, currentUser);
        comparisonGrowthVo.setThirtyDay(thirtyDay);
        //最近三十天销售订单数
        Integer thirteenDayOrder = baseMapper.sellOrderNumberByDate(thirtyDayBeforeStr, format, currentUser);
        comparisonGrowthVo.setThirtyDayOrderNum(thirteenDayOrder);
        //最近三十天销售额增长比  与上一个三十天对比
//        LocalDate thirtyOneDayBefore = now.plusDays(-30L);
//        String thirtyOneDayBeforeStr = thirtyOneDayBefore.format(formatter);
//        LocalDate sixtyDayBefore = now.plusDays(-59L);
//        String sixtyDayBeforeStr = sixtyDayBefore.format(formatter);
//        HashMap sixtyDay = aoOrderInfoMapper.sellNumberByDate(sixtyDayBeforeStr, thirtyOneDayBeforeStr, currentUser);
//        String thirtyDayGrowth = growthConversion(thirtyDay, sixtyDay);
//        comparisonGrowthVo.setThirtyDayOrderGrowth(thirtyDayGrowth);
        //半年的销售额  默认182天
        String halfYear = now.plusDays(-181L).format(formatter);
        List<AoMergeComparisonGrowthInteriorVo> halfYearDay = baseMapper.sellNumberByDate(halfYear, format,null, currentUser);
        comparisonGrowthVo.setSixMonth(halfYearDay);
        //最近半年的销售订单数  默认182天
        Integer halfYearOrder = baseMapper.sellOrderNumberByDate(halfYear, format, currentUser);
        comparisonGrowthVo.setSixMonthOrderNum(halfYearOrder);
        //最近半年销售额增长比  与上一个半年对比
//        LocalDate halfYearDayBefore = now.plusDays(-182L);
//        String halfYearBeforeStr = halfYearDayBefore.format(formatter);
//        LocalDate oneYearBefore = now.plusDays(-365L);
//        String oneYearBeforeStr = oneYearBefore.format(formatter);
//        Integer oneYearDay = aoOrderInfoMapper.sellNumberByDate(oneYearBeforeStr, halfYearBeforeStr, currentUser);
//        String halfYearGrowth = growthConversion(halfYearOrder, oneYearDay);
//        comparisonGrowthVo.setSixMonthOrderGrowth(halfYearGrowth);
        return comparisonGrowthVo;
    }

    @Override
    public AoMergeSellDetailsVo salesRecordByUser(AoMergeSalesRecordDetailDto dto) {
        PsiUser authentication = (PsiUser) AuthenticationUtil.getAuthentication();
        String currentUser = authentication.getUsername();
        AoMergeSellDetailsVo aoMergeSellDetailsVo = new AoMergeSellDetailsVo();
        PsiPage<AoMergeSalesRecordVo> page = new PsiPage<>(dto.getCurrent(), dto.getPageSize());
        PsiPage<AoMergeSalesRecordVo> aoMergeSalesRecordVoPsiPage = baseMapper.salesRecordByUser(page, dto, currentUser);
        List<AoMergeComparisonGrowthInteriorVo> aoMergeComparisonGrowthInteriorVos = baseMapper.sellNumberByDate(dto.getBegin(), dto.getEnd(),dto.getOrderType(), currentUser);
        aoMergeSellDetailsVo.setDetalis(aoMergeSalesRecordVoPsiPage);
        aoMergeSellDetailsVo.setSellSumPrice(aoMergeComparisonGrowthInteriorVos);
        return aoMergeSellDetailsVo;
    }
}
