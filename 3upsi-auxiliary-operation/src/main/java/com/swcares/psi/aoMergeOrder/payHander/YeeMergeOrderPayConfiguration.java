package com.swcares.psi.aoMergeOrder.payHander;

import com.swcares.psi.config.YeeBaseConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/10/19 16:44
 */
@Slf4j
@Data
public class YeeMergeOrderPayConfiguration extends YeeBaseConfig {


}
