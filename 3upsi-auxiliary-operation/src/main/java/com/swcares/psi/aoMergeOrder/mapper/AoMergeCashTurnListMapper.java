package com.swcares.psi.aoMergeOrder.mapper;

import com.swcares.psi.aoMergeOrder.entity.AoMergeCashTurnList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.vo.CashTurnOrderTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 合并支付现金缴纳清单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Mapper
public interface AoMergeCashTurnListMapper extends BaseMapper<AoMergeCashTurnList> {
    List<CashTurnOrderTaskVo> getCashTurnOrderAll(@Param("createTime") String createTime);

}
