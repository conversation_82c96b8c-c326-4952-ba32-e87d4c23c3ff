package com.swcares.psi.irregular.flight.vo;

import com.swcares.psi.common.utils.encryption.Encryption;
import lombok.Data;

/**
 * ClassName：com.swcares.psi.flight.vo <br>
 * Description：代领审核列表信息展示VO <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月16日 10:17 <br>
 * @version v1.0 <br>
 */
@Data
public class ActAuditInfoVo {
    /**
     * 申领单号
     */
    private String applyCode;
    /**
     * 审核状态(0领取中、1已领取、2领取失败)
     */
    private String status;

    /**
     * 支付状态(0未支付,1已支付,2支付失败)
     */
    private String payStatus;
    /**
     * 申请时间
     */
    private String createTime;
    /**
     * 申请人
     */
    private String applyUser;
    /**
     * 代领人数
     */
    private String applyCustNum;
    /**
     * 申领金额
     */
    private String transAmount;
    /**
     * 联系电话
     */
    @Encryption
    private String telephone;
    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 领取方式(0微信，1银联,2支付宝,3优惠券,4现金,5 易宝)
     */
    private String getMoneyWay;
    /**
     * 领取账户
     */
    private String getMoneyAccount;
    /**
     * 到账时间
     */
    private String receiveTime;
    /**
     * 开户行
     */
    private String openBank;
    /**
     * 是否快速支付
     */
    private String quickPay;
    /**
     * 2未通过1通过0待审核
     */
    private String applyStatus;

    /**
     * 不正常航班补偿0、异常行李1、超售旅客2
     */
    private String payType;
}