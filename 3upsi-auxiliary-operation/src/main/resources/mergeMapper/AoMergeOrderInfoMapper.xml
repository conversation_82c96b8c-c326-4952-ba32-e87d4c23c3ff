<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeOrderInfoMapper">


    <select id="vaidatePayOrderList" resultType="com.swcares.psi.aoMergeOrder.vo.VaidatePayOrderVo">
            select
             amoi.id as orderId,
             amoi.ORDER_NO,
             ampoi.id as payOrderId,
             ampoi.BANK_ORDER_NO as bankOrderNo,
             amoi.CREATE_DATE,
             ampoi.PAY_TYPE
             from   ao_merge_order_info amoi
             left  join  ao_merge_pay_order_info  ampoi on ampoi.id=amoi.ORDER_PAY_ID and ampoi.STATUS='1'
             where ampoi.PAY_TYPE in('0','1','2')
             and  ampoi.CREATE_DATE  <![CDATA[ >=  ]]>  DATE_FORMAT( #{date}, '%Y-%m-%d %H:%i:%s' )
             and amoi.MERGE_ORDER_STATUS='0'

    </select>

    <select id="cashTurnQuery" resultType="com.swcares.psi.aoMergeOrder.vo.CashTurnQueryVo">
        select
        megerOrder.id as mergeOrderId,
        suborder.id as subOrderId,
        suborder.PRACTICAL_ORDER_PRICE as subPrice
        from ao_merge_order_info megerOrder

        left join ao_merge_suborder_info suborder on megerOrder.id=suborder.AO_MERGE_ORDER_INFO_ID
        inner join ao_merge_pay_order_info ampoi on ampoi.id=megerOrder.ORDER_PAY_ID
        where megerOrder.TURN_STATUS='0' and megerOrder.MERGE_ORDER_STATUS in ('1','2')
        and suborder.SUBORDER_ORDER_STATUS ='1' and suborder.SUBORDER_ORDER_PAY_STATUS='1'
        and ampoi.PAY_TYPE='3'
        and megerOrder.CASH_RECEIVE_USER=#{userNo}
        and megerOrder.SERVICE_PORT=#{servicePort}
        and megerOrder.SERVICE_PORT_TYPE=#{servicePortType}
        and not exists (
        select a.id from ao_merge_sub_order_join_cash_return_pay a where a.MERGE_ORDER_ID = megerOrder.id
        )


        <if test="mergeOrderIdList != null and mergeOrderIdList.size() > 0">
            and megerOrder.ID in
            <foreach collection="mergeOrderIdList" item="ele" open="(" close=")" separator=",">
                #{ele}
            </foreach>

        </if>


    </select>


    <select id="h5OrderList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeH5OrderListVo">

select * from (

        select
        amoi.ID as mergeOrderId,
        (select count(p.id) from ao_merge_suborder_info p where p.AO_MERGE_ORDER_INFO_ID=amoi.id and INVOICE_JOIN_ID is  null ) as permitInvoice,
        amoi.ORDER_NO as orderNo,
        amoi.TURN_NUMBER as turnNumber,
        amoi.TURN_STATUS as turnStatus,
        amoi.MERGE_ORDER_STATUS as mergeOrderStatus,
        ampoi.PAY_TYPE as payType,
        CONCAT(get_city_name(amoi.SERVICE_PORT),"-", amoi.SERVICE_PORT) as servicePort,
        date_format( amoi.CREATE_DATE, '%Y-%m-%d') as createDate,
        ampoi.CURRENCY_SPECIES as currencySpecies,
        ampoi.CURRENCY_SPECIES_NAME as currencySpeciesName,
        ampoi.PRICE as price,
        if(
            (
            select count(amroi.id) from ao_merge_refund_order_info  amroi where amroi.REFUND_STATUS='3' and amroi.AO_MERGE_ORDER_INFO_ID=amoi.ID
            ) <![CDATA[ < ]]> 1
            ,
            ampoi.PRICE
            ,
            ampoi.PRICE- (
            select sum(REFUND_PRICE) from ao_merge_refund_order_info  amroi where amroi.REFUND_STATUS='3' and amroi.AO_MERGE_ORDER_INFO_ID=amoi.ID
            )
        )  as receivePrice,
        GROUP_CONCAT(DISTINCT amsi.ORDER_TYPE SEPARATOR ',')as orderTypeList,
        GROUP_CONCAT(DISTINCT amsi.ORDER_TYPE_NAME SEPARATOR ',')as orderTypeNameList,
        GROUP_CONCAT(DISTINCT amsi.PAX_NAME SEPARATOR ',')as paxNameList
        from ao_merge_order_info amoi
        inner join ao_merge_pay_order_info ampoi on amoi.ORDER_PAY_ID=ampoi.id
        left join ao_merge_suborder_info amsi on amoi.id=amsi.AO_MERGE_ORDER_INFO_ID
        <where>
            <if test="dto.orderType != null and dto.orderType != ''">
                find_in_set(amsi.ORDER_TYPE, #{dto.orderType})
            </if>

            <if test="dto.payType != null and dto.payType != ''">
                and find_in_set(ampoi.PAY_TYPE, #{dto.payType})
            </if>

            <if test="dto.payStatus != null and dto.payStatus != ''">
                and find_in_set(amsi.PAY_STATUS, #{dto.payStatus})
            </if>

            <if test="dto.orderStatus != null and dto.orderStatus != ''">
                and find_in_set(amoi.MERGE_ORDER_STATUS, #{dto.orderStatus})
            </if>

            <if test="dto.turnStatus != null and dto.turnStatus != ''">
                and find_in_set(amoi.TURN_STATUS, #{dto.turnStatus})
            </if>

            <if test="dto.servicePortCode != null and dto.servicePortCode != ''">
                and amoi.SERVICE_PORT = #{dto.servicePortCode}
            </if>
            <if test="dto.createUserOrNo != null and dto.createUserOrNo != ''">
                and (amoi.CREATE_NO like concat('%',#{dto.createUserOrNo},'%') or amoi.CREATE_USER like
                concat('%',#{dto.createUserOrNo},'%'))
            </if>


            <if test=" userNo != null and userNo != '' ">
                AND ( amoi.CREATE_NO=#{userNo}
                <!-- # 指定用户只能查看的数据-->
                <if test=" userId != null and userId != ''">
                    OR amoi.CREATE_NO in (
                    select DISTINCT(emy.TUNO) from data_permissions dp
                    LEFT JOIN user_role ur on dp.ROLE_ID=ur.ROLE_ID
                    LEFT JOIN user_role ur2 on ur2.ROLE_ID=dp.PERMISSIONS_ROLE_ID
                    LEFT JOIN employee emy on ur2.EMPLOYEE_ID=emy.id
                    where ur.EMPLOYEE_ID=#{userId}
                    )
                </if>
                )
            </if>

            <if test=" dto.orderStartDate != null and dto.orderStartDate != '' ">
                AND amoi.CREATE_DATE <![CDATA[ >= ]]> DATE_FORMAT(#{dto.orderStartDate},'%Y-%m-%d 00:00:00')
            </if>

            <if test=" dto.orderEndDate != null and dto.orderEndDate != '' ">
                AND amoi.CREATE_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test=" dto.currencySpecies != null and dto.currencySpecies != '' ">
                AND ampoi.CURRENCY_SPECIES = #{dto.currencySpecies}
            </if>

        </where>
        group by amoi.ID
        order by amoi.CREATE_DATE desc,ampoi.PAY_TIME desc
        ) temp
        where 1=1
         <if test="dto.param != null and dto.param != ''">
            and (
             temp.orderTypeNameList like concat('%',#{dto.param},'%')
            or temp.orderNo like concat('%',#{dto.param},'%')
            or temp.paxNameList like concat('%',#{dto.param},'%')
            )
        </if>
    </select>




    <select id="getMergeOrderInfo" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOrderManageVo">
        <include refid="mergeOrderSql"></include>
    </select>
    <select id="getMergeOrderInfoList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOrderManageVo">
        <include refid="mergeOrderSql"></include>
    </select>
    <sql id="mergeOrderSql">
        select
        amoi.id as mergeOrderId,
        (select count(p.id) from ao_merge_suborder_info p where p.AO_MERGE_ORDER_INFO_ID=amoi.id and INVOICE_JOIN_ID is  null ) as permitInvoice,
        CONCAT(get_city_name(amoi.SERVICE_PORT), '-',amoi.SERVICE_PORT) as servicePort,
        date_format( amoi.CREATE_DATE, '%Y-%m-%d') as orderDate,
        amoi.ORDER_NO as orderNo,
        case amoi.MERGE_ORDER_STATUS
        when '0' then '待支付'
        when '1' then '已完成'
        when '2' then '部分退款'
        when '3' then '已退款'
        when '4' then '已过期'
        when '5' then '已取消'
        else '支付中'
        end as mergeOrderStatus,
        ifnull(
        (
        select
        sum(subOrder.PRACTICAL_ORDER_PRICE)
        from ao_merge_suborder_info subOrder
        where subOrder.AO_MERGE_ORDER_INFO_ID=amoi.id
        ),
        0
        ) as  mergeOrderPayPrice,
        ifnull(
        (
        select
        sum(subOrder.PRACTICAL_ORDER_PRICE)
        from ao_merge_suborder_info subOrder
        where subOrder.AO_MERGE_ORDER_INFO_ID=amoi.id
        and subOrder.SUBORDER_ORDER_STATUS='4'
        ),
        0
        ) as mergeOrderRefundPrice,

        ifnull(
        (
        select
        sum(subOrder.PRACTICAL_ORDER_PRICE)
        from ao_merge_suborder_info subOrder
        where subOrder.AO_MERGE_ORDER_INFO_ID=amoi.id
        and subOrder.SUBORDER_ORDER_STATUS='1'
        ),
        0
        ) as mergeOrderPracticalPrice,
        case ampoi.PAY_TYPE
        when '0' then '微信'
        when '1' then '易宝'
        when '2' then '支付宝'
        when '3' then '现金'
        when '4' then 'pos机'
        when '5' then 'worldpay'
        end as payType,
        date_format(ampoi.PAY_TIME, '%Y-%m-%d %H:%i:%s') as payTime,
        CONCAT(amoi.CREATE_USER,'-',amoi.CREATE_NO )as createUser
        from ao_merge_suborder_info amsoi
        inner join  ao_merge_order_info amoi on amsoi.AO_MERGE_ORDER_INFO_ID=amoi.id
        inner join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
        <include refid="orderWhere"></include>
        group by amoi.ORDER_NO
        order by amoi.CREATE_DATE desc,ampoi.PAY_TIME desc
    </sql>
    <sql id="orderWhere">
        <where>
        1=1
            <if test="dto.servicePort != null and dto.servicePort !=''">
                and amoi.SERVICE_PORT=#{dto.servicePort}
            </if>

            <if test="dto.orderDateStart != null and dto.orderDateStart !=''">
                AND amoi.CREATE_DATE <![CDATA[ >= ]]> DATE_FORMAT(#{dto.orderDateStart},'%Y-%m-%d 00:00:00')
            </if>

            <if test=" dto.orderDateEnd != null and dto.orderDateEnd != '' ">
                AND amoi.CREATE_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{dto.orderDateEnd},'%Y-%m-%d 23:59:59')
            </if>

            <if test="dto.orderNo != null and dto.orderNo !=''">
                and amoi.ORDER_NO like CONCAT('%',#{dto.orderNo},'%')
            </if>

            <if test="dto.mergeOrderStatus != null and dto.mergeOrderStatus !=''">
                and find_in_set(amoi.MERGE_ORDER_STATUS, #{dto.mergeOrderStatus})
            </if>

            <if test="dto.payType != null and dto.payType !=''">
                and find_in_set(ampoi.PAY_TYPE, #{dto.payType})
            </if>

            <if test="dto.createUserOrNo != null and dto.createUserOrNo !=''">
                and (amoi.CREATE_USER like CONCAT('%',#{dto.createUserOrNo},'%') or amoi.CREATE_NO like
                CONCAT('%',#{dto.createUserOrNo},'%'))

            </if>






            <if test="dto.subOrderNo != null and dto.subOrderNo !=''">
                and amsoi.SUBORDER_ORDER_NO like CONCAT('%',#{dto.subOrderNo},'%')

            </if>

            <if test="dto.orderType != null and dto.orderType !=''">
                and find_in_set(amsoi.ORDER_TYPE, #{dto.orderType})

            </if>

            <if test="dto.paxName != null and dto.paxName !=''">
                and amsoi.PAX_NAME like CONCAT('%',#{dto.paxName},'%')
            </if>

            <if test="dto.suborderOrderStatus != null and dto.suborderOrderStatus !=''">
                and find_in_set(amsoi.SUBORDER_ORDER_STATUS, #{dto.suborderOrderStatus})
            </if>

            <if test="dto.suborderOrderPayStatus != null and dto.suborderOrderPayStatus !=''">
                and find_in_set(amsoi.SUBORDER_ORDER_PAY_STATUS, #{dto.suborderOrderPayStatus})
            </if>

            <if test="dto.paxNo != null and dto.paxNo !=''">
                and amsoi.PAX_NO = #{dto.paxNo}
            </if>

            <if test="dto.flightNo != null and dto.flightNo !=''">
                and amsoi.FLIGHT_NO like CONCAT('%',#{dto.flightNo},'%')
            </if>

            <if test="dto.flightStartDate != null and dto.flightStartDate !=''">
                AND amsoi.FLIGHT_DATE <![CDATA[ >= ]]> DATE_FORMAT(#{dto.flightStartDate},'%Y-%m-%d 00:00:00')
            </if>

            <if test=" dto.flightEndDate != null and dto.flightEndDate != '' ">
                AND amsoi.FLIGHT_DATE <![CDATA[ <= ]]> DATE_FORMAT(#{dto.flightEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="dto.org != null and dto.org !=''">
                and amsoi.ORG=#{dto.org}
            </if>

            <if test="dto.dst != null and dto.dst !=''">
                and amsoi.DST=#{dto.dst}
            </if>

            <if test="dto.flightType != null and dto.flightType !=''">
                and amsoi.FLIGHT_TYPE=#{dto.flightType}
            </if>

            <if test="dto.invoice != null and dto.invoice !=''">
                <choose>
                    <when test="dto.invoice == 0">
                        and amsoi.INVOICE_JOIN_ID is null
                    </when>
                    <when test="dto.invoice == 1">
                        and amsoi.INVOICE_JOIN_ID is not null
                    </when>
                </choose>
            </if>

        </where>
    </sql>



    <select id="getSubOrderList"
            resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeOrderManageVo$AoMergeSubOrderManageVo">
        <include refid="subEle"></include>

        inner join ao_merge_order_info amoi on amsoi.AO_MERGE_ORDER_INFO_ID=amoi.id
        inner join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
        <include refid="orderWhere"></include>
        <if test="dto.mergeOrderId != null and dto.mergeOrderId !=''">
            AND amsoi.AO_MERGE_ORDER_INFO_ID = #{dto.mergeOrderId}
        </if>
    </select>
    <sql id="subEle">
         select
        amsoi.SUBORDER_ORDER_NO as suborderNo,
        amsoi.SUBORDER_ORDER_STATUS as suborderOrderStatusCode,
          case amsoi.SUBORDER_ORDER_STATUS
            when '0' then '待支付'
            when '1' then '已完成'
            when '2' then '待退款'
            when '3' then '退款中'
            when '4' then '已退款'
            when '5' then '已过期'
            when '6' then '已取消'
            else '支付中'
          end as suborderOrderStatus,
        case amsoi.SUBORDER_ORDER_PAY_STATUS
            when '0' then '待支付'
            when '1' then '支付成功'
            when '2' then '支付失败'
            when '3' then '退款失败'
            when '4' then '退款成功'
            when '5' then '支付中'
        end as suborderOrderPayStatus,
        amsoi.PRACTICAL_ORDER_PRICE as practicalOrderPrice,
        amsoi.PAX_NAME as paxName,
        amsoi.PAX_NO as paxNo,
        date_format(amsoi.FLIGHT_DATE, '%Y-%m-%d')  as flightDate,
        amsoi.FLIGHT_NO as flightNo,
        amsoi.ORDER_TYPE as orderType,
         amsoi.ORDER_TYPE_NAME as orderTypeName,
        case  amsoi.FLIGHT_TYPE
            when 'I' then '国际'
            when 'D' then '国内'
            else '地区'
        end as flightType,
         CONCAT(get_city_name(amsoi.ORG), '-',amsoi.ORG) as org,
         CONCAT(get_city_name(amsoi.DST), '-',amsoi.DST) as dst,
        if( amsoi.INVOICE_JOIN_ID IS NULL or amsoi.INVOICE_JOIN_ID='' ,'未开票','已开票') as invoiceStatus
    from ao_merge_suborder_info  amsoi
    </sql>

    <select id="getMergeOrderInfoByCpax" resultType="com.swcares.psi.aoMergeOrder.vo.AoOrderInfoListByCpaxVo">
       select x.* from (
           (
            <include refid="oldAuxiliary"></include>
            )union
            (
            <include refid="mergeAuxiliary"></include>
            )
        ) x  order by x.dt  desc
    </select>

    <sql id="oldAuxiliary">
        select
            a.versions,
            a.servicePort,
            a.segment,
            a.flightDate,
            a.orderPrice,
            a.currencySpeciesName,
            a.payType,
            a.dt,
            a.orderId,
            a.orderNo,
            a.paxName,
            a.orderTypeName
         from (
                select
                '1' as versions,
                null as servicePort,
                CONCAT(get_city_name(aoi.ORG),aoi.ORG,'-',get_city_name(aoi.DST),aoi.DST) AS segment,
                date_format(aoi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
                aoi.ORDER_PRICE  as orderPrice,
                 case  aoi.CURRENCY_SPECIES
                    when 'CNY' then  concat(aoi.CURRENCY_SPECIES,'(人民币)')
                    else   concat(aoi.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=aoi.CURRENCY_SPECIES limit 1),')')
                end as currencySpeciesName,
                aoi.PAX_NAME as paxName,
                CASE
                    WHEN aoi.ORDER_TYPE_CODE = '1' THEN '一人多座'
                    WHEN aoi.ORDER_TYPE_CODE = '2' THEN '付费选座'
                    WHEN aoi.ORDER_TYPE_CODE = '3' THEN '逾重行李'
                    WHEN aoi.ORDER_TYPE_CODE = '4' THEN '贵宾室'
                    when aoi.ORDER_TYPE_CODE = '5' then '柜台升舱'
                END AS orderTypeName,
                CASE
                    WHEN aoi.CHARGE_TYPE = '0' THEN '微信'
                    WHEN aoi.CHARGE_TYPE = '1' THEN '银行卡'
                    WHEN aoi.CHARGE_TYPE = '2' THEN '支付宝'
                    WHEN aoi.CHARGE_TYPE = '3' THEN '现金'
                    WHEN aoi.CHARGE_TYPE = '4' THEN 'pos机'
                    when aoi.CHARGE_TYPE = '5' then 'worldPay'
                    ELSE aoi.CHARGE_TYPE
                END AS payType,
                aoi.id as orderId,
                aoi.FLIGHT_DATE as dt,
                aoi.ORDER_NO as orderNo
                from  ao_order_info aoi
                 where aoi.CURRENCY_SPECIES='CNY' AND  find_in_set(aoi.PAX_NO, #{paxNo})
                <if test="invoice != null and invoice != '' ">
                    <choose>
                        <when test="invoice == 0">
                            and (aoi.INVOICE is null or aoi.INVOICE ='0') and aoi.ORDER_STATUS ='2'
                        </when>
                        <when test="invoice == 1">
                            and aoi.INVOICE ='1'
                        </when>
                    </choose>
                </if>

        ) a
        <where>
            1=1
            <if test="param != null and param != '' ">
                and (
                a.orderTypeName like CONCAT('%', #{param}, '%')
                or a.orderNo like CONCAT('%', #{param}, '%')
                or a.paxName like CONCAT('%', #{param}, '%')
                )
            </if>
        </where>
    </sql>

    <sql id="mergeAuxiliary">
        select c.* from (
                select
                '2' as versions,
                CONCAT(get_city_name(amoi.SERVICE_PORT),'-',amoi.SERVICE_PORT) AS servicePort,
                null as segment,
                date_format(amsi.FLIGHT_DATE,'%Y-%m-%d') as flightDate,
                sum(amsi.PRACTICAL_ORDER_PRICE) as orderPrice,
                ampoi.CURRENCY_SPECIES_NAME as currencySpeciesName,
                case ampoi.PAY_TYPE
                when '0' then '微信'
                when '1' then '易宝'
                when '2' then '支付宝'
                when '3' then '现金'
                when '4' then 'pos机'
                when '5' then 'worldpay'
                end as payType,
                amoi.CREATE_DATE as dt,
                amoi.ID as orderId,
                amoi.ORDER_NO as orderNo,
                (select GROUP_CONCAT(DISTINCT xxx.PAX_NAME SEPARATOR ',') from ao_merge_suborder_info xxx where
                xxx.AO_MERGE_ORDER_INFO_ID=amoi.id) as paxName,
                (select GROUP_CONCAT(DISTINCT xxx.ORDER_TYPE_NAME SEPARATOR ',') from ao_merge_suborder_info xxx where
                xxx.AO_MERGE_ORDER_INFO_ID=amoi.id) as orderTypeName
                from ao_merge_suborder_info amsi
                inner join ao_merge_order_info amoi on amsi.AO_MERGE_ORDER_INFO_ID=amoi.id

                left join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID

                where ampoi.CURRENCY_SPECIES='CNY'
                and amoi.MERGE_ORDER_STATUS in ('1','2')
                and amsi.SUBORDER_ORDER_STATUS='1'
                and amsi.AO_MERGE_ORDER_INFO_ID IN (select n.AO_MERGE_ORDER_INFO_ID from ao_merge_suborder_info n where  find_in_set(n.PAX_NO, #{paxNo}))
                <if test="invoice != null and invoice != '' ">
                    <choose>
                            <when test="invoice == 0">
                                and amsi.INVOICE_JOIN_ID is null and  amsi.SUBORDER_ORDER_STATUS='1'
                            </when>
                            <when test="invoice == 1">
                                and amsi.INVOICE_JOIN_ID is not null
                            </when>
                    </choose>
                </if>
                group by amoi.id
            ) c
        where 1=1
        <if test="param != null and param != '' ">
            and (
            c.orderTypeName like CONCAT('%', #{param}, '%')
            or c.orderNo like CONCAT('%', #{param}, '%')
            or c.paxName like CONCAT('%', #{param}, '%')
            )
        </if>
    </sql>

</mapper>
