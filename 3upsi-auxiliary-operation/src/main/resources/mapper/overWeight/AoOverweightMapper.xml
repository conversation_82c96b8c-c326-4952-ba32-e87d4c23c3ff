<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.overweightbkg.mapper.AoOverweightBkgMapper">

    <select id="getOrderByOrderNo" resultType="com.swcares.psi.ao.overweightbkg.vo.AoOverweightBkgBusinessVo">
      SELECT
      aob.ID as productId,
      aoi.PAX_NAME,
      aoi.PAX_NO,
      aob.OVER_NUMBER_BKG_WEIGHT_INTERNATIONAL,
      aob.OVER_NUMBER_BKG_SIZE_INTERNATIONAL,
      aob.OVER_NUMBER_BKG_NUMBER_INTERNATIONAL,
      aob.OVERWEIGHT_BKG_WEIGHT_PRICE_INTERNATIONAL,
      aob.OVERWEIGHT_BKG_SIZE_PRICE_INTERNATIONAL,
      aob.OVERWEIGHT_BKG_NUMBER_PRICE_INTERNATIONAL,
      aob.OVERWEIGHT_BKG_WEIGHT AS overweightBkgWeight,
      aob.OVERWEIGHT_BKG_TOTAL_PRICE_INTERNAL,
      aob.OVERWEIGHT_BKG_WEIGHT_INTERNATIONAL AS overweightBkgWeightInternational,
      aob.SPECIAL_BKG_TOTAL_PRICE,
      aob.OVERWEIGHT_BKG_TOTAL_PRICE_INTERNATIONAL AS overweightBkgTotalPriceInternational
      FROM
        ao_overweight_bkg aob
      LEFT JOIN
        ao_order_info aoi
      ON
        aob.ORDER_NO = aoi.ORDER_NO
      <where>
      AND  aoi.ORDER_NO = #{orderNo}
      </where>
    </select>
</mapper>