<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeSuborderInfoMapper">

    <select id="sellNumberByDate" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeComparisonGrowthInteriorVo">
        select
        IFNULL(SUM(sub.PRACTICAL_ORDER_PRICE), 0) as price,
        pay.CURRENCY_SPECIES as currencySpecies,
        pay.CURRENCY_SPECIES_NAME as currencySpeciesName

        from ao_merge_suborder_info sub
        left join ao_merge_order_info amoi on amoi.ID=sub.AO_MERGE_ORDER_INFO_ID
        left join ao_merge_pay_order_info pay on pay.id=amoi.ORDER_PAY_ID
        where sub.SUBORDER_ORDER_STATUS='1'
        and amoi.CREATE_NO = #{userNo}
        <if test="orderType != null and orderType != ''">
            and sub.ORDER_TYPE = #{orderType}
        </if>
        and amoi.CREATE_DATE &gt;= DATE_FORMAT(#{begin}, '%Y-%m-%d 00:00:00')
        and amoi.CREATE_DATE &lt;= DATE_FORMAT(#{end}, '%Y-%m-%d 23:59:59')
        group by pay.CURRENCY_SPECIES

    </select>



    <select id="sellOrderNumberByDate" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(1), 0)
        FROM ao_merge_suborder_info sub
          inner join ao_merge_order_info amoi on amoi.ID=sub.AO_MERGE_ORDER_INFO_ID
        where SUBORDER_ORDER_STATUS='1'
          and amoi.CREATE_NO = #{userNo}
          and amoi.CREATE_DATE &gt;= DATE_FORMAT(#{begin}, '%Y-%m-%d 00:00:00')
          and amoi.CREATE_DATE &lt;= DATE_FORMAT(#{end}, '%Y-%m-%d 23:59:59')
    </select>


    <select id="salesRecordByUser" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeSalesRecordVo">
        select
        sub.ID as subOrderId,
        sub.ORDER_TYPE as orderType,
        sub.PAX_NAME as paxName,
        sub.PRACTICAL_ORDER_PRICE as price,
        pay.CURRENCY_SPECIES as currencySpecies,
        pay.CURRENCY_SPECIES_NAME as currencySpeciesName

        from ao_merge_suborder_info sub
        left join ao_merge_order_info amoi on amoi.ID=sub.AO_MERGE_ORDER_INFO_ID
        left join ao_merge_pay_order_info pay on pay.id=amoi.ORDER_PAY_ID
        where sub.SUBORDER_ORDER_STATUS='1'
        and amoi.CREATE_NO = #{userNo}
        <if test="dto.orderType != null and dto.orderType != ''">
            and sub.ORDER_TYPE = #{dto.orderType}
        </if>
        and amoi.CREATE_DATE &gt;= DATE_FORMAT(#{dto.begin}, '%Y-%m-%d 00:00:00')
        and amoi.CREATE_DATE &lt;= DATE_FORMAT(#{dto.end}, '%Y-%m-%d 23:59:59')


    </select>

</mapper>
