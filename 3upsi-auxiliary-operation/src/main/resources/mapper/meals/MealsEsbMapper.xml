<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.mapper.MealsEsbMapper">

    <select id="selectPassengerSalesData" resultType="com.swcares.psi.ao.model.vo.PassengerSalesDataVo" parameterType="com.swcares.psi.ao.model.dto.MealsEsbDto">
        select
            temp.FLIGHT_NUMBER as FLIGHT_NO,
            temp.FLIGHT_DATE,
            sum(if(temp.PASSENGER_TYPE = 'INF', 1, 0)) as infant_Num,
            sum(if(temp.PASSENGER_TYPE = 'CHD', 1, 0)) as children_Num,
            sum(if(temp.CABIN_CHANGE_TYPE = 'H' and temp.SPECIAL_CODE like '%VIP%', 1, 0)) as upgrade_Vip_Num,
            sum(if(temp.MAIN_CABIN &lt;&gt; 'C', 1, 0)) as ECONOMY_NUM,
            sum(if(temp.MAIN_CABIN = 'C', 1, 0)) as BUSINESS_NUM,
            sum(if(temp.MAIN_CABIN = 'C' and temp.SPECIAL_CODE like '%VIP%', 1,0)) as BUSINESS_VIP_NUM,
            sum(if(temp.MAIN_CABIN &lt;&gt; 'C' and temp.SPECIAL_CODE like '%VIP%', 1,0)) as ECONOMY_VIP_NUM
        FROM(
            SELECT
            distinct  fpri.id,
            ffri.FLIGHT_NUMBER,
            ffri.FLIGHT_DATE,
            PASSENGER_TYPE,
            CABIN_CHANGE_TYPE,
            fpss.SPECIAL_CODE,
            IFNULL(fpri.CARRY_CABIN,fpri.SUB_CABIN) as SUB_CABIN
        from
            flt_flight_real_info ffri
        LEFT JOIN
            flt_passenger_real_info fpri
        ON
            ffri.FLIGHT_NUMBER = fpri.FLIGHT_NUMBER
        and
            ffri.FLIGHT_DATE = fpri.FLIGHT_DATE
        and (
            ffri.ORG = fpri.ORG
            OR
            ffri.DST = fpri.DST
        )
        left join
            flt_passenger_special_service fpss
        on
            fpri .ID = fpss.PASSR_ID
        <where>
                fpri .IS_CANCEL = 'N'
            AND
                ffri.FLIGHT_STATE != 'C'
            AND
                (ifnull(fpri.TICKET_NUMBER, '00') != '00'
            OR
                fpri .CHECK_IN_TYPE = 'AC'
            OR
                ifnull(fpri .SEAT_NUMBER, '00') != '00')

            <if test="flightId != null and flightId != ''">
                AND ffri.FOC_ID = #{flightId}
            </if>

            <if test="flightNo != null and flightNo != ''">
                AND ffri.FLIGHT_NUMBER = #{flightNo}
            </if>

            <if test="flightDate != null and flightDate != ''">
                AND ffri.FLIGHT_DATE = #{flightDate}
            </if>
        </where>
        ) as temp
    </select>

    <select id="selectAirTrafficData" resultType="com.swcares.psi.ao.model.vo.AirTrafficDataVo" parameterType="com.swcares.psi.ao.model.dto.AirTrafficDataDto">
        select temp.*
        from (
            select
                ft.FLIGHT_ID ,
                ft.FLIGHT_NO ,
                ft.FLIGHT_DATE ,
                ft.DEPARTURE_AIRPORT,
                ft.ARRIVAL_AIRPORT,
                ft.GROUP_ID
            from
                foc50_t2001 ft
            left
                join flt_flight_real_info ffri
            on
                ft.FLIGHT_ID = ffri.FOC_ID
            <where>
                <if test="flightDateStart != null and flightDateStart != ''" >
                    AND ffri .FLIGHT_DATE <![CDATA[  > ]]> date_sub(#{flightDateStart}, interval 1 day)
                </if>

                <if test="flightDateStop != null and flightDateStop != ''">
                    and ffri .FLIGHT_DATE <![CDATA[  < ]]> date_add(#{flightDateStop}, interval 2 day)
                </if>
            </where>
        order by
            LOAD_TIME desc) as temp
        group by
            temp.GROUP_ID
    </select>

    <select id="selectSpecificPassengerData" resultType="string">
        SELECT fpri.TICKET_NUMBER
            FROM flt_passenger_real_info fpri
            WHERE  fpri.TICKET_NUMBER is not null
            AND fpri.IS_CANCEL  = 'Y'
            AND (fpri.CHECK_STATUS is null
                    OR
                fpri.CHECK_STATUS = 'NA')
            AND fpri.FLIGHT_DATE  <![CDATA[  <= ]]>  date_add(now(), interval 7 day)
            AND fpri.FLIGHT_DATE <![CDATA[  >= ]]> now()
            AND fpri.DATA_COMING_TIME <![CDATA[  >= ]]> date_sub(now(), interval 40 minute)
            AND fpri.DATA_COMING_TIME <![CDATA[  <= ]]> now()
    </select>
</mapper>