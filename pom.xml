<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!--pom文件-->
    <groupId>com.swcares.3upsi</groupId>
    <artifactId>3upsi-msapi</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>

    <properties>
        <spring-boot.version>2.2.7.RELEASE</spring-boot.version>
        <spring-cloud.version>Hoxton.SR3</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring-boot-admin.version>2.2.2</spring-boot-admin.version>
        <hutool.version>5.4.3</hutool.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <dynamic-ds.version>3.0.0</dynamic-ds.version>
        <kaptcha.version>0.0.9</kaptcha.version>
        <velocity.version>1.7</velocity.version>
        <jasypt.version>3.0.3</jasypt.version>
        <fastjson.version>2.0.42</fastjson.version>
        <swagger.fox.version>2.9.2</swagger.fox.version>
        <swagger.core.version>1.5.24</swagger.core.version>
        <security.oauth.version>2.3.6.RELEASE</security.oauth.version>
        <sharding-sphere.version>5.0.0</sharding-sphere.version>
        <hikari.version>3.4.3</hikari.version>
        <hutool-all.version>5.6.5</hutool-all.version>
        <project.version>1.0.0</project.version>
        <spring-security-oauth2-autoconfigure.version>2.3.3.RELEASE</spring-security-oauth2-autoconfigure.version>
    </properties>

    <dependencies>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
<!--        &lt;!&ndash;监控客户端&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>de.codecentric</groupId>-->
<!--            <artifactId>spring-boot-admin-starter-client</artifactId>-->
<!--            <version>${spring-boot-admin.version}</version>-->
<!--        </dependency>-->
        <!--断路器依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.12</version>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <modules>
        <module>3upsi-register</module>
        <module>3upsi-common</module>
        <module>3upsi-auth</module>
        <module>3upsi-gateway</module>
        <module>3upsi-upms</module>
        <module>3upsi-luggage</module>
        <module>3upsi-standard-services</module>
        <module>3upsi-standard-services-pssn</module>
        <module>3upsi-base-data</module>
        <module>3upsi-viproom</module>
        <module>3upsi-auxiliary-operation</module>
        <module>3upsi-cmd</module>
        <module>3upsi-upgrade</module>
        <module>3upsi-schedule</module>
        <module>3upsi-flight-delay-message</module>
        <module>3upsi-work</module>
        <module>3upsi-ground</module>
        <module>3upsi-flight-delay-message-record</module>
        <module>3upsi-seat-reserve</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring cloud alibaba-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--稳定版本，替代spring security bom内置-->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth.version}</version>
            </dependency>
            <!--swagger 最新依赖内置版本-->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <!--fastjson 版本-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--web 模块-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!--排除tomcat依赖-->
                    <exclusion>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 组件依赖版本管理 -->
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-quartz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-message</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-overbooking</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-irregular-flight</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-luggage</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-activity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-swagger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-upms-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-ground-api</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.swcares.3upsi</groupId>-->
<!--                <artifactId>3upsi-common-log</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-combine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-base-data-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-base-data-biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-viproom-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-sharding-jdbc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-timediff</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikari.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-yml</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-wechat</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-cmd-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security.oauth.boot</groupId>
                <artifactId>spring-security-oauth2-autoconfigure</artifactId>
                <version>${spring-security-oauth2-autoconfigure.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-worldpay</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-coupon</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-passenger-event-subscribe</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.3upsi</groupId>
                <artifactId>3upsi-common-cz</artifactId>
                <version>${version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <includeSystemScope>true</includeSystemScope>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.5</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <!--阿里云主仓库，代理了maven central和jcenter仓库-->
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>


</project>