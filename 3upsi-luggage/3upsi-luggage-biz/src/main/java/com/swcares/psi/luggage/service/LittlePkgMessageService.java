package com.swcares.psi.luggage.service;

import com.swcares.psi.luggage.vo.LittlePkgMessageVo;

import java.util.List;

/**
 * ClassName：com.swcares.psi.pkg.service <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月09日 10:47 <br>
 * @version v1.0 <br>
 */
public interface LittlePkgMessageService {
    /**
     * Title：getLittlePkgMessage <br>
     * Description： 获取到期未处理结案少收行李信息<br>
     * author：王建文 <br>
     * date：2020-4-9 10:45 <br>
     * @param
     * @return
     */
    public List<LittlePkgMessageVo> getLittlePkgMessage();
    /**
     * Title：updateLittlePkgSendStatus <br>
     * Description：标记已发送消息<br>
     * author：王建文 <br>
     * date：2020-4-9 11:14 <br>
     * @param  accidentId 事故单号
     * @return
     */
    public void updateLittlePkgSendStatus(String accidentId);
}
