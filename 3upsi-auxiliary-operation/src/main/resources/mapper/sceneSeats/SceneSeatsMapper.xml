<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.sceneSeats.mapper.SceneSeatsMapper">

    <sql id="reportSql">
        SELECT (<include refid="com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper.getPaxHedCategories"></include>) as paxTypeDesc,
            pax.FQTV_NUMBER as ffpNumber,
            date_format(aoi.ORDER_DATE,'%Y-%m-%d') as ORDER_DATE,
            ifnull(aoi.ORDER_NO_CTERMINAL,aoi.ORDER_NO) as ORDER_NO,
            aoi.BANK_ORDER_NO,
            aoi.COMPLETE_DEPARTMENT_INFO,
            aoi.FINANCE_ORDER_NO,
            date_format(aoi.FLIGHT_DATE,'%Y-%m-%d') as FLIGHT_DATE,
            aoi.FLIGHT_NO,
            aoi.ORG,
            aoi.DST,
            aoi.TKT_NO,
            aoi.SEGMENT_TYPE,
            CASE aoi.CHARGE_TYPE
                WHEN  '0' THEN
                    '微信'
                WHEN  '1' THEN
                    '银行卡'
                WHEN  '2' THEN
                    '支付宝'
                WHEN  '3' THEN
                    '现金'
                when  '5' then 'worldPay'
                ELSE
                    'pos机'
                END AS CHARGE_TYPE,
            CASE aoi.CURRENCY_SPECIES
                WHEN  'CNY' THEN
                    '人民币'
                ELSE
                    aoi.CURRENCY_SPECIES
                END AS CURRENCY_SPECIES,
        case aoi.CURRENCY_SPECIES
        when 'CNY' THEN CONCAT(aoi.CURRENCY_SPECIES,'(人民币)')
        ELSE
        (SELECT CONCAT(aoi.CURRENCY_SPECIES,'(', DATA_VALUE,')') FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=aoi.CURRENCY_SPECIES limit 1)
        end AS currencySpeciesName,
            CASE apr.PAY_TYPE
                when '3' then
                    concat('-',aoi.ORDER_PRICE)
                else aoi.ORDER_PRICE
                end as ORDER_PRICE,
            CASE apr.PAY_TYPE
                WHEN  '0' THEN
                    '未支付'
                WHEN  '1' THEN
                    '支付完成'
                WHEN  '2' THEN
                    '支付失败'
                WHEN  '3' THEN
                    '退款成功'
                WHEN  '4' THEN
                    '退款失败'
                END AS ORDER_STATUS,
            CASE apr.PAY_TYPE
                WHEN  '0' THEN
                    '未支付'
                WHEN  '1' THEN
                    '支付完成'
                WHEN  '2' THEN
                    '支付失败'
                WHEN  '3' THEN
                    '退款成功'
                WHEN  '4' THEN
                    '退款失败'
                END AS PAY_STATUS,
            date_format(apr.OPERATION_DATE,'%Y-%m-%d %H:%i:%s') as PAY_TIME,
            aoi.PAX_NAME,
            aoi.CELL_CHANNEL,
            aoi.USER_NO,
            aoi.USER_NAME,
            aoi.DEPARTMENT,
            aoi.DEPARTMENT_ID,
            aoi.TURN_NUMBER,
            CASE
                WHEN aoi.TURN_STATUS = '0' THEN+-
                    '成功'
                WHEN aoi.TURN_STATUS = '1' THEN
                    '失败'
                else
                    aoi.TURN_STATUS
                end as TURN_STATUS,
            aoi.AREA_COMPANY,
            date_format(aoi.PAY_TIME,'%Y-%m-%d %H:%i:%s')as OPERATION_DATE
        FROM ao_order_info aoi
            LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
            left join flt_passenger_real_info pax on pax.id=aoi.pax_id
    </sql>
    <sql id="reportWhere">
        <where>
            aoi.ORDER_TYPE_CODE='2' and  apr.PAY_TYPE  in ('1','3','4')

            <if test="dto.departmentName != null and dto.departmentName !='' ">
                AND aoi.DEPARTMENT like concat('%',#{dto.departmentName},'%')
            </if>

            <if test="dto.areaCompany != null and dto.areaCompany !='' ">
                AND aoi.AREA_COMPANY like concat('%',#{dto.areaCompany},'%')
            </if>

            <if test="dto.orderBeginDate != null and dto.orderBeginDate !=''">
                and aoi.ORDER_DATE &gt;= date_format(#{dto.orderBeginDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="dto.orderEndDate != null and dto.orderEndDate !='' ">
                and aoi.ORDER_DATE &lt;= date_format(#{dto.orderEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="dto.payBeginDate != null and dto.payBeginDate !=''">
                and apr.OPERATION_DATE &gt;= date_format(#{dto.payBeginDate},'%Y-%m-%d 00:00:00')
            </if>
            <if test="dto.payEndDate != null and dto.payEndDate !='' ">
                and apr.OPERATION_DATE &lt;= date_format(#{dto.payEndDate},'%Y-%m-%d 23:59:59')
            </if>

            <if test="dto.orderNo != null and dto.orderNo !='' ">
                and aoi.ORDER_NO like concat('%',#{dto.orderNo},'%')
            </if>
            <if test="dto.flightNo != null and dto.flightNo !='' ">
                and aoi.FLIGHT_NO like concat('%',#{dto.flightNo},'%')
            </if>
            <if test="dto.bankOrderNo != null and dto.bankOrderNo !='' ">
                and aoi.BANK_ORDER_NO like concat('%',#{dto.bankOrderNo},'%')
            </if>

            <if test="dto.flightBeginDate != null  ">
                and aoi.FLIGHT_DATE &gt;= #{dto.flightBeginDate}
            </if>
            <if test="dto.flightEndDate != null ">
                and aoi.FLIGHT_DATE &lt;= #{dto.flightEndDate}
            </if>
            <if test="dto.org != null and dto.org !='' ">
                and aoi.ORG = #{dto.org}
            </if>
            <if test="dto.dst != null and dto.dst !='' ">
                and aoi.DST = #{dto.dst}
            </if>
            <if test="dto.tktNo != null and dto.tktNo !='' ">
                and aoi.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.segmentType != null and dto.segmentType !='' ">
                and aoi.SEGMENT_TYPE = #{dto.segmentType}
            </if>
            <if test="dto.chargeType != null and dto.chargeType !='' ">
                AND FIND_IN_SET(aoi.CHARGE_TYPE,#{dto.chargeType}) >0
            </if>
            <if test="dto.payStatus != null and dto.payStatus !='' ">
                AND FIND_IN_SET(aoi.PAY_STATUS,#{dto.payStatus}) >0
            </if>
            <if test="dto.payType != null and dto.payType !='' ">
                AND FIND_IN_SET(apr.PAY_TYPE,#{dto.payType}) >0
            </if>
            <if test="dto.cellChannel != null and dto.cellChannel !='' ">
                and aoi.CELL_CHANNEL = #{dto.cellChannel}
            </if>
            <if test="dto.turnNumber != null and dto.turnNumber !='' ">
                and aoi.TURN_NUMBER  like concat('%',#{dto.turnNumber},'%')
            </if>

            <if test="dto.financeOrderNo != null and dto.financeOrderNo !='' ">
                and aoi.FINANCE_ORDER_NO  like concat('%',#{dto.financeOrderNo},'%')
            </if>
        </where>
    </sql>

    <sql id="payTypesSummaryMoneySql">
        select t.* from (
        SELECT
        sum(CASE WHEN rs.chargeType = '0' THEN rs.ORDER_PRICE ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN rs.chargeType = '1' THEN rs.ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
        sum(CASE WHEN rs.chargeType = '2'  THEN rs.ORDER_PRICE ELSE 0 end ) AS alipayMoney,
        sum(CASE WHEN (rs.chargeType = '3' or rs.chargeType = '4') THEN rs.ORDER_PRICE ELSE 0 end) AS cashMoney,
        sum(CASE WHEN rs.chargeType = '5' THEN rs.ORDER_PRICE ELSE 0 end) AS worldPay,
        sum(rs.ORDER_PRICE  ) AS allMoney,
        case  rs.CURRENCY_SPECIES
        when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
        else   concat(rs.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
        end as currencySpecies
        from(
        SELECT
        aoi.CURRENCY_SPECIES,
        aoi.CHARGE_TYPE AS chargeType,
        CASE apr.PAY_TYPE
        when '3' then
        concat('-',aoi.ORDER_PRICE)
        else aoi.ORDER_PRICE
        end as ORDER_PRICE,
        aoi.ORDER_PRICE AS totalPrice
        FROM
        ao_order_info aoi
        LEFT join ao_pay_record apr on aoi.ID=apr.ORDER_ID
        <include refid="reportWhere"></include>
        ) rs
        group by rs.CURRENCY_SPECIES
        ) t where t.allMoney > 0
    </sql>

    <select id="getAoScenSeatsAccountsList" resultType="com.swcares.psi.ao.sceneSeats.vo.AoScenSeatsAccountsReportVo"
            parameterType="com.swcares.psi.ao.sceneSeats.dto.AoScenSeatsAccountsReportDto">
        <include refid="reportSql"></include>
        <include refid="reportWhere"></include>
    </select>

    <select id="getAoScenSeatsAccountsPage" resultType="com.swcares.psi.ao.sceneSeats.vo.AoScenSeatsAccountsReportVo"
            parameterType="com.swcares.psi.ao.sceneSeats.dto.AoScenSeatsAccountsReportDto">
        <include refid="reportSql"></include>
        <include refid="reportWhere"></include>
    </select>
    <select id="getListByOrderNo" resultType="com.swcares.psi.ao.sceneSeats.vo.ScenSeatsVo">
        select
        ID as productId,
        ORDER_NO,
        PAX_ID,
        PAX_NAME,
        ID_NO,
        SEATS_PRICE,
        SEATS_TYPE
        from ao_scene_seats
        <where>
            <if test="orderNo !=null and orderNo != '' ">
                ORDER_NO=#{orderNo}
            </if>
        </where>
    </select>
    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo">
        <include refid="payTypesSummaryMoneySql"></include>
    </select>


</mapper>