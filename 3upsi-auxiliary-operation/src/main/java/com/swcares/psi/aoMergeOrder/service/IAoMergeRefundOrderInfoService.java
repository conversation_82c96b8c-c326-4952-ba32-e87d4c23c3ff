package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.dto.AoMergeH5RefundOrderListDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeRefundOrderInfo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeH5RefundOrderDetailsVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeH5RefundOrderListVo;
import com.swcares.psi.aoMergeOrder.vo.RefundOrderTaskVo;
import com.swcares.psi.aoMergeOrder.vo.RefundSendTaskVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

/**
 * <p>
 * 合并支付退款订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeRefundOrderInfoService extends IService<AoMergeRefundOrderInfo> {
    List<RefundOrderTaskVo> getRefundOrderAll();
    List<RefundSendTaskVo> getRefundSendOrder();

    PsiPage<AoMergeH5RefundOrderListVo> h5RefundOrderList(AoMergeH5RefundOrderListDto dto);
    AoMergeH5RefundOrderDetailsVo h5RefundOrderDetails(String refundId);
}
