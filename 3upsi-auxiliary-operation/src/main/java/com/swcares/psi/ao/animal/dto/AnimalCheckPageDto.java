package com.swcares.psi.ao.animal.dto;

import com.swcares.psi.common.utils.encryption.Encryption;
import com.swcares.psi.common.utils.query.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName：AnimalCheckPageDto
 * @Description：宠物pc筛选dto
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/1/3 10:26
 * @version： v1.0
 */
@Data
public class AnimalCheckPageDto extends BaseDto {

    @ApiModelProperty(value = "预约状态 1 预约成功 2 已取消 3 已完成")
    private List<String> status;

    @NotBlank(message = "航班起止日期不能为空!")
    @ApiModelProperty(value = "航班开始日期:2020-02-02")
    private String flightStartDate;

    @NotBlank(message = "航班起止日期不能为空!")
    @ApiModelProperty(value = "航班结束日期:2020-02-02")
    private String flightEndDate;

    @ApiModelProperty(value = "航班号")
    private String flightNumber;

    @ApiModelProperty(value = "出发航站")
    private String org;

    @ApiModelProperty(value = "到达航站")
    private String dst;

    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;


    @ApiModelProperty(value = "证件号")
    private String idNumber;
    @ApiModelProperty("票号")
    private String ticketNum;

}
