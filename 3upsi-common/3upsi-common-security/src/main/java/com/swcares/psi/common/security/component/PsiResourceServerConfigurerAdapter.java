

package com.swcares.psi.common.security.component;

import com.swcares.psi.common.security.logaop.RequestInterceptorAspect;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.expression.OAuth2WebSecurityExpressionHandler;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.switchuser.SwitchUserFilter;

/**
 * <AUTHOR>
 * @date 2020-05-27
 *
 * <p>
 * 1. 支持remoteTokenServices 负载均衡
 * 2. 支持 获取用户全部信息
 */
@Slf4j
public class PsiResourceServerConfigurerAdapter extends ResourceServerConfigurerAdapter {
    @Autowired
    protected ResourceAuthExceptionEntryPoint resourceAuthExceptionEntryPoint;
    @Autowired
    private AccessDeniedHandler psiAccessDeniedHandler;
    @Autowired
    private PermitAllUrlProperties permitAllUrl;

    @Value("${security_key:psi@123}")
    private String securityKey;

    /**
     * 默认的配置，对外暴露
     *
     * @param httpSecurity
     */
    @Override
    @SneakyThrows
    public void configure(HttpSecurity httpSecurity) {
        // 拦截请求clone请求体并缓存到上下文供RequestInterceptorAspect切面记录
        httpSecurity.addFilterAfter(new RequestInterceptorAspect.RequestBodyCachingFilter(), SwitchUserFilter.class); // OrderedRequestContextFilter

        //允许使用iframe 嵌套，避免swagger-ui 不被加载的问题
        httpSecurity.headers().frameOptions().disable();
        ExpressionUrlAuthorizationConfigurer<HttpSecurity>
                .ExpressionInterceptUrlRegistry registry = httpSecurity
                .authorizeRequests();
        permitAllUrl.getUrls()
                .forEach(url -> registry.antMatchers(url).permitAll());
        registry.antMatchers(
                "/api/ground/temp/**",
                "/api/auth/login",
                "/api/ao/pad/apk/check",
                "/api/ao/pad/apk/downApk/**",
                "/api/auth/refresh",
                "/api/auth/login4h5",
                "/api/auth/ssologin",
                "/api/auth/pad/login",
                "/api/h5/auth/ssologin",
                "/api/login/logOut",
                "/api/h5/login/logOut",
                "/api/auth/paxpaylogin",
                "/api/auth/smsCode",
                "/api/ao/notify/**",
                "/api/login/getCodeImage",
                "/api/login/forgetPassword",
                "/api/uploadAndDownload/**",
                "/api/h5/uploadAndDownload/**",
                "/cmd/simpleCmd",
                "/cmd/releaseCmdSource",
                "/cmd/avFligtSeatNumInfo",
                "/cmd/analyMixed",
                "/cmd/getDetrCmdInfo",
                "/cmd/getCmdSource",
                "/cmd/getPnrInfo",
                "/cmd/resvSeat",
                "/cmd/resvSeatCancel",
                "/api/ao/pass/**",
                "/api/freeShuttle/H5/orderInfoReturn",
                "/api/freeShuttle/H5/checkOrderInfo",
                "/api/freeShuttle/H5/getPassInfoById",
                "/api/ao/VipRegister/public/**",
                "/api/flightDelay/getPaxInfo",
                "/api/ao/flightUpgrades/saveFlightUpgrades",
                "/api/ao/svrSeat/saveSeatApply",
                "/api/ao/svrSeat/saveSeatApplyAndOverSeat",
                "/api/ao/sysMealOrder/saveMealList",
                "/api/ao/sysMealOrder/saveMileageConversion",
                "/api/ao/sysMealOrder/savePayOrder",
                "/api/ao/public/esbservice/**",
                "/api/subscribe/rule/**",
                "/api/ao/svrSeat/getSvrGetSeatExternal",
                "/api/dp/passCategory/loyalCategory",
                "/api/dp/sys/base/cip/cipFlightSendSmsValidata",
                "/api/luggage/pull/**",
                "/api/passengertrip/**",
                "/api/external/firstRide/markOrCancel",
                "/api/external/firstRide/getMarkInfo",
                "/api/sms/upstream/receive",
                "/api/wp/notify/**",
                "/api/dp/sys/expectOverBook/pushExpectOverBook",
                "/api/dp/sys/expectOverBook/queryVipExpectOverBook",
                "/api/dp/flt/h5/flight/getPaintingTypeFlt",
                "/api/passengerdelayflights/**",
                "/api/hrsync/**",
                "/api/ao/dataTransfer/**",
                "/api/ao/merge/invoice/getInvoiceParam",
                "/api/passengerdelayflights/**",
                "/api/ao/userBehavior/**",
                "/api/ground/ot/**"
        ).permitAll();
        // 其他url必须通过BearerToken认证跟用户接口权限校验
        registry.anyRequest()
                .access("authenticated and @userResourceAuthService.hasPermission(request)")
                .and().csrf().disable();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {
        DefaultAccessTokenConverter defaultAccessTokenConverter = new DefaultAccessTokenConverter();
        defaultAccessTokenConverter.setUserTokenConverter(new PsiUserAuthenticationConverter());

        JwtAccessTokenConverter jwtAccessTokenConverter = new JwtAccessTokenConverter();
        try {
            jwtAccessTokenConverter.setSigningKey(securityKey);
            jwtAccessTokenConverter.afterPropertiesSet();
        } catch (Exception e) {
            System.out.printf("*********Init JwtAccessTokenConverter SigningKey Occur Error!", e);
            System.exit(1);
        }
        jwtAccessTokenConverter.setAccessTokenConverter(defaultAccessTokenConverter);

        JwtTokenStore jwtTokenStore = new JwtTokenStore(jwtAccessTokenConverter);
        DefaultTokenServices tokenServices = new DefaultTokenServices();

        tokenServices.setTokenStore(jwtTokenStore);

        resources.authenticationEntryPoint(resourceAuthExceptionEntryPoint)
                .accessDeniedHandler(psiAccessDeniedHandler)
                .tokenServices(tokenServices)
                .tokenExtractor(PsiTokenExtractor.getInstance());

        //uriMatchingServiceImpl 用户资源权限过滤配置前置条件
        resources.expressionHandler(expressionHandler);
    }

    @Bean
    public OAuth2WebSecurityExpressionHandler oAuth2WebSecurityExpressionHandler(ApplicationContext applicationContext) {
        OAuth2WebSecurityExpressionHandler expressionHandler = new OAuth2WebSecurityExpressionHandler();
        expressionHandler.setApplicationContext(applicationContext);
        return expressionHandler;
    }

    @Autowired
    private OAuth2WebSecurityExpressionHandler expressionHandler;

}
