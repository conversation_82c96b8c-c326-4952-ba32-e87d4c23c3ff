package com.swcares.psi.aoMergeOrder.service.impl;

import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeManySeatsInputReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeManySeats;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeManySeatsMapper;
import com.swcares.psi.aoMergeOrder.service.IAoMergeManySeatsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeManySeatsInputReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 合并支付一人多座产品详情订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class AoMergeManySeatsServiceImpl extends ServiceImpl<AoMergeManySeatsMapper, AoMergeManySeats> implements IAoMergeManySeatsService {
    @Override
    public List<AoMergeManySeatsAccountsReportVo> getAccountsList(AoMergeManySeatsAccountsReportDto dto) {
        return baseMapper.getAccountsList(dto);
    }

    @Override
    public PsiPage<AoMergeManySeatsAccountsReportVo> getAccountsListPage(AoMergeManySeatsAccountsReportDto dto) {
        PsiPage<AoMergeManySeatsAccountsReportVo> page = new PsiPage<>(dto);
        return baseMapper.getAccountsListPage(page,dto);
    }

    @Override
    public List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeManySeatsAccountsReportDto dto) {
        return baseMapper.payTypesSummaryMoney(dto);
    }


    @Override
    public PsiPage<AoMergeManySeatsInputReportVo> getAoMergeManySeatsInputReportListPage(AoMergeManySeatsInputReportDto dto) {
        PsiPage<AoMergeManySeatsInputReportVo> page = new PsiPage<>(dto);
        return baseMapper.getAoMergeManySeatsInputReportListPage(page,dto);
    }

    @Override
    public List<AoMergeManySeatsInputReportVo> getAoMergeManySeatsInputReportList(AoMergeManySeatsInputReportDto dto) {
        return baseMapper.getAoMergeManySeatsInputReportList(dto);
    }
}
