package com.swcares.psi.aoMergeOrder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.ao.common.vo.ElectronicVoucherVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightInputTemplateReportDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeOverweightBkg;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightInputTemplateReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;

/**
 * <p>
 * 合并逾重行李产品详情订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeOverweightBkgService extends IService<AoMergeOverweightBkg> {
    List<AoMergeOverweightBkgAccountsReportVo> getAccountsList(AoMergeOverweightBkgAccountsReportDto dto);
    PsiPage<AoMergeOverweightBkgAccountsReportVo> getAccountsListPage(AoMergeOverweightBkgAccountsReportDto dto);
    List<PayTypesSummaryMoneyVo> payTypesSummaryMoney(AoMergeOverweightBkgAccountsReportDto dto);


    PsiPage<AoMergeOverweightInputTemplateReportVo> getAoMergeOverweightInputTemplatePage(AoMergeOverweightInputTemplateReportDto dto);
    List<AoMergeOverweightInputTemplateReportVo> getAoMergeOverweightInputTemplateList(AoMergeOverweightInputTemplateReportDto dto);
    ElectronicVoucherVo getElectronicVoucher(String suborderNo);
}
