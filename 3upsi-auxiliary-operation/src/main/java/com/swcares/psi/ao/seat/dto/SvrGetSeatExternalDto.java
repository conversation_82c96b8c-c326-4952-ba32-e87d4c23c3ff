package com.swcares.psi.ao.seat.dto;

import com.swcares.psi.common.utils.validatesign.EncryptedField;
import com.swcares.psi.common.utils.validatesign.ValidateBaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.seat.dto.SvrGetSeatExternalDto <br>
 * Description：机组占座外部接口DTO<br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022-9-27 09:53:55 <br>
 * @version v1.0 <br>
 */
@Data
public class SvrGetSeatExternalDto  extends ValidateBaseDto implements Serializable {

    private static final long serialVersionUID = -1506596552430880141L;

    @ApiModelProperty(name = "applyIds", value = "申报单ID", required = true)
    @NotEmpty(message = "申报单ID不能为空")
    @EncryptedField()
    private String applyIds;
}

