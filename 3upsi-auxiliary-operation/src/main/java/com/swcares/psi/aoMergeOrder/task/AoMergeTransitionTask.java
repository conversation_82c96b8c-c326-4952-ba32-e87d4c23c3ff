package com.swcares.psi.aoMergeOrder.task;

import com.swcares.psi.ao.cons.AoOrderConstant;
import com.swcares.psi.ao.invoice.cons.InvoiceCons;
import com.swcares.psi.ao.invoice.entity.AoInvoiceInfoEntity;
import com.swcares.psi.ao.invoice.vo.AoInvoiceInfoVo;
import com.swcares.psi.aoMergeOrder.cons.RedisLockKeyCons;
import com.swcares.psi.aoMergeOrder.entity.AoMergeInvoiceInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSubOrderJoinInvoiceInfo;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSuborderInfo;
import com.swcares.psi.aoMergeOrder.service.*;
import com.swcares.psi.aoMergeOrder.service.impl.AoCommonService;
import com.swcares.psi.base.data.api.entity.SysDict;
import com.swcares.psi.base.data.service.ISysDictService;
import com.swcares.psi.common.invoice.ElectronicInfoBackVo;
import com.swcares.psi.common.invoice.InvoiceSendParam;
import com.swcares.psi.common.invoice.InvoiceUtil;
import com.swcares.psi.common.invoice.v2.constant.InvoiceStatus;
import com.swcares.psi.common.invoice.v2.entity.YyInvoice;
import com.swcares.psi.common.invoice.v2.service.IYyInvoiceService;
import com.swcares.psi.common.utils.AesEncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * todo-houchuan 临时过渡
 */
@Component
@Slf4j
public class AoMergeTransitionTask {

    @Autowired
    Redisson redisson;

    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;
    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;
    @Autowired
    IAoMergeInvoiceInfoService aoMergeInvoiceInfoService;
    @Autowired
    AoMergeSubOrderJoinInvoiceInfoService aoMergeSubOrderJoinInvoiceInfoService;
    @Autowired
    ISysDictService sysDictService;
    @Autowired
    InvoiceUtil invoiceUtil;


    @Autowired
    IYyInvoiceService yyInvoiceService;



    public void updateInvoiceStatus() {
        log.info("开始发票状态更新定时任务！");
        RLock lock = null;
        try {
            lock = redisson.getLock(RedisLockKeyCons.AO_MERGE_INVOICE_QUERY_LOOK + "0");
            if (!lock.tryLock()) {
                log.info("已有服务器执行发票状态更新synchronizeData定时任务！");
                return;
            }
            List<AoMergeInvoiceInfo> invoiceInfo = aoMergeInvoiceInfoService.lambdaQuery()
                    .in(AoMergeInvoiceInfo::getInvoiceStatus, new String[]{InvoiceStatus.AWAIT_INVOICE})
                    .list();
            if (invoiceInfo.size() == 0) {
                return;
            }
            for (AoMergeInvoiceInfo ele : invoiceInfo) {
                try {
                    AoInvoiceInfoVo vo = new AoInvoiceInfoVo();
                    vo.setId(ele.getId());
                    vo.setInvoiceTitle(ele.getInvoiceTitle());
                    vo.setInvoiceNo(ele.getSeqNo());
                    vo.setInvoicePrice(ele.getInvoicePrice());
                    vo.setTaxNo(ele.getTaxNo());
                    vo.setEmail(ele.getEmail());
                    vo.setPhone(AesEncryptUtil.decryption(ele.getPhone()));
                    vo.setPurchaserBank(ele.getPurchaserBank());
                    vo.setPurchaserBankNo(ele.getPurchaserBankNo());
                    vo.setPurchaserAddress(ele.getPurchaserAddress());
                    vo.setPurchaserPhone(AesEncryptUtil.decryption(ele.getPurchaserPhone()));
                    List<AoMergeSubOrderJoinInvoiceInfo> list = aoMergeSubOrderJoinInvoiceInfoService.lambdaQuery()
                            .eq(AoMergeSubOrderJoinInvoiceInfo::getSeqNo, ele.getSeqNo())
                            .list();
                    AoMergeSuborderInfo byId = aoMergeSuborderInfoService.getById(list.get(0).getSuborderOrderId());
                    Integer gat = sysDictService.lambdaQuery()
                            .in(SysDict::getDataCode, new String[]{byId.getOrg(), byId.getDst()})
                            .eq(SysDict::getDataStatus, "0")
                            .eq(SysDict::getDataTypeCode, "GAT")
                            .count();
                    if(gat>0){
                        vo.setFlightType("GAT");
                    }else {
                        vo.setFlightType(byId.getFlightType());
                    }
                    vo.setOrderType(byId.getOrderType());
                    invoice(vo);
                } catch (Exception e) {
                    log.info("发票状态更新异常：发票id{}", ele.getId(),e);
                }
            }

        } catch (Exception e) {
            log.error("发票状态更新定时任务加任务锁异常:{}", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public void invoice(AoInvoiceInfoVo ele) {
        log.info("开票构建参数>>{}",ele);
        InvoiceSendParam param = new InvoiceSendParam();
        param.setGmfMc(ele.getInvoiceTitle());
        param.setYwdh(StringUtils.isEmpty(ele.getInvoiceNo()) ? ele.getOrderNo() : ele.getInvoiceNo());
        param.setJe(ele.getInvoicePrice());
        param.setGmfNsrsbh(ele.getTaxNo());
        if (StringUtils.isEmpty(ele.getEmail()) && StringUtils.isEmpty(ele.getPhone())) {
            log.info("开具发票手机号和邮箱不能同是为空-订单号{}", ele.getOrderNo());
            return;
        }
        param.setGmfDzyx(ele.getEmail());
        param.setGmfSjh(ele.getPhone());
        if(StringUtils.isNotEmpty(ele.getPurchaserBank())){
            param.setGmfYhzh(ele.getPurchaserBank());
        }
        if( StringUtils.isNotEmpty(ele.getPurchaserBankNo())){
            param.setGmfYhzh(ele.getPurchaserBankNo());
        }

        if(StringUtils.isNotEmpty(ele.getPurchaserAddress())){
            param.setGmfDzdh(ele.getPurchaserAddress());
        }
        if( StringUtils.isNotEmpty(ele.getPurchaserPhone())){
            param.setGmfDzdh(ele.getPurchaserPhone());
        }


        if (AoOrderConstant.ORDER_TYPE_CODE_MANY_SEATS.equals(ele.getOrderType())) {
            param.setSpId("0001101");
            param.setYwlx("16");
        } else if (AoOrderConstant.ORDER_TYPE_CODE_SCENE_SEATS.equals(ele.getOrderType())) {
            param.setYwlx("08");
            param.setSpId("111");
        } else if (AoOrderConstant.ORDER_TYPE_CODE_OVERWEIGHT_PKG.equals(ele.getOrderType())) {
            param.setYwlx("02");
            if ("D".equals(ele.getFlightType())) {
                param.setSpId("0000301");
            } else if ("GAT".equals(ele.getFlightType())) {
                param.setSpId("043");
            } else {
                param.setSpId("042");
            }
        } else if (AoOrderConstant.ORDER_TYPE_CODE_VIP.equals(ele.getOrderType())) {
            param.setSpId("10");
            param.setYwlx("07");
        } else if (AoOrderConstant.ORDER_TYPE_CODE_UPGRADE.equals(ele.getOrderType())) {
            param.setYwlx("04");
            if ("D".equals(ele.getFlightType())) {
                param.setSpId("071");
            } else if ("GAT".equals(ele.getFlightType())) {
                param.setSpId("073");
            }  else {
                param.setSpId("072");
            }
        }else if (AoOrderConstant.ORDER_TYPE_CODE_ON_UPGRADE.equals(ele.getOrderType())) {
            param.setYwlx("04");
            if ("D".equals(ele.getFlightType())) {
                param.setSpId("071");
            } else if ("GAT".equals(ele.getFlightType())) {
                param.setSpId("073");
            }   else {
                param.setSpId("072");
            }
        }else if ((AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT+AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_UPGRADE).equals(ele.getOrderType())) {
            param.setYwlx("04");
            if ("D".equals(ele.getFlightType())) {
                param.setSpId("071");
            } else if ("GAT".equals(ele.getFlightType())) {
                param.setSpId("073");
            }   else {
                param.setSpId("072");
            }
        }else if ((AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT+AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_OVERWEIGHT_PKG).equals(ele.getOrderType())) {
            param.setYwlx("02");
            if ("D".equals(ele.getFlightType())) {
                param.setSpId("0000301");
            } else if ("GAT".equals(ele.getFlightType())) {
                param.setSpId("043");
            } else {
                param.setSpId("042");
            }
        }else if ((AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT+AoOrderConstant.ORDER_TYPE_CODE_LINE_PAYMENT_CHANGE_DATE).equals(ele.getOrderType())) {
            param.setYwlx("3049900000000000000");
            param.setSpId("05");
        }

        //放入数据请求流水号
        String dataExchangeId = UUID.randomUUID().toString().replace("-", "").substring(0, 32);
        param.setDataExchangeId(dataExchangeId);
        //todo-houchuan 测试调试  上线恢复
//        ElectronicInfoBackVo electronicInfoBackVo = new ElectronicInfoBackVo();
//        electronicInfoBackVo.setKprq(new Date());
//        electronicInfoBackVo.setRespCode("0000");
        ElectronicInfoBackVo electronicInfoBackVo = invoiceUtil.sendInvoice(param);

        log.info("++++发票推送结果++++" + electronicInfoBackVo == null ? "未知错误" : electronicInfoBackVo.getResoult());
        if (electronicInfoBackVo != null && "0000".equals(electronicInfoBackVo.getRespCode())) {
            Date kprq = electronicInfoBackVo.getKprq();
            Instant instant = kprq.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
            aoMergeInvoiceInfoService.lambdaUpdate()
                    .eq(AoMergeInvoiceInfo::getId,ele.getId())
                    .set(AoMergeInvoiceInfo::getInvoiceStatus,InvoiceStatus.INVOICE_SUCCESS)
                    .set(AoMergeInvoiceInfo::getInvoiceTime,localDateTime)
                    .update();
            yyInvoiceService.lambdaUpdate()
                    .eq(YyInvoice::getSeqNo,ele.getInvoiceNo())
                    .set(YyInvoice::getInvoiceStatus,InvoiceStatus.INVOICE_SUCCESS)
                    .update();

        } else {
            aoMergeInvoiceInfoService.lambdaUpdate()
                    .eq(AoMergeInvoiceInfo::getId,ele.getId())
                    .set(AoMergeInvoiceInfo::getInvoiceStatus,InvoiceStatus.INVOICE_FAILED)
                    .set(AoMergeInvoiceInfo::getCauseFailure,electronicInfoBackVo == null ? "未知错误" : electronicInfoBackVo.getRespMsg())
                    .update();
            yyInvoiceService.lambdaUpdate()
                    .eq(YyInvoice::getSeqNo,ele.getInvoiceNo())
                    .set(YyInvoice::getInvoiceStatus,InvoiceStatus.INVOICE_FAILED)
                    .update();
        }
    }

}
