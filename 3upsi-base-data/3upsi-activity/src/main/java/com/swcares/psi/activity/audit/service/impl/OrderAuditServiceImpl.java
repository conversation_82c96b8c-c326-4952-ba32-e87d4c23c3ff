package com.swcares.psi.activity.audit.service.impl;

import com.swcares.psi.activity.api.ActivitiService;
import com.swcares.psi.activity.api.ProcessService;
import com.swcares.psi.activity.audit.dao.OrderActMiddleDao;
import com.swcares.psi.activity.audit.dao.OrderAuditDao;
import com.swcares.psi.activity.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.psi.activity.audit.dto.AuditOrderPaxInfo;
import com.swcares.psi.activity.audit.dto.DeptUserInfoDto;
import com.swcares.psi.activity.audit.dto.OrderAuditDataDto;
import com.swcares.psi.activity.audit.dto.OrderAuditProcessDto;
import com.swcares.psi.activity.audit.dto.OrderAuditQueryParams;
import com.swcares.psi.activity.audit.entity.OrderActMiddle;
import com.swcares.psi.activity.audit.entity.OrderAudit;
import com.swcares.psi.activity.audit.enums.AuditStatusEnum;
import com.swcares.psi.activity.audit.enums.AuditTypeEnum;
import com.swcares.psi.activity.audit.service.OrderAuditService;
import com.swcares.psi.activity.audit.vo.ActRunTaskInfoVo;
import com.swcares.psi.activity.audit.vo.ActSendMessgeResultVo;
import com.swcares.psi.activity.audit.vo.ActSendMessgeVo;
import com.swcares.psi.activity.audit.vo.ActTaskProcdefInfoVo;
import com.swcares.psi.activity.audit.vo.AuditTaskInfoVo;
import com.swcares.psi.activity.audit.vo.AuditUserInfoVo;
import com.swcares.psi.activity.audit.vo.CompensationProgressVo;
import com.swcares.psi.activity.audit.vo.OrderAuditDataVo;
import com.swcares.psi.activity.audit.vo.OrderAuditProgressVo;
import com.swcares.psi.activity.audit.vo.OrderAuditQuertListVo;
import com.swcares.psi.activity.audit.vo.OrderAuditRecordVo;
import com.swcares.psi.activity.audit.vo.OrderAuditResult;
import com.swcares.psi.activity.audit.vo.PaxInfosQueryResultVo;
import com.swcares.psi.activity.audit.vo.PreTaskInfoVo;
import com.swcares.psi.base.util.Asserts;
import com.swcares.psi.base.util.PinyinUtil;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.combine.util.UID;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.QueryResults;
import com.swcares.psi.message.api.enums.MessageTypeEnum;
import com.swcares.psi.message.api.form.MessageSendForm;
import com.swcares.psi.message.service.MessageService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

import static com.swcares.psi.combine.constant.CommonConstants.AUTO_AUDIT_TUNO;

/**
 * ClassName：com.swcares.psi.audit.service.impl <br>
 * Description：审核模块 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 14:56 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class OrderAuditServiceImpl implements OrderAuditService {

    @Autowired
    private ActivitiService activitiService;
    @Autowired
    private ProcessService processService;
    @Resource
    private OrderActMiddleDao orderActMiddleDao;
    @Resource
    private OrderAuditDaoImpl orderAuditDaoImpl;
    @Resource
    private OrderAuditDao orderAuditDao;
    @Resource
    private MessageService messageService;
    @Resource
    private OrderAuditService orderAuditService;
    @Resource
    private TaskService taskService;
    @Autowired
    private UID uid;

    //补偿单审批状态
    public static final String AUDIT_STATUS_RESULT_PREFIX = "AUDIT_STATUS_RESULT";
    //补偿单审批过期时间
    public static final int AUDIT_EXPIRE_DATE = 180;


    /*流程节点 1 发起*/
    private static final String PROCESS_START = "1";
    /*流程节点 2 AOC审核*/
    private static final String PROCESS_AOC = "2";
    /*流程节点 3 值班审核*/
    private static final String PROCESS_TWO = "3";
    /* 处理状态 1已处理 */
    private static final String STATIC_YES = "1";

    /*H5消息跳转- 审核结果 不正常航班-跳转至补偿单详情页*/
    private static final String MOBILE_URL_IRREGULAR = "/passengerCompensation/ListOfCompensation/detailsOfCompensation?orderId={0}&payType={1}";

    /*H5消息跳转- 审核结果 异常行李-跳转至异常行李事故详情页*/
    private static final String MOBILE_URL_ABNORMAL_BAGGAGE = "/examine/accidentDetail?accidentId={0}&payType={1}";

    /*H5消息跳转- 审核结果 旅客超售-跳转至超售事故单详情页*/
    private static final String MOBILE_URL_OVERBOOK = "/examine/accidentDetail?orderId={0}&payType={1}";

    //补偿类型 不正常航班 0、
    private static final String STATUS_IRREGULAR = "0";
    //补偿类型 异常行李1、
    private static final String STATUS_ABNORMAL_BAGGAGE = "1";
    //补偿类型 超售旅客2、
    private static final String STATUS_OVERBOOK = "2";

    //异常行李二级审核角色名
    private static final String ABNORMAL_BAGGAGE_SECOND_LEVEL_ROLE_NAME = "异常行李二级审核";
    //异常行李三级审核角色名
    private static final String ABNORMAL_BAGGAGE_THREE_LEVEL_ROLE_NAME = "异常行李三级审核";
    //异常行李四级审核角色名
    private static final String ABNORMAL_BAGGAGE_FOUR_LEVEL_ROLE_NAME = "异常行李四级审核";

    //状态6-驳回
    private static final String ORDER_STATUS_REJECT = "6";

    //流程最后一个节点标识
    private static final String PROCESS_END_SIGN = "PROCESS_END";

    @Override
    public boolean deployAuditProcess(String delployName, String bpmnFielUrl, String pngFielUrl) {
        return activitiService.delployFlow(delployName, bpmnFielUrl, pngFielUrl);
    }

    /**
     * Title：launchAuditProcess <br>
     * Description：发起审核流程<br>
     * author：傅欣荣 <br>
     * date：2020/3/11 15:02 <br>
     *
     * @param userId      发起人, actKey
     * @param orderId     服务单id
     * @param handleUser  审核人 多个,分隔
     * @param amount      补偿金额，异常行李
     * @param auditType   审核类型，自动、手动
     * @param dOrI        是否国际国内 D 国内  I 国际
     * @param airportType 0 成都 1 分公司  2 无人航站
     * @param businessParam 自定义参数 尽量不要与固有参数重名
     * @return void
     */
    @SneakyThrows
    @Override
    @Transactional
    public void launchAuditProcess(String userId, String actKey, String orderId, String handleUser, String amount
            , String auditType, String dOrI, String airportType,Map<String,Object>... businessParam) {
        log.info("-------->>>审核-方法【发起审核】参数 ：发起人{}，流程key{}，补偿单id{}，审核人员{}, 金额{}, 审核类型{}", userId, actKey, orderId, handleUser, amount, auditType);
        //校验发起审核参数，并判断是否为重复发起，并推送一级审核消息
        OrderAuditProcessDto auditDto = getStartAuditParams(userId, orderId, handleUser, amount,
                auditType, dOrI, airportType);
        if (this.checkLaunchAuditData(auditDto)) return;
        String processKey = actKey;
        Map<String, Object> data = new HashMap<>();
        data.put("submitter", userId);
        if (StringUtils.isNotEmpty(handleUser)) {
            //审核人员解密方法暂时关闭
            //handleUser = this.getDecrypHandler(handleUser);
            data.put("handleUser", handleUser);
        }
        if (StringUtils.isNotEmpty(amount)) {
            data.put("amount", amount);
        }
        if (StringUtils.isNotEmpty(auditType)) {
            data.put("auditType", auditType);
        }

        if(ObjectUtils.isNotEmpty(businessParam) && businessParam.length>0){
            data.putAll(businessParam[0]);
        }
        log.info("-------->>>审核-方法【发起审核】参数 ：发起人{}，补偿单id{}，审核流程key{}，流程变量：{}", userId, orderId, processKey, data.toString());
        ProcessInstance processInstance = activitiService.startProcessResult(userId, processKey, true, data, amount, auditType, dOrI, airportType);

        log.info("-------->>>审核-方法【发起审核结果】参数 ：发起人{}，补偿单id{}，ProcessInstance{}", userId, orderId, processInstance);
        if (ObjectUtils.isEmpty(processInstance)) {
            log.error("-------->>>审核-【发起审核】，异常! 补偿单id{}，ProcessInstance{}", orderId, processInstance);
            throw new BusinessException(MessageCode.LAUNCH_PROCESS_ERROR.getCode(), new String[]{orderId});
        }
        log.info("-------->>>审核-方法【发起审核-保存审核业务数据】参数 ：发起人{}，补偿单id {}，ProcessInstance{}", userId, orderId, processInstance);
        //保存审核记录
        this.saveAuditInfo(orderId, processInstance, userId);
        //川航自动审核
        if (AuditTypeEnum.AUTO_AUDIT.getKey().equals(auditType)) {
            AutoAuditHandler(auditDto, processInstance.getId(), businessParam);
        } else if (AuditTypeEnum.MANUAL_AUDIT.getKey().equals(auditType)) {
            //给下一级推送审核消息
            this.pushAuditMessage(processInstance.getId(), orderId, handleUser);
        }
    }

    /**
     * Title： checkLaunchAuditData <br>
     * Description： 发起审核流程、进行校验。判断是否为重复发起<br>
     * author：傅欣荣 <br>
     * date：2020/6/15 15:48 <br>
     *
     * @param
     * @return
     */
    public boolean checkLaunchAuditData(OrderAuditProcessDto auditDto) {
        //判断orderId是否已发起过审核流程
        String orderId = auditDto.getOrderId();
        String userId = auditDto.getAuditor();
        String handleUser = auditDto.getHandleUser();
        String auditType = auditDto.getAuditType();
        OrderAuditResult resultT = orderAuditDaoImpl.getTaskIdByOrderId(orderId, userId);
        log.info("-------->>>审核-方法【发起审核-查询用户当前订单的流程任务】参数 ：发起人{}，补偿单id{}，查询结果：{}", userId, orderId, resultT.toString());
        String taskId = resultT.getTaskId();
        if (ObjectUtils.isNotEmpty(taskId)) {
            log.info("-------->>>审核-方法【发起审核-查询用户当前订单的流程任务，有值，用户再次发起流程】参数 ：发起人{}，补偿单id{}，查询结果：{}", userId, orderId, resultT.toString());
            auditDto.setTaskId(taskId);
            if (AuditTypeEnum.AUTO_AUDIT.getKey().equals(auditType)) {
                AutoAuditHandler(auditDto, "");
            } else if (AuditTypeEnum.MANUAL_AUDIT.getKey().equals(auditType)) {
                // TODO 重复发起 手动审核处理情况
                handleAuditProcess(auditDto);
                //推送审核消息（下一级审核者）
                //审核人员解密方法暂时关闭
                //this.pushAuditMessage(resultT.getActPinId(),orderId,this.getDecrypHandler(handleUser));
                //this.pushAuditMessage(resultT.getActPinId(),orderId,handleUser);
            }
            return true;
        } else if (ObjectUtils.isNotEmpty(resultT.getOrderId()) && ObjectUtils.isEmpty(taskId)) {
            log.info("-------->>>审核-方法【发起审核-当前orderId已发起过审核流程，用户不是流程发起人不能发起审核】参数 ：发起人{}，补偿单id{}，查询结果：{}", userId, orderId, resultT.toString());
            throw new BusinessException(MessageCode.LAUNCH_REPEAT_ERROR.getCode(), new String[]{orderId, userId});
        }
        return false;
    }


    /**
     * 执行下一个工作流节点 （不是前任节点 比如驳回）  不做业务处理
     * @return
     */
    @Override
    public boolean simpleAuditProcess(String orderId , String userId,Map<String,Object> variables) {
        //判断orderId是否已发起过审核流程
        OrderAuditResult resultT = orderAuditDaoImpl.getTaskIdByOrderId(orderId, userId);
        String taskId = resultT.getTaskId();
        log.info("拨动执行任务{}",taskId);
        if (ObjectUtils.isNotEmpty(taskId)) {
            taskService.complete(taskId, variables);
            return true;
        } else if (ObjectUtils.isNotEmpty(resultT.getOrderId()) && ObjectUtils.isEmpty(taskId)) {
            log.info("-------->>>拨动执行任务【发起审核-当前orderId已发起过审核流程，用户不是流程发起人不能发起审核】参数 ：发起人{}，补偿单id{}，查询结果：{}", userId, orderId, resultT.toString());
            return false;
        }
        return false;
    }



    /**
     * Title： getDecrypHandler<br>
     * Description： 解密后审批人数据<br>
     * author：傅欣荣 <br>
     * date：2020/6/26 21:29 <br>
     *
     * @param
     * @return
     */
    //private String getDecrypHandler(String handleUser) {
    //    if (ObjectUtils.isNotEmpty(handleUser)) {
    //        String privateKey = UserEnum.PASSWORD_ENCRYPT_KEY.getValue();
    //        String key = null;
    //        try {
    //            key = Base64.encodeBase64String(privateKey.getBytes(CharsetUtil.UTF8));
    //        } catch (UnsupportedEncodingException e) {
    //            log.error("发起审批-下一级处理人key加密错误{}", e);
    //            throw new BusinessException(MessageCode.AUDIT_HANDLEUSER_DECODE_ERROR.getCode());
    //        }
    //        return AesEncryptUtil.aesDecrypt(key, handleUser);
    //    }
    //    return new String();
    //}

    /**
     * Title：pushAuditMessage <br>
     * Description： 发送审核消息:有handleUser值直接发送，没有则从实例中取<br>
     * author：傅欣荣 <br>
     * date：2020/6/15 16:16 <br>
     *
     * @param
     * @return
     */
    public void pushAuditMessage(String processInstanceId, String orderId, String handleUser) {
        log.info("-------->>>审核-方法【发起审核-发送消息验证】，补偿单id{}，人员{}，processInstanceId{}", orderId, handleUser, processInstanceId);
        if (StringUtils.isNotBlank(handleUser)) {
            log.info("-------->>>审核-方法【发起审核-发送消息给下一处理人】，补偿单id{}，人员{}", orderId, handleUser);
            this.sendAuditMessage(orderId, handleUser);
        }
    }

    @Transactional
    public void saveAuditInfo(String orderId, ProcessInstance processInstance, String userId) {
        String recordId = handleId();
        orderAuditDaoImpl.updOrderStatus(orderId, AuditStatusEnum.OPINION_START.getStatus());
        this.saveActMiddleInfo(orderId, processInstance.getId(), recordId);
        this.saveAuditDefault(orderId, userId, new Date(), recordId);
    }

    /**
     * 创建发起人-再次发起处理请求参数
     */
    private OrderAuditProcessDto getStartAuditParams(String userId, String orderId, String handleUser,
                                                     String amount, String auditType, String dOrI, String airportType) {
        OrderAuditProcessDto auditDto = new OrderAuditProcessDto();
        auditDto.setOrderId(orderId);
        auditDto.setOpinion(AuditStatusEnum.OPINION_START.getKey());//发起
        auditDto.setProcessNode(PROCESS_START);//1发起
        auditDto.setAuditor(userId);//处理人
        auditDto.setHandleUser(handleUser);
        auditDto.setAmount(amount);
        auditDto.setAuditType(auditType);
        auditDto.setDOrI(dOrI);
        auditDto.setAirportType(airportType);
        return auditDto;
    }


    /**
     * Title： <br>
     * Description： 校验审批参数是否合法<br>
     * author：傅欣荣 <br>
     * date：2020/6/15 16:20 <br>
     *
     * @param
     * @return
     */
    private String checkHandleAudit(OrderAuditProcessDto orderAuditPd) {
        log.info("补偿单审核提交数据，orderAuditProcessDto:{}", orderAuditPd);
        if (ObjectUtils.isEmpty(orderAuditPd.getTaskId())) {
            throw new BusinessException(MessageCode.TASK_ID_IS_NULL.getCode());
        }
        if (ObjectUtils.isNotEmpty(orderAuditPd.getHandleUser())
                && orderAuditPd.getHandleUser().contains(orderAuditPd.getAuditor())) {
            throw new BusinessException(MessageCode.AUDIT_USER_ERROR.getCode(), new String[]{orderAuditPd.getAuditor()});
        }
        if (PROCESS_AOC.equals(orderAuditPd.getProcessNode())
                && AuditStatusEnum.OPINION_AGREE.getKey().equals(orderAuditPd.getOpinion())
                && ObjectUtils.isEmpty(orderAuditPd.getHandleUser())) {
            throw new BusinessException(MessageCode.AUDIT_HANDLEUSER_IS_NULL.getCode());
        }

        //查询taskId 是否存在。
        ActRunTaskInfoVo actRunTaskInfoVo = orderAuditDaoImpl.getActInfoByTaskId(orderAuditPd.getTaskId());
        log.info("审批-查询补偿单号[{}],审批任务[{}],查询结果：[{}]", orderAuditPd.getOrderId(), orderAuditPd.getTaskId(), actRunTaskInfoVo.toString());
        Asserts.notNull(actRunTaskInfoVo.getTaskId(), MessageCode.AUDIT_REPEAT_ERROR.getCode(),
                new String[]{orderAuditPd.getTaskId(), orderAuditPd.getOrderId()});
        Asserts.notNull(actRunTaskInfoVo.getProcessInstancesId(), MessageCode.AUDIT_REPEAT_ERROR.getCode(),
                new String[]{orderAuditPd.getTaskId(), orderAuditPd.getOrderId()});
        return actRunTaskInfoVo.getProcessInstancesId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAuditProcess(OrderAuditProcessDto orderAuditPd) {

        String processNode = orderAuditPd.getProcessNode();
        String processInstancesId = this.checkHandleAudit(orderAuditPd);
        String orderId = orderAuditPd.getOrderId();
        String option = orderAuditPd.getOpinion();
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"orderId"});
        String previousHandlerUser = "";
        // 驳回状态 查询绝对上级的源数据
        if (AuditStatusEnum.OPINION_REJECT.getKey().equals(option)) {
            Task task = taskService.createTaskQuery().processInstanceId(processInstancesId).singleResult();
            // 获取当前节点的DefinitionKey
            String taskDefinitionKey = task.getTaskDefinitionKey();
            String preTaskDefinitionKey = taskDefinitionKey.split("_")[taskDefinitionKey.split("_").length - 1];
            // 根据preTaskDefinitionKey获取流程图中绝对上一节点
            preTaskDefinitionKey = "_" + (Integer.valueOf(preTaskDefinitionKey) - 1);
            // 获取流程图中绝对上一节点的TaskId和 处理人
            List<PreTaskInfoVo> preTaskInfoVos = orderAuditDaoImpl.getPreTaskInfo(preTaskDefinitionKey, processInstancesId);
            PreTaskInfoVo preTaskInfoVo = new PreTaskInfoVo();
            if (ObjectUtils.isNotEmpty(preTaskInfoVos) && !preTaskInfoVos.isEmpty()) {
                preTaskInfoVo = preTaskInfoVos.get(0);
            }
            // 根据TaskId 获取之前设置的流程处理人 ，流程驳回转向后需要再次成为处理人
            List<AuditUserInfoVo> auditUserInfoVos = orderAuditDaoImpl.getUserIdByTaskIdAndType(preTaskInfoVo.getTaskId());
            String handleUsers = "";
            for (AuditUserInfoVo auditUserInfoVo : auditUserInfoVos) {
                handleUsers += auditUserInfoVo.getUserId() + ",";
            }
            handleUsers = handleUsers.length() > 0 ? handleUsers.substring(0, handleUsers.length() - 1) : "";
            previousHandlerUser = preTaskInfoVo.getAssginee();
            orderAuditPd.setHandleUser(handleUsers);
        }
        //根据节点流程变量
        Map<String, Object> data = new HashMap<>();
        data.put("result", option);//任务变量：审批状态 1通过、0不通过 2 驳回
        if (StringUtils.isNotEmpty(orderAuditPd.getHandleUser())) {
            //审核人员解密方法暂时关闭
            //String handler = this.getDecrypHandler(orderAuditPd.getHandleUser());
            String handler = orderAuditPd.getHandleUser();
            orderAuditPd.setHandleUser(handler);
            data.put("handleUser", handler);//值班人员多个
        }
        if (StringUtils.isNotEmpty(orderAuditPd.getAuditType())) {
            data.put("auditType", orderAuditPd.getAuditType());
        }
        if (StringUtils.isNotEmpty(orderAuditPd.getDOrI())) {
            data.put("dOrI", orderAuditPd.getDOrI());
        }
        if (StringUtils.isNotEmpty(orderAuditPd.getAirportType())) {
            data.put("airportType", orderAuditPd.getAirportType());
        }
        if (StringUtils.isNotEmpty(orderAuditPd.getAmount())) {
            data.put("amount", orderAuditPd.getAmount());
        }

        //********审批处理**************/
        boolean flag = activitiService.complementTask(orderAuditPd.getTaskId(), orderAuditPd.getAuditor(), orderAuditPd.getRemark(), data);
        log.info("补偿单审核提交,审核节点{},审核状态{},审核人{},任务执行状态{}", orderAuditPd.getProcessNode()
                , option, orderAuditPd.getAuditor(), flag);
        // 审批处理失败
        if (!flag) {
            throw new BusinessException(MessageCode.AUDIT_PROCESS_ERROR.getCode(), new String[]{orderId, orderAuditPd.getTaskId()});
        }
        boolean pushNextAudit = false;
        boolean processSuccessEnd = false;
        String status = "";
        boolean rejectCreateUser = false;
        ActTaskProcdefInfoVo procdef = orderAuditDaoImpl.getActProcdefInfo(orderAuditPd.getOrderId());
        log.info("补偿单审核-当前任务流程信息查询，结果{}", procdef.toString());
        //发起
        if (AuditStatusEnum.OPINION_START.getKey().equals(orderAuditPd.getOpinion())) {
            processNode = "1";
            status = AuditStatusEnum.OPINION_START.getStatus();
            //推送消息给下一个处理人
            pushNextAudit = true;
        }
        //不同意
        if (AuditStatusEnum.OPINION_NOT_AGREE.getKey().equals(orderAuditPd.getOpinion())) {
            processNode = "3";
            status = AuditStatusEnum.OPINION_NOT_AGREE.getStatus();
            processSuccessEnd = true;
            //如果不同意需要修改事故单状态
            int count = Integer.parseInt(String.valueOf(orderAuditDaoImpl.getOrderCountByAccidentId(orderId)));
            if (count == 0) {
                orderAuditDaoImpl.updateAccidentStatus(orderId);
            }
        }//驳回
        if (AuditStatusEnum.OPINION_REJECT.getKey().equals(orderAuditPd.getOpinion())) {
            processNode = "2";
            // 驳回状态下 判断是否驳回到发起人 ，驳回到发起人补偿单状态需要改成驳回
            CompensationProgressVo compensationProgressVo = orderAuditDaoImpl.queryCpsProgressQuery(orderId);
            String creatUser = compensationProgressVo.getCreateUser();
            if (creatUser.equals(previousHandlerUser)) {
                rejectCreateUser = true;
                status = AuditStatusEnum.OPINION_REJECT.getStatus();
                //如果驳回到发起人需要修改事故单状态
                int count = Integer.parseInt(String.valueOf(orderAuditDaoImpl.getOrderCountByAccidentId(orderId)));
                if (count == 0) {
                    orderAuditDaoImpl.updateAccidentStatus(orderId);
                }
            } else {
                status = AuditStatusEnum.OPINION_AGREE_MIDDLE.getStatus();
            }
        }
        //审批流程结束推送审批结果；未结束，推送审核消息给下一处理人
        // 例：若分2级审核 1级审核者：同意，补偿单状态为 审核中 。 2级审核者：同意，补偿单状态为 通过
        if (AuditStatusEnum.OPINION_AGREE.getKey().equals(orderAuditPd.getOpinion())) {

            if (activitiService.isProcessEnd(processInstancesId)) {
                processNode = "3";
                status = AuditStatusEnum.OPINION_AGREE.getStatus();
                processSuccessEnd = true;
            } else {
                processNode = "2";
                status = AuditStatusEnum.OPINION_AGREE_MIDDLE.getStatus();
                pushNextAudit = true;//推送消息给下一个处理人
            }
        }
        orderAuditPd.setProcessNode(processNode);
        String orderType = orderAuditDaoImpl.getOrderType(orderId);
        //保存审核数据
        if ((!("1".equals(orderType)))|| !processSuccessEnd) {//行查审核结束单独处理
            this.updAuditStatus(orderAuditPd, status);
        }else{
            List<OrderAudit> byOrderId = orderAuditDao.findByOrderId(orderAuditPd.getOrderId());
            OrderAudit orderAudit = byOrderId.stream().max(Comparator.comparing(OrderAudit::getCreateTime)).get();
            orderAudit.setStatus(STATIC_YES);//1已处理
            orderAudit.setAuditor(orderAuditPd.getAuditor());//处理人
            orderAudit.setOpinion(orderAuditPd.getOpinion());
            orderAudit.setOrderId(orderAuditPd.getOrderId());
            orderAudit.setRemark(orderAuditPd.getRemark());
            orderAuditDao.save(orderAudit);
        }

        //驳回 推送消息
        if (AuditStatusEnum.OPINION_REJECT.getKey().equals(option)) {
            log.info("补偿单审核-【审核流程驳回，推送审核消息给上一级处理人】，订单号 {}，处理人 {}",
                    orderId, previousHandlerUser);
            //根据orderId 查询 推送消息数据
            ActSendMessgeResultVo actSendMessgeResultVo = orderAuditDaoImpl.getSendMessageResultInfo(orderId);
            actSendMessgeResultVo.setRemarks(orderAuditPd.getRemark());
            // 如果是驳回到发起人，就推送审核结果
            if (rejectCreateUser) {
                //组装消息内容
                MessageSendForm sendForm = assembleResultSendMessage(actSendMessgeResultVo, previousHandlerUser);
                try {
                    messageService.sendMsg(sendForm);
                } catch (Exception e) {
                    throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
                }
            } else {
                // 给上一级所有审核人推送消息
                this.sendAuditMessage(orderId, orderAuditPd.getHandleUser());
            }
        }

        //推送处理结果
        if (processSuccessEnd) {
            log.info("补偿单审核-【推送处理结果】，订单号 {}", orderId);
            sendAuditResultMessage(orderAuditPd);
        }
        //审核流程未结束，推送审核消息给下一处理人。
        if (pushNextAudit) {
            log.info("补偿单审核-【审核流程未结束，推送审核消息给下一处理人】，订单号 {}，处理人 {}",
                    orderId, orderAuditPd.getHandleUser());
            this.pushAuditMessage(processInstancesId, orderId, orderAuditPd.getHandleUser());
        }
    }

    /**
     * Title： updAuditStatus <br>
     * Description： 更新审核对应补偿单状态，插入审核记录。<br>
     * author：傅欣荣 <br>
     * date：2020/6/19 10:54 <br>
     *
     * @param
     * @return
     */
    private void updAuditStatus(OrderAuditProcessDto orderAuditPd, String status) {
        String recordId = handleId();
        log.info("补偿单审核-审核后，更新补偿单状态，订单号{}，操作记录id{}，状态{}",
                orderAuditPd.getOrderId(), recordId, status);
        //更新补偿单申请表状态
        orderAuditDaoImpl.updOrderStatus(orderAuditPd.getOrderId(), status);
        //更新补偿单审核状态 【中间表】
        orderAuditDaoImpl.updOrderAuditStatic(orderAuditPd.getOrderId(), recordId);
        //保存审核记录
        this.saveAuditRecord(orderAuditPd, recordId);

    }

    /**
     * Title： handleId<br>
     * Description： 审批id生成规则<br>
     * author：傅欣荣 <br>
     * date：2020/6/16 14:15 <br>
     *
     * @param
     * @return
     */
    private String handleId() {
        //精确时间到分12位
        String idStr = uid.getIdStr();
        return idStr;
    }

    @Override
    public List<OrderAuditRecordVo> findOrderAuditRecord(String orderId) {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"补偿单id"});
        return orderAuditDaoImpl.findOrderAuditRecord(orderId);

    }

    @Override
    public List<OrderAuditRecordVo> findOrderAuditRecordAsc(String orderId) {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"补偿单id"});
        return orderAuditDaoImpl.findOrderAuditRecordAsc(orderId);

    }

    @Override
    public QueryResults findAuditProcessByUserId(OrderAuditDataDto orderAuditDataDto) {
        log.info("WEB-补偿单审核列表查询【请求参数】" + orderAuditDataDto.toString());
        Asserts.isNotEmpty(orderAuditDataDto.getUserId(), MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"用户id"});
        Asserts.isNotEmpty(orderAuditDataDto.getType(), MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"审核查询类型type"});
        return orderAuditDaoImpl.findAuditProcess(orderAuditDataDto);
    }


    @Override
    public QueryResults queryOrderAuditList(OrderAuditQueryParams orderAuditQueryParams) {
        log.info("H5-审核列表查询【前端请求参数】{}", orderAuditQueryParams.toString());
        return orderAuditDaoImpl.queryOrderAuditList(orderAuditQueryParams);
    }

    @Override
    public Integer countAudit(OrderAuditQueryParams orderAuditQueryParams) {
        List<OrderAuditQuertListVo> orderAuditQuertListVoList = orderAuditDaoImpl.queryCountAudit(orderAuditQueryParams);
        return orderAuditQuertListVoList.size();
    }

    @Override
    public Map<String, Object> getIsAocByUserId(String userId) {
        log.info("审核-查用户是否在aoc人员，参数{}", userId);
        Asserts.isNotEmpty(userId, MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"用户id"});
        String isAocUser = orderAuditDaoImpl.getIsAocByUserId(userId);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("isAocUser", isAocUser);
        return dataMap;
    }

    /**
     * Title：saveAuditRecord <br>
     * Description： 保存流程处理记录<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:38 <br>
     *
     * @param orderAuditPd
     * @return void
     */
    private void saveAuditRecord(OrderAuditProcessDto orderAuditPd, String recordId) {

        Date createTime = new Date();
        //审核流程跟踪相关
        OrderAudit orderAudit = new OrderAudit();
        orderAudit.setId(recordId);
        orderAudit.setProcessName(orderAuditPd.getProcessNode());//1发起 2审核中 3 审核结束等待发放
        orderAudit.setStatus(STATIC_YES);//1已处理
        orderAudit.setAuditor(orderAuditPd.getAuditor());//处理人
        orderAudit.setOpinion(orderAuditPd.getOpinion());
        orderAudit.setOrderId(orderAuditPd.getOrderId());
        orderAudit.setRemark(orderAuditPd.getRemark());
        orderAudit.setAuditTime(createTime);
        orderAudit.setCreateTime(createTime);
        orderAuditDao.save(orderAudit);
    }

    /**
     * Title：saveAuditDefault <br>
     * Description： 保存提交默认审核记录<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:36 <br>
     *
     * @param orderId, userId, createTime
     * @return void
     */
    private void saveAuditDefault(String orderId, String userId, Date createTime, String recordId) {
        OrderAudit orderAudit = new OrderAudit();
        orderAudit.setId(recordId);
        orderAudit.setOrderId(orderId);
        orderAudit.setProcessName(PROCESS_START);//1发起
        orderAudit.setStatus(STATIC_YES);//1已处理
        orderAudit.setAuditor(userId);//处理人
        orderAudit.setOpinion(AuditStatusEnum.OPINION_START.getKey());
        orderAudit.setAuditTime(createTime);
        orderAudit.setCreateTime(createTime);
        orderAuditDao.save(orderAudit);
    }

    /**
     * Title：saveActMiddleInfo <br>
     * Description：保存服务单于审核模块中间表<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:31 <br>
     *
     * @param orderId, pinId
     * @return void
     */
    private void saveActMiddleInfo(String orderId, String pinId, String recordId) {
        OrderActMiddle actMiddle = new OrderActMiddle();
        actMiddle.setOrderId(orderId);
        actMiddle.setActPinId(pinId);
        actMiddle.setRecordId(recordId);
        orderActMiddleDao.save(actMiddle);
    }

    @Override
    public List<OrderAuditProgressVo> getAuditProgressInfo(String orderId) {
        return orderAuditDaoImpl.getAuditProgressInfo(orderId);
    }

    @Override
    public CompensationProgressVo queryCpsProgressQuery(String orderId) {
        CompensationProgressVo cpVo = orderAuditDaoImpl.queryCpsProgressQuery(orderId);
        if (null != cpVo) {
            cpVo.setIsShowReEdit(verifyIsShowReEdit(orderId, cpVo.getCreateUser(), cpVo.getCpsProgressStatus()));
        }
        return cpVo;
    }

    /**
     * Title： verifyIsShowReEdit<br>
     * Description： 判断是否显示重新编辑按钮<br>
     * author：傅欣荣 <br>
     * date：2020/6/3 14:04 <br>
     *
     * @param
     * @return 1 显示
     */
    @Override
    public String verifyIsShowReEdit(String orderId, String createUser, String status) {

        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();

        //订单审核流程流转到发起人
        OrderAuditDataVo orderAuditData = orderAuditService.getUserTaskIdByOrderId(orderId, userId);
        String taskId = orderAuditData.getTaskId();
        String node = orderAuditData.getProcessNode();
        if (userId.equals(createUser) && ORDER_STATUS_REJECT.equals(status)
                && StringUtils.isNotBlank(taskId) && PROCESS_START.equals(node)) {
            return "1";
        }
        return new String();
    }

    @Override
    public Map<String, Object> getOrderPaxInfo(AuditOrderPaxInfo auditOrderPaxInfo) {
        Map<String, Object> dataMap = new HashMap<>();
        PaxInfosQueryResultVo membersCount = orderAuditDaoImpl.getOrderMembersCount(auditOrderPaxInfo.getOrderId());
        QueryResults paxList = orderAuditDaoImpl.getOrderPaxInfo(auditOrderPaxInfo);
        dataMap.put("membersCount", membersCount.getMembersCount());
        dataMap.put("paxList", paxList);
        return dataMap;
    }


    /**
     * Title: getDeptUserInfo
     * param: [deptUserInfoDto]
     *
     * @return com.swcares.psi.common.utils.query.QueryResults
     * Description:  查询审核人员列表service
     * author: makai
     * date: 2020-10-26
     */
    @Override
    public QueryResults getDeptUserInfo(DeptUserInfoDto deptUserInfoDto) {
        QueryResults deptUserInfoResult = orderAuditDaoImpl.getDeptUserInfo(deptUserInfoDto);
        List<AuditUserInfoVo> deptUserInfoList = (List<AuditUserInfoVo>) deptUserInfoResult.getList();
        deptUserInfoList.forEach(deptUserInfo -> {
            deptUserInfo.setSpell(PinyinUtil.getChineseCharacters(deptUserInfo.getUserName()));
        });
        deptUserInfoResult.setList(deptUserInfoList);
        return deptUserInfoResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendAuditMessage(String orderId, String userId) {
        //根据orderId 查询 推送消息数据
        ActSendMessgeVo actSendMessgeVo = orderAuditDaoImpl.getSendMessageInfo(orderId);
        log.info("------------->>>审核-给审核人消息推送-正在向用户：[{}] , 订单{}，查询发送消息{}", userId, orderId, actSendMessgeVo.toString());
        if (StringUtils.isBlank(userId)) {
            log.info("------------->>>审核-给审核人消息推送-正在向 订单{}，查询发送审核操作信息异常{}", orderId);
            throw new BusinessException(MessageCode.AUDIT_PUSH_USER_IS_NULL.getCode());
        }
        //组装消息内容
        MessageSendForm sendForm = assembleSendMessage(actSendMessgeVo, userId);

        log.info("------------->>>审核-给审核人消息推送-正在向订单{}，组装后发送消息内容{}，接收人{}", orderId, sendForm.toString(), userId);
        //向下节点审批人
        try {
            messageService.sendMsg(sendForm);
        } catch (Exception e) {
            throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
        }

    }

    /**
     * Title：sendAuditResultMessage <br>
     * Description： 发送审核结果消息 <br>
     * author：傅欣荣 <br>
     * date：2020/4/10 14:29 <br>
     *
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendAuditResultMessage(String orderId) {
        //根据orderId 查询 推送消息数据
        ActSendMessgeResultVo actSendMessgeResultVo = orderAuditDaoImpl.getSendMessageResultInfo(orderId);
        log.info("------------->>>审核结果消息推送-正在向订单{}，查询发送审核结果信息{}", orderId, actSendMessgeResultVo.toString());
        String userId = actSendMessgeResultVo.getCreateId();
        if (StringUtils.isBlank(userId)) {
            log.info("------------->>>审核结果消息推送-正在向 订单{}，查询发送审核结果信息异常{}", orderId);
            throw new BusinessException(MessageCode.AUDIT_PUSH_USER_IS_NULL.getCode());
        }
        //组装消息内容
        MessageSendForm sendForm = assembleResultSendMessage(actSendMessgeResultVo, userId);
        //向发起人
        try {
            messageService.sendMsg(sendForm);
        } catch (Exception e) {
            throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
        }
    }

    /**
     * Title：sendAuditResultMessage <br>
     * Description： 发送审核结果 参数携带部分数据（由于自动审核事务没有提交，通过SQL查询数据不精确）<br>
     * author：傅欣荣 <br>
     * date：2020/4/10 14:29 <br>
     *
     * @param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendAuditResultMessage(OrderAuditProcessDto orderAuditProcessDto) {
        String orderId = orderAuditProcessDto.getOrderId();
        //根据orderId 查询 推送消息数据
        ActSendMessgeResultVo actSendMessgeResultVo = orderAuditDaoImpl.getSendMessageResultInfo(orderId);
        actSendMessgeResultVo.setRemarks(orderAuditProcessDto.getRemark());
        if (AuditStatusEnum.OPINION_AGREE.getKey().equals(orderAuditProcessDto.getOpinion())) {
            actSendMessgeResultVo.setAuditResult("通过");
        } else if (AuditStatusEnum.OPINION_NOT_AGREE.getKey().equals(orderAuditProcessDto.getOpinion())) {
            actSendMessgeResultVo.setAuditResult(AuditStatusEnum.OPINION_NOT_AGREE.getValue());
        }
        if (StringUtils.isEmpty(actSendMessgeResultVo.getSumMoney())) {
            actSendMessgeResultVo.setSumMoney(orderAuditProcessDto.getAmount());
        }
        log.info("------------->>>审核结果消息推送-正在向订单{}，查询发送审核结果信息{}", orderId, actSendMessgeResultVo.toString());
        String userId = actSendMessgeResultVo.getCreateId();
        if (StringUtils.isBlank(userId)) {
            log.info("------------->>>审核结果消息推送-正在向 订单{}，查询发送审核结果信息异常{}", orderId);
            throw new BusinessException(MessageCode.AUDIT_PUSH_USER_IS_NULL.getCode());
        }
        //组装消息内容
        MessageSendForm sendForm = assembleResultSendMessage(actSendMessgeResultVo, userId);
        //向发起人
        try {
            messageService.sendMsg(sendForm);
        } catch (Exception e) {
            throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
        }
    }

    /**
     * Title： assembleSendMessage<br>
     * Description： 组装消息内容 发送审核结果<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 16:59 <br>
     *
     * @param sendMessage
     * @param userId      收消息人
     * @return
     */
    public MessageSendForm assembleResultSendMessage(ActSendMessgeResultVo sendMessage, String userId) {
        String[] userIds = userId.split(",");
        StringBuffer content = new StringBuffer();
        content.append("<h3>").append("审核结果：").append(sendMessage.getAuditResult()).append(" 备注：").append(sendMessage.getRemarks()).append("</h3>");
        content.append("<p>").append("补偿类型：").append(sendMessage.getPayTypeStr()).append("</p>");
        content.append("<p>").append("补偿航班：").append(sendMessage.getFlightNum()).append(" ")
                .append(sendMessage.getFlightDate()).append("</p>");
        content.append("<p>").append("补偿总金额：").append(sendMessage.getSumMoney()).append("元").append("</p>");
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getChildType() + "");
        sendForm.setMsgChildTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getChildTypeName());
        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getPcUrl()
                , sendMessage.getOrderId()));
        String mobileUrl = "";
        if (STATUS_IRREGULAR.equals(sendMessage.getPayType())) {
            mobileUrl = MessageFormat.format(MOBILE_URL_IRREGULAR, sendMessage.getOrderId(), sendMessage.getPayType());
        }
        if (STATUS_ABNORMAL_BAGGAGE.equals(sendMessage.getPayType())) {
            mobileUrl = MessageFormat.format(MOBILE_URL_ABNORMAL_BAGGAGE, sendMessage.getAccidentId(), sendMessage.getPayType());
        }
        if (STATUS_OVERBOOK.equals(sendMessage.getPayType())) {
            mobileUrl = MessageFormat.format(MOBILE_URL_OVERBOOK, sendMessage.getOrderId(), sendMessage.getPayType());
        }
        sendForm.setMobileUrl(mobileUrl);
        sendForm.setIsAudit(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getIsAudit());
        sendForm.setMsgDate(new Date());
        sendForm.setMsgTitle("补偿单审核结果");
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(sendMessage.getFlightNum());
        sendForm.setFlightDate(sendMessage.getFlightDate());
        sendForm.setMsgReplyUser(userIds);
        return sendForm;
    }


    /**
     * Title： assembleSendMessage<br>
     * Description： 组装消息内容 向审核人发起消息<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 16:59 <br>
     *
     * @param actSendMessgeVo
     * @param userId          收消息人
     * @return
     */
    public MessageSendForm assembleSendMessage(ActSendMessgeVo actSendMessgeVo, String userId) {
        String orderId = actSendMessgeVo.getOrderId();
        StringBuffer content = new StringBuffer();
        content.append("<h3>").append(actSendMessgeVo.getFlightDate()).append(" ").append(actSendMessgeVo.getFlightNum())
                .append(" ").append(actSendMessgeVo.getServiceCity()).append(" ").append(actSendMessgeVo.getPromoterName()).append("</h3>");
        content.append("<p>").append(actSendMessgeVo.getPayType()).append(": ").append("补偿人数").append(actSendMessgeVo.getPaxTotalCount()).append("人</p>")
                .append("<p>补偿标准:").append(StringUtils.isBlank(actSendMessgeVo.getCpsNum()) ? actSendMessgeVo.getSumMoney() : actSendMessgeVo.getCpsNum()).append("元/人").append("</p>");
        content.append("<p>").append("合计补偿金额：").append(actSendMessgeVo.getSumMoney()).append("元").append("</p>");
        MessageSendForm sendForm = getSendFormCommon(actSendMessgeVo.getOrderId());
        sendForm.setMsgType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getChildType() + "");
        sendForm.setMsgChildTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getChildTypeName());
        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getPcUrl(), orderId));
        sendForm.setMobileUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getMobileUrl(), orderId));
        sendForm.setIsAudit(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getIsAudit());
        sendForm.setMsgTitle("补偿单审核");
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(actSendMessgeVo.getFlightNum());
        sendForm.setFlightDate(actSendMessgeVo.getFlightDate());
        String[] userIds = userId.split(",");
        sendForm.setMsgReplyUser(userIds);
        return sendForm;
    }

    /**
     * Title： getSendFormCommon<br>
     * Description：公共发送消息实体 <br>
     * author：傅欣荣 <br>
     * date：2020/4/11 17:12 <br>
     *
     * @param
     * @return
     */
    public MessageSendForm getSendFormCommon(String orderId) {
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getChildType() + "");
        sendForm.setMsgChildTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getChildTypeName());
        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getPcUrl(), orderId));
        sendForm.setMobileUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getMobileUrl(), orderId));
        sendForm.setIsAudit(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }

    @Override
    public OrderAuditDataVo getUserTaskIdByOrderId(String orderId, String userId) {
        OrderAuditDataVo orderAuditDataVo = new OrderAuditDataVo();
        String taskId = orderAuditDaoImpl.getUserTaskIdByOrderId(orderId, userId);
        String processNode = orderAuditDaoImpl.getAuditNode((StringUtils.isBlank(taskId) ? "0" : taskId), orderId);
        orderAuditDataVo.setTaskId(taskId);
        orderAuditDataVo.setProcessNode(processNode);
        return orderAuditDataVo;
    }

    @Override
    public void deleteById(String id, boolean isReal) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        RepositoryService repositoryService = processEngine.getRepositoryService();
        if (isReal) {
            repositoryService.deleteDeployment(id);
        } else {
            repositoryService.suspendProcessDefinitionById(id);
        }
    }

    @Override
    public List<AuditUserInfoVo> getNextReceiverByRole(String roleName) {
        return orderAuditDaoImpl.getNextReceiverByRole(roleName);
    }

    /**
     * Title: AutoAuditHandler
     * param: [auditDto, processId]
     *
     * @return void
     * Description: 自动审核处理方法
     * author: makai
     * date: 2020-09-27
     */
    private void AutoAuditHandler(OrderAuditProcessDto auditDto, String processId,Map<String,Object>... businessParam) {
        String taskId = auditDto.getTaskId();
        if (StringUtils.isEmpty(taskId)) {
            taskId = (String) orderAuditDaoImpl.getTaskIdByProcessInstanceId(processId);
        }
        Asserts.isNotEmpty(taskId, MessageCode.LAUNCH_PROCESS_ERROR.getCode(), new String[]{auditDto.getOrderId()});
        Map<String, Object> variables = new HashMap<>();
        variables.put("result", "4");
        if(ObjectUtils.isNotEmpty(businessParam) && businessParam.length>0){
            variables.putAll(businessParam[0]);
        }
        taskService.complete(taskId, variables);
        OrderAuditProcessDto orderAuditProcessDto = new OrderAuditProcessDto();
        // TODO 字段确认后后续还需要设置其他值
        orderAuditProcessDto.setAmount(auditDto.getAmount());
        orderAuditProcessDto.setOpinion(AuditStatusEnum.OPINION_AGREE.getKey());
        orderAuditProcessDto.setOrderId(auditDto.getOrderId());
        orderAuditProcessDto.setTaskId(taskId);
        orderAuditProcessDto.setProcessNode("3");
        orderAuditProcessDto.setRemark("系统自动过审");
        orderAuditProcessDto.setAuditor(AUTO_AUDIT_TUNO);
        String orderType = orderAuditDaoImpl.getOrderType(auditDto.getOrderId());
        //保存审核数据
        if (!("1".equals(orderType))) {//行查单独处理
        this.updAuditStatus(orderAuditProcessDto, AuditStatusEnum.OPINION_AGREE.getStatus());
        }
        //自动审核后给发起人发送审核完成消息。
        this.sendAuditResultMessage(orderAuditProcessDto);
    }

    /**
     * Title: judgeIsProcessEnd
     * param: [taskId,orderId]
     *
     * @return com.swcares.psi.activity.audit.vo.AuditTaskInfoVo
     * Description: 判断当前流程信息，是否处于最终节点，并返回下一级审核人角色名
     * author: makai
     * date: 2020-10-14
     */
    @Override
    public AuditTaskInfoVo judgeIsProcessEnd(String taskId, String orderId) {
        AuditTaskInfoVo auditTaskInfoVo = new AuditTaskInfoVo();
        if (StringUtils.isNotEmpty(taskId)) {
            Task task = taskService.createTaskQuery() // 创建任务查询
                    .taskId(taskId) // 根据任务id查询
                    .singleResult();
            String taskDefinitionKey = task.getTaskDefinitionKey();
            // 判断是否处于最后一个节点
            String isProcessEnd = taskDefinitionKey.contains(PROCESS_END_SIGN) ? "0" : "1";
            auditTaskInfoVo.setIsProcessEnd(isProcessEnd);
            // 获取当前节点的DefinitionKey
            String auditLevel = taskDefinitionKey.split("_")[taskDefinitionKey.split("_").length - 1];
            String orderPayType = orderAuditDaoImpl.getOrderTypeByOrderId(orderId).getOrderPayType();
            // 下一级审核人角色
            if (STATUS_ABNORMAL_BAGGAGE.equals(orderPayType)) {
                switch (auditLevel) {
                    case "1":
                        auditTaskInfoVo.setRoleName(ABNORMAL_BAGGAGE_SECOND_LEVEL_ROLE_NAME);
                        break;
                    case "2":
                        auditTaskInfoVo.setRoleName(ABNORMAL_BAGGAGE_THREE_LEVEL_ROLE_NAME);
                        break;
                    case "3":
                        auditTaskInfoVo.setRoleName(ABNORMAL_BAGGAGE_FOUR_LEVEL_ROLE_NAME);
                        break;
                    default:
                        auditTaskInfoVo.setRoleName("");
                }
            }
        }
        return auditTaskInfoVo;
    }

}
