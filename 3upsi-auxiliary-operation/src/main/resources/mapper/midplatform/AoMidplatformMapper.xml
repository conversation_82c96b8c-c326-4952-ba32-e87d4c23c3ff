<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.ao.midplatform.mapper.AoMidplatformMapper">

    <select id="queryFlightInfo" resultType="com.swcares.psi.ao.midplatform.vo.AoMidplatformFlightInfoVo">
        SELECT
            DATE_FORMAT(FLTORG.STD,'%H:%i') AS std,
            FLTORG.FLT_DEPA_TERMINAL AS deptTerminal,
            FLTDST.FLT_ARRI_TERMINAL AS arrTerminal,
            DATE_FORMAT(FLTDST.STA,'%H:%i') AS sta
        FROM
            FLT_FLIGHT_REAL_INFO FLTORG,
            FLT_FLIGHT_REAL_INFO FLTDST
        WHERE 1=1
            and FLTORG.FLIGHT_DATE=#{flightDate}
            and FLTORG.FLIGHT_NUMBER=#{flightNo}
            and FLTORG.ORG=#{org}
            and FLTDST.FLIGHT_DATE=#{flightDate}
            and FLTDST.FLIGHT_NUMBER=#{flightNo}
            and FLTDST.DST=#{dst}
    </select>

    <select id="queryPaxInfo" resultType="com.swcares.psi.ao.midplatform.vo.AoMidplatformPaxInfoVo">
        SELECT
        PAX.PNR_REF AS pnr,
        PAX.TICKET_NUMBER AS tktNo,
        IFNULL(PAX.PASSENGER_NAME,PAX.PASSENGER_NAME_EN) AS paxName,
        CASE PAX.ID_TYPE WHEN 'NI' THEN '身份证' WHEN 'PP' THEN '护照' WHEN 'PSPT' THEN '护照'
        WHEN 'CC' THEN '信用卡' WHEN 'ID' THEN '军官证' WHEN 'UU' THEN '其他' END AS paxIdType,
        PAX.ID_NUMBER AS paxIdNo,
        PAX.FQTV_NUMBER AS ffpNo,
        PAX.LOYAL_LEVEL AS ffpType,
        GROUP_CONCAT(FPC.PHONE_NUMBER) AS tel,
        (<include refid="com.swcares.psi.base.data.mapper.FltPassengerRealInfoMapper.getPaxHedCategories"></include>
        ) AS paxType
        FROM
        FLT_PASSENGER_REAL_INFO PAX
        LEFT JOIN
        FLT_PASSENGER_CONTACTS FPC
        ON PAX.ID=FPC.PASSR_ID
        WHERE 1=1
        AND PAX.FLIGHT_DATE=#{flightDate}
        AND PAX.FLIGHT_NUMBER=#{flightNo}
        AND PAX.TICKET_NUMBER=#{tktNo}
        GROUP BY PAX.ID
    </select>

    <select id="queryMidplatformPaxInfo" resultType="com.swcares.psi.ao.midplatform.vo.AoOrderToMidplatformVo$Passenger">
        SELECT
        PAX.ID AS paxId,
        IFNULL(FFA.FLIGHT_TYPE, FFRI.FLIGHT_TYPE) AS flightType,
        PAX.FLIGHT_NUMBER AS flightNo,
        DATE_FORMAT(PAX.FLIGHT_DATE,'%Y-%m-%d') AS flightDate,
        PAX.ORG AS org,
        PAX.DST AS dst,
        IFNULL(FFA.STD, FFRI.STD) AS std,
        IFNULL(FFA.STA, FFRI.STA) AS sta,
        PAX.ID_TYPE AS paxIdType,
        PAX.ID_NUMBER AS paxIdNo,
        PAX.GENDER AS gender,
        IFNULL(PAX.PASSENGER_NAME,PAX.PASSENGER_NAME_EN) AS paxName,
        PAX.TICKET_NUMBER AS tktNo,
        GROUP_CONCAT(FPC.PHONE_NUMBER) AS tel
        FROM
        FLT_PASSENGER_REAL_INFO PAX
        LEFT JOIN
        FLT_PASSENGER_CONTACTS FPC
        ON PAX.ID=FPC.PASSR_ID
        LEFT JOIN FLT_FLIGHT_AC FFA
        ON FFA.FLIGHT_DATE = PAX.FLIGHT_DATE
        AND FFA.FLIGHT_NUMBER = PAX.FLIGHT_NUMBER
        AND FFA.ORG = PAX.ORG
        AND FFA.DST = PAX.DST
        LEFT JOIN FLT_FLIGHT_REAL_INFO FFRI
        ON FFRI.FLIGHT_DATE = PAX.FLIGHT_DATE
        AND FFRI.FLIGHT_NUMBER = PAX.FLIGHT_NUMBER
        AND FFRI.ORG = PAX.ORG
        where PAX.ID=#{paxId}
    </select>
</mapper>
