package com.swcares.psi.common.compensate.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.psi.flight.vo <br>
 * Description：服务单详情展示层 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * ate 2020年 03月06日 9:51 <br>
 * @version v1.0 <br>
 */
@Data
public class OrderDetailInfoVo {

    @ApiModelProperty(value = "赔偿单id")
    private String orderId;

    @ApiModelProperty(value = "赔偿类型 不正常航班补偿0、异常行李1、超售旅客2")
    private String payType;

    @ApiModelProperty(value = "事故单ID")
    private String accidentId;

    @ApiModelProperty(value = "服务航站")
    private String serviceSegment;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "延误原因")
    private String lateReason;

    @ApiModelProperty(value = "延误时长")
    private String delayTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "补偿人数")
    private String personTotalCount;

    @ApiModelProperty(value = "总补偿金额 临时和结案补偿金额都是这个字段")
    private int totalMoney;

    @ApiModelProperty(value = "成/童/婴 人数显示")
    private String membersCount;

    @ApiModelProperty(value = "客票差价")
    private String tktPriceDiff;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "航班信息id")
    private String flightId;

    @ApiModelProperty(value = "起始航站三字码")
    private String orig;

    @ApiModelProperty(value = "到达航站三字码")
    private String dest;

    @ApiModelProperty(value = "行李号")
    private String pkgNo;

    @ApiModelProperty(value = "是否是临时赔偿还是结案赔偿 0临时赔偿 1，结案赔偿")
    private String compensateType;

    @ApiModelProperty(value = "赔偿原因")
    private String paymentReason;

    @ApiModelProperty(value = "运输费用")
    private Integer transportPay;

    @ApiModelProperty(value = "超重费用")
    private Integer overWeightPay;

    @ApiModelProperty(value = "赔偿金额")
    private Integer paymentMoney;

    @ApiModelProperty(value = "箱包尺寸")
    private String pkgSize;

    @ApiModelProperty(value = "箱包数量")
    private String pkgCount;

    @ApiModelProperty(value = "箱包成本")
    private String pkgPrice;

    @ApiModelProperty(value = "事故单类型")
    private String accidentType;

    @ApiModelProperty(value = "子类型")
    private String orderSubType;

    @ApiModelProperty(value = "赔偿单备注")
    private String orderRemark;

    @ApiModelProperty(value = "说明")
    private String orderExplain;

    @ApiModelProperty(value = "卡卷赔付活动id")
    private String activityId;


    @JSONField(serialize = false)
    private String oiFlightDate;
    @JSONField(serialize = false)
    private String oiPlanDate;
    @JSONField(serialize = false)
    private String fFlightDate;
    @JSONField(serialize = false)
    private String oiStandardPrice;
    @JSONField(serialize = false)
    private String oiSpecialPrice;
    @JSONField(serialize = false)
    private String oiFixedPrice;
    @JSONField(serialize = false)
    private String oiPercentPrice;
    @JSONField(serialize = false)
    private String oiType;
    @JSONField(serialize = false)
    private String oiPayWay;
    @JSONField(serialize = false)
    private String oiPrice;

    @ApiModelProperty(value = "航延调整类型：T-临时性调整 P-计划性调整")
    private String adjustType;
    @ApiModelProperty(value = "是否线上审核：0 否，1 是")
    private String isOnLineAudit;

    @ApiModelProperty(value = "标识此赔付单包含的旅客所能享受的赔付方式`1-线上退款2-优惠券 12-两种都可以默认线上退款`")
    private String paymentClass;




}