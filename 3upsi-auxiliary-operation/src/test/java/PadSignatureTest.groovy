import cn.hutool.core.lang.UUID
import com.alibaba.fastjson.JSON
import com.swcares.psi.AoApplication
import com.swcares.psi.ao.pad.dto.AoOlpCostUpgradesOrderDto
import com.swcares.psi.ao.pad.service.impl.AoOlpCostUpgradesServiceImpl
import com.swcares.psi.ao.pad.service.impl.PadDataUploadService
import com.swcares.psi.base.data.api.dto.OlpCostUpgradeUpLoadInfoDto
import com.swcares.psi.base.fileuploadanddownload.UploadAndDownload
import com.swcares.psi.base.util.DateUtils
import com.swcares.psi.combine.user.impl.PsiUser
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import spock.lang.Specification

import java.nio.file.Files

@SpringBootTest(classes = AoApplication.class)
class PadSignatureTest extends Specification {
    def setSecurityContext() {
        def authentication = Mock(PsiUser)
        authentication.getUsername() >> "admin300"
        authentication.getRealName() >> "mock测试账号"
        authentication.getDeptId();
        String deptName = authentication.getDeptName();
        authentication.getPrincipal() >> "2520565"
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        mock.getUserAuthentication() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    @Autowired
    AoOlpCostUpgradesServiceImpl aoOlpCostUpgradesService;

    @Autowired
    UploadAndDownload uploadAndDownload;




    def "平板旅客上传签名PNG:signatureUpload"() {
        given:"mock file"
        def file = new File("/Users/<USER>/Typora/typora/imags/12.png")
        def fileBytes = Files.readAllBytes(file.toPath())
        def multipartFile = new MockMultipartFile("file", file.getName(), "application/vnd.openxmlformats-officedocument.wordprocessingml.document", fileBytes)

        when:"mock upload"
        Date date = new Date();
        if (StringUtils.isNotEmpty(multipartFile.getOriginalFilename())) {
            String fileName = UUID.randomUUID().toString();
            print("文件名称:"+fileName)
            String fileSuffix =
                    multipartFile.getOriginalFilename().substring(
                            multipartFile.getOriginalFilename().lastIndexOf("."));
            String remote = fileName + "-" + date.getTime() + fileSuffix;// 为防止重名文件需加上时间戳后缀
            uploadAndDownload.ftpUpload(multipartFile, "/upload/signature", remote);
            print("存到表中的字段:"+ DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDD)+"/"+remote)
        }

        then:"result info"
        noExceptionThrown()
    }


    def "下载航班旅客信息:loadPassengerAndFlightInfo"() {
        given:"构造查询参数，检查signatureUrl"
        setSecurityContext()
        def dto = new AoOlpCostUpgradesOrderDto();
        dto.setOrderStartDate("2023-09-01")
        dto.setOrderEndDate("2023-12-05")
        dto.setPageSize(20)
        dto.setCurrent(1)

        when:""
        // 查询出来，查看组装的url，然后开放接口直接调用查看。
        def order = aoOlpCostUpgradesService.downloadOrder(dto)

        then:
        print(order)
        noExceptionThrown()
    }

    @Autowired
    PadDataUploadService padDataUploadService;


    def "测试查询返回base64格式图片"(){
        // 20231206/0899b51d-2c7f-4c60-94ea-a661ad2cf6f2-1701826134447.png
        // http://localhost:9900/qhyu?realFilePath=20231206/0899b51d-2c7f-4c60-94ea-a661ad2cf6f2-1701826134447.png&signature=Y0nm8fQln32qCHGzqYIcVV35erXQPqg3TdG%2B%2FeB94hBEqXM%2F%2FMzAr6VSGHvr%2Fwq9QvWt3%2FjV0Ixo5dFfZ64Zjw%3D%3D
        given:""
        print(JSON.toJSONString(aoOlpCostUpgradesService.getOrderInfo("227339212582027622")))
    }

    def "pad上传接口测试"(){
        given:"模拟请求参数"
        setSecurityContext()
        // 现金上传 payType=0，其他都是线上
        String jsonstr = "{\"currencySpecies\":\"人民币\",\"currencySpeciesCode\":\"CNY\",\"dst\":\"CTU\",\"flightDate\":\"2023-12-05\",\"flightNo\":\"3U8888\",\"flightType\":\"A319\",\"id\":\"228427246119420233\",\"idNo\":\"51342419840918141X\",\"idType\":\"身份证\",\"newSeatNumber\":\"32A\",\"oldSeatNumber\":\"32B\",\"oldShippingSpace\":\"Y\",\"orderDate\":\"2023-12-07 10:20:06\",\"orderNo\":\"231207102088880001\",\"orderPaxId\":\"231204231555021\",\"orderPaxName\":\"王超\",\"orderPayStatus\":\"1\",\"orderStatus\":\"2\",\"orderTktNo\":\"8763033657409\",\"orderType\":\"机上升舱\",\"orderTypeCode\":\"6\",\"org\":\"PEK\",\"padOrderPaxId\":\"2310081054290046120710200665940\",\"paxId\":\"231204231555021\",\"paxName\":\"王超\",\"paxNo\":\"51342419840918141X\",\"paxStatus\":\"2\",\"payTime\":\"2023-12-07 10:20:30\",\"payType\":\"0\",\"price\":1,\"qrCode\":\"https://qr.alipay.com/bax09407h4n4uhzz9ooc0078\",\"remark\":\"\",\"segmentType\":\"国内\",\"signatureUrl\":\"20231207/268e72a1-32d0-41b9-9873-a76bd7709f30-1701915693454.png\",\"tktNo\":\"8763033657409\",\"totalPrice\":1,\"userName\":\"系统管理员3\",\"userNo\":\"admin300\"}"
        def array = JSON.parseArray(jsonstr, OlpCostUpgradeUpLoadInfoDto.class);

        when:"转换调用"
        padDataUploadService.uploadOrderData(array);

        then:""
        noExceptionThrown()
    }
}