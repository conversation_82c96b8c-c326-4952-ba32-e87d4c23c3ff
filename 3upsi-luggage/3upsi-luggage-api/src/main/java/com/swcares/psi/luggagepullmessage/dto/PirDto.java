package com.swcares.psi.luggagepullmessage.dto;

import com.swcares.psi.common.utils.encryption.Encryption;
import com.swcares.psi.common.utils.validatesign.ValidateBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@ApiModel("给川航提供的行查接口--事故单处理进度dto")
public class PirDto extends ValidateBaseDto implements Serializable {
    @ApiModelProperty(value = "航班日期")
    private String fltDate;
    @ApiModelProperty(value = "航班号")
    private String fltNo;
    @Encryption
    @ApiModelProperty(value = "证件号")
    private String psgCardNo;
    @ApiModelProperty(value = "旅客姓名")
    private String psgName;
    @ApiModelProperty(value = "事故单编号")
    private String pirCode;
    @ApiModelProperty(value = "事故类型")
    private String pirType;
}
