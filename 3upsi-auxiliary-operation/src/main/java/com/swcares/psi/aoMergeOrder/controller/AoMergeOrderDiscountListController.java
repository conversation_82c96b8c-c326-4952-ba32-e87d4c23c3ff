package com.swcares.psi.aoMergeOrder.controller;


import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradeInputTemplateReportDto;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeInputTemplateReportVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountListSaveDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountQueryDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderDiscountListService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <p>
 * 合并支付订单折扣清单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@Api(tags = "合并支付订单折扣")
@RequestMapping("/api/ao/order/discount")
public class AoMergeOrderDiscountListController {
@Autowired
private IAoMergeOrderDiscountListService aoMergeOrderDiscountListService;

    @GetMapping("/config/page")
    @ApiOperation(value = "列表查询")
    public RenderResult< PsiPage<AoMergeOrderDiscountListVo> > page(AoMergeOrderDiscountQueryDto dto ) {
        PsiPage<AoMergeOrderDiscountListVo> page = aoMergeOrderDiscountListService.getPage(dto);
        return RenderResult.success(page);
    }
    @PostMapping("/config/save")
    @ApiOperation(value = "保存")
    public RenderResult  save(@RequestBody AoMergeOrderDiscountListSaveDto dto ) {
        aoMergeOrderDiscountListService.saveConfig(dto);
        return RenderResult.success();
    }
    @PostMapping("/config/delete")
    @ApiOperation(value = "删除")
    public RenderResult  deleteConfig(String id ) {
        aoMergeOrderDiscountListService.deleteConfig(id);
        return RenderResult.success();
    }



    @GetMapping("/config/export")
    @ApiOperation(value = "产品打包折扣配置导出")
    public void export(AoMergeOrderDiscountQueryDto dto, HttpServletResponse response) throws Exception{
        response.setContentType("application/csv");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("产品打包折扣配置", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeOrderDiscountListVo> list = aoMergeOrderDiscountListService.getList(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), list ,AoMergeOrderDiscountListVo.class , "产品打包折扣配置",
                null);
    }
}

