package com.swcares.psi.aoMergeOrder.controller;


import com.component.core.exception.ColumnAuthException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.component.core.processor.FieldPermissionsProcessor;
import com.swcares.psi.ao.counterUpgrade.dto.ApplyPageDto;
import com.swcares.psi.ao.counterUpgrade.dto.UpgradeStatusUpDateApplyDto;
import com.swcares.psi.ao.counterUpgrade.vo.UpgradeStatusUpDateVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeApplyPageDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCounterUpgradeService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradePageVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeUpgradeStatusUpDateVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合并订单柜台升舱产品详情订单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@Api(tags = "合并订单柜台升舱")
@RequestMapping("/api/ao/mergeOrder/counterUpgrade")
public class AoMergeCounterUpgradeController {
    @Autowired
    private IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;
    @Autowired
    private FieldPermissionsProcessor fieldPermissionsProcessor;

    @GetMapping("/accounts/page")
    @ApiOperation(value = "柜台升舱结算报表分页查询")
    public RenderResult<AoMergeCounterUpgradePageVo> getCounterUpgradeAccountsReportPage(AoMergeCounterUpgradeAccountsReportDto dto){
        PsiPage<AoMergeCounterUpgradeAccountsReportVo> page = aoMergeCounterUpgradeService.getAccountsListPage(dto);
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVo = aoMergeCounterUpgradeService.payTypesSummaryMoney(dto);
        AoMergeCounterUpgradePageVo pageVo = new AoMergeCounterUpgradePageVo();
        pageVo.setPage(page);
        pageVo.setPayTypesSummaryMoneyVo(payTypesSummaryMoneyVo);
        return RenderResult.success(pageVo);
    }

    @GetMapping("/accounts/report")
    @ApiOperation(value = "柜台升舱结算报表导出")
    public void aoCounterUpgradeAccountExport(AoMergeCounterUpgradeAccountsReportDto dto, HttpServletResponse response, HttpServletRequest request) throws Exception {
        response.setContentType("application/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("柜台升舱结算报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeCounterUpgradeAccountsReportVo> list = aoMergeCounterUpgradeService.getAccountsList(dto);

        AoMergeCounterUpgradePageVo pageVo = new AoMergeCounterUpgradePageVo();
        PsiPage<AoMergeCounterUpgradeAccountsReportVo> page = new PsiPage<>();
        page.setRecords(list);
        pageVo.setPage(page);
        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            pageVo = (AoMergeCounterUpgradePageVo)fieldPermissionsProcessor.process(serverHttpRequest, pageVo);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            throw new ColumnAuthException(e);
        }
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVos = aoMergeCounterUpgradeService.payTypesSummaryMoney(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), Arrays.asList("柜台升舱结算", "合计"), Arrays.asList(pageVo.getPage().getRecords(),payTypesSummaryMoneyVos), Arrays.asList(AoMergeCounterUpgradeAccountsReportVo.class, PayTypesSummaryMoneyVo.class), null);

    }


    @ApiOperation(value = "柜台升舱-升舱状态修改申请 subOrderId 子订单id逗号分割 ,applyUpDateStatusReason 修改原因")
    @PostMapping("/upgradeStatusUpDateApply")
    public RenderResult upgradeStatusUpDateApply(String subOrderId,String applyUpDateStatusReason)  {
        aoMergeCounterUpgradeService.upgradeStatusUpDateApply(subOrderId,applyUpDateStatusReason);
        return RenderResult.success();
    }

    @ApiOperation(value = "柜台升舱-升舱状态修改申请撤销接口 subOrderIdList 子订单id集合")
    @PostMapping("/upgradeStatusUpDateRevocation")
    public RenderResult upgradeStatusUpDateRevocation(@RequestBody List<String> subOrderIdList)  {
        aoMergeCounterUpgradeService.upgradeStatusUpDateRevocation(subOrderIdList);
        return RenderResult.success();
    }


    @ApiOperation(value = "柜台升舱-修改升舱状态批量审批接口 idList 申请单id集合")
    @PostMapping("/batchApply")
    public RenderResult batchApply(@RequestBody List<String> idList) {
        aoMergeCounterUpgradeService.apply(idList);
        return RenderResult.success();
    }

    @ApiOperation(value = "柜台升舱-修改升舱状态审批表")
    @GetMapping("/applyPage")
    public RenderResult<PsiPage<AoMergeUpgradeStatusUpDateVo>> applyPage( AoMergeApplyPageDto dto) {
        PsiPage<AoMergeUpgradeStatusUpDateVo> aoMergeUpgradeStatusUpDateVoPsiPage = aoMergeCounterUpgradeService.applyPage(dto);
        return RenderResult.success(aoMergeUpgradeStatusUpDateVoPsiPage);
    }

}

