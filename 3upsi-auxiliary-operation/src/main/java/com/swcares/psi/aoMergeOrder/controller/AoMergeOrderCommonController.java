package com.swcares.psi.aoMergeOrder.controller;

import com.swcares.psi.ao.sceneSeats.service.AoSceneSeatsPriceConfigService;
import com.swcares.psi.ao.sceneSeats.vo.AoSceneSeatsPriceConfigInfoVo;
import com.swcares.psi.aoMergeOrder.dto.*;
import com.swcares.psi.aoMergeOrder.exception.AoMergeOrderException;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCashTurnListService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeRefundOrderInfoService;
import com.swcares.psi.aoMergeOrder.service.IAoMergeSuborderInfoService;
import com.swcares.psi.aoMergeOrder.service.impl.AoCommonService;
import com.swcares.psi.aoMergeOrder.service.impl.AoMegerOrderNotifyLockService;
import com.swcares.psi.aoMergeOrder.utils.I18nMsgUtils;
import com.swcares.psi.common.utils.PeUpgradeUtils;
import com.swcares.psi.aoMergeOrder.utils.vo.PeSeatVo;
import com.swcares.psi.aoMergeOrder.vo.*;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

@Api(tags = "合并订单控制器")
@RestController
@Slf4j
@RequestMapping("/api/ao/merge/common")
public class AoMergeOrderCommonController {

    @Autowired
    AoCommonService aoCommonService;
    @Autowired
    AoMegerOrderNotifyLockService aoMegerOrderNotifyLockService;

    @Autowired
    AoSceneSeatsPriceConfigService aoSceneSeatsPriceConfigService;

    @Autowired
    IAoMergeOrderInfoService aoMergeOrderInfoService;

    @Autowired
    IAoMergeCashTurnListService aoMergeCashTurnListService;

    @Autowired
    IAoMergeRefundOrderInfoService aoMergeRefundOrderInfoService;

    @Autowired
    IAoMergeSuborderInfoService aoMergeSuborderInfoService;


    @PostMapping("/saveOrder")
    @ApiOperation(value = "保存")
    public RenderResult<AoMergeOrderValidataDto> save(@RequestBody AoMergeOrderValidataDto dto ) {
        try {
            aoCommonService.saveOrder(dto);
        } catch (AoMergeOrderException e) {
            return RenderResult.success(e.getDto());
        }
        return RenderResult.success(dto);
    }


    @PostMapping("/orderValidata")
    @ApiOperation(value = "订单数据验证")
    public RenderResult<AoMergeOrderValidataDto> orderValidata(@RequestBody AoMergeOrderValidataDto dto ) {
        aoCommonService.validata(dto);
        if (dto.getCodes().size() > 0) {
            List<AoMergeOrderValidataDto.ReasonRefusal> reasonRefusals = I18nMsgUtils.messageConvert(dto.getCodes());
            dto.setReasonRefusal(reasonRefusals);
            return RenderResult.success(dto);
        }
        return RenderResult.success(dto);
    }


    /**
     * 前端worldPay支付确认
     * @param orderNo 订单号
     * @return
     */
    @ApiOperation(value = "前端worldPay支付确认")
    @GetMapping("/worldpayResultAffirm")
    public RenderResult worldpayResultAffirm(String orderNo) {
        return RenderResult.success(aoMegerOrderNotifyLockService.worldpayResultAffirm(orderNo)) ;
    }



    /**
     * 前端订单状态获取 支付确认
     * @param orderNo 订单号
     * @return
     */
    @ApiOperation(value = "订单状态获取")
    @GetMapping("/orderPayResultQuery")
    public RenderResult orderPayResultQuery(String orderNo) {
        return RenderResult.success(aoCommonService.orderPayResultQuery(orderNo)) ;
    }


    @PostMapping("/orderCancel")
    @ApiOperation(value = "订单取消")
    public RenderResult orderCancel(String mergeOrderNo) {
        aoMegerOrderNotifyLockService.orderCancel(mergeOrderNo);
        return RenderResult.success();
    }



    @GetMapping("/getSaetMap")
    @ApiOperation(value = "获取PE座位图")
    public RenderResult<List<PeSeatVo>> getSaetMap(String flightDate, String flightNo, String dst, String org) {
        List<PeSeatVo>  saetMapJson = PeUpgradeUtils.getSaetMap(flightDate, flightNo, dst, org);
        return RenderResult.success(saetMapJson);
    }





    @ApiOperation(value = "获取现场座位价格配置")
    @GetMapping("priceConfig")
    public RenderResult<List<AoSceneSeatsPriceConfigInfoVo>> priceConfig(String tktNo,String paxId) {
        List<AoSceneSeatsPriceConfigInfoVo> priceInfo = aoSceneSeatsPriceConfigService.getPaxPriceInfo(tktNo, paxId);
        return RenderResult.success(priceInfo);
    }

    /**
     * 发起退款
     * @param subOrderNo 子订单号
     * @return
     */
    @ApiOperation(value = "发起退款   mergeOrderNo总订单号,subOrderNo 子订单号")
    @PostMapping("startRefund")
    public RenderResult startRefund(String mergeOrderNo,String subOrderNo) {
        aoMegerOrderNotifyLockService.startRefund(mergeOrderNo,subOrderNo);
        return RenderResult.success() ;
    }
    /**
     * 取消退款申请
     * @param  subOrderNo 子订单号
     * @return
     */
    @ApiOperation(value = "取消退款申请  mergeOrderNo总订单号,subOrderNo 子订单号")
    @PostMapping("cancelRefund")
    public RenderResult cancelRefund(String mergeOrderNo,String  subOrderNo) {
        aoMegerOrderNotifyLockService.cancelRefund(mergeOrderNo,subOrderNo);
        return RenderResult.success() ;
    }


    /**
     * 退款
     * @return
     */
    @ApiOperation(value = "退款 ")
    @PostMapping("refund")
    public RenderResult refund(@RequestBody AoRefundDto dto) {
        aoMegerOrderNotifyLockService.refund(dto);
        return RenderResult.success() ;
    }

    /**
     * 现金收款
     * @param orderNo 订单号
     * @param posOrderNo pos机单号
     * @return
     */
    @ApiOperation(value = "现金收款")
    @PostMapping("cash")
    public RenderResult cash(String orderNo,String posOrderNo) {
        aoMegerOrderNotifyLockService.cash(orderNo,posOrderNo);
        return RenderResult.success() ;
    }



    @ApiOperation(value = "现金缴纳 servicePort服务航站  payType 支付类型 ,mergeOrderIds 合并订单ID逗号分割(批量不用传)")
    @PostMapping("cashTurn")
    public RenderResult<CashTurnSaveVo> cashTurn(String servicePort,String payType, String mergeOrderIds) {
        CashTurnSaveVo cashTurnSaveVo = aoMegerOrderNotifyLockService.cashTurn(servicePort, payType, mergeOrderIds);
        return RenderResult.success(cashTurnSaveVo) ;
    }


    @ApiOperation(value = "现金收款金额查询 servicePort服务航站  mergeOrderIds 合并订单ID逗号分割(批量不用传)")
    @GetMapping("cashTurnPrice")
    public RenderResult<CashTurnSaveVo> cashTurnPrice(String servicePort, String mergeOrderIds) {
        CashTurnSaveVo cashTurnSaveVo = aoCommonService.cashTurnPrice(servicePort, mergeOrderIds);
        return RenderResult.success(cashTurnSaveVo) ;
    }




    @ApiOperation(value = "现金缴纳结果查询 turnNo 缴纳单号  返回null缴纳单不存在")
    @GetMapping("cashTurnResult")
    public RenderResult<CashTurnSaveVo> cashTurnResult(String turnNo) {
        return RenderResult.success(aoMergeCashTurnListService.cashTurnResult(turnNo)) ;
    }

    @ApiOperation(value = "刷新缴纳二维码 turnNo 缴纳单号  表单提交  返回null缴纳单不存在")
    @PostMapping("refreshCashTurnResult")
    public RenderResult<CashTurnSaveVo> refreshCashTurnResult(String turnNo) {
        return RenderResult.success(aoMergeCashTurnListService.refreshCashTurnResult(turnNo)) ;
    }



    @ApiOperation(value = "H5订单列表查询")
    @GetMapping("h5/orderList")
    public RenderResult<PsiPage<AoMergeH5OrderListVo>> h5OrderList(AoMergeH5OrderListDto dto) {
        PsiPage<AoMergeH5OrderListVo> page = aoMergeOrderInfoService.h5OrderList(dto);
        return RenderResult.success(page) ;
    }


    @ApiOperation(value = "H5退款订单列表查询 表单")
    @GetMapping("h5/refundOrderList")
    public RenderResult<PsiPage<AoMergeH5RefundOrderListVo>> h5RefundOrderList(AoMergeH5RefundOrderListDto dto) {
        PsiPage<AoMergeH5RefundOrderListVo> page = aoMergeRefundOrderInfoService.h5RefundOrderList(dto);
        return RenderResult.success(page) ;
    }



    @ApiOperation(value = "订单详情  mergeOrderId 合并订单ID")
    @GetMapping("order/details")
    public RenderResult<AoMergeOrderDetailsVo> orderDetails(String mergeOrderId) {
        AoMergeOrderDetailsVo aoMergeOrderDetailsVo = aoMergeOrderInfoService.orderDetails(mergeOrderId);
        return RenderResult.success(aoMergeOrderDetailsVo);
    }


    @ApiOperation(value = "订单旅客集合  mergeOrderNo 合并订单订单号")
    @GetMapping("order/paxList")
    public RenderResult<List<AoMergeOrderPaxVo>> orderPaxList(String mergeOrderNo) {
        List<AoMergeOrderPaxVo> vo = aoMergeOrderInfoService.orderPaxList(mergeOrderNo);
        return RenderResult.success(vo);
    }

    @ApiOperation(value = "退款订单详情  refundOrderId 退款订单ID")
    @GetMapping("order/refundDetails")
    public RenderResult<AoMergeH5RefundOrderDetailsVo> refundOrderDetails(String refundOrderId) {
        AoMergeH5RefundOrderDetailsVo refundOrderDetailsVo = aoMergeRefundOrderInfoService.h5RefundOrderDetails(refundOrderId);
        return RenderResult.success(refundOrderDetailsVo) ;
    }

    @ApiOperation(value = "销售记录查询")
    @GetMapping("comparisonGrowth")
    public RenderResult<AoMergeComparisonGrowthVo> comparisonGrowth(){
        return RenderResult.success(aoMergeSuborderInfoService.getComparisonGrowth());
    }

    @ApiOperation(value = "销售记录详情查询")
    @GetMapping("salesRecord")
    public RenderResult<AoMergeSellDetailsVo> salesRecord(AoMergeSalesRecordDetailDto dto){
        return RenderResult.success(aoMergeSuborderInfoService.salesRecordByUser(dto));
    }

    @ApiOperation(value = "订单管理")
    @GetMapping("getMergeOrderInfo")
    public RenderResult<PsiPage<AoMergeOrderManageVo>> getMergeOrderInfo(AoMergeOrderManageDto dto){
        return RenderResult.success(aoMergeOrderInfoService.getMergeOrderInfo(dto));
    }

    @ApiOperation(value = "合并订单导出")
    @GetMapping("mergeOrderInfo/export")
    public void manySeatsOrderPage(AoMergeOrderManageDto dto, HttpServletResponse response, HttpServletRequest request) throws Exception{
        response.setContentType("application/csv");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("合并订单导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeOrderManageVo> list = aoMergeOrderInfoService.getMergeOrderInfoList(dto);
        List<AoMergeOrderManageVo.AoMergeSubOrderManageVo> subOrderList = aoMergeOrderInfoService.getSubOrderList(dto);
        ExcelUtils.writeExcel(response.getOutputStream(), Arrays.asList("总订单", "子订单"), Arrays.asList(list, subOrderList), Arrays.asList(AoMergeOrderManageVo.class, AoMergeOrderManageVo.AoMergeSubOrderManageVo.class), null);

    }


    @ApiOperation(value = "C端开票订单列表")
    @GetMapping("getMergeOrderInfoByCpax")
    public RenderResult<PsiPage<AoOrderInfoListByCpaxVo>> getMergeOrderInfoByCpax(AoOrderInfoListByCpaxDto dto){
        return RenderResult.success(aoMergeOrderInfoService.getMergeOrderInfoByCpax(dto));
    }



    @ApiOperation(value = "C端订单详情  orderId 订单ID")
    @GetMapping("orderDetailsByCpax")
    public RenderResult<AoOrderDetailsByCpaxVo> orderDetailsByCpax(String orderId) {
        return RenderResult.success(aoMergeOrderInfoService.orderDetailsByCpax(orderId)) ;
    }



    @ApiOperation(value = "更改支付方式")
    @GetMapping("updateOrderPayType")
    public RenderResult<String> updateOrderPayType(String mergeOrderNo,String payType,String currencySpecies) {
        String payPath = aoMegerOrderNotifyLockService.updateOrderPayType(mergeOrderNo, payType, currencySpecies);
        return RenderResult.success(payPath) ;
    }

}
