<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.aoMergeOrder.mapper.AoMergeCounterUpgradeMapper">


    <sql id="selectSql">
        select
        aosi.SUBORDER_ORDER_NO as subOrderNo,
        ampoi.BANK_ORDER_NO as bankOrderNo,
        ifnull(amob.EMD_NO,aosi.FINANCE_ORDER_NO) as emdNo,
         case amob.UPGRADE_STATUS
         when '2' then '升舱成功'
         when '3' then '升舱失败'
        end as upgradeStatus
         ,
        aosi.FLIGHT_NO as flightNo,
        date_format(aosi.FLIGHT_DATE, '%Y-%m-%d') flightDate,
        CONCAT(get_city_name(aosi.ORG), '-',aosi.ORG) as org,
        CONCAT(get_city_name(aosi.DST), '-',aosi.DST) as dst,
        amob.TKT_NO as tktNo,
        amob.ORIGINAL_CABIN as originalCabin,
        amob.NEW_CABIN as newCabin,
        amob.FLIGHT_MODEL AS flightModel,
        case ampoi.PAY_TYPE
            when '0' then '微信'
            when '1' then '易宝'
            when '2' then '支付宝'
            when '3' then '现金'
            when '4' then 'pos机'
            else 'worldpay'
        end as payType,
        CONCAT( ampoi.CURRENCY_SPECIES,'-', ampoi.CURRENCY_SPECIES_NAME) as currencySpecies,
        case amrel.PAY_EVENT
            when '1' then aosi.PRACTICAL_ORDER_PRICE
            when '4' then CONCAT('-',aosi.PRACTICAL_ORDER_PRICE)
        end as payOrRefundPrice,
         case
                   when aosi.FLIGHT_TYPE = 'D' then '国内'
                   when aosi.FLIGHT_TYPE = 'I' then '国际'
                   when aosi.FLIGHT_TYPE = 'R' then '地区'
                   end                                                 as FLIGHT_TYPE,
        case amrel.PAY_EVENT
            when '1' then '已完成'
            when '4' then '已退款'
        end as orderStatus,

        case amrel.PAY_EVENT
            when '1' then '支付成功'
            when '4' then '退款成功'
        end as payStatus,
        if(amrel.PAY_EVENT='4', date_format(amroi.REFUND_END_TIME,'%Y-%m-%d %H:%i:%s'), date_format(ampoi.PAY_TIME,'%Y-%m-%d %H:%i:%s')) as payDate,
        aosi.PAX_NAME,
        ifnull(aosi.FFP_NO,'-') as ffpNo,
        ifnull(aosi.PAX_TYPE,'-') as paxType,
        ifnull(aosi.PAX_CATEGORY,'-') as paxCategory,
        amoi.CREATE_NO,
        amoi.CREATE_USER,
        amoi.CREATE_USER_DEPARTMENT as department,
        amoi.TURN_NUMBER,
        case amoi.TURN_STATUS
        when '1' then '缴纳中'
        when '2' then '缴纳失败'
        when '3' then '已缴纳'
        end as turnStatus,
        amoi.AREA_COMPANY
        from
        ao_merge_suborder_info  aosi

        inner join ao_merge_counter_upgrade  amob on aosi.id=amob.AO_MERGE_SUBORDER_INFO_ID
        left join ao_merge_order_info amoi on aosi.AO_MERGE_ORDER_INFO_ID =amoi.ID
        left join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
        left join ao_merge_report_event_log amrel on amrel.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        left join ao_merge_refund_order_info amroi on amroi.AO_MERGE_SUBORDER_INFO_ID =aosi.id
    </sql>
    <sql id="whereSql">
        where aosi.SUBORDER_ORDER_STATUS in ('1','2','3','4')

        <if test="dto.orderDateStart != null and dto.orderDateStart !=''">
            and amoi.CREATE_DATE  <![CDATA[ >= ]]> date_format(#{dto.orderDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.orderDateEnd != null and dto.orderDateEnd !=''">
            and amoi.CREATE_DATE  <![CDATA[ <= ]]> date_format(#{dto.orderDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.flightDateStart != null and dto.flightDateStart !=''">
            and aosi.FLIGHT_DATE  <![CDATA[ >= ]]>  date_format(#{dto.flightDateStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.flightDateEnd != null and dto.flightDateEnd !=''">
            and aosi.FLIGHT_DATE <![CDATA[ <= ]]> date_format(#{dto.flightDateEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.payTimeStart != null and dto.payTimeStart !=''">
            and ampoi.PAY_TIME  <![CDATA[ >= ]]>  date_format(#{dto.payTimeStart}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="dto.payTimeEnd != null and dto.payTimeEnd !=''">
            and ampoi.PAY_TIME <![CDATA[ <= ]]> date_format(#{dto.payTimeEnd}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="dto.suborderOrderNoOrUpgradeOrderNo != null and dto.suborderOrderNoOrUpgradeOrderNo !=''">
            and (aosi.SUBORDER_ORDER_NO like concat('%',#{dto.suborderOrderNoOrUpgradeOrderNo},'%') or amob.UPGRADE_ORDER like concat('%',#{dto.suborderOrderNoOrUpgradeOrderNo},'%') )
        </if>

        <if test="dto.bankOrderNo != null and dto.bankOrderNo !=''">
            and ampoi.BANK_ORDER_NO like concat('%',#{dto.bankOrderNo},'%')
        </if>

        <if test="dto.flightNo != null and dto.flightNo !=''">
            and aosi.FLIGHT_NO =#{dto.flightNo}
        </if>

        <if test="dto.org != null and dto.org !=''">
            and aosi.ORG =#{dto.org}
        </if>

        <if test="dto.dst != null and dto.dst !=''">
            and aosi.DST =#{dto.dst}
        </if>

        <if test="dto.paxName != null and dto.paxName !=''">
            and aosi.PAX_NAME like concat('%',#{dto.paxName},'%')
        </if>

        <if test="dto.tktNo != null and dto.tktNo !=''">
            and amob.TKT_NO like concat('%',#{dto.tktNo},'%')
        </if>

        <if test="dto.flightType != null and dto.flightType !=''">
            and amob.FLIGHT_TYPE=#{dto.flightType}
        </if>

        <if test="dto.payType != null and dto.payType !=''">
            and find_in_set(ampoi.PAY_TYPE, #{dto.payType})
        </if>

        <if test="dto.payStatus != null and dto.payStatus !=''">
            and find_in_set(amrel.PAY_EVENT, #{dto.payStatus})
        </if>

        <if test="dto.department != null and dto.department !=''">
            and amoi.CREATE_USER_DEPARTMENT like concat('%',#{dto.department},'%')
        </if>

        <if test="dto.turnNumber != null and dto.turnNumber !=''">
            and amoi.TURN_NUMBER like concat('%',#{dto.turnNumber},'%')
        </if>

        <if test="dto.areaCompany != null and dto.areaCompany !=''">
            and amoi.AREA_COMPANY like concat('%',#{dto.areaCompany},'%')
        </if>
    </sql>

    <select id="getAccountsListPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
    </select>

    <select id="getAccountsList" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeCounterUpgradeAccountsReportVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto">
        <include refid="selectSql"></include>
        <include refid="whereSql"></include>
    </select>


    <select id="payTypesSummaryMoney" resultType="com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo"
            parameterType="com.swcares.psi.aoMergeOrder.dto.AoMergeCounterUpgradeAccountsReportDto">
        select t.* from (
        select
        sum(CASE WHEN rs.PAY_TYPE = '0' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS wxMoney,
        sum(CASE WHEN rs.PAY_TYPE = '1' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS bankCardMoney,
        sum(CASE WHEN rs.PAY_TYPE = '2'  THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end ) AS alipayMoney,
        sum(CASE WHEN (rs.PAY_TYPE = '3' or rs.PAY_TYPE = '4') THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end) AS cashMoney,
        sum(CASE WHEN rs.PAY_TYPE = '5' THEN rs.PRACTICAL_ORDER_PRICE ELSE 0 end) AS worldPay,
        sum(rs.PRACTICAL_ORDER_PRICE  ) AS allMoney,
        case  rs.CURRENCY_SPECIES
        when 'CNY' then  concat(rs.CURRENCY_SPECIES,'(人民币)')
        else   concat(rs.CURRENCY_SPECIES,'(',(SELECT  DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE_CODE='currency_type' AND DATA_STATUS='0' and DATA_CODE=rs.CURRENCY_SPECIES limit 1),')')
        end as currencySpecies
        from (
        select

        CASE amrel.PAY_EVENT
        when '4' then
        concat('-',PRACTICAL_ORDER_PRICE)
        else PRACTICAL_ORDER_PRICE
        end as PRACTICAL_ORDER_PRICE,
        ampoi.PAY_TYPE,
        ampoi.CURRENCY_SPECIES
        from
        ao_merge_suborder_info  aosi

        inner join ao_merge_counter_upgrade  amob on aosi.id=amob.AO_MERGE_SUBORDER_INFO_ID
        left join ao_merge_order_info amoi on aosi.AO_MERGE_ORDER_INFO_ID =amoi.ID
        left join ao_merge_pay_order_info ampoi on ampoi.id=amoi.ORDER_PAY_ID
        left join ao_merge_report_event_log amrel on amrel.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        left join ao_merge_refund_order_info amroi on amroi.AO_MERGE_SUBORDER_INFO_ID =aosi.id
        <include refid="whereSql"></include>
        ) rs  group by rs.CURRENCY_SPECIES
        )t where t.allMoney > 0
    </select>


    <select id="applyPage" resultType="com.swcares.psi.aoMergeOrder.vo.AoMergeUpgradeStatusUpDateVo">
        SELECT
        amoi.id as mergeOrderId,
        aoi.id as suborderId,
        upgrade.id as upgradeId,
        aoi.PAX_NAME as paxName,
        aoi.PAX_NO as paxNo,
        aoi.SUBORDER_ORDER_NO as subOrderNo,
        date_format(amoi.CREATE_DATE,'%Y-%m-%d') as orderDate,
        case
        when aoi.SUBORDER_ORDER_PAY_STATUS = '0' then '待支付'
        when aoi.SUBORDER_ORDER_PAY_STATUS = '1' then '已完成'
        when aoi.SUBORDER_ORDER_PAY_STATUS = '2' then '支付失败'
        when aoi.SUBORDER_ORDER_PAY_STATUS = '3' then '退款失败'
        when aoi.SUBORDER_ORDER_PAY_STATUS = '4' then '退款成功'
        else '支付中'
        end                                                 as suborderOrderPayStatus,
        case
        when aoi.SUBORDER_ORDER_STATUS = '0' then '待支付'
        when aoi.SUBORDER_ORDER_STATUS = '1' then '已完成'
        when aoi.SUBORDER_ORDER_STATUS = '2' then '待退款'
        when aoi.SUBORDER_ORDER_STATUS = '3' then '退款中'
        when aoi.SUBORDER_ORDER_STATUS = '4' then '已退款'
        when aoi.SUBORDER_ORDER_STATUS = '5' then '已过期'
        when aoi.SUBORDER_ORDER_STATUS = '6' then '已取消'
        else '支付中'
        end                                                 as ORDER_STATUS,
        case
        when upgrade.UPGRADE_STATUS = '0' then '初始化'
        when upgrade.UPGRADE_STATUS = '1' then '升舱中'
        when upgrade.UPGRADE_STATUS = '2' then '升舱成功'
        when upgrade.UPGRADE_STATUS = '3' then '升舱失败'
        end                                                 as upgradeStatus,
        aoi.PRACTICAL_ORDER_PRICE,
        case
        when ampoi.PAY_TYPE = '0' then '微信'
        when ampoi.PAY_TYPE = '1' then '易宝'
        when ampoi.PAY_TYPE = '2' then '支付宝'
        when ampoi.PAY_TYPE = '3' then '现金'
        when ampoi.PAY_TYPE = '5' then 'worldPay'
        else '现金'
        end                                                 as  payType,
        date_format(ampoi.PAY_TIME,'%Y-%m-%d %H:%i:%s') as payTime,
        concat(upgrade.APPLY_USER,upgrade.APPLY_USER_NO) as applyUserNo,
        date_format(upgrade.APPLY_DATE,'%Y-%m-%d %H:%i:%s') as applyDate
        FROM
        ao_merge_counter_upgrade upgrade
        LEFT JOIN ao_merge_suborder_info aoi
        ON upgrade.AO_MERGE_SUBORDER_INFO_ID = aoi.id
        left join ao_merge_order_info amoi
        on amoi.id=aoi.AO_MERGE_ORDER_INFO_ID
        left join  ao_merge_pay_order_info ampoi
        on ampoi.id=amoi.ORDER_PAY_ID
        <where>
            upgrade.UPGRADE_UPDATE_STATUS is not null
            <if test="dto.startDate != null and dto.startDate != ''">
                and upgrade.APPLY_DATE <![CDATA[ >= ]]> CONCAT(#{dto.startDate},' 00:00:00')
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                and upgrade.APPLY_DATE <![CDATA[ <= ]]> CONCAT(#{dto.endDate},' 23:59:59')
            </if>
            <if test="dto.status != null and dto.status != ''">
                and upgrade.UPGRADE_UPDATE_STATUS = #{dto.status}
            </if>
        </where>
    </select>


</mapper>
