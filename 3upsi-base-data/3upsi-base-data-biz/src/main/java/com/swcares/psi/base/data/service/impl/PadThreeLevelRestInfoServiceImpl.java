package com.swcares.psi.base.data.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.base.data.api.dto.PadThreeLevelRestInfoPageDto;
import com.swcares.psi.base.data.api.dto.PadThreeLevelRestUpdateDto;
import com.swcares.psi.base.data.api.entity.PadThreeLevelRestInfo;
import com.swcares.psi.base.data.api.vo.PadThreeLevelRestInfoPageVo;
import com.swcares.psi.base.data.mapper.PadThreeLevelRestInfoMapper;
import com.swcares.psi.base.data.service.PadThreeLevelRestInfoService;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.security.util.AuthenticationUtil;
import com.swcares.psi.common.utils.query.PsiPage;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * Created on  2022/5/20
 * Title:       [平板端三级休息区]
 * Description: [平板端三级休息区]
 *
 * @author: like
 * @version: 1.0
 */
@Service
public class PadThreeLevelRestInfoServiceImpl extends ServiceImpl<PadThreeLevelRestInfoMapper, PadThreeLevelRestInfo> implements PadThreeLevelRestInfoService {

    @Override
    public IPage<PadThreeLevelRestInfoPageVo> getPage(PadThreeLevelRestInfoPageDto dto) {
        IPage<PadThreeLevelRestInfoPageVo> page = new PsiPage<>(dto);
        return baseMapper.getPage(page, dto);
    }

    @Override
    public Boolean deleteRest(String ids) {
        List<PadThreeLevelRestInfo> updateList = new ArrayList<>();
        Arrays.asList(ids.split(",")).forEach(id -> {
            PadThreeLevelRestInfo padThreeLevelRestInfo = new PadThreeLevelRestInfo();
            padThreeLevelRestInfo.setId(id);
            padThreeLevelRestInfo.setIsDel(PadThreeLevelRestInfo.IS_DEL_YES);
            updateList.add(padThreeLevelRestInfo);
        });
        return this.updateBatchById(updateList);
    }

    @Override
    public Boolean updateStatus(String ids, String status) {
        String currentUser = String.valueOf(AuthenticationUtil.getAuthentication().getPrincipal());
        List<PadThreeLevelRestInfo> updateList = new ArrayList<>();
        Arrays.asList(ids.split(",")).forEach(id -> {
            PadThreeLevelRestInfo padThreeLevelRestInfo = this.getById(id);
            padThreeLevelRestInfo.setStatus(status);
            padThreeLevelRestInfo.setUpdateTime(new Date());
            padThreeLevelRestInfo.setUpdateUser(currentUser);
            updateList.add(padThreeLevelRestInfo);
        });
        return this.updateBatchById(updateList);
    }

    @Override
    public Boolean updateInfo(PadThreeLevelRestUpdateDto dto) {
        String currentUser = String.valueOf(AuthenticationUtil.getAuthentication().getPrincipal());
        if (StringUtils.isNotBlank(dto.getId())) {
            PadThreeLevelRestInfo byId = this.getById(dto.getId());
            if (byId != null && (!byId.getDst().equals(dto.getDst())
                    || !byId.getOrg().equals(dto.getOrg())
                    || !byId.getFlightNo().equals(dto.getFlightNo()))) {
                Integer count = this.lambdaQuery()
                        .eq(PadThreeLevelRestInfo::getFlightNo, dto.getFlightNo())
                        .eq(PadThreeLevelRestInfo::getOrg, dto.getOrg())
                        .eq(PadThreeLevelRestInfo::getDst, dto.getDst())
                        .eq(PadThreeLevelRestInfo::getIsDel, "0")
                        .count();
                if (count != null && count != 0) {
                    throw new BusinessException(MessageCode.THREE_REPEAT_ADD.getCode());
                }
            }
            LocalDateTime effectTime = null;
            LocalDateTime failureTime = null;
            if (StringUtils.isNotBlank(dto.getEffectTime())) {
                effectTime = LocalDateTime.parse(dto.getEffectTime() + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtils.isNotBlank(dto.getFailureTime())) {
                failureTime = LocalDateTime.parse(dto.getFailureTime() + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            return this.lambdaUpdate()
                    .set(PadThreeLevelRestInfo::getUpdateTime,new Date())
                    .set(PadThreeLevelRestInfo::getUpdateUser,currentUser)
                    .set(PadThreeLevelRestInfo::getFlightNo,dto.getFlightNo())
                    .set(PadThreeLevelRestInfo::getOrg,dto.getOrg())
                    .set(PadThreeLevelRestInfo::getDst,dto.getDst())
                    .set(PadThreeLevelRestInfo::getPlaneCode,dto.getPlaneCode())
                    .set(PadThreeLevelRestInfo::getPlaneType,dto.getPlaneType())
                    .set(PadThreeLevelRestInfo::getRestSeat,dto.getRestSeat())
                    .set(PadThreeLevelRestInfo::getEffectTime, effectTime)
                    .set(PadThreeLevelRestInfo::getFailureTime, failureTime)
                    .eq(PadThreeLevelRestInfo::getId,dto.getId())
                    .update();

        } else {
            PadThreeLevelRestInfo padThreeLevelRestInfo = new PadThreeLevelRestInfo();
            BeanUtils.copyProperties(dto, padThreeLevelRestInfo);

            LocalDateTime effectTime = null;
            LocalDateTime failureTime = null;
            if (StringUtils.isNotBlank(dto.getEffectTime())) {
                effectTime = LocalDateTime.parse(dto.getEffectTime() + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtils.isNotBlank(dto.getFailureTime())) {
                failureTime = LocalDateTime.parse(dto.getFailureTime() + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            padThreeLevelRestInfo.setEffectTime(effectTime);
            padThreeLevelRestInfo.setFailureTime(failureTime);

            Integer count = this.lambdaQuery()
                    .eq(PadThreeLevelRestInfo::getFlightNo, padThreeLevelRestInfo.getFlightNo())
                    .eq(PadThreeLevelRestInfo::getOrg, padThreeLevelRestInfo.getOrg())
                    .eq(PadThreeLevelRestInfo::getDst, padThreeLevelRestInfo.getDst())
                    .eq(PadThreeLevelRestInfo::getIsDel, "0")
                    .count();
            if (count != null && count != 0) {
                throw new BusinessException(MessageCode.THREE_REPEAT_ADD.getCode());
            }
            padThreeLevelRestInfo.setStatus(PadThreeLevelRestInfo.STATUS_ENABLE);
            padThreeLevelRestInfo.setIsDel(PadThreeLevelRestInfo.IS_DEL_N0);
            padThreeLevelRestInfo.setCreateUser(currentUser);
            padThreeLevelRestInfo.setCreateTime(new Date());
            padThreeLevelRestInfo.setUpdateTime(new Date());
            return this.save(padThreeLevelRestInfo);
        }
    }
}
