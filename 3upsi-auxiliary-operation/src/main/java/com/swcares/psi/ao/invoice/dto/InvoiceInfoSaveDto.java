package com.swcares.psi.ao.invoice.dto;

import com.swcares.psi.common.invoice.v2.InvoiceParam;
import com.swcares.psi.common.utils.encryption.Encryption;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2021/1/19 13:20
 */
@Data
public class InvoiceInfoSaveDto {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "产品ID 表示业务表的id")
    private String productId;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "抬头类型 0企业 1个人或者非企业")
    private String titleType;

    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    @ApiModelProperty(value = "税号")
    private String taxNo;

    @ApiModelProperty(value = "发票金额")
    private String invoicePrice;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品金额")
    private String productPrice;

    @ApiModelProperty(value = "商品数量")
    private Integer productNumber;

    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ApiModelProperty(value = "开票操作人名称")
    private String paxName;

    @Encryption
    @ApiModelProperty(value = "电话号码")
    private String phone;

    @Encryption
    @ApiModelProperty(value = "购买方电话")
    private String purchaserPhone;

    @ApiModelProperty(value = "购买方地址")
    private String purchaserAddress;

    @ApiModelProperty(value = "购买方银行")
    private String purchaserBank;

    @ApiModelProperty(value = "购买方银行账号")
    private String purchaserBankNo;

    @ApiModelProperty(value = "信息来源 1 辅营B端 2  C端小程序 3  ipad")
    private String sourceInformation;


    @ApiModelProperty(value = "非参数")
    private InvoiceParam.Builder builder;

}
