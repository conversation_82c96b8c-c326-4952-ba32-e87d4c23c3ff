package com.swcares.psi.permission.column.dao;

import com.swcares.psi.base.base.BaseDAO;
import com.swcares.psi.common.permission.impl.column.model.ColumnPermission;
import com.swcares.psi.permission.entity.ColumnPermissionData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：ColumnPermissionDao
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/4/25 15:13
 * @version： v1.0
 */
@Repository
public class ColumnPermissionDao {

    @Autowired
    private BaseDAO baseDAO;
    /**
     * @title removePermissions
     * @description 移除某个角色的列数据权限
     * <AUTHOR>
     * @date 2022/4/25 16:34
     * @param sql
     * @param parameters 角色id
     * @return void
     */
    public void removePermissions(String sql, String parameters) {
        if (StringUtils.isNotBlank(parameters)){
            baseDAO.batchUpdate(sql, parameters);
        }
    }

    /**
     * @title getColumnPermissionByResource
     * @description 查询列数据权限
     * <AUTHOR>
     * @date 2022/4/25 20:17
     * @param sql
     * @param map
     * @return java.util.List<com.swcares.psi.user.permission.column.model.entity.ColumnPermissionData>
     */
    public List<ColumnPermissionData> getColumnPermissionByResource(String sql, Map<String,Object> map){
        if (StringUtils.isNotBlank(sql)){
            return (List<ColumnPermissionData>) baseDAO.queryEntity(sql,map, ColumnPermissionData.class);
        }
        return null;
    }
    /**
     * @title getColumnPermissionByResources
     * @description 重载用户列数据权限到redis
     * <AUTHOR>
     * @date 2022/5/17 10:53
     * @param sql
     * @param map
     * @return java.util.List<com.swcares.psi.common.permission.impl.column.model.ColumnPermission>
     */
    public List<ColumnPermission> getColumnPermissionByResources(String sql, Map<String,Object> map){
        if (StringUtils.isNotBlank(sql)){
            return (List<ColumnPermission>) baseDAO.findBySQL_comm(sql,map, ColumnPermission.class);
        }
        return null;
    }

}
