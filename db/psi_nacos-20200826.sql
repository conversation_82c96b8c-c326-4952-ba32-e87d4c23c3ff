/*
 Navicat Premium Data Transfer

 Source Server         : Local
 Source Server Type    : MySQL
 Source Server Version : 50728
 Source Host           : localhost:3306
 Source Schema         : psi_config

 Target Server Type    : MySQL
 Target Server Version : 50728
 File Encoding         : 65001

 Date: 26/08/2020 15:52:22
*/
CREATE DATABASE  `psi_nacos` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

USE `psi_nacos`;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `c_use` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `effect` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `c_schema` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfo_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 96 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_info
-- ----------------------------
INSERT INTO `config_info` VALUES (21, '3upsi-auth-dev.yml', 'dev', 'server:\n  port: 3000\n# 数据源\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    username: psi\n    password: 123456\n    url: jdbc:mysql://*************:3306/psi_3u?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai\n  freemarker:\n    allow-request-override: false\n    allow-session-override: false\n    cache: true\n    charset: UTF-8\n    check-template-location: true\n    content-type: text/html\n    enabled: true\n    expose-request-attributes: false\n    expose-session-attributes: false\n    expose-spring-macro-helpers: true\n    prefer-file-system-access: true\n    suffix: .ftl\n    template-loader-path: classpath:/templates/', 'cb8395c07c8cb681177a740b623247ab', '2020-05-26 09:05:44', '2020-08-18 15:52:00', NULL, '127.0.0.1', '', 'dev', 'null', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (23, '3upsi-gateway-dev.yml', 'dev', 'server:\n  port: 8999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n        #standard-services 模块\n        - id: 3upsi-standard-services\n          uri: lb://3upsi-standard-services\n          predicates:\n            - Path=/standard-services/**\n        #standard-services-pssn 模块\n        - id: 3upsi-standard-services-pssn\n          uri: lb://3upsi-standard-services-pssn\n          predicates:\n            - Path=/standard-services-pssn/**\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '7b95beb87f02c8e35e78bebd814d9c81', '2020-05-26 09:05:44', '2020-08-26 15:36:24', NULL, '127.0.0.1', '', 'dev', 'gatway-dev', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (24, '3upsi-monitor-dev.yml', 'dev', 'server:\n  port: 5001\nspring:\n  # 安全配置\n  security:\n    user:\n      name: ENC(CHCM6yac891TzafXkyADWw==)     # psi\n      password: ENC(Xmrwk3/Vg5YViLRWzV4AYQ==) # psi\n  boot:\n    admin:\n      monitor:\n        read-timeout: 2000\n', 'b9955c33683ac17cc6828ecb471a5503', '2020-05-26 09:05:44', '2020-05-28 13:22:33', NULL, '0:0:0:0:0:0:0:1', '', 'dev', 'null', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (25, '3upsi-upms-biz.yml', 'dev', 'security:\n  oauth2:\n    client:\n      client-id: ENC(HFsHxL0H5YLAeqWRhOJqbQ==)\n      client-secret: ENC(siAtxj3JdbSQItuiVhSEog==)\n      scope: server\n# 数据源\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    username: psi\n    password: 123456\n    url: jdbc:mysql://*************:3306/psi_3u?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowMultiQueries=true&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai\nmybatis-plus:\n  configuration:\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl', '80a30e28e5e1fb659538c885c6c72e06', '2020-05-26 09:05:44', '2020-08-18 15:52:49', NULL, '127.0.0.1', '', 'dev', 'null', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (58, '3upsi-upms-biz.yml', 'dev', 'security:\n  oauth2:\n    client:\n      client-id: ENC(imENTO7M8bLO38LFSIxnzw==)\n      client-secret: ENC(i3cDFhs26sa2Ucrfz2hnQw==)\n      scope: server\n\n# 数据源\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    username: root\n    password: 1q2w3e4r\n    url: jdbc:mysql://*************:3306/psi?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowMultiQueries=true&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai\n', 'c84b2125283a059faff1e8fe3e1a946d', '2020-05-26 14:22:33', '2020-05-26 14:22:33', NULL, '0:0:0:0:0:0:0:1', '', '50694230-0ebf-4798-89b4-c51850c90225', NULL, NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (59, 'application-dev.yml', 'dev', '# 加解密根密码\njasypt:\n  encryptor:\n    password: encryptor #根密码\n\n# Spring 相关\nspring:\n  redis:\n    password: 1q2w3e4r\n    host: *************\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n\n# feign 配置\nfeign:\n  hystrix:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n\n# hystrix 配置\nhystrix:\n  command:\n    default:\n      execution:\n        isolation:\n          strategy: SEMAPHORE\n          thread:\n            timeoutInMilliseconds: 60000\n  shareSecurityContext: true\n\n#请求处理的超时时间\nribbon:\n  ReadTimeout: 10000\n  ConnectTimeout: 10000\n\n# mybaits-plus配置\nmybatis-plus:\n  mapper-locations: classpath:/mapper/*mapper.xml\n  global-config:\n    banner: false\n    db-config:\n      id-type: auto\n      table-underline: true\n      logic-delete-value: 1\n      logic-not-delete-value: 0\n  configuration:\n    map-underscore-to-camel-case: true\n\n# spring security 配置\nsecurity:\n  oauth2:\n    resource:\n      loadBalanced: true\n      token-info-uri: http://3upsi-auth/oauth/check_token\n    # 通用放行URL，服务个性化，请在对应配置文件覆盖\n    ignore:\n      urls:\n        - /v2/api-docs\n        - /actuator/**\n# swagger 配置\nswagger:\n  title: Psi Swagger API\n  license: Powered By Psi4cloud\n  licenseUrl:  https://psi4cloud.com\n  terms-of-service-url: https://psi4cloud.com\n  contact:\n    email: <EMAIL>\n    url: https://www.baidu.com\n  authorization:\n    name: psicloud OAuth\n    auth-regex: ^.*$\n    authorization-scope-list:\n      - scope: server\n        description: server all\n    token-url-list:\n      - http://127.0.0.1:9999/auth/oauth/token', '6d569a2a3d071b3d5b1bf4296e24c75e', '2020-05-26 14:22:43', '2020-05-26 14:22:43', NULL, '0:0:0:0:0:0:0:1', '', '50694230-0ebf-4798-89b4-c51850c90225', NULL, NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (61, 'application-bash-dev.yml', 'DEFAULT_GROUP', '# 加解密根密码\r\njasypt:\r\n  encryptor:\r\n    password: psi #根密码\r\n\r\n# Spring 相关\r\nspring:\r\n  redis:\r\n    cluster:\r\n      nodes: *************:6379,*************:6380,*************:6381,*************:6382,*************:6383,*************:6384\r\n    maxRedirects: 3\r\n    password: 123456\r\n    host: *************\r\n\r\n# 暴露监控端点\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\n\r\n# feign 配置\r\nfeign:\r\n  hystrix:\r\n    enabled: true\r\n  okhttp:\r\n    enabled: true\r\n  httpclient:\r\n    enabled: false\r\n  client:\r\n    config:\r\n      default:\r\n        connectTimeout: 10000\r\n        readTimeout: 10000\r\n  compression:\r\n    request:\r\n      enabled: true\r\n    response:\r\n      enabled: true\r\n\r\n# hystrix 配置\r\nhystrix:\r\n  command:\r\n    default:\r\n      execution:\r\n        isolation:\r\n          strategy: SEMAPHORE\r\n          thread:\r\n            timeoutInMilliseconds: 60000\r\n  shareSecurityContext: true\r\n\r\n#请求处理的超时时间\r\nribbon:\r\n  ReadTimeout: 10000\r\n  ConnectTimeout: 10000\r\n\r\n# mybaits-plus配置\r\nmybatis-plus:\r\n  mapper-locations: classpath:/mapper/*mapper.xml\r\n  global-config:\r\n    banner: false\r\n    db-config:\r\n      id-type: auto\r\n      table-underline: true\r\n      logic-delete-value: 1\r\n      logic-not-delete-value: 0\r\n  configuration:\r\n    map-underscore-to-camel-case: true\r\n\r\n# spring security 配置\r\nsecurity:\r\n  oauth2:\r\n    resource:\r\n      loadBalanced: true\r\n      token-info-uri: http://3upsi-auth/oauth/check_token\r\n    # 通用放行URL，服务个性化，请在对应配置文件覆盖\r\n    ignore:\r\n      urls:\r\n        - /v2/api-docs\r\n        - /actuator/**\r\n# swagger 配置\r\nswagger:\r\n  title: Psi Swagger API\r\n  license: Powered By Psi4cloud\r\n  licenseUrl:  https://psi4cloud.com\r\n  terms-of-service-url: https://psi4cloud.com\r\n  contact:\r\n    email: <EMAIL>\r\n    url: https://www.baidu.com\r\n  authorization:\r\n    name: psicloud OAuth\r\n    auth-regex: ^.*$\r\n    authorization-scope-list:\r\n      - scope: server\r\n        description: server all\r\n    token-url-list:\r\n      - http://localhost:9999/auth/oauth/token', '25a26fee83b9ef4473f2d82a5d46181d', '2020-05-26 14:29:40', '2020-08-18 15:43:43', NULL, '127.0.0.1', '', 'dev', 'null', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (88, '3upsi-monitor-email-dev.yml', 'DEFAULT_GROUP', 'spring:\r\n  mail:\r\n    password: effvqwlujmkibjcj\r\n    host: smtp.qq.com\r\n    properties:\r\n      mail:\r\n        smtp:\r\n          starttls:\r\n            enable: true\r\n            required: true\r\n          auth: true\r\n    username: 412900691\r\n  boot:\r\n    admin:\r\n      notify:\r\n        mail:\r\n          from: <EMAIL>\r\n          to: <EMAIL>\r\n', '40ff1daf792cccd61c891429d8f11582', '2020-05-28 10:31:32', '2020-05-28 13:37:17', NULL, '0:0:0:0:0:0:0:1', '', 'dev', '邮箱配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (90, '3upsi-standard-services-dev.yml', 'dev', 'spring:  \r\n  management:\r\n    endpoints:\r\n      web:\r\n        exposure:\r\n          include: \'*\'\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(I9nR6xgz5Zp9jFjtv/fbbQ==)\r\n#    password: ENC(e8kzRdFIUZN3CtNfgbUHQdFvavHnMLZP)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5InnoDBDialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 40MB\r\n      #单个文件的最大值\r\n      max-file-size: 20MB\r\n  #==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: ************\r\n  port: 21\r\n  username: ftpuser\r\n  password: userA9527\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 2097152\r\n  file-suffix: bmp,jpg,jpeg,png,tif,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,WMF,webp\r\n#============对称加密密钥============  \r\naesencrypt:\r\n  # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n  secretKey: c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=\r\n#============人力接口的配置============  \r\nhum:\r\n  inits: true\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n  # 人力接口地址\r\n  authTokenUrl: http://************/login/?wsdl\r\n  authTokenInterfaceName: ssoTicketValidate\r\n  userWsdl:\r\n    userName: ramstest\r\n    password: 12344321\r\n  #H5应用ID\r\n  appId: EPortal\r\n  #H5应用KEY\r\n  appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n  #H5地址\r\n  appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n#==========swagger启用禁用配置===================\r\nswagger:\r\n  enable: true\r\npay:\r\n  allowNode: ************\r\n#==============admin监控配置==================    \r\n#management:\r\n#  endpoints:\r\n#    web:\r\n#      exposure:\r\n#        include: \'*\'\r\n#  endpoint:\r\n#    health:\r\n#      show-details: ALWAYS\r\n    #================web单点重定向地址===================\r\nSSO:\r\n  redirectUrl: http://192.168.12.39:8000/user/login\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n  enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n', '6a3dda50c4e67c17e26cd4a9c73e6a14', '2020-08-26 12:40:59', '2020-08-26 15:03:02', NULL, '127.0.0.1', '', 'dev', 'null', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (91, '3upsi-standard-services-pssn-dev.yml', 'dev', 'spring:\r\n  management:\r\n    endpoints:\r\n      web:\r\n        exposure:\r\n          include: \'*\'\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(6M0zDtnWOKd7/PaIezh/7g==)\r\n#    password: ENC(N3pSTLAwK13fiqQKX8VKsepIplLiVaeo)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5Dialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 200MB\r\n      #单个文件的最大值\r\n      max-file-size: 60MB\r\n#==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: *************\r\n  port: 21\r\n  username: ftpuser\r\n  password: kaiya@123\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 52428800\r\n  file-suffix: doc,docx,xls,xlsx,pdf,txt,jpg,jpeg,png\r\n#============对称加密密钥============  \r\naesencrypt:\r\n   # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n   secretKey: ZXVwc2kjMjAyMHN3Y2FyZXMkZXVwc2lAMjAyMHRyYXZlbHNreQ==\r\n#============人力接口的配置============  \r\nhum:\r\n  init: fasle\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n   # 人力接口地址\r\n   authTokenUrl: http://************/login/?wsdl\r\n   authTokenInterfaceName: ssoTicketValidate\r\n   userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n   #H5应用ID\r\n   appId: EPortal\r\n   #H5应用KEY\r\n   appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n   #H5地址\r\n   appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n   #==========swagger启用禁用配置===================\r\nswagger:\r\n    enable: true\r\n    \r\njasypt:\r\n  encryptor:\r\n    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7\r\npay:\r\n   allowNode: 192.168.0.104\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\n  endpoint:\r\n    health:\r\n      show-details: ALWAYS\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n    enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n#=============trace接口信息===================\r\n#trace:\r\n#  mothod: getPsgListByFilght\r\n#  appkey: dmfwzhbzpt\r\n#  cont:\r\n#    url: http://************:80/getPsgListByFilght\r\n#    sysid: 2011\r\n#    subchannelcode: 2011001\r\n#  idx:\r\n#    url: http://************:80/getPsgByIdx\r\n#    sysid: 2012\r\n#    subchannelcode: 2012001\r\n##========== 获取foc的接口路径，通过nginx进行转发===========\r\n#foc:\r\n#  url: http://************/foc/\r\n##===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\n#firewall:\r\n#  frequentlyAccessDuration: 60\r\n#  frequentlyAccessThreshhold: 80\r\n', '82d96cea12b2bd9b2bb7ab5e7835e8ff', '2020-08-26 14:04:38', '2020-08-26 15:02:19', NULL, '127.0.0.1', '', 'dev', 'null', 'null', 'null', 'yaml', 'null');

-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime(0) NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfoaggr_datagrouptenantdatum`(`data_id`, `group_id`, `tenant_id`, `datum_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '增加租户字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfobeta_datagrouptenant`(`data_id`, `group_id`, `tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info_beta' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfotag_datagrouptenanttag`(`data_id`, `group_id`, `tenant_id`, `tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info_tag' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation`  (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nid`) USING BTREE,
  UNIQUE INDEX `uk_configtagrelation_configidtag`(`id`, `tag_name`, `tag_type`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_tag_relation' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '集群、各Group容量信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info`  (
  `id` bigint(64) UNSIGNED NOT NULL,
  `nid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `gmt_create` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00',
  `gmt_modified` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `src_ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `op_type` char(10) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`nid`) USING BTREE,
  INDEX `idx_gmt_create`(`gmt_create`) USING BTREE,
  INDEX `idx_gmt_modified`(`gmt_modified`) USING BTREE,
  INDEX `idx_did`(`data_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 136 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '多租户改造' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of his_config_info
-- ----------------------------
INSERT INTO `his_config_info` VALUES (61, 121, 'application-bash-dev.yml', 'DEFAULT_GROUP', '', '# 加解密根密码\r\njasypt:\r\n  encryptor:\r\n    password: psi #根密码\r\n\r\n# Spring 相关\r\nspring:\r\n  redis:\r\n    database: 1\r\n    password: 1q2w3e4r\r\n    host: *************\r\n\r\n# 暴露监控端点\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\n\r\n# feign 配置\r\nfeign:\r\n  hystrix:\r\n    enabled: true\r\n  okhttp:\r\n    enabled: true\r\n  httpclient:\r\n    enabled: false\r\n  client:\r\n    config:\r\n      default:\r\n        connectTimeout: 10000\r\n        readTimeout: 10000\r\n  compression:\r\n    request:\r\n      enabled: true\r\n    response:\r\n      enabled: true\r\n\r\n# hystrix 配置\r\nhystrix:\r\n  command:\r\n    default:\r\n      execution:\r\n        isolation:\r\n          strategy: SEMAPHORE\r\n          thread:\r\n            timeoutInMilliseconds: 60000\r\n  shareSecurityContext: true\r\n\r\n#请求处理的超时时间\r\nribbon:\r\n  ReadTimeout: 10000\r\n  ConnectTimeout: 10000\r\n\r\n# mybaits-plus配置\r\nmybatis-plus:\r\n  mapper-locations: classpath:/mapper/*mapper.xml\r\n  global-config:\r\n    banner: false\r\n    db-config:\r\n      id-type: auto\r\n      table-underline: true\r\n      logic-delete-value: 1\r\n      logic-not-delete-value: 0\r\n  configuration:\r\n    map-underscore-to-camel-case: true\r\n\r\n# spring security 配置\r\nsecurity:\r\n  oauth2:\r\n    resource:\r\n      loadBalanced: true\r\n      token-info-uri: http://3upsi-auth/oauth/check_token\r\n    # 通用放行URL，服务个性化，请在对应配置文件覆盖\r\n    ignore:\r\n      urls:\r\n        - /v2/api-docs\r\n        - /actuator/**\r\n# swagger 配置\r\nswagger:\r\n  title: Psi Swagger API\r\n  license: Powered By Psi4cloud\r\n  licenseUrl:  https://psi4cloud.com\r\n  terms-of-service-url: https://psi4cloud.com\r\n  contact:\r\n    email: <EMAIL>\r\n    url: https://www.baidu.com\r\n  authorization:\r\n    name: psicloud OAuth\r\n    auth-regex: ^.*$\r\n    authorization-scope-list:\r\n      - scope: server\r\n        description: server all\r\n    token-url-list:\r\n      - http://localhost:9999/auth/oauth/token', '9d4a2449dbf7678cddfa0cfe0d3897a9', '2010-05-05 00:00:00', '2020-08-18 15:43:43', NULL, '127.0.0.1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (21, 122, '3upsi-auth-dev.yml', 'dev', '', 'server:\n  port: 3000\n# 数据源\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    username: psi\n    password: 1q2w3e4r\n    url: ****************************************************************************************************************************************************************************************************************************************  freemarker:\n    allow-request-override: false\n    allow-session-override: false\n    cache: true\n    charset: UTF-8\n    check-template-location: true\n    content-type: text/html\n    enabled: true\n    expose-request-attributes: false\n    expose-session-attributes: false\n    expose-spring-macro-helpers: true\n    prefer-file-system-access: true\n    suffix: .ftl\n    template-loader-path: classpath:/templates/', '8d6ef4a9e89d65968c4f0bb31e2436fe', '2010-05-05 00:00:00', '2020-08-18 15:52:00', NULL, '127.0.0.1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (25, 123, '3upsi-upms-biz.yml', 'dev', '', 'security:\n  oauth2:\n    client:\n      client-id: ENC(HFsHxL0H5YLAeqWRhOJqbQ==)\n      client-secret: ENC(siAtxj3JdbSQItuiVhSEog==)\n      scope: server\n# 数据源\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    username: psi\n    password: 1q2w3e4r\n    url: ******************************************************************************************************************************************************************************************************************************************************************************  configuration:\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl', 'a1cad3c4365df58eee7521b6679aa118', '2010-05-05 00:00:00', '2020-08-18 15:52:49', NULL, '127.0.0.1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (23, 124, '3upsi-gateway-dev.yml', 'dev', '', 'server:\n  port: 9999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '8b541a34e2878ca72301645d09fa3a10', '2010-05-05 00:00:00', '2020-08-19 13:30:50', NULL, '0:0:0:0:0:0:0:1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (23, 125, '3upsi-gateway-dev.yml', 'dev', '', 'server:\n  port: 9999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n        #stand-services 模块\n        - id: stand-services\n          uri: lb://stand-services\n          predicates:\n            - Path=/stand-services/**\n\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '5401ddaab6699572097cdd63fa49252', '2010-05-05 00:00:00', '2020-08-19 13:32:51', NULL, '0:0:0:0:0:0:0:1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (23, 126, '3upsi-gateway-dev.yml', 'dev', '', 'server:\n  port: 9999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n        #standard-services 模块\n        - id: standard-services\n          uri: lb://standard-services\n          predicates:\n            - Path=/standard-services/**\n\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '77b2648e443bf7c85221a47ca84dcebc', '2010-05-05 00:00:00', '2020-08-19 13:33:27', NULL, '0:0:0:0:0:0:0:1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (23, 127, '3upsi-gateway-dev.yml', 'dev', '', 'server:\n  port: 9999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n        #standard-services 模块\n        - id: 3upsi-standard-services\n          uri: lb://3upsi-standard-services\n          predicates:\n            - Path=/standard-services/**\n\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '43fb07e607b58ca24d0e5283438aa647', '2010-05-05 00:00:00', '2020-08-19 13:36:34', NULL, '0:0:0:0:0:0:0:1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (0, 128, '3upsi-standard-service-dev.yml', 'dev', '', 'management:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\nspring:\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(I9nR6xgz5Zp9jFjtv/fbbQ==)\r\n#    password: ENC(e8kzRdFIUZN3CtNfgbUHQdFvavHnMLZP)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5InnoDBDialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 40MB\r\n      #单个文件的最大值\r\n      max-file-size: 20MB\r\n  #==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: ************\r\n  port: 21\r\n  username: ftpuser\r\n  password: userA9527\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 2097152\r\n  file-suffix: bmp,jpg,jpeg,png,tif,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,WMF,webp\r\n#============对称加密密钥============  \r\naesencrypt:\r\n  # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n  secretKey: c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=\r\n#============人力接口的配置============  \r\nhum:\r\n  inits: true\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n  # 人力接口地址\r\n  authTokenUrl: http://************/login/?wsdl\r\n  authTokenInterfaceName: ssoTicketValidate\r\n  userWsdl:\r\n    userName: ramstest\r\n    password: 12344321\r\n  #H5应用ID\r\n  appId: EPortal\r\n  #H5应用KEY\r\n  appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n  #H5地址\r\n  appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n#==========swagger启用禁用配置===================\r\nswagger:\r\n  enable: true\r\npay:\r\n  allowNode: ************\r\n#==============admin监控配置==================    \r\n#management:\r\n#  endpoints:\r\n#    web:\r\n#      exposure:\r\n#        include: \'*\'\r\n#  endpoint:\r\n#    health:\r\n#      show-details: ALWAYS\r\n    #================web单点重定向地址===================\r\nSSO:\r\n  redirectUrl: http://192.168.12.39:8000/user/login\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n  enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n', 'e3f7c7d91092c10d6075c5d16c5bb672', '2010-05-05 00:00:00', '2020-08-26 12:39:40', NULL, '127.0.0.1', 'I', 'dev');
INSERT INTO `his_config_info` VALUES (0, 129, '3upsi-standard-services-dev.yml', 'dev', '', 'management:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\nspring:\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(I9nR6xgz5Zp9jFjtv/fbbQ==)\r\n#    password: ENC(e8kzRdFIUZN3CtNfgbUHQdFvavHnMLZP)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5InnoDBDialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 40MB\r\n      #单个文件的最大值\r\n      max-file-size: 20MB\r\n  #==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: ************\r\n  port: 21\r\n  username: ftpuser\r\n  password: userA9527\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 2097152\r\n  file-suffix: bmp,jpg,jpeg,png,tif,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,WMF,webp\r\n#============对称加密密钥============  \r\naesencrypt:\r\n  # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n  secretKey: c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=\r\n#============人力接口的配置============  \r\nhum:\r\n  inits: true\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n  # 人力接口地址\r\n  authTokenUrl: http://************/login/?wsdl\r\n  authTokenInterfaceName: ssoTicketValidate\r\n  userWsdl:\r\n    userName: ramstest\r\n    password: 12344321\r\n  #H5应用ID\r\n  appId: EPortal\r\n  #H5应用KEY\r\n  appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n  #H5地址\r\n  appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n#==========swagger启用禁用配置===================\r\nswagger:\r\n  enable: true\r\npay:\r\n  allowNode: ************\r\n#==============admin监控配置==================    \r\n#management:\r\n#  endpoints:\r\n#    web:\r\n#      exposure:\r\n#        include: \'*\'\r\n#  endpoint:\r\n#    health:\r\n#      show-details: ALWAYS\r\n    #================web单点重定向地址===================\r\nSSO:\r\n  redirectUrl: http://192.168.12.39:8000/user/login\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n  enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n', 'e3f7c7d91092c10d6075c5d16c5bb672', '2010-05-05 00:00:00', '2020-08-26 12:40:59', NULL, '127.0.0.1', 'I', 'dev');
INSERT INTO `his_config_info` VALUES (89, 130, '3upsi-standard-service-dev.yml', 'dev', '', 'management:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\nspring:\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(I9nR6xgz5Zp9jFjtv/fbbQ==)\r\n#    password: ENC(e8kzRdFIUZN3CtNfgbUHQdFvavHnMLZP)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5InnoDBDialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 40MB\r\n      #单个文件的最大值\r\n      max-file-size: 20MB\r\n  #==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: ************\r\n  port: 21\r\n  username: ftpuser\r\n  password: userA9527\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 2097152\r\n  file-suffix: bmp,jpg,jpeg,png,tif,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,WMF,webp\r\n#============对称加密密钥============  \r\naesencrypt:\r\n  # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n  secretKey: c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=\r\n#============人力接口的配置============  \r\nhum:\r\n  inits: true\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n  # 人力接口地址\r\n  authTokenUrl: http://************/login/?wsdl\r\n  authTokenInterfaceName: ssoTicketValidate\r\n  userWsdl:\r\n    userName: ramstest\r\n    password: 12344321\r\n  #H5应用ID\r\n  appId: EPortal\r\n  #H5应用KEY\r\n  appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n  #H5地址\r\n  appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n#==========swagger启用禁用配置===================\r\nswagger:\r\n  enable: true\r\npay:\r\n  allowNode: ************\r\n#==============admin监控配置==================    \r\n#management:\r\n#  endpoints:\r\n#    web:\r\n#      exposure:\r\n#        include: \'*\'\r\n#  endpoint:\r\n#    health:\r\n#      show-details: ALWAYS\r\n    #================web单点重定向地址===================\r\nSSO:\r\n  redirectUrl: http://192.168.12.39:8000/user/login\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n  enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n', 'e3f7c7d91092c10d6075c5d16c5bb672', '2010-05-05 00:00:00', '2020-08-26 12:41:41', NULL, '127.0.0.1', 'D', 'dev');
INSERT INTO `his_config_info` VALUES (0, 131, '3upsi-standard-services-pssn-dev.yml', 'dev', '', 'spring:\r\n  security:\r\n    oauth2:\r\n      client:\r\n        client-id: ENC(HFsHxL0H5YLAeqWRhOJqbQ==)\r\n        client-secret: ENC(siAtxj3JdbSQItuiVhSEog==)\r\n        scope: server\r\n      resource:\r\n        jwt:\r\n          key-value: JWTKey@123\r\n  management:\r\n    endpoints:\r\n      web:\r\n        exposure:\r\n          include: \'*\'\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(6M0zDtnWOKd7/PaIezh/7g==)\r\n#    password: ENC(N3pSTLAwK13fiqQKX8VKsepIplLiVaeo)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5Dialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 200MB\r\n      #单个文件的最大值\r\n      max-file-size: 60MB\r\n#==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: *************\r\n  port: 21\r\n  username: ftpuser\r\n  password: kaiya@123\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 52428800\r\n  file-suffix: doc,docx,xls,xlsx,pdf,txt,jpg,jpeg,png\r\n#============对称加密密钥============  \r\naesencrypt:\r\n   # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n   secretKey: ZXVwc2kjMjAyMHN3Y2FyZXMkZXVwc2lAMjAyMHRyYXZlbHNreQ==\r\n#============人力接口的配置============  \r\nhum:\r\n  init: fasle\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n   # 人力接口地址\r\n   authTokenUrl: http://************/login/?wsdl\r\n   authTokenInterfaceName: ssoTicketValidate\r\n   userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n   #H5应用ID\r\n   appId: EPortal\r\n   #H5应用KEY\r\n   appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n   #H5地址\r\n   appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n   #==========swagger启用禁用配置===================\r\nswagger:\r\n    enable: true\r\n    \r\njasypt:\r\n  encryptor:\r\n    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7\r\npay:\r\n   allowNode: 192.168.0.104\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\n  endpoint:\r\n    health:\r\n      show-details: ALWAYS\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n    enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n#=============trace接口信息===================\r\n#trace:\r\n#  mothod: getPsgListByFilght\r\n#  appkey: dmfwzhbzpt\r\n#  cont:\r\n#    url: http://************:80/getPsgListByFilght\r\n#    sysid: 2011\r\n#    subchannelcode: 2011001\r\n#  idx:\r\n#    url: http://************:80/getPsgByIdx\r\n#    sysid: 2012\r\n#    subchannelcode: 2012001\r\n##========== 获取foc的接口路径，通过nginx进行转发===========\r\n#foc:\r\n#  url: http://************/foc/\r\n##===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\n#firewall:\r\n#  frequentlyAccessDuration: 60\r\n#  frequentlyAccessThreshhold: 80\r\n', 'bcb7e2474e73e4581af40f9dd798d329', '2010-05-05 00:00:00', '2020-08-26 14:04:38', NULL, '127.0.0.1', 'I', 'dev');
INSERT INTO `his_config_info` VALUES (91, 132, '3upsi-standard-services-pssn-dev.yml', 'dev', '', 'spring:\r\n  security:\r\n    oauth2:\r\n      client:\r\n        client-id: ENC(HFsHxL0H5YLAeqWRhOJqbQ==)\r\n        client-secret: ENC(siAtxj3JdbSQItuiVhSEog==)\r\n        scope: server\r\n      resource:\r\n        jwt:\r\n          key-value: JWTKey@123\r\n  management:\r\n    endpoints:\r\n      web:\r\n        exposure:\r\n          include: \'*\'\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(6M0zDtnWOKd7/PaIezh/7g==)\r\n#    password: ENC(N3pSTLAwK13fiqQKX8VKsepIplLiVaeo)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5Dialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 200MB\r\n      #单个文件的最大值\r\n      max-file-size: 60MB\r\n#==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: *************\r\n  port: 21\r\n  username: ftpuser\r\n  password: kaiya@123\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 52428800\r\n  file-suffix: doc,docx,xls,xlsx,pdf,txt,jpg,jpeg,png\r\n#============对称加密密钥============  \r\naesencrypt:\r\n   # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n   secretKey: ZXVwc2kjMjAyMHN3Y2FyZXMkZXVwc2lAMjAyMHRyYXZlbHNreQ==\r\n#============人力接口的配置============  \r\nhum:\r\n  init: fasle\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n   # 人力接口地址\r\n   authTokenUrl: http://************/login/?wsdl\r\n   authTokenInterfaceName: ssoTicketValidate\r\n   userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n   #H5应用ID\r\n   appId: EPortal\r\n   #H5应用KEY\r\n   appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n   #H5地址\r\n   appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n   #==========swagger启用禁用配置===================\r\nswagger:\r\n    enable: true\r\n    \r\njasypt:\r\n  encryptor:\r\n    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7\r\npay:\r\n   allowNode: 192.168.0.104\r\nmanagement:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\n  endpoint:\r\n    health:\r\n      show-details: ALWAYS\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n    enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n#=============trace接口信息===================\r\n#trace:\r\n#  mothod: getPsgListByFilght\r\n#  appkey: dmfwzhbzpt\r\n#  cont:\r\n#    url: http://************:80/getPsgListByFilght\r\n#    sysid: 2011\r\n#    subchannelcode: 2011001\r\n#  idx:\r\n#    url: http://************:80/getPsgByIdx\r\n#    sysid: 2012\r\n#    subchannelcode: 2012001\r\n##========== 获取foc的接口路径，通过nginx进行转发===========\r\n#foc:\r\n#  url: http://************/foc/\r\n##===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\n#firewall:\r\n#  frequentlyAccessDuration: 60\r\n#  frequentlyAccessThreshhold: 80\r\n', 'bcb7e2474e73e4581af40f9dd798d329', '2010-05-05 00:00:00', '2020-08-26 15:02:19', NULL, '127.0.0.1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (90, 133, '3upsi-standard-services-dev.yml', 'dev', '', 'management:\r\n  endpoints:\r\n    web:\r\n      exposure:\r\n        include: \'*\'\r\nspring:\r\n  datasource:\r\n    url: jdbc:mysql://*************:3306/psi_3u?useUnicode=yes&characterEncoding=utf-8\r\n    username: psi\r\n    password: 123456\r\n#    username: ENC(I9nR6xgz5Zp9jFjtv/fbbQ==)\r\n#    password: ENC(e8kzRdFIUZN3CtNfgbUHQdFvavHnMLZP)\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    type: com.alibaba.druid.pool.DruidDataSource\r\n    #最大活跃数\r\n    maxActive: 20\r\n    #初始化数量\r\n    initialSize: 1\r\n    #最大连接等待超时时间\r\n    maxWait: 60000\r\n    #打开PSCache，并且指定每个连接PSCache的大小\r\n    poolPreparedStatements: true\r\n    maxPoolPreparedStatementPerConnectionSize: 20\r\n    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录\r\n    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n    minIdle: 1\r\n    timeBetweenEvictionRunsMillis: 60000\r\n    minEvictableIdleTimeMillis: 300000\r\n    validationQuery: select 1 from dual\r\n    testWhileIdle: true\r\n    testOnBorrow: false\r\n    testOnReturn: false\r\n  #JPA设置\r\n  jpa:\r\n    show-sql: true\r\n    open-in-view: false\r\n    properties:\r\n      hibernate:\r\n        hbm2ddl.auto: none\r\n        dialect: org.hibernate.dialect.MySQL5InnoDBDialect\r\n        format_sql: true\r\n        temp:\r\n          use_jdbc_metadata_defaults: false\r\n  #文件大小设置\r\n  servlet:\r\n    multipart:\r\n      #上传文件总的最大值\r\n      max-request-size: 40MB\r\n      #单个文件的最大值\r\n      max-file-size: 20MB\r\n  #==================activiti========\r\n  activiti:\r\n    database-schema-update: false\r\n    check-process-definitions: false\r\n#===============国际化============== \r\ninternational:\r\n  message:\r\n    #internationalization profiles\r\n    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]\r\n#===============FTP=================\r\nftp:\r\n  ip: ************\r\n  port: 21\r\n  username: ftpuser\r\n  password: userA9527\r\n#==============文件保存路径(文件大小单位为字节)==========\r\nupload-file:\r\n  remote-path: /upload/official\r\n  remote-path-temp: /upload/temp\r\n  file-max-size: 2097152\r\n  file-suffix: bmp,jpg,jpeg,png,tif,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,WMF,webp\r\n#============对称加密密钥============  \r\naesencrypt:\r\n  # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey\r\n  secretKey: c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=\r\n#============人力接口的配置============  \r\nhum:\r\n  inits: true\r\n  departmentresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryOrgan: \"pageQueryOrgan\"}\r\n  employeeresources:\r\n    url: http://************/hum/?wsdl\r\n    authCode: e9f00a44-45a2-4b6c-945e-4d0b77158918\r\n    userWsdl:\r\n      userName: ramstest\r\n      password: 12344321\r\n    map: {pageQueryUser: \"pageQueryUser\"}\r\n#============单点登录验证token接口的配置============  \r\nauthtokensources:\r\n  # 人力接口地址\r\n  authTokenUrl: http://************/login/?wsdl\r\n  authTokenInterfaceName: ssoTicketValidate\r\n  userWsdl:\r\n    userName: ramstest\r\n    password: 12344321\r\n  #H5应用ID\r\n  appId: EPortal\r\n  #H5应用KEY\r\n  appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6\r\n  #H5地址\r\n  appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s\r\n#==========swagger启用禁用配置===================\r\nswagger:\r\n  enable: true\r\npay:\r\n  allowNode: ************\r\n#==============admin监控配置==================    \r\n#management:\r\n#  endpoints:\r\n#    web:\r\n#      exposure:\r\n#        include: \'*\'\r\n#  endpoint:\r\n#    health:\r\n#      show-details: ALWAYS\r\n    #================web单点重定向地址===================\r\nSSO:\r\n  redirectUrl: http://192.168.12.39:8000/user/login\r\n#==============sms短信配置==================\r\nsms:\r\n  url: http://************/sms/?wsdl\r\n  username: gssp\r\n  password: 82085591\r\n  clientId: cn.sda.marketing.gssp\r\n  operationCode: cn.sda.management.scsms.SMSService.submit\r\n#==========登录验证码启用禁用配置===================\r\nverifyCode:\r\n  enable: false\r\n#=============trace接口信息===================\r\ntrace:\r\n  mothod: getPsgListByFilght\r\n  appkey: dmfwzhbzpt\r\n  cont:\r\n    url: http://************:80/getPsgListByFilght\r\n    sysid: 2011\r\n    subchannelcode: 2011001\r\n  idx:\r\n    url: http://************:80/getPsgByIdx\r\n    sysid: 2012\r\n    subchannelcode: 2012001\r\n#========== 获取foc的接口路径，通过nginx进行转发===========\r\nfoc:\r\n  url: http://************/foc/\r\n#===================访问频率限制frequentlyAccessThreshhold如果为0则不限制===================\r\nfirewall:\r\n  frequentlyAccessDuration: 60\r\n  frequentlyAccessThreshhold: 80\r\n', 'e3f7c7d91092c10d6075c5d16c5bb672', '2010-05-05 00:00:00', '2020-08-26 15:03:02', NULL, '127.0.0.1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (23, 134, '3upsi-gateway-dev.yml', 'dev', '', 'server:\n  port: 9999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n        #standard-services 模块\n        - id: 3upsi-standard-services\n          uri: lb://3upsi-standard-services\n          predicates:\n            - Path=/standard-services/**\n\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '43fb07e607b58ca24d0e5283438aa647', '2010-05-05 00:00:00', '2020-08-26 15:33:25', NULL, '127.0.0.1', 'U', 'dev');
INSERT INTO `his_config_info` VALUES (23, 135, '3upsi-gateway-dev.yml', 'dev', '', 'server:\n  port: 8999\nspring:\n  cloud:\n    gateway:\n      locator:\n        enabled: true\n      routes:\n        # 认证中心\n        - id: 3upsi-auth\n          uri: lb://3upsi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - ValidateCodeGatewayFilter\n            # 前端密码解密\n            - PasswordDecoderFilter\n        #UPMS 模块\n        - id: 3upsi-upms-biz\n          uri: lb://3upsi-upms-biz\n          predicates:\n            - Path=/admin/**\n          filters:\n            # 限流配置\n            - name: RequestRateLimiter\n              args:\n                key-resolver: \'#{@remoteAddrKeyResolver}\'\n                redis-rate-limiter.replenishRate: 10 #桶的容积\n                redis-rate-limiter.burstCapacity: 20 #每秒的数量\n              # 降级配置\n            - name: Hystrix\n              args:\n                name: default\n                fallbackUri: \'forward:/fallback\'\n        # 代码生成模块\n        - id: 3upsi-codegen\n          uri: lb://3upsi-codegen\n          predicates:\n            - Path=/gen/**\n        #standard-services 模块\n        - id: 3upsi-standard-services\n          uri: lb://3upsi-standard-services\n          predicates:\n            - Path=/standard-services/**\n\n\nsecurity:\n  encode:\n    # 前端密码密钥，必须16位\n    key: \'1234567887654321\'\n\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n', '7cb2eab36bc9143a0a8c0afd964b20cc', '2010-05-05 00:00:00', '2020-08-26 15:36:24', NULL, '127.0.0.1', 'U', 'dev');

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `resource` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `uk_role_permission`(`role`, `resource`, `action`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `uk_username_role`(`username`, `role`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES ('nacos', 'ROLE_ADMIN');

-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数',
  `max_aggr_size` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime(0) NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '租户容量信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint(20) NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint(20) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_info_kptenantid`(`kp`, `tenant_id`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'tenant_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tenant_info
-- ----------------------------
INSERT INTO `tenant_info` VALUES (1, '1', 'dev', 'dev', '开发环境', 'nacos', 1590455129825, 1590455129825);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('nacos', '$2a$10$EuWPZHzz32dJN7jexM34MOeYirDdFAZm2kuWj7VEOJhhZkDrxfvUu', 1);

SET FOREIGN_KEY_CHECKS = 1;
