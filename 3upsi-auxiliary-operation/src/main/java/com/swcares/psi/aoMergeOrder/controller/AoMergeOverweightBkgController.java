package com.swcares.psi.aoMergeOrder.controller;


import com.component.core.exception.ColumnAuthException;
import com.component.core.processor.FieldPermissionsProcessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.swcares.psi.ao.model.vo.OverWeightPageNewVO;
import com.swcares.psi.ao.overweightbkg.dto.AoOverweightInputTemplateReportDto;
import com.swcares.psi.ao.overweightbkg.vo.AoOverweightInputTemplateReportVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightInputTemplateReportDto;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgPageVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightInputTemplateReportVo;
import com.swcares.psi.aoMergeOrder.vo.PayTypesSummaryMoneyVo;
import com.swcares.psi.ao.overweightbkg.vo.AoOverWeightAccountsReportVo;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOverweightBkgAccountsReportDto;
import com.swcares.psi.aoMergeOrder.service.IAoMergeOverweightBkgService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOverweightBkgAccountsReportVo;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合并逾重行李产品详情订单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Api(tags = "合并逾重行李")
@Slf4j
@RestController
@RequestMapping("/api/ao/mergeOrder/overweightBkg")
public class AoMergeOverweightBkgController {

    @Autowired
    private IAoMergeOverweightBkgService aoMergeOverweightBkgService;
    @Autowired
    private FieldPermissionsProcessor fieldPermissionsProcessor;


    @GetMapping("/accounts/page")
    @ApiOperation(value = "逾重行李结算报表分页查询")
    public RenderResult<AoMergeOverweightBkgPageVo> getOverweightAccountsReportPage(AoMergeOverweightBkgAccountsReportDto aoOverWeightAccountsReportDto){
        PsiPage<AoMergeOverweightBkgAccountsReportVo> overweightAccountsReportPage = aoMergeOverweightBkgService.getAccountsListPage(aoOverWeightAccountsReportDto);
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVo = aoMergeOverweightBkgService.payTypesSummaryMoney(aoOverWeightAccountsReportDto);
        AoMergeOverweightBkgPageVo overweightPageNewVO = new AoMergeOverweightBkgPageVo();
        overweightPageNewVO.setPage(overweightAccountsReportPage);
        overweightPageNewVO.setPayTypesSummaryMoneyVo(payTypesSummaryMoneyVo);
        return RenderResult.success(overweightPageNewVO);
    }

    @GetMapping("/accounts/report")
    @ApiOperation(value = "逾重行李结算报表导出")
    public void aoOverweightAccountExport(AoMergeOverweightBkgAccountsReportDto aoOverWeightAccountsReportDto, HttpServletResponse response, HttpServletRequest request) throws Exception {
        response.setContentType("application/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("逾重行李结算报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeOverweightBkgAccountsReportVo> aoOverWeightAccountsReportVoList = aoMergeOverweightBkgService.getAccountsList(aoOverWeightAccountsReportDto);

        AoMergeOverweightBkgPageVo overweightPageNewVO = new AoMergeOverweightBkgPageVo();
        PsiPage<AoMergeOverweightBkgAccountsReportVo> page = new PsiPage<>();
        page.setRecords(aoOverWeightAccountsReportVoList);
        overweightPageNewVO.setPage(page);
        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            overweightPageNewVO = (AoMergeOverweightBkgPageVo)fieldPermissionsProcessor.process(serverHttpRequest, overweightPageNewVO);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            throw new ColumnAuthException(e);
        }
        List<PayTypesSummaryMoneyVo> payTypesSummaryMoneyVos = aoMergeOverweightBkgService.payTypesSummaryMoney(aoOverWeightAccountsReportDto);
        ExcelUtils.writeExcel(response.getOutputStream(), Arrays.asList("逾重行李结算", "合计"), Arrays.asList(overweightPageNewVO.getPage().getRecords(),payTypesSummaryMoneyVos), Arrays.asList(AoMergeOverweightBkgAccountsReportVo.class, PayTypesSummaryMoneyVo.class), null);

    }

    @GetMapping("/report/template/page")
    @ApiOperation(value = "国内逾重行李票录入模板分页查询")
    public RenderResult<PsiPage<AoMergeOverweightInputTemplateReportVo>> getAoOverweightInputTemplateReportPage(AoMergeOverweightInputTemplateReportDto dto){
        return RenderResult.success(aoMergeOverweightBkgService.getAoMergeOverweightInputTemplatePage(dto));
    }
    @GetMapping("/report/template/export")
    @ApiOperation(value = "国内逾重行李票录入模板导出")
    public void aoOverweightInputTemplateExport(AoMergeOverweightInputTemplateReportDto dto,HttpServletResponse response, HttpServletRequest request) throws Exception{
        response.setContentType("application/csv;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("逾重行李的国内逾重录入报表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
        List<AoMergeOverweightInputTemplateReportVo> aoMergeOverweightInputTemplateList = aoMergeOverweightBkgService.getAoMergeOverweightInputTemplateList(dto);

        ServerHttpRequest serverHttpRequest = new ServletServerHttpRequest(request);
        try {
            aoMergeOverweightInputTemplateList = (List<AoMergeOverweightInputTemplateReportVo>)fieldPermissionsProcessor.process(serverHttpRequest, aoMergeOverweightInputTemplateList);
        } catch (NoSuchFieldException | IllegalAccessException | InstantiationException | ClassNotFoundException | JsonProcessingException e) {
            throw new ColumnAuthException(e);
        }

        ExcelUtils.writeExcel(response.getOutputStream(), aoMergeOverweightInputTemplateList ,AoMergeOverweightInputTemplateReportVo.class , "逾重行李的国内逾重录入",
                null);
    }

}

