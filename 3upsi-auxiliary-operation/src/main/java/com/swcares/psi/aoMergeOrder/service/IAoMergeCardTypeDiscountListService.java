package com.swcares.psi.aoMergeOrder.service;

import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeDiscountListSaveDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeCardTypeQueryDto;
import com.swcares.psi.aoMergeOrder.dto.AoMergeOrderDiscountQueryDto;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCardTypeDiscountList;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.psi.aoMergeOrder.vo.AoMergeCardTypegetListVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderDiscountListVo;
import com.swcares.psi.aoMergeOrder.vo.AoMergeOrderTypeListVo;
import com.swcares.psi.common.utils.query.PsiPage;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合并支付卡级别折扣清单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IAoMergeCardTypeDiscountListService extends IService<AoMergeCardTypeDiscountList> {

    void saveOrUpdate(AoMergeCardTypeDiscountListSaveDto dto);
    void deleteConfig(String id);

    List<AoMergeOrderTypeListVo> getPaxTypeList();


     PsiPage<AoMergeCardTypegetListVo> getPage(AoMergeCardTypeQueryDto dto);
     List<AoMergeCardTypegetListVo> getList(AoMergeCardTypeQueryDto dto);

}
