package com.swcares.psi.luggagepullmessage.service.impl;


import cn.hutool.core.io.IORuntimeException;
import com.swcares.psi.base.fileuploadanddownload.UploadAndDownload;
import com.swcares.psi.combine.config.PSISystemConfiguration;
import com.swcares.psi.combine.constant.CacheConstants;
import com.swcares.psi.combine.util.UID;
import com.swcares.psi.common.redis.RedisService;
import com.swcares.psi.common.utils.encryption.DecryptMethod;
import com.swcares.psi.common.utils.encryption.EncryptMethod;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.lost.entity.Img;
import com.swcares.psi.lost.enums.LostCategoryEnum;
import com.swcares.psi.lost.service.IImgService;
import com.swcares.psi.lost.service.ILostArticlesInfoService;
import com.swcares.psi.luggagepullmessage.dto.LessLuggageDto;
import com.swcares.psi.luggagepullmessage.dto.LostItemsInfoDto;
import com.swcares.psi.luggagepullmessage.dto.MailInformationDto;
import com.swcares.psi.luggagepullmessage.dto.PirDto;
import com.swcares.psi.luggagepullmessage.mapper.LuggagePullMessageMapper;
import com.swcares.psi.luggagepullmessage.service.LuggagePullMessageService;
import com.swcares.psi.luggagepullmessage.vo.ArticleTypeDictNameVo;
import com.swcares.psi.luggagepullmessage.vo.LessLuggageVo;
import com.swcares.psi.luggagepullmessage.vo.LostItemPickupCity;
import com.swcares.psi.luggagepullmessage.vo.LostItemsInfoVo;
import com.swcares.psi.luggagepullmessage.vo.MailInformationVo;
import com.swcares.psi.luggagepullmessage.vo.PirVo;
import com.swcares.psi.pay.util.chinapay.StringUtil;
import com.swcares.psi.terminal.service.IDpPkgTerminalService;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LuggagePullMessageServiceImpl implements LuggagePullMessageService {


    @Autowired
    ILostArticlesInfoService iLostArticlesInfoService;
    @Autowired
    LuggagePullMessageMapper luggagePullMessageMapper;
    @Autowired
    IImgService iImgService;
    @Autowired
    private IDpPkgTerminalService pkgTerminalService;

    @Autowired
    private UploadAndDownload uploadAndDownload;

    @Autowired
    private RedisService redisService;

    @Resource
    private PSISystemConfiguration psiSystemConfiguration;

    @Autowired
    private UID uid;

    @Override
    public List<ArticleTypeDictNameVo> queryArticleTypeDictName() {
        return Stream.of(LostCategoryEnum.values()).map(elem -> new ArticleTypeDictNameVo(String.valueOf(elem.getCategory()), elem.getCategoryName())).collect(Collectors.toList());
    }

    @DecryptMethod
    @Override
    public PsiPage<LostItemsInfoVo> queryLostItemsInfo(LostItemsInfoDto lostItemsInfoDto) {
        List<LostItemsInfoVo> vos = luggagePullMessageMapper.getLostItemsInfo(lostItemsInfoDto);

        final String keySearch = lostItemsInfoDto.getKeySearch();
        if (StringUtil.isNotEmpty(keySearch)) {
            vos = vos.stream().filter(vo -> {
                if (vo.getLostItemName() != null && vo.getLostItemName().contains(keySearch)) {
                    return true;
                }
                LostCategoryEnum categoryEnum = LostCategoryEnum.getLostCategoryEnum(vo.getLostItemCategory());
                if (categoryEnum != LostCategoryEnum.UNKNOWN && categoryEnum.getCategoryName().contains(keySearch)) {
                    return true;
                }
                if (vo.getPickupAiportNameCn() != null && vo.getPickupAiportNameCn().contains(keySearch)) {
                    return true;
                }
                if (vo.getPickupAiportNameEn() != null && vo.getPickupAiportNameEn().contains(keySearch)) {
                    return true;
                }
                if (vo.getPickupLocation() != null && vo.getPickupLocation().contains(keySearch)) {
                    return true;
                }
                if (psiSystemConfiguration.getLuggageAirports() != null && psiSystemConfiguration.getLuggageAirports().stream().anyMatch(airport -> airport.getCityCode() != null && airport.getCityCode().equalsIgnoreCase(vo.getPickupLocation()) && airport.getName() != null && airport.getName().contains(keySearch))) {
                   return true;
                }
                return false;
            }).collect(Collectors.toList());
        }

        // 分页处理
        final int total = vos.size();
        final int pageSize = lostItemsInfoDto.getPageSize();
        final int current = lostItemsInfoDto.getCurrent();
        final int pages = total / pageSize + (total % pageSize > 0 ? 1 : 0);
        if (pages >= current) {
            int from = (current - 1) * pageSize;
            int to = Math.min(from + pageSize, total);
            vos = vos.subList(from, to);
        }

        PsiPage<LostItemsInfoVo> psiPage = new PsiPage<>();
        psiPage.setRecords(vos);
        psiPage.setTotal(total);
        psiPage.setCurrent(current);
        psiPage.setPages(pages);
        psiPage.setSize(pageSize);

        String lostCodes = vos.stream().map(LostItemsInfoVo::getLostCode).collect(Collectors.joining(","));
        List<Img> imgByRelevance = iImgService.getImgByRelevance(lostCodes);
        Map<String, List<String>> imageBase64 = new HashMap<>();
        for (Img img : imgByRelevance) {
            //将图片转为base64
            String imgResouceCode = this.loadImgResouce(img.getImgUrl());
            if (StringUtils.isNoneBlank(imgResouceCode)) {
                List<String> list = imageBase64.computeIfAbsent(img.getRelevance(), k -> new ArrayList<>());
                list.add(imgResouceCode);
            }
        }
        vos.forEach(t -> {
            t.setLostItemType(LostCategoryEnum.getLostCategoryEnum(t.getLostItemCategory()).getCategoryName());

            if (imageBase64.containsKey(t.getLostCode())) {
                t.setPictureB64(imageBase64.get(t.getLostCode()));
            }
        });

        return psiPage;
    }

    private String loadImgResouce(final String imgUrl) {
        String imgBase64 = urlToBase64(imgUrl);
        if (imgBase64 != null) {
            final String code = uid.getIdStr();
            final int expireSeconds = psiSystemConfiguration.getLuggageLostItemImgCacheSeconds();
            // 缓存图片路径与图片资源code
            redisService.set(CacheConstants.LUGGAGE_LOST_ITEM_IMG + imgUrl, imgBase64, expireSeconds);
            // 缓存base64图片资源
            redisService.set(CacheConstants.LUGGAGE_LOST_ITEM_IMG_CODE + code, imgUrl, expireSeconds);
            return code;
        }
        return null;
    }

    private String urlToBase64(String imgUrl)  {
        String imgBase64 = redisService.get(CacheConstants.LUGGAGE_LOST_ITEM_IMG + imgUrl);
        if (imgBase64 != null) {
            return "".equals(imgBase64) ? null : imgBase64;
        }
        imgBase64 = "";
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            String remote = imgUrl.substring(imgUrl.lastIndexOf("/")+1);
            String remotePath = imgUrl.substring(0,imgUrl.lastIndexOf("/"));
            // FTP下载图片
            uploadAndDownload.ftpDownload(outputStream, remote, remotePath);
            // 图片Base64编码
            imgBase64 = new String(Base64.getEncoder().encode(outputStream.toByteArray()), "UTF-8");

        } catch (IORuntimeException e) {
            log.info("行查对外接口查询遗失物品的图片资源未找到！url:{}", imgUrl, e);
        } catch (Exception e) {
            log.error("行查对外接口查询遗失物品的图片资源时出错！url:{}", imgUrl, e);
        }

       return imgBase64;
    }

    @Override
    @DecryptMethod
    @EncryptMethod
    public List<PirVo> queryPir(PirDto pirDto) {

        List<PirVo> pirVos = luggagePullMessageMapper.queryPir(pirDto);
        for (PirVo pirVo : pirVos) {
            pirVo.setCreateAirportPhone(pkgTerminalService.getTerminalPhone(pirVo.getCreateAirport3Code()));
        }
        return pirVos;
    }

    @EncryptMethod
    @Override
    public List<MailInformationVo> queryMailInformation(MailInformationDto mailInformationDto) {
        return luggagePullMessageMapper.queryMailInformation(mailInformationDto);
    }

    @EncryptMethod
    @Override
    public List<LessLuggageVo> queryLessLuggage(LessLuggageDto lessLuggageDto) {
        return luggagePullMessageMapper.queryLessLuggage(lessLuggageDto);
    }

    @Override
    public List<LostItemPickupCity> queryLostItemPickupAirports() {
        return luggagePullMessageMapper.queryLostItemPickupAirports();
    }

}
