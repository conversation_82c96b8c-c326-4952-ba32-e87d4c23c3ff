<?xml version="1.0" encoding="UTF-8"?>


<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.psi.admin.mapper.SysMenuMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.swcares.psi.admin.api.entity.SysMenu">
		<id column="ID" property="id"/>
		<result column="NAME" property="name"/>
		<result column="TYPES" property="types"/>
		<result column="STATUS" property="status"/>
		<result column="RESOURCE_URL" property="resourceUrl"/>
		<result column="CONTAIN_URL" property="containUrl"/>
		<result column="ICON" property="icon"/>
		<result column="PORT" property="port"/>
		<result column="RESOURCE_CODE" property="resourceCode"/>
		<result column="PID" property="pid"/>
		<result column="REMARKS" property="remarks"/>
		<result column="COMPONENTS_ID" property="componentsId"/>
		<result column="FUNCTIONAL" property="functional"/>
	</resultMap>

	<select id="listMenusByRoleId" resultMap="MenuVoResultMap" parameterType="string">
		SELECT
		resources_scgsi.*
		FROM
		resources_scgsi
		LEFT JOIN role_resources ON resources_scgsi.ID = role_resources.RESOURCES_ID
		WHERE
		1 = 1
		AND role_resources.ROLE_ID = #{roleId} order by weights
	</select>

	<!--通过角色查询菜单信息-->
	<resultMap id="MenuVoResultMap" type="com.swcares.psi.admin.api.vo.MenuVO">
		<id column="ID" property="id"/>
		<result column="NAME" property="name"/>
		<result column="TYPES" property="types"/>
		<result column="STATUS" property="status"/>
		<result column="RESOURCE_URL" property="resourceUrl"/>
		<result column="CONTAIN_URL" property="containUrl"/>
		<result column="ICON" property="icon"/>
		<result column="PORT" property="port"/>
		<result column="RESOURCE_CODE" property="resourceCode"/>
		<result column="PID" property="pid"/>
		<result column="REMARKS" property="remarks"/>
		<result column="COMPONENTS_ID" property="componentsId"/>
		<result column="FUNCTIONAL" property="functional"/>
	</resultMap>

	<!--通过角色ID 查询权限-->
	<select id="listPermissionsByRoleIds" resultType="java.lang.String" parameterType="string">
        SELECT
            m.CONTAIN_URL
        FROM
        resources_scgsi m, role_resources rm WHERE m.ID = rm.RESOURCES_ID AND rm.ROLE_ID IN (#{roleIds})
    </select>

	<select id="listResourceTreeByUserIdAndProt" resultType="com.swcares.psi.admin.api.vo.ResourceTreeVo" parameterType="string">
		SELECT DISTINCT(RS.ID),RS.NAME,RS.TYPES AS TYPE,RS.RESOURCE_URL AS PATH,RS.CONTAIN_URL AS CONTAINPATH,RS.ICON,RS.PID,RS.REMARKS FROM RESOURCES_SCGSI RS
                                LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = RS.ID
                                LEFT JOIN USER_ROLE UR ON UR.ROLE_ID =RR.ROLE_ID
                                LEFT JOIN ROLE R ON RR.ROLE_ID = R.ID
                                WHERE RS.STATUS = 1 AND R.STATUS = 1 AND RS.TYPES in (1,2,3) AND RS.PORT = #{resourceProt}
										AND UR.EMPLOYEE_ID =#{userId}
										ORDER BY RS.WEIGHTS ASC
	</select>

	<select id="listResourceTreeByRoleIdAndProt" resultType="com.swcares.psi.admin.api.vo.ResourceTreeVo" parameterType="string">
		SELECT DISTINCT(RS.ID),RS.NAME,RS.TYPES AS TYPE,RS.RESOURCE_URL AS PATH,RS.CONTAIN_URL AS CONTAINPATH,RS.ICON,RS.PID FROM RESOURCES_SCGSI RS
        LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = RS.ID
        WHERE RS.STATUS = 1 AND RS.TYPES in (1,2,3) AND RS.PORT = #{resourceProt}
		<if test="roleIds!=null and roleIds.size()>0">
			AND RR.ROLE_ID IN
			<foreach collection="roleIds" item="ele" open="(" close=")" separator=",">
				#{ele}
			</foreach>
		</if>
		order by RS.WEIGHTS
	</select>

	<select id="listModuleResourcesByUserIdAndProt" resultType="string" parameterType="string">
		SELECT DISTINCT(RS.COMPONENTS_ID) FROM RESOURCES_SCGSI RS
                                LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = RS.ID
                                LEFT JOIN USER_ROLE UR ON UR.ROLE_ID =RR.ROLE_ID
                                LEFT JOIN ROLE R ON RR.ROLE_ID = R.ID
                                WHERE RS.STATUS = 1 AND R.STATUS = 1 AND RS.TYPES in (0,3) AND RS.PORT = #{resourceProt}
										AND UR.EMPLOYEE_ID =#{userId}

	</select>

	<select id="listModuleResourcesByRoleIdAndProt" resultType="string" parameterType="string">
		SELECT DISTINCT(RS.COMPONENTS_ID) FROM RESOURCES_SCGSI RS
        LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = RS.ID
        WHERE RS.STATUS = 1 AND RS.TYPES in (0,3) AND RS.PORT = #{resourceProt}
        <if test="roleIds!=null and roleIds.size()>0">
			AND RR.ROLE_ID IN
			<foreach collection="roleIds" item="ele" open="(" close=")" separator=",">
				#{ele}
			</foreach>
		</if>
	</select>

</mapper>
