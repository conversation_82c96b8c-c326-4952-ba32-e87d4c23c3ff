package com.swcares.psi.aoMergeOrder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.psi.aoMergeOrder.entity.AoMergeSubOrderJoinInvoiceInfo;
import com.swcares.psi.aoMergeOrder.mapper.AoMergeSubOrderJoinInvoiceInfoMapper;
import com.swcares.psi.aoMergeOrder.service.AoMergeSubOrderJoinInvoiceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 开票关系
 */
@Slf4j
@Service
public class AoMergeSubOrderJoinInvoiceInfoServiceImpl extends ServiceImpl<AoMergeSubOrderJoinInvoiceInfoMapper, AoMergeSubOrderJoinInvoiceInfo> implements AoMergeSubOrderJoinInvoiceInfoService {


}
