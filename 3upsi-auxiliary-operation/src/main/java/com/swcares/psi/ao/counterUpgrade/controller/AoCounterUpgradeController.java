package com.swcares.psi.ao.counterUpgrade.controller;

import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradeConfigDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradeConfigPriceDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradesPriceConfigDto;
import com.swcares.psi.ao.counterUpgrade.dto.AoCounterUpgradesPriceConfigSaveDto;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgradeConfig;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeService;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradesPriceConfigService;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeConfigPriceH5Vo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradeConfigPriceVo;
import com.swcares.psi.ao.counterUpgrade.vo.AoCounterUpgradesPriceConfigPageVo;
import com.swcares.psi.ao.pad.dto.OlpCostUpgradesPriceConfigDto;
import com.swcares.psi.ao.pad.improHander.ImporEntity;
import com.swcares.psi.ao.pad.vo.OlpCostUpgradesCashVo;
import com.swcares.psi.ao.pad.vo.OlpCostUpgradesPriceConfigPageVo;
import com.swcares.psi.base.fileuploadanddownload.UploadAndDownload;
import com.swcares.psi.combine.constant.MessageCode;
import com.swcares.psi.common.security.exception.BusinessException;
import com.swcares.psi.common.utils.ExcelUtils;
import com.swcares.psi.common.utils.query.PsiPage;
import com.swcares.psi.common.utils.query.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;


/**
 * Created on  2020/12/21
 * Title:       [柜台升舱维护]
 * Description: [柜台升舱维护]
 *
 * @author: like
 * @version: 1.0
 */
@Api(tags = "柜台升舱维护")
@RestController
@RequestMapping("/api/ao/upgrade")
@Slf4j
@RefreshScope
public class AoCounterUpgradeController {

    @Autowired
    private AoCounterUpgradeService aoCounterUpgradeService;

    @Autowired
    AoCounterUpgradesPriceConfigService aoCounterUpgradesPriceConfigService;

    @Autowired
    private UploadAndDownload uploadAndDownload;

    @Value("${ao_counter_upgrade_config_templat.path}")
    private String tempFilePath;
    @Value("${ao_counter_upgrade_config_templat.name}")
    private String tempFileName;

    @ApiOperation(value = "新增柜台升舱维护")
    @PostMapping("/aoCounterUpgrade/save")
    public RenderResult<String> saveCounterPriceUpgrade( @RequestBody AoCounterUpgradesPriceConfigSaveDto dto) {
        aoCounterUpgradesPriceConfigService.saveOrUpdateEntity(dto);
        return RenderResult.success();
    }

    @ApiOperation(value = "查询柜台升舱维护信息列表")
    @GetMapping("/aoCounterUpgrade/page")
    public RenderResult<PsiPage<AoCounterUpgradesPriceConfigPageVo>> counterPricePage(AoCounterUpgradesPriceConfigDto dto){
        PsiPage<AoCounterUpgradesPriceConfigPageVo> page = aoCounterUpgradesPriceConfigService.getPage(dto);
        return RenderResult.success(page);
    }

    @ApiOperation(value = "删除柜台升舱维护信息列表")
    @PostMapping("/aoCounterUpgrade/delete")
    public RenderResult<String> deleteCounterUpgrade(
            @RequestBody @ApiParam(name = "ids", value = "升舱维护信息列表信息id", required = true) List<String> ids) {
        log.info("ao-查询柜台升舱维护信息列表接口调用--参数详情：{}", ids.toString());
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(MessageCode.PARAM_EXCEPTION.getCode());
        }
        if (aoCounterUpgradesPriceConfigService.deleteCounterUpgrade(ids)) {
            return RenderResult.success();
        } else {
            return RenderResult.fail();
        }
    }

    @ApiOperation(value = "导入柜台升舱维护信息")
    @PostMapping("/aoCounterUpgrade/importExcel")
    public RenderResult<List<ImporEntity> > importExcel(@RequestParam("file") MultipartFile file)  {
        List<ImporEntity> imporEntities = aoCounterUpgradesPriceConfigService.importExcel(file);
        return RenderResult.success(imporEntities);
    }


    @ApiOperation(value = "下载柜台升舱维护信息列表excel模板")
    @GetMapping("/aoCounterUpgrade/downloadExcel")
    public void downLoadExcel(HttpServletResponse response)  {
        response.setContentType("multipart/form-data");
        try {
            // 下载文件能正常显示中文
            response.addHeader(
                    "Content-Disposition",
                    "attachment;fileName=" + new String("柜台升舱价格模板.xlsx".trim().getBytes("UTF-8"), "iso-8859-1"));
            uploadAndDownload.ftpDownload(response.getOutputStream(), tempFileName, tempFilePath);
        }catch (Exception e){
            log.error("机上升舱价格模板下载错误",e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
    }

    @ApiOperation(value = "获取当前两个航站之间的升舱价格")
    @GetMapping("/aoCounterUpgrade/h5/getPrice")
    public RenderResult<AoCounterUpgradeConfigPriceH5Vo> getCounterUpgradePrice( AoCounterUpgradeConfigPriceDto aoCounterUpgradeConfigPriceDto) {
        return RenderResult.success(aoCounterUpgradesPriceConfigService.getPrice(aoCounterUpgradeConfigPriceDto));
    }

}
