package com.swcares.psi.stimulate.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PcReviewDto {
    private String orderId;

    private String olpUpId;

    @ApiModelProperty(value = "后舱监督人员  乘务员-刘姗, 44292")
    private String supervisionPersonnel;

    @ApiModelProperty(value = "后台销售人员  乘务员-刘姗, 44292")
    private String salesPersonnel;
    @ApiModelProperty(value = "公务舱乘务员  姓名拼接  李星驰,刘芬,周宁B")
    private String businessClassSteward;
}
