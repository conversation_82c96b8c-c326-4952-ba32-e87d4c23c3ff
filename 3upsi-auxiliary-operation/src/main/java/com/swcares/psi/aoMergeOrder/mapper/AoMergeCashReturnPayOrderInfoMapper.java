package com.swcares.psi.aoMergeOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.psi.aoMergeOrder.entity.AoMergeCashReturnPayOrderInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface AoMergeCashReturnPayOrderInfoMapper extends BaseMapper<AoMergeCashReturnPayOrderInfo> {

    Integer validateTurnOrder(@Param("subOrderIdList") List<String> subOrderIdList);
    List<String> turnNotifyOrderNoLock(@Param("turnOrderNo") String turnOrderNo);

}
