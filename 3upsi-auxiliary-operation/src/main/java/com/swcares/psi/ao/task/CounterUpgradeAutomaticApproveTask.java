package com.swcares.psi.ao.task;

import com.swcares.psi.ao.cons.AoCons;
import com.swcares.psi.ao.counterUpgrade.entity.AoCounterUpgrade;
import com.swcares.psi.ao.counterUpgrade.service.AoCounterUpgradeOrderService;
import com.swcares.psi.ao.utils.AoUtils;
import com.swcares.psi.aoMergeOrder.service.IAoMergeCounterUpgradeService;
import com.swcares.psi.base.data.api.enums.SysConfigEnum;
import com.swcares.psi.combine.constant.CacheConstants;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * ClassName：com.swcares.psi.ao.task.CounterUpgradeAutomaticApproveTask <br>
 * Description：柜台升舱系统升舱失败人工手动升舱成功线上申请修改系统升舱状态自动过审任务<br>
 * Copyright © 2023/11/1 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * @date 2023/11/1 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class CounterUpgradeAutomaticApproveTask {

    private final AoCounterUpgradeOrderService aoCounterUpgradeOrderService;

    private final Redisson redisson;

    private final CounterUpgradeAutomaticApproveTask counterUpgradeAutomaticApproveTask;
    private final IAoMergeCounterUpgradeService aoMergeCounterUpgradeService;

    @Lazy
    public CounterUpgradeAutomaticApproveTask(final AoCounterUpgradeOrderService aoCounterUpgradeOrderService, final Redisson redisson, final CounterUpgradeAutomaticApproveTask counterUpgradeAutomaticApproveTask,final IAoMergeCounterUpgradeService aoMergeCounterUpgradeService) {
        this.aoCounterUpgradeOrderService = aoCounterUpgradeOrderService;
        this.redisson = redisson;
        this.counterUpgradeAutomaticApproveTask = counterUpgradeAutomaticApproveTask;
        this.aoMergeCounterUpgradeService = aoMergeCounterUpgradeService;
    }

    public void action() {
        RLock lock = redisson.getLock(CacheConstants.COUNTER_UPGRADE_AUTO_APPROVE_LOCK);
        if (lock == null || !lock.tryLock()) {
            log.info("系统执行修改柜台升舱系统升舱状态任务未获取到分布式锁！");
            return;
        }
        try {
            counterUpgradeAutomaticApproveTask.automaticApprove();
            aoMergeCounterUpgradeService.automaticApprove();
        } catch (Exception e) {
            log.error("系统执行修改柜台升舱系统升舱状态任务出错！", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void automaticApprove() {
        List<AoCounterUpgrade> list = aoCounterUpgradeOrderService.lambdaQuery()
                .eq(AoCounterUpgrade::getUpgradeUpdateStatus, "1")
                .list();
        LocalDateTime now = LocalDateTime.now();
        list.forEach(ele -> {
            String financeOrderNo = AoUtils.getFinanceOrderNo(SysConfigEnum.AO_FINANCE_COUNTER_UPGRADE_SEQUENCE, AoCons.COUNTER_UPGRADE_ACCOUNT_ENTRY_NO_KEY);
            ele.setApproveUserNo("系统");
            ele.setApproveDate(now);
            ele.setAccountEntryNo(financeOrderNo);
            ele.setUpgradeStatus("2");
            ele.setUpgradeUpdateStatus("2");
        });
        if (!list.isEmpty()) {
            aoCounterUpgradeOrderService.updateBatchById(list);
        }
        log.info("系统执行修改柜台升舱系统升舱状态任务执行，完成修改订单列表:{}", list.stream().map(AoCounterUpgrade::getOrderNo).collect(Collectors.joining(",")));
    }
    
}
